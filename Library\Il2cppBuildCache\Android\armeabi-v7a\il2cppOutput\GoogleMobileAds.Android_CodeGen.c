﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Android.AdErrorClient::.ctor(UnityEngine.AndroidJavaObject)
extern void AdErrorClient__ctor_m0185601EFC506718045C5988A1A4FDFA74EE0F1D (void);
// 0x00000002 System.Int32 GoogleMobileAds.Android.AdErrorClient::GetCode()
extern void AdErrorClient_GetCode_m80557F59259F0162E40F032FD4D3117441F05B2E (void);
// 0x00000003 System.String GoogleMobileAds.Android.AdErrorClient::GetDomain()
extern void AdErrorClient_GetDomain_m06CE26AAD8982F4FA0FF74FD2374E0E012B05D7E (void);
// 0x00000004 System.String GoogleMobileAds.Android.AdErrorClient::GetMessage()
extern void AdErrorClient_GetMessage_m5532F15FB8A84044B0BBF9AA3E41B07106DC93B1 (void);
// 0x00000005 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Android.AdErrorClient::GetCause()
extern void AdErrorClient_GetCause_m81BA38976D12BDA6F82C70D5A067077365CF4523 (void);
// 0x00000006 System.String GoogleMobileAds.Android.AdErrorClient::ToString()
extern void AdErrorClient_ToString_m9A794874FDF77FA78777AAA7F85CA35C31C796E0 (void);
// 0x00000007 System.Void GoogleMobileAds.Android.AdInspectorErrorClient::.ctor(UnityEngine.AndroidJavaObject)
extern void AdInspectorErrorClient__ctor_m0E15403523CE5B34AF234411C363A02BEC4D5791 (void);
// 0x00000008 System.Void GoogleMobileAds.Android.AdInspectorListener::.ctor(System.Action`1<GoogleMobileAds.Common.AdInspectorErrorClientEventArgs>)
extern void AdInspectorListener__ctor_mA440C5D4211D77BDAE5A60980CC41985E3EB6C45 (void);
// 0x00000009 System.Void GoogleMobileAds.Android.AdInspectorListener::onAdInspectorClosed(UnityEngine.AndroidJavaObject)
extern void AdInspectorListener_onAdInspectorClosed_mA853FAC0206C53BE1DDC1FBD80A4028C7AB8BC33 (void);
// 0x0000000A System.Void GoogleMobileAds.Android.AdManagerBannerClient::.ctor()
extern void AdManagerBannerClient__ctor_m105F0412C32FB23FD3CCE4EB789A9AD17EEB833F (void);
// 0x0000000B System.Void GoogleMobileAds.Android.AdManagerBannerClient::add_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerBannerClient_add_OnAppEvent_m5F8300D593C203EC05E932142F89A13E12F3716B (void);
// 0x0000000C System.Void GoogleMobileAds.Android.AdManagerBannerClient::remove_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerBannerClient_remove_OnAppEvent_mACF0D286EC76EB0C411CEEB048E7EE0954C2B4DF (void);
// 0x0000000D System.Collections.Generic.List`1<GoogleMobileAds.Api.AdSize> GoogleMobileAds.Android.AdManagerBannerClient::get_ValidAdSizes()
extern void AdManagerBannerClient_get_ValidAdSizes_mEE20AD67859042B4B4FB338D62695F1562349295 (void);
// 0x0000000E System.Void GoogleMobileAds.Android.AdManagerBannerClient::set_ValidAdSizes(System.Collections.Generic.List`1<GoogleMobileAds.Api.AdSize>)
extern void AdManagerBannerClient_set_ValidAdSizes_mB8524468281C51C9677969A4C011C4076A42582C (void);
// 0x0000000F System.Void GoogleMobileAds.Android.AdManagerBannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
extern void AdManagerBannerClient_LoadAd_mB2CA25A1D0FED30345526F391415C292B902DB41 (void);
// 0x00000010 System.Void GoogleMobileAds.Android.AdManagerBannerClient::onAppEvent(System.String,System.String)
extern void AdManagerBannerClient_onAppEvent_m4FCBE07F117DBE2E08B296582B30AB99C2F0F04E (void);
// 0x00000011 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::.ctor()
extern void AdManagerInterstitialClient__ctor_mA6951DFA191F25E486C3466D8D336903259368D3 (void);
// 0x00000012 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdLoaded_m6C9C6F0B61279B1851508A08EDF1A766626899F1 (void);
// 0x00000013 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdLoaded_mD682CB03DEC9D12462F8D1B217D751D021C1A329 (void);
// 0x00000014 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_add_OnAdFailedToLoad_m77825515A4C8567C2456E71F98D0F574870835A4 (void);
// 0x00000015 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdFailedToLoad_m2D075112C6F2735CCA5B45A3EFFC9835E8B26A71 (void);
// 0x00000016 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_add_OnAdFailedToPresentFullScreenContent_mF6B5201DF62CE943677E67F0B3A0D716EC479E23 (void);
// 0x00000017 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m9E07E9AC38CB482921D4E840A63BBC5ACFF87377 (void);
// 0x00000018 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdDidPresentFullScreenContent_m80612F8428316CA7DC697D736A0C1E1A9A3C414B (void);
// 0x00000019 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdDidPresentFullScreenContent_mF57FB8CBFE6CB68F701E2194D069004CA250CD70 (void);
// 0x0000001A System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdDidDismissFullScreenContent_m669360E9BA2847890C721D277998E67735822EEC (void);
// 0x0000001B System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdDidDismissFullScreenContent_mB8275B947032E309960128800D3867BD29DB4BEF (void);
// 0x0000001C System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdDidRecordImpression_m2CDEB1BE44D7CA1A9B85CBCB4C96D30E85705548 (void);
// 0x0000001D System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdDidRecordImpression_mEB7B224B690588A570D84D074E610A9955B8EB79 (void);
// 0x0000001E System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void AdManagerInterstitialClient_add_OnPaidEvent_mD5C0CD5BB2C3B95467CD8BCC424399D5E43B47B4 (void);
// 0x0000001F System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void AdManagerInterstitialClient_remove_OnPaidEvent_m3AB47794A10A8F8E596C834C12569BF52C5341AF (void);
// 0x00000020 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerInterstitialClient_add_OnAppEvent_m21410CF9FEF28DFCF41A9C2064982C90D659BD77 (void);
// 0x00000021 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerInterstitialClient_remove_OnAppEvent_mAD112D67AA5E94F849B473FBB705A70338F4DBE4 (void);
// 0x00000022 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdClicked(System.Action)
extern void AdManagerInterstitialClient_add_OnAdClicked_mDE2B9371C09753FDF9B28848E0B2D63863F2409B (void);
// 0x00000023 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdClicked(System.Action)
extern void AdManagerInterstitialClient_remove_OnAdClicked_m74BFD900CCCBFE2F5FE8FEE5B4AD6C5F71C2F967 (void);
// 0x00000024 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::CreateInterstitialAd()
extern void AdManagerInterstitialClient_CreateInterstitialAd_m1D4B6FDDC565D9D60252F4FC9ACF613F31FC1D36 (void);
// 0x00000025 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void AdManagerInterstitialClient_LoadAd_mCF244AAA92D1DF85680AF00308FEB98E14333E7A (void);
// 0x00000026 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::Show()
extern void AdManagerInterstitialClient_Show_m9C4F6F7C30E4BC40A113EE46247CFFB5C1F44096 (void);
// 0x00000027 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::DestroyInterstitial()
extern void AdManagerInterstitialClient_DestroyInterstitial_mFE1359C56BB83F7AF8DE0F5834F8FCA31C869D77 (void);
// 0x00000028 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.AdManagerInterstitialClient::GetResponseInfoClient()
extern void AdManagerInterstitialClient_GetResponseInfoClient_m512A188956259C9C715BFCABB34A75A66CC31B3A (void);
// 0x00000029 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onInterstitialAdLoaded()
extern void AdManagerInterstitialClient_onInterstitialAdLoaded_m924294020D4DB2C083C2E184B499471D88CC3CA4 (void);
// 0x0000002A System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onInterstitialAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void AdManagerInterstitialClient_onInterstitialAdFailedToLoad_mD5902A9B8D5E0500F3E9FD21AF57EA20313DCC9A (void);
// 0x0000002B System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void AdManagerInterstitialClient_onAdFailedToShowFullScreenContent_mD1C7AA6C891AD45B656B52ACB53D7BC21514737B (void);
// 0x0000002C System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdShowedFullScreenContent()
extern void AdManagerInterstitialClient_onAdShowedFullScreenContent_m94C2597BB3BE12252AE94A85687A706A0038BB4C (void);
// 0x0000002D System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdDismissedFullScreenContent()
extern void AdManagerInterstitialClient_onAdDismissedFullScreenContent_mCB75443DF7D8EE8DB1385D49FCB1E16994051A23 (void);
// 0x0000002E System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdImpression()
extern void AdManagerInterstitialClient_onAdImpression_m087EB282BC848DA7269A56D8D1539C3351735DF0 (void);
// 0x0000002F System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdClicked()
extern void AdManagerInterstitialClient_onAdClicked_mE614F6CC58B0928BA4F5BB6B690E339C1D15E8F9 (void);
// 0x00000030 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void AdManagerInterstitialClient_onPaidEvent_m3AE77BDEB8D3C996D835C95F060E10ECDEE232E0 (void);
// 0x00000031 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAppEvent(System.String,System.String)
extern void AdManagerInterstitialClient_onAppEvent_mBDD85F848CEFB9C7CEF7277B490C7E8FD6001DDC (void);
// 0x00000032 System.Void GoogleMobileAds.Android.AdapterResponseInfoClient::.ctor(UnityEngine.AndroidJavaObject)
extern void AdapterResponseInfoClient__ctor_m9C1DEB469244702C9D2BD01C5BF2BF31B1B2AB02 (void);
// 0x00000033 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdapterClassName()
extern void AdapterResponseInfoClient_get_AdapterClassName_mFB90D8DDAE89249421D3CAE08598273C1EDED93A (void);
// 0x00000034 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceId()
extern void AdapterResponseInfoClient_get_AdSourceId_mF763B4972041647E78C566614871C704F625E3D2 (void);
// 0x00000035 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceName()
extern void AdapterResponseInfoClient_get_AdSourceName_m503C0FFC57DD9D20A5610AEEB24CE52F500C70B2 (void);
// 0x00000036 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceInstanceId()
extern void AdapterResponseInfoClient_get_AdSourceInstanceId_m8DDC39431E1C95F15997D1D67FF206B17822178B (void);
// 0x00000037 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceInstanceName()
extern void AdapterResponseInfoClient_get_AdSourceInstanceName_mDCCC80E687B471116FEFD88E0B9937C4382DA7EA (void);
// 0x00000038 System.Int64 GoogleMobileAds.Android.AdapterResponseInfoClient::get_LatencyMillis()
extern void AdapterResponseInfoClient_get_LatencyMillis_mD8507BA954B48C5F9FE03EAC10631A5B5196A957 (void);
// 0x00000039 System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdUnitMapping()
extern void AdapterResponseInfoClient_get_AdUnitMapping_m9B3E34798B6290296B5E9C5CFF55854FD2718EDF (void);
// 0x0000003A GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdError()
extern void AdapterResponseInfoClient_get_AdError_mA0EBEB424F7542218DCF3BEC6ACEC532F5D6521A (void);
// 0x0000003B System.String GoogleMobileAds.Android.AdapterResponseInfoClient::ToString()
extern void AdapterResponseInfoClient_ToString_m447C839089A58482F4483FE2AFDF4A289ADE20C9 (void);
// 0x0000003C System.Void GoogleMobileAds.Android.AppOpenAdClient::.ctor()
extern void AppOpenAdClient__ctor_m2FE29A763889491EE721163BE219EC4A0BAB3C8E (void);
// 0x0000003D System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdLoaded_m55C4EDB91560B609C65832375099C653CF8848C2 (void);
// 0x0000003E System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdLoaded_mC3C80D20FD5E4BB915BD3EA14A934695D9ABC6AE (void);
// 0x0000003F System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AppOpenAdClient_add_OnAdFailedToLoad_m14C5FD77EE005760BCBADBE9436ACFF683B6DAE7 (void);
// 0x00000040 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AppOpenAdClient_remove_OnAdFailedToLoad_mB0BB5B70E7E63366CCBF31A993D1034F32EF92A6 (void);
// 0x00000041 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void AppOpenAdClient_add_OnPaidEvent_mE0C068D8A71615592852E4CB415489F8A160B019 (void);
// 0x00000042 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void AppOpenAdClient_remove_OnPaidEvent_mD2FD65C1EB8FAEA4FE2392484CBAC3A6AC6A30FE (void);
// 0x00000043 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AppOpenAdClient_add_OnAdFailedToPresentFullScreenContent_m04BDB58DB44FEB01D9F4507F604184C8F7DAB03D (void);
// 0x00000044 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AppOpenAdClient_remove_OnAdFailedToPresentFullScreenContent_m3D779AECA71FFF5053BB02D9DF80D32A998AD207 (void);
// 0x00000045 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdDidPresentFullScreenContent_mC8C5F2905C777F286E320883622665F0289FAE7C (void);
// 0x00000046 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdDidPresentFullScreenContent_m878A705BE6E72279D149B2DA8E2A86D5896AE7D6 (void);
// 0x00000047 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdDidDismissFullScreenContent_m16722863B2BD3CF0A7D30EA5D0E8DEE002140391 (void);
// 0x00000048 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdDidDismissFullScreenContent_mB1825BA289FF874FA07801E4F4E1CC907258FB31 (void);
// 0x00000049 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdDidRecordImpression_m57B1F640A8DC061EEF2DBA0F59AD878B0D089096 (void);
// 0x0000004A System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdDidRecordImpression_m778672C78A73E8CEB3F20D1517625081BB5CCD4B (void);
// 0x0000004B System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdClicked(System.Action)
extern void AppOpenAdClient_add_OnAdClicked_m6744BAE84737CADA09E5035C710A6AD1D3A64017 (void);
// 0x0000004C System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdClicked(System.Action)
extern void AppOpenAdClient_remove_OnAdClicked_m2D7CBFCC8AFFF9E593A6447FB0E136ABE9A26E02 (void);
// 0x0000004D System.Void GoogleMobileAds.Android.AppOpenAdClient::CreateAppOpenAd()
extern void AppOpenAdClient_CreateAppOpenAd_m2D064C41A2A65816DA56A41A2E3D8162BF4642AC (void);
// 0x0000004E System.Void GoogleMobileAds.Android.AppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void AppOpenAdClient_LoadAd_m54DB68DDAA019DA821F3422A7FA8A26AA9BF4EE5 (void);
// 0x0000004F System.Void GoogleMobileAds.Android.AppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest,UnityEngine.ScreenOrientation)
extern void AppOpenAdClient_LoadAd_m9464F9C55BA776E94A8559B040B2647A18EE5B60 (void);
// 0x00000050 System.Void GoogleMobileAds.Android.AppOpenAdClient::Show()
extern void AppOpenAdClient_Show_m6B08D79E8F1711076D7DEA2A06527054C004A2BE (void);
// 0x00000051 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.AppOpenAdClient::GetResponseInfoClient()
extern void AppOpenAdClient_GetResponseInfoClient_mF9ED2B44E49BF513D7085592444833344504E45C (void);
// 0x00000052 System.Void GoogleMobileAds.Android.AppOpenAdClient::DestroyAppOpenAd()
extern void AppOpenAdClient_DestroyAppOpenAd_m5803B1B02343C71251040FB424529F5B87DA89EB (void);
// 0x00000053 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAppOpenAdLoaded()
extern void AppOpenAdClient_onAppOpenAdLoaded_m71E78205E603B5F64555937B0ACBD7F76803D188 (void);
// 0x00000054 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAppOpenAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void AppOpenAdClient_onAppOpenAdFailedToLoad_m52F02F9C87D97EEE6A9F47DED0F9E4BAF412B9E5 (void);
// 0x00000055 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void AppOpenAdClient_onAdFailedToShowFullScreenContent_m469DEDE81E8D52318B2D24FBFA44631BE196BB94 (void);
// 0x00000056 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdShowedFullScreenContent()
extern void AppOpenAdClient_onAdShowedFullScreenContent_m808D62CEDC30DD880073439E0F23243C87F4C855 (void);
// 0x00000057 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdDismissedFullScreenContent()
extern void AppOpenAdClient_onAdDismissedFullScreenContent_m5B38FC721954B9A68EF30BAE0B2C6397DA3F54AD (void);
// 0x00000058 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdImpression()
extern void AppOpenAdClient_onAdImpression_mF7FA73A44C0AC225EBFF6DA946C74894F19C2171 (void);
// 0x00000059 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdClicked()
extern void AppOpenAdClient_onAdClicked_m7025EAEA368A79457E43F2664E298CAF44A21BB4 (void);
// 0x0000005A System.Void GoogleMobileAds.Android.AppOpenAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void AppOpenAdClient_onPaidEvent_m6CC77FCB5F3983734C2F2146ED3F4CC99AC550DA (void);
// 0x0000005B System.Void GoogleMobileAds.Android.AppStateEventClient::.ctor()
extern void AppStateEventClient__ctor_m84C1FA2B7BE1EA3719AB469D885396421EEF7682 (void);
// 0x0000005C System.Void GoogleMobileAds.Android.AppStateEventClient::add_appStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_appStateChanged_m1D4AA6489F8FFF06FF99BD83DAF1B6A3D44561AC (void);
// 0x0000005D System.Void GoogleMobileAds.Android.AppStateEventClient::remove_appStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_appStateChanged_m1632EDB3D054B68EC325F97A3144516D2C4322D3 (void);
// 0x0000005E System.Void GoogleMobileAds.Android.AppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_AppStateChanged_mB1A83171A7D5080940A3ECB29F15FCE6E2A7F060 (void);
// 0x0000005F System.Void GoogleMobileAds.Android.AppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_AppStateChanged_mCC045A0237BCA34BC41AA2D34891715EE4BF8533 (void);
// 0x00000060 System.Void GoogleMobileAds.Android.AppStateEventClient::onAppStateChanged(System.Boolean)
extern void AppStateEventClient_onAppStateChanged_m6DC8D8DB51BB65A7888320EA0F4A82AD4FEAEF07 (void);
// 0x00000061 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::.ctor()
extern void ApplicationPreferencesClient__ctor_mC8A7FB1D1BA82CCBC54110F6B0D4DA2F81EAC17E (void);
// 0x00000062 GoogleMobileAds.Android.ApplicationPreferencesClient GoogleMobileAds.Android.ApplicationPreferencesClient::get_Instance()
extern void ApplicationPreferencesClient_get_Instance_mA432E5B2FAE581F724D5D171409834DBB146BEF6 (void);
// 0x00000063 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::SetInt(System.String,System.Int32)
extern void ApplicationPreferencesClient_SetInt_m74F20DD920A5B368A81C752C85BF1D76E28C0C10 (void);
// 0x00000064 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::SetString(System.String,System.String)
extern void ApplicationPreferencesClient_SetString_m19FFE0B821888927B942A90C840BE9BEEBBC7801 (void);
// 0x00000065 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::.cctor()
extern void ApplicationPreferencesClient__cctor_mDE86FE5D3D1895E2DDBD05968EF650C9E486033D (void);
// 0x00000066 System.Void GoogleMobileAds.Android.BannerClient::.ctor(System.String)
extern void BannerClient__ctor_mB1A47FD4831CEC323AF74B1C80A3FF2437218B68 (void);
// 0x00000067 System.Void GoogleMobileAds.Android.BannerClient::.ctor()
extern void BannerClient__ctor_m0ECA1B3BAF02E232C3C09A7ADACB45217A948888 (void);
// 0x00000068 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_add_OnAdLoaded_m8A6F35A33481C0A18D6AD2E2B6B5821C5F98CF05 (void);
// 0x00000069 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_remove_OnAdLoaded_m1BEF29D2D130947692079B0769AE5F22E6598FCF (void);
// 0x0000006A System.Void GoogleMobileAds.Android.BannerClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void BannerClient_add_OnAdFailedToLoad_mDC08D68E1C0125ED54CCA64D60B403563EA23799 (void);
// 0x0000006B System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void BannerClient_remove_OnAdFailedToLoad_m91ADED11E06329DC9307EA1DF9F10161B3EF377E (void);
// 0x0000006C System.Void GoogleMobileAds.Android.BannerClient::add_OnAdOpening(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_add_OnAdOpening_m6489FB246A3D6F79CB11D0AB285A3716AFCF8F4E (void);
// 0x0000006D System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdOpening(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_remove_OnAdOpening_mAE1AFFD6E65DB39D4324751BB1679AF6FA0582BD (void);
// 0x0000006E System.Void GoogleMobileAds.Android.BannerClient::add_OnAdClosed(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_add_OnAdClosed_mF22894E78439D3937BF2EBF7A9063A39D7236751 (void);
// 0x0000006F System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdClosed(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_remove_OnAdClosed_m582E84F2B6586E28C175C1F198D817553B76D73F (void);
// 0x00000070 System.Void GoogleMobileAds.Android.BannerClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void BannerClient_add_OnPaidEvent_mECD0F9BA85159990381AD8CEDDA7FFB671F46180 (void);
// 0x00000071 System.Void GoogleMobileAds.Android.BannerClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void BannerClient_remove_OnPaidEvent_m0227B71145352BB5095148C6EA3FA0E45F569A45 (void);
// 0x00000072 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdClicked(System.Action)
extern void BannerClient_add_OnAdClicked_m90F5259DB951CC17366A531C496BAAD8ECB98130 (void);
// 0x00000073 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdClicked(System.Action)
extern void BannerClient_remove_OnAdClicked_m3A4D8DB0C5AEA358EE8B5EC33DC844FCA47341EC (void);
// 0x00000074 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdImpressionRecorded(System.Action)
extern void BannerClient_add_OnAdImpressionRecorded_mAF8BCAFACA8D0DB4E7EEC37737D33059D40ADD72 (void);
// 0x00000075 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdImpressionRecorded(System.Action)
extern void BannerClient_remove_OnAdImpressionRecorded_mDDEDCD8C47CC5C4C67DEA57DB2FDCC9C67E59BE8 (void);
// 0x00000076 System.Void GoogleMobileAds.Android.BannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void BannerClient_CreateBannerView_m41DA7317D40B1BE0B2A4654783F0CB2275FD45F9 (void);
// 0x00000077 System.Void GoogleMobileAds.Android.BannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,System.Int32,System.Int32)
extern void BannerClient_CreateBannerView_m5D9AE92B9E2FB266AEA3903E4547A07C6BBA86EE (void);
// 0x00000078 System.Void GoogleMobileAds.Android.BannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
extern void BannerClient_LoadAd_m496B17CC3B59F764463AA063051F2D315B14A845 (void);
// 0x00000079 System.Void GoogleMobileAds.Android.BannerClient::ShowBannerView()
extern void BannerClient_ShowBannerView_m7F1A9B45DCBC108F256AE61DB213DAEDDCE71EF3 (void);
// 0x0000007A System.Void GoogleMobileAds.Android.BannerClient::HideBannerView()
extern void BannerClient_HideBannerView_m3FD46ABABB1C9BD5D42931DA713DF07A9AE7088C (void);
// 0x0000007B System.Void GoogleMobileAds.Android.BannerClient::DestroyBannerView()
extern void BannerClient_DestroyBannerView_m7875BEEA3586EDCF18E0A8D2B4C29AC56E1FD5BA (void);
// 0x0000007C System.Single GoogleMobileAds.Android.BannerClient::GetHeightInPixels()
extern void BannerClient_GetHeightInPixels_m2252B24C6547FD265A53A46332A0C45637BC1552 (void);
// 0x0000007D System.Single GoogleMobileAds.Android.BannerClient::GetWidthInPixels()
extern void BannerClient_GetWidthInPixels_mA0D3DD47C3EE9B4F6E1074BEE57FC529D583B827 (void);
// 0x0000007E System.Void GoogleMobileAds.Android.BannerClient::SetPosition(GoogleMobileAds.Api.AdPosition)
extern void BannerClient_SetPosition_mCF78647F2784591590B5D602D24F59F43B3B978D (void);
// 0x0000007F System.Void GoogleMobileAds.Android.BannerClient::SetPosition(System.Int32,System.Int32)
extern void BannerClient_SetPosition_m738BD54714459E92A44805D2368532236D652D55 (void);
// 0x00000080 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.BannerClient::GetResponseInfoClient()
extern void BannerClient_GetResponseInfoClient_m29746BF2731086874ADA49C96999B509140D7995 (void);
// 0x00000081 System.Void GoogleMobileAds.Android.BannerClient::onAdLoaded()
extern void BannerClient_onAdLoaded_m259743AAFBD13E11CD3DB18D6F93B6198E820666 (void);
// 0x00000082 System.Void GoogleMobileAds.Android.BannerClient::onAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void BannerClient_onAdFailedToLoad_mE13729FFCDACC466C0B185C816414A15AEB34EAD (void);
// 0x00000083 System.Void GoogleMobileAds.Android.BannerClient::onAdOpened()
extern void BannerClient_onAdOpened_m0B5950BE73736B90425E4D1195FF7971996C3372 (void);
// 0x00000084 System.Void GoogleMobileAds.Android.BannerClient::onAdClosed()
extern void BannerClient_onAdClosed_m4E1EA60395EFD69F37B66B810EE0D6E90D2991AC (void);
// 0x00000085 System.Void GoogleMobileAds.Android.BannerClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void BannerClient_onPaidEvent_m00EA9988B1027BE1B0B9CED0B96DBEE7EEE236F6 (void);
// 0x00000086 System.Void GoogleMobileAds.Android.BannerClient::onAdClicked()
extern void BannerClient_onAdClicked_mA24EF72C2E21A955F42ADEC9915E02BB945B9F49 (void);
// 0x00000087 System.Void GoogleMobileAds.Android.BannerClient::onAdImpression()
extern void BannerClient_onAdImpression_mE7036CE7CDC32B7E01B8F42D273B1E1959691B10 (void);
// 0x00000088 System.Void GoogleMobileAds.Android.DisplayMetrics::.ctor()
extern void DisplayMetrics__ctor_m3B44CFB0F73A5483B365734EC3154E2C2ACF00EB (void);
// 0x00000089 System.Single GoogleMobileAds.Android.DisplayMetrics::get_Density()
extern void DisplayMetrics_get_Density_m5BBA9AAF1C438586D623B776DC62D21ADBDD01C3 (void);
// 0x0000008A System.Void GoogleMobileAds.Android.DisplayMetrics::set_Density(System.Single)
extern void DisplayMetrics_set_Density_m244A681967A7B69BB93B3931B59B0CA8FECF5CD9 (void);
// 0x0000008B System.Int32 GoogleMobileAds.Android.DisplayMetrics::get_HeightPixels()
extern void DisplayMetrics_get_HeightPixels_m7BBC143C3B9FFF9F97C69D3E9B799BA764C7A615 (void);
// 0x0000008C System.Void GoogleMobileAds.Android.DisplayMetrics::set_HeightPixels(System.Int32)
extern void DisplayMetrics_set_HeightPixels_m349A1286D37CC049FC26CAB58D7E4B96562F1D85 (void);
// 0x0000008D System.Int32 GoogleMobileAds.Android.DisplayMetrics::get_WidthPixels()
extern void DisplayMetrics_get_WidthPixels_m9AB232432360FC5F26479DC473D20A76CFD27533 (void);
// 0x0000008E System.Void GoogleMobileAds.Android.DisplayMetrics::set_WidthPixels(System.Int32)
extern void DisplayMetrics_set_WidthPixels_m39FDEBE0DF505CE4718746F1DDD63991FBD6A34E (void);
// 0x0000008F System.Void GoogleMobileAds.GoogleMobileAdsClientFactory::.ctor()
extern void GoogleMobileAdsClientFactory__ctor_m5E8C27566C48BA54D8926CC2E5F6008E49895B4C (void);
// 0x00000090 GoogleMobileAds.Common.IAppStateEventClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAppStateEventClient()
extern void GoogleMobileAdsClientFactory_BuildAppStateEventClient_mA98923F97363C7294470909CDF44D5623BE87647 (void);
// 0x00000091 GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAppOpenAdClient()
extern void GoogleMobileAdsClientFactory_BuildAppOpenAdClient_mC1F4DE1C3EAE901A63472DF5527CA3CAD5EA6EB1 (void);
// 0x00000092 GoogleMobileAds.Common.IBannerClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildBannerClient()
extern void GoogleMobileAdsClientFactory_BuildBannerClient_m4AE8AAA31BD80F66A293C160B315F780C4712F81 (void);
// 0x00000093 GoogleMobileAds.Common.IAdManagerBannerClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAdManagerBannerClient()
extern void GoogleMobileAdsClientFactory_BuildAdManagerBannerClient_mB37CE8610A2B42235C7212BCDAA3AE748AE80179 (void);
// 0x00000094 GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildInterstitialClient()
extern void GoogleMobileAdsClientFactory_BuildInterstitialClient_mF0DF1180F7661CF50EB2B98F610C479433594D0F (void);
// 0x00000095 GoogleMobileAds.Common.IAdManagerInterstitialClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAdManagerInterstitialClient()
extern void GoogleMobileAdsClientFactory_BuildAdManagerInterstitialClient_m4B99E4356EB390823F17EB4FDA2713EFBB28E6DD (void);
// 0x00000096 GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildRewardedAdClient()
extern void GoogleMobileAdsClientFactory_BuildRewardedAdClient_m0656DF4184998CA421A5E7A7ACCFEE812E7AFEF8 (void);
// 0x00000097 GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildRewardedInterstitialAdClient()
extern void GoogleMobileAdsClientFactory_BuildRewardedInterstitialAdClient_m6E1B63ADCE9A853B96683C3B3636E98DB514B61A (void);
// 0x00000098 GoogleMobileAds.Common.IApplicationPreferencesClient GoogleMobileAds.GoogleMobileAdsClientFactory::ApplicationPreferencesInstance()
extern void GoogleMobileAdsClientFactory_ApplicationPreferencesInstance_m14E57C1AD32C45EE7CE4E1B22DCCB5C8981B20FE (void);
// 0x00000099 GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.GoogleMobileAdsClientFactory::MobileAdsInstance()
extern void GoogleMobileAdsClientFactory_MobileAdsInstance_mAFD95F983A979855432E64FDED5DF4B49AA62634 (void);
// 0x0000009A System.Void GoogleMobileAds.Android.InitializationStatusClient::.ctor(UnityEngine.AndroidJavaObject)
extern void InitializationStatusClient__ctor_m215EA3EF7CA95B30C4F753F3B6F746F2E8A5A3C8 (void);
// 0x0000009B GoogleMobileAds.Api.AdapterStatus GoogleMobileAds.Android.InitializationStatusClient::getAdapterStatusForClassName(System.String)
extern void InitializationStatusClient_getAdapterStatusForClassName_m2DE6AA537279EA8CAFE1EA00BCB4E141C917025E (void);
// 0x0000009C System.Collections.Generic.Dictionary`2<System.String,GoogleMobileAds.Api.AdapterStatus> GoogleMobileAds.Android.InitializationStatusClient::getAdapterStatusMap()
extern void InitializationStatusClient_getAdapterStatusMap_m2F5A27C28AE21CBF8626E4A1527E4AFA27C0EF8A (void);
// 0x0000009D System.String[] GoogleMobileAds.Android.InitializationStatusClient::getKeys()
extern void InitializationStatusClient_getKeys_m18748D88273B9FD9A2DDE7B52D8205A04B29C194 (void);
// 0x0000009E System.Void GoogleMobileAds.Android.InterstitialClient::.ctor()
extern void InterstitialClient__ctor_m5296112DD8099A52EB278FC271535A4D843980C5 (void);
// 0x0000009F System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdLoaded_mA8F6907D1445BCC0A1DCABD7673542E48D04FC7C (void);
// 0x000000A0 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdLoaded_m1341FAB9FEC34A4F6D42192EB2FB7D11811FA713 (void);
// 0x000000A1 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void InterstitialClient_add_OnAdFailedToLoad_m9CD467FEA5E1DB8D096E4CB35363F3C8FCA910D1 (void);
// 0x000000A2 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void InterstitialClient_remove_OnAdFailedToLoad_mE5C9132865B70FBB803BCF54331DE833BF632B71 (void);
// 0x000000A3 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void InterstitialClient_add_OnAdFailedToPresentFullScreenContent_mD138928B39C07EB119ED7936FB5E27F7254C2B1A (void);
// 0x000000A4 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void InterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m3BB56D63D8206C6536F9D92CBC0A6DFA066F6297 (void);
// 0x000000A5 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdDidPresentFullScreenContent_mD95B98CD4684DDEC3A6822AEDCEF01FE06D9DF1B (void);
// 0x000000A6 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdDidPresentFullScreenContent_mA7FC82FA4C77CD11472685B44F4E6674A8DD1859 (void);
// 0x000000A7 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdDidDismissFullScreenContent_m56A749F001A3FE5AE4AA5C74E69446E92F73012C (void);
// 0x000000A8 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdDidDismissFullScreenContent_m6A809B3099F988FC08A57D2B8293C45FD79F0446 (void);
// 0x000000A9 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdDidRecordImpression_mEAC60BD7C729C0DA8376A45CA8B21020D95F5C92 (void);
// 0x000000AA System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdDidRecordImpression_mB4B40C5986B4C06FDB0654905A4032C2A995E694 (void);
// 0x000000AB System.Void GoogleMobileAds.Android.InterstitialClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void InterstitialClient_add_OnPaidEvent_m2DA4005EB9110EE21EA1B437ECEDE98A79408546 (void);
// 0x000000AC System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void InterstitialClient_remove_OnPaidEvent_m3EFD77533D3B7A1EDB9AF5E86D75B366272A7F1B (void);
// 0x000000AD System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdClicked(System.Action)
extern void InterstitialClient_add_OnAdClicked_m916D31652433DAAADA6D938BEA18F864EDC3400E (void);
// 0x000000AE System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdClicked(System.Action)
extern void InterstitialClient_remove_OnAdClicked_m33AF5AB873523E0A3D6AF6709471F9B764B764D7 (void);
// 0x000000AF System.Void GoogleMobileAds.Android.InterstitialClient::CreateInterstitialAd()
extern void InterstitialClient_CreateInterstitialAd_m4AC3FF31C2F006B0426205541C689BC233F179E1 (void);
// 0x000000B0 System.Void GoogleMobileAds.Android.InterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void InterstitialClient_LoadAd_m3A071DB68F88B4D9402FFE5652ED5E5F2E8F5D79 (void);
// 0x000000B1 System.Void GoogleMobileAds.Android.InterstitialClient::Show()
extern void InterstitialClient_Show_mE168F56BFE957491851C05792611C69EE77EB245 (void);
// 0x000000B2 System.Void GoogleMobileAds.Android.InterstitialClient::DestroyInterstitial()
extern void InterstitialClient_DestroyInterstitial_m72FBE89BF2109DBCD92CDABE0B7E259C15642D62 (void);
// 0x000000B3 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.InterstitialClient::GetResponseInfoClient()
extern void InterstitialClient_GetResponseInfoClient_m61FFFD585C208D470987D2849F3A97B269854127 (void);
// 0x000000B4 System.Void GoogleMobileAds.Android.InterstitialClient::onInterstitialAdLoaded()
extern void InterstitialClient_onInterstitialAdLoaded_mFDB17A3D271A4FA823D402891948BAC580C45DD7 (void);
// 0x000000B5 System.Void GoogleMobileAds.Android.InterstitialClient::onInterstitialAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void InterstitialClient_onInterstitialAdFailedToLoad_m530CA70B59AA2CD1E1B748111764029EFDFB37F0 (void);
// 0x000000B6 System.Void GoogleMobileAds.Android.InterstitialClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void InterstitialClient_onAdFailedToShowFullScreenContent_m8F8D50964DF7D94238A000177CCE7AB3E2F8E102 (void);
// 0x000000B7 System.Void GoogleMobileAds.Android.InterstitialClient::onAdShowedFullScreenContent()
extern void InterstitialClient_onAdShowedFullScreenContent_mE618C335A7E947367570640C935707FE0E99A6CB (void);
// 0x000000B8 System.Void GoogleMobileAds.Android.InterstitialClient::onAdDismissedFullScreenContent()
extern void InterstitialClient_onAdDismissedFullScreenContent_mDACE8063EB10A858C7CB47DE84B0AFF55342044B (void);
// 0x000000B9 System.Void GoogleMobileAds.Android.InterstitialClient::onAdImpression()
extern void InterstitialClient_onAdImpression_m40531DA92D406F0F5C872181D227BDDB1BBAC519 (void);
// 0x000000BA System.Void GoogleMobileAds.Android.InterstitialClient::onAdClicked()
extern void InterstitialClient_onAdClicked_m097A801795CD37191135AFCBF32B665A80FA4977 (void);
// 0x000000BB System.Void GoogleMobileAds.Android.InterstitialClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void InterstitialClient_onPaidEvent_mA4B4FF210FC8C6FEA5196B297539BE01E8B0B9B6 (void);
// 0x000000BC System.Void GoogleMobileAds.Android.LoadAdErrorClient::.ctor(UnityEngine.AndroidJavaObject)
extern void LoadAdErrorClient__ctor_mCF892A9928A98CB198A43199D4C47F2319E34FD5 (void);
// 0x000000BD System.Int32 GoogleMobileAds.Android.LoadAdErrorClient::GetCode()
extern void LoadAdErrorClient_GetCode_mF1EBB0640EB82C000AB4C5D9054F06EC033345C7 (void);
// 0x000000BE System.String GoogleMobileAds.Android.LoadAdErrorClient::GetDomain()
extern void LoadAdErrorClient_GetDomain_m3AADB4656C201AC39EB80BECCB3E40D68FD63C44 (void);
// 0x000000BF System.String GoogleMobileAds.Android.LoadAdErrorClient::GetMessage()
extern void LoadAdErrorClient_GetMessage_m828F8F37466E067D0D6F11DAF173968E5359F806 (void);
// 0x000000C0 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Android.LoadAdErrorClient::GetCause()
extern void LoadAdErrorClient_GetCause_m3D8A6C3DAE436EE7157B14563F5033E3A7E61EF0 (void);
// 0x000000C1 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.LoadAdErrorClient::GetResponseInfoClient()
extern void LoadAdErrorClient_GetResponseInfoClient_m22AB66521AE6F1CC8A6C508D4806FB91A36AAE2B (void);
// 0x000000C2 System.String GoogleMobileAds.Android.LoadAdErrorClient::ToString()
extern void LoadAdErrorClient_ToString_m42D55FD1F1D257D2191E3D585293A55CF856F579 (void);
// 0x000000C3 System.Void GoogleMobileAds.Android.MobileAdsClient::.ctor()
extern void MobileAdsClient__ctor_mA2AA26902EFCECF018B57F4C272C928AD495251D (void);
// 0x000000C4 GoogleMobileAds.Android.MobileAdsClient GoogleMobileAds.Android.MobileAdsClient::get_Instance()
extern void MobileAdsClient_get_Instance_mFB3C674CFE7A94D10717DD4209D485C2150CFF6F (void);
// 0x000000C5 System.Void GoogleMobileAds.Android.MobileAdsClient::Initialize(System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>)
extern void MobileAdsClient_Initialize_m50D417F258672738BFD18D9AA82FC119A8A5C6C3 (void);
// 0x000000C6 System.Void GoogleMobileAds.Android.MobileAdsClient::SetApplicationVolume(System.Single)
extern void MobileAdsClient_SetApplicationVolume_m5784B6EE532DD3707A78CC38AEE9612FF87C5615 (void);
// 0x000000C7 System.Void GoogleMobileAds.Android.MobileAdsClient::DisableMediationInitialization()
extern void MobileAdsClient_DisableMediationInitialization_mABA1D5C78B657CDFB1B6AA839CE78C0C2FCF712D (void);
// 0x000000C8 System.Void GoogleMobileAds.Android.MobileAdsClient::SetApplicationMuted(System.Boolean)
extern void MobileAdsClient_SetApplicationMuted_m4047866E2197341938CA7CEBEB0CD211C5119F6E (void);
// 0x000000C9 System.Void GoogleMobileAds.Android.MobileAdsClient::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
extern void MobileAdsClient_SetRequestConfiguration_m33CEE71BC67F16BBC70176855B469263BDB5DB84 (void);
// 0x000000CA GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Android.MobileAdsClient::GetRequestConfiguration()
extern void MobileAdsClient_GetRequestConfiguration_mA63130DF1D9F123408F7931E15422585A9D5FE89 (void);
// 0x000000CB System.Void GoogleMobileAds.Android.MobileAdsClient::SetiOSAppPauseOnBackground(System.Boolean)
extern void MobileAdsClient_SetiOSAppPauseOnBackground_m2B3ADD9E6F33F2A1063E2BFACAAD42B362D8E239 (void);
// 0x000000CC System.Void GoogleMobileAds.Android.MobileAdsClient::OpenAdInspector(System.Action`1<GoogleMobileAds.Common.AdInspectorErrorClientEventArgs>)
extern void MobileAdsClient_OpenAdInspector_m64441DF6E496BABD7492F7C9091C042D25AA19AA (void);
// 0x000000CD System.Single GoogleMobileAds.Android.MobileAdsClient::GetDeviceScale()
extern void MobileAdsClient_GetDeviceScale_m2F9BE9B5903BC925DAC196CD494B8ABD0FEBBADC (void);
// 0x000000CE System.Int32 GoogleMobileAds.Android.MobileAdsClient::GetDeviceSafeWidth()
extern void MobileAdsClient_GetDeviceSafeWidth_m61122BD9FDC76B65F02C21A937E99C2BB1C6AA09 (void);
// 0x000000CF System.Void GoogleMobileAds.Android.MobileAdsClient::onInitializationComplete(UnityEngine.AndroidJavaObject)
extern void MobileAdsClient_onInitializationComplete_m1A928CD8B564D5C32FF5733D852454B3DF10E1BD (void);
// 0x000000D0 System.Void GoogleMobileAds.Android.MobileAdsClient::.cctor()
extern void MobileAdsClient__cctor_m968D47BBAD3C1307E7E90300F4651D6AB279EB8B (void);
// 0x000000D1 System.Void GoogleMobileAds.Android.RequestConfigurationClient::.ctor()
extern void RequestConfigurationClient__ctor_m8C7864534714E56FC51B2195C788C0BD39FD7E3B (void);
// 0x000000D2 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.RequestConfigurationClient::BuildRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
extern void RequestConfigurationClient_BuildRequestConfiguration_m96D3A2D15C8F5084AE566565188066ED8FE9429C (void);
// 0x000000D3 GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Android.RequestConfigurationClient::GetRequestConfiguration(UnityEngine.AndroidJavaObject)
extern void RequestConfigurationClient_GetRequestConfiguration_m6BB0F11A46A56451F711F8765F86446EEF3B2C00 (void);
// 0x000000D4 System.Void GoogleMobileAds.Android.ResponseInfoClient::.ctor(GoogleMobileAds.Common.ResponseInfoClientType,UnityEngine.AndroidJavaObject)
extern void ResponseInfoClient__ctor_m6D3188882F6C1AFDBEFA44C5E59429CE5DBB1622 (void);
// 0x000000D5 System.Collections.Generic.List`1<GoogleMobileAds.Common.IAdapterResponseInfoClient> GoogleMobileAds.Android.ResponseInfoClient::GetAdapterResponses()
extern void ResponseInfoClient_GetAdapterResponses_m7B10DDAC125319BC32C9C7218CB7846C62FEB91A (void);
// 0x000000D6 GoogleMobileAds.Common.IAdapterResponseInfoClient GoogleMobileAds.Android.ResponseInfoClient::GetLoadedAdapterResponseInfo()
extern void ResponseInfoClient_GetLoadedAdapterResponseInfo_m6E9941D5BEB6FF49735772C46609061458478D84 (void);
// 0x000000D7 System.String GoogleMobileAds.Android.ResponseInfoClient::GetMediationAdapterClassName()
extern void ResponseInfoClient_GetMediationAdapterClassName_m6A5AD6DB52511C02E5F8CDD6764313C31AB28306 (void);
// 0x000000D8 System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Android.ResponseInfoClient::GetResponseExtras()
extern void ResponseInfoClient_GetResponseExtras_m688638F0E3E3E6B061C056647C00DA16BA6E290A (void);
// 0x000000D9 System.String GoogleMobileAds.Android.ResponseInfoClient::GetResponseId()
extern void ResponseInfoClient_GetResponseId_mB902AA39B2FF2533E6E8345013B1546A5F9D2094 (void);
// 0x000000DA System.String GoogleMobileAds.Android.ResponseInfoClient::ToString()
extern void ResponseInfoClient_ToString_m151A096A0A090D4FBF5A116645D7C03C275BA0FE (void);
// 0x000000DB System.Void GoogleMobileAds.Android.RewardedAdClient::.ctor()
extern void RewardedAdClient__ctor_m41AEB4A67C8000CF5C394E7C37F22DF5D4D2DFBE (void);
// 0x000000DC System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdLoaded_m07526AA3F62437BDBBCA1F2A3EA628AF60306636 (void);
// 0x000000DD System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdLoaded_m233567FE8EDD78C794770F6F53C53B3BEA584FD9 (void);
// 0x000000DE System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedAdClient_add_OnAdFailedToLoad_mCDB1A9951BC0E39B1E811447E42FC62CE1ACDCFF (void);
// 0x000000DF System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedAdClient_remove_OnAdFailedToLoad_m3FD61474E11B1A4AC0F9D127FB3E2E7CF526196D (void);
// 0x000000E0 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedAdClient_add_OnUserEarnedReward_m87FD0678E524E831427C5D6350F11660E6ACDE55 (void);
// 0x000000E1 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedAdClient_remove_OnUserEarnedReward_mE52C36121C91D9F0053B1213F03189CCA7C4206B (void);
// 0x000000E2 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void RewardedAdClient_add_OnPaidEvent_mD9C2E353F2A459F62522EAEBE9B7C38380DF9715 (void);
// 0x000000E3 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void RewardedAdClient_remove_OnPaidEvent_m27689B5B588DCCC18AB8A3FFFFAECF5D29119090 (void);
// 0x000000E4 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedAdClient_add_OnAdFailedToPresentFullScreenContent_m0F5BBE757619DA7A5662B262C4601CB11ABCBA4C (void);
// 0x000000E5 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedAdClient_remove_OnAdFailedToPresentFullScreenContent_mD80340DF497C67C5554F9426A4C5FC6897CA3490 (void);
// 0x000000E6 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdDidPresentFullScreenContent_m3A6345BED2192A6D57BAF515C752DCE8CE865C78 (void);
// 0x000000E7 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdDidPresentFullScreenContent_m85023D795C9950362F9403AD2F59112A1DAF6A9D (void);
// 0x000000E8 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdDidDismissFullScreenContent_m9DED5F20A9C41AA5249529B14EAA689FAB741195 (void);
// 0x000000E9 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdDidDismissFullScreenContent_m15A08C33F031B5338C52E1823F0F8ED6C60C1CA5 (void);
// 0x000000EA System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdDidRecordImpression_m6F170C1E856369720D2E1B9E65155E2384634089 (void);
// 0x000000EB System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdDidRecordImpression_m07459DBF9E80064F44838808780AB35860646631 (void);
// 0x000000EC System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdClicked(System.Action)
extern void RewardedAdClient_add_OnAdClicked_m5DD2C1CB3BCF881F81567490E2463623272DCFB2 (void);
// 0x000000ED System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdClicked(System.Action)
extern void RewardedAdClient_remove_OnAdClicked_mBBDBC98DD877D452D28D618AA9C745E36C7B2D2C (void);
// 0x000000EE System.Void GoogleMobileAds.Android.RewardedAdClient::CreateRewardedAd()
extern void RewardedAdClient_CreateRewardedAd_m7FBDF005EB747CF9D66FB0F4E9D8EFE1F0F47B12 (void);
// 0x000000EF System.Void GoogleMobileAds.Android.RewardedAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void RewardedAdClient_LoadAd_m762A2AD29641FDE4D43810634F7DEB91B89B1894 (void);
// 0x000000F0 System.Void GoogleMobileAds.Android.RewardedAdClient::Show()
extern void RewardedAdClient_Show_m496550B0E9BDF58FF3B3106E8D3C1DE449FFDEB4 (void);
// 0x000000F1 System.Void GoogleMobileAds.Android.RewardedAdClient::SetServerSideVerificationOptions(GoogleMobileAds.Api.ServerSideVerificationOptions)
extern void RewardedAdClient_SetServerSideVerificationOptions_mCF1DA490F753D6D8E51B6D92828BAB43B8F96C43 (void);
// 0x000000F2 GoogleMobileAds.Api.Reward GoogleMobileAds.Android.RewardedAdClient::GetRewardItem()
extern void RewardedAdClient_GetRewardItem_mD65E2B8E1684CA0DA3364990CB75CAB1D9A25B6C (void);
// 0x000000F3 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.RewardedAdClient::GetResponseInfoClient()
extern void RewardedAdClient_GetResponseInfoClient_mC1EF3E94F34DFED124A064FFB2D72CCFDF28A7BA (void);
// 0x000000F4 System.Void GoogleMobileAds.Android.RewardedAdClient::DestroyRewardedAd()
extern void RewardedAdClient_DestroyRewardedAd_m6BD4840D8B346A06E925E43FA6556D00C569146D (void);
// 0x000000F5 System.Void GoogleMobileAds.Android.RewardedAdClient::onRewardedAdLoaded()
extern void RewardedAdClient_onRewardedAdLoaded_m600B9594686677C262132144A8D2F2FE4E95134D (void);
// 0x000000F6 System.Void GoogleMobileAds.Android.RewardedAdClient::onRewardedAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void RewardedAdClient_onRewardedAdFailedToLoad_m5CC9B905CAA413C988FAE2DDCE043766D798EA04 (void);
// 0x000000F7 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void RewardedAdClient_onAdFailedToShowFullScreenContent_m8D1EDD2D192E0CBBF0B2994D51A1326F6A9551FC (void);
// 0x000000F8 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdShowedFullScreenContent()
extern void RewardedAdClient_onAdShowedFullScreenContent_mB76B59614B39011ED4CFE37F64DAB33BB5F51FA1 (void);
// 0x000000F9 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdDismissedFullScreenContent()
extern void RewardedAdClient_onAdDismissedFullScreenContent_m58005918F7A0942201310E2FF43193BA10D9F1D1 (void);
// 0x000000FA System.Void GoogleMobileAds.Android.RewardedAdClient::onAdImpression()
extern void RewardedAdClient_onAdImpression_m61A310D933EAFA55720AEB5173D6EA2D18F12319 (void);
// 0x000000FB System.Void GoogleMobileAds.Android.RewardedAdClient::onAdClicked()
extern void RewardedAdClient_onAdClicked_m2EA64B755E5A4BA2A37D665E2D81C85476531D45 (void);
// 0x000000FC System.Void GoogleMobileAds.Android.RewardedAdClient::onUserEarnedReward(System.String,System.Single)
extern void RewardedAdClient_onUserEarnedReward_m99ED20CC533A6AEC3BE818E8733A8E5AB5BFD81B (void);
// 0x000000FD System.Void GoogleMobileAds.Android.RewardedAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void RewardedAdClient_onPaidEvent_m9B0A0376003912071432C1E3E1ABA93D936FE523 (void);
// 0x000000FE System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::.ctor()
extern void RewardedInterstitialAdClient__ctor_m74FCA7CFA8C8D38F5F0C1B4D3171179B82FBD6DB (void);
// 0x000000FF System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdLoaded_m7497C801540548F4DCC4563672170CB0AFF5119E (void);
// 0x00000100 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdLoaded_m69199E4DFB945C91A4B23D6FF1A5AE2A004B6C7F (void);
// 0x00000101 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdFailedToLoad_mAE481E9C7FF442E7057EF0D617FACFA5AE2DAD7E (void);
// 0x00000102 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdFailedToLoad_m19420BD5F20C960E1035CF701CBDD60E8C8BDCED (void);
// 0x00000103 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedInterstitialAdClient_add_OnUserEarnedReward_m57EA995F940C547CF223F9C9BBA7C5FEAB2DE2A5 (void);
// 0x00000104 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedInterstitialAdClient_remove_OnUserEarnedReward_m4961DF438BE47F08BCF8C5DE8A374C98FD6F2C2E (void);
// 0x00000105 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void RewardedInterstitialAdClient_add_OnPaidEvent_m0BC04064FBFB1F3567591B642DB8A51CDC9FB14D (void);
// 0x00000106 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
extern void RewardedInterstitialAdClient_remove_OnPaidEvent_m5FC98286CDC6675308D6A912470C20B6EA1A8582 (void);
// 0x00000107 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdFailedToPresentFullScreenContent_m9153B7D019C1B1B8205EBD884C0A62992D01D1D2 (void);
// 0x00000108 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdFailedToPresentFullScreenContent_m431C8BAC0F5A005F3774C2A3085B6C19ECD0DFFF (void);
// 0x00000109 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdDidPresentFullScreenContent_m760E279DA9401C2BADDC3463E67CD6A26BED310B (void);
// 0x0000010A System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdDidPresentFullScreenContent_mA028A5E52BC8DAF9559A3DFEB3C76172E484CCC5 (void);
// 0x0000010B System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdDidDismissFullScreenContent_mAFCAEF1A11EBEFF7F9FDAD4063E5874239500E54 (void);
// 0x0000010C System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdDidDismissFullScreenContent_mDA42064AFBC479BAB958045E049865580F6F5D6D (void);
// 0x0000010D System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdDidRecordImpression_m9E5EF225D4A68598F5190FADD0ED050B92BA3D5D (void);
// 0x0000010E System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdDidRecordImpression_m75A89BA956B771F5440F2751C375A92A4A106D32 (void);
// 0x0000010F System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdClicked(System.Action)
extern void RewardedInterstitialAdClient_add_OnAdClicked_mE6A7025656686589845F35377E46EEA24EF7C8B2 (void);
// 0x00000110 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdClicked(System.Action)
extern void RewardedInterstitialAdClient_remove_OnAdClicked_m67DE196EC623040CBC374AC4AED32C90D1724611 (void);
// 0x00000111 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::CreateRewardedInterstitialAd()
extern void RewardedInterstitialAdClient_CreateRewardedInterstitialAd_mCC1AF2C202E7461D2D94EAEA3BFAE740A422EE7A (void);
// 0x00000112 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void RewardedInterstitialAdClient_LoadAd_m834FF38F0C8CEF2975F7A3096D521BFE78F6E349 (void);
// 0x00000113 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::Show()
extern void RewardedInterstitialAdClient_Show_mE7B0DE792CE9CE9DB80E563885222657DA724985 (void);
// 0x00000114 GoogleMobileAds.Api.Reward GoogleMobileAds.Android.RewardedInterstitialAdClient::GetRewardItem()
extern void RewardedInterstitialAdClient_GetRewardItem_mB3F8C8457A9411D5FFA1F810B117CF0217E5AAE3 (void);
// 0x00000115 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::SetServerSideVerificationOptions(GoogleMobileAds.Api.ServerSideVerificationOptions)
extern void RewardedInterstitialAdClient_SetServerSideVerificationOptions_m4C78B0AC378E427695BBF9E07047EFF592B6AAA0 (void);
// 0x00000116 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.RewardedInterstitialAdClient::GetResponseInfoClient()
extern void RewardedInterstitialAdClient_GetResponseInfoClient_m302751C7FE2D3A561C5747AAADD37F158BC1C930 (void);
// 0x00000117 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::DestroyRewardedInterstitialAd()
extern void RewardedInterstitialAdClient_DestroyRewardedInterstitialAd_m0CEE1307380907DAE5ADFC75E9362FE01DE62F20 (void);
// 0x00000118 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onRewardedInterstitialAdLoaded()
extern void RewardedInterstitialAdClient_onRewardedInterstitialAdLoaded_m5DC3E116C3881C48E81B139B592A373FF0690997 (void);
// 0x00000119 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onRewardedInterstitialAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void RewardedInterstitialAdClient_onRewardedInterstitialAdFailedToLoad_mD2712E0AA78D957251B5779A8F2D836CBBD74CC9 (void);
// 0x0000011A System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void RewardedInterstitialAdClient_onAdFailedToShowFullScreenContent_m57691FE66D9AA5A707735EE2766D222A03FE8FA6 (void);
// 0x0000011B System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdShowedFullScreenContent()
extern void RewardedInterstitialAdClient_onAdShowedFullScreenContent_m245CE50E2AB77C6184AAD24034328E05C0A78229 (void);
// 0x0000011C System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdDismissedFullScreenContent()
extern void RewardedInterstitialAdClient_onAdDismissedFullScreenContent_m4871CC12E70D023F101E7753593D03ECBA82BBC5 (void);
// 0x0000011D System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdImpression()
extern void RewardedInterstitialAdClient_onAdImpression_m624653E10E386298DC18D4E211ACF238BD4B40B7 (void);
// 0x0000011E System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdClicked()
extern void RewardedInterstitialAdClient_onAdClicked_m3C3705C6243570CD2600A77844E23771965C2DC9 (void);
// 0x0000011F System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onUserEarnedReward(System.String,System.Single)
extern void RewardedInterstitialAdClient_onUserEarnedReward_mCDD3BA6C84008D94ADE87622E56D30A7C9FED506 (void);
// 0x00000120 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void RewardedInterstitialAdClient_onPaidEvent_m7C6390FBFA195B1401FB1B3CBDF952009EDB5C1E (void);
// 0x00000121 System.Void GoogleMobileAds.Android.Utils::.ctor()
extern void Utils__ctor_m569C5D7514FBA4299E890AAEBEB042C9E779ADED (void);
// 0x00000122 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetAdSizeJavaObject(GoogleMobileAds.Api.AdSize)
extern void Utils_GetAdSizeJavaObject_mD29A1675DE25DC4E29F5A3283732ED145F7AF35D (void);
// 0x00000123 System.Int32 GoogleMobileAds.Android.Utils::GetAppOpenAdOrientation(UnityEngine.ScreenOrientation)
extern void Utils_GetAppOpenAdOrientation_m1B20D87B5E263D45B61988688E0BB4C6A515F521 (void);
// 0x00000124 System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Android.Utils::GetDictionary(UnityEngine.AndroidJavaObject)
extern void Utils_GetDictionary_m343858B493463E56AA7C8AE42792149B22B162B8 (void);
// 0x00000125 System.Int32 GoogleMobileAds.Android.Utils::GetScreenWidth()
extern void Utils_GetScreenWidth_m34B29CCB9487724AF545597CDF60913CFBCCB058 (void);
// 0x00000126 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetAdRequestJavaObject(GoogleMobileAds.Api.AdRequest,System.String)
extern void Utils_GetAdRequestJavaObject_mD64364AB1CC93E8FA0C79E3F345B45B26D2C950D (void);
// 0x00000127 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetAdManagerAdRequestJavaObject(GoogleMobileAds.Api.AdRequest,System.String)
extern void Utils_GetAdManagerAdRequestJavaObject_mC080B7D28C353C3A5C319702687F91EB260F73D2 (void);
// 0x00000128 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetJavaListObject(System.Collections.Generic.List`1<System.String>)
extern void Utils_GetJavaListObject_mAD6D1C8F238330040F2E0C3106F08B1A5D74D6C0 (void);
// 0x00000129 System.Collections.Generic.List`1<System.String> GoogleMobileAds.Android.Utils::GetCsTypeList(UnityEngine.AndroidJavaObject)
extern void Utils_GetCsTypeList_m6CBC1A7B651FAD9856463A3E74EF32329BE49F57 (void);
// 0x0000012A UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetServerSideVerificationOptionsJavaObject(GoogleMobileAds.Api.ServerSideVerificationOptions)
extern void Utils_GetServerSideVerificationOptionsJavaObject_m2DD6ACBB2E7D85E82ECFFF00302945B515E25233 (void);
static Il2CppMethodPointer s_methodPointers[298] = 
{
	AdErrorClient__ctor_m0185601EFC506718045C5988A1A4FDFA74EE0F1D,
	AdErrorClient_GetCode_m80557F59259F0162E40F032FD4D3117441F05B2E,
	AdErrorClient_GetDomain_m06CE26AAD8982F4FA0FF74FD2374E0E012B05D7E,
	AdErrorClient_GetMessage_m5532F15FB8A84044B0BBF9AA3E41B07106DC93B1,
	AdErrorClient_GetCause_m81BA38976D12BDA6F82C70D5A067077365CF4523,
	AdErrorClient_ToString_m9A794874FDF77FA78777AAA7F85CA35C31C796E0,
	AdInspectorErrorClient__ctor_m0E15403523CE5B34AF234411C363A02BEC4D5791,
	AdInspectorListener__ctor_mA440C5D4211D77BDAE5A60980CC41985E3EB6C45,
	AdInspectorListener_onAdInspectorClosed_mA853FAC0206C53BE1DDC1FBD80A4028C7AB8BC33,
	AdManagerBannerClient__ctor_m105F0412C32FB23FD3CCE4EB789A9AD17EEB833F,
	AdManagerBannerClient_add_OnAppEvent_m5F8300D593C203EC05E932142F89A13E12F3716B,
	AdManagerBannerClient_remove_OnAppEvent_mACF0D286EC76EB0C411CEEB048E7EE0954C2B4DF,
	AdManagerBannerClient_get_ValidAdSizes_mEE20AD67859042B4B4FB338D62695F1562349295,
	AdManagerBannerClient_set_ValidAdSizes_mB8524468281C51C9677969A4C011C4076A42582C,
	AdManagerBannerClient_LoadAd_mB2CA25A1D0FED30345526F391415C292B902DB41,
	AdManagerBannerClient_onAppEvent_m4FCBE07F117DBE2E08B296582B30AB99C2F0F04E,
	AdManagerInterstitialClient__ctor_mA6951DFA191F25E486C3466D8D336903259368D3,
	AdManagerInterstitialClient_add_OnAdLoaded_m6C9C6F0B61279B1851508A08EDF1A766626899F1,
	AdManagerInterstitialClient_remove_OnAdLoaded_mD682CB03DEC9D12462F8D1B217D751D021C1A329,
	AdManagerInterstitialClient_add_OnAdFailedToLoad_m77825515A4C8567C2456E71F98D0F574870835A4,
	AdManagerInterstitialClient_remove_OnAdFailedToLoad_m2D075112C6F2735CCA5B45A3EFFC9835E8B26A71,
	AdManagerInterstitialClient_add_OnAdFailedToPresentFullScreenContent_mF6B5201DF62CE943677E67F0B3A0D716EC479E23,
	AdManagerInterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m9E07E9AC38CB482921D4E840A63BBC5ACFF87377,
	AdManagerInterstitialClient_add_OnAdDidPresentFullScreenContent_m80612F8428316CA7DC697D736A0C1E1A9A3C414B,
	AdManagerInterstitialClient_remove_OnAdDidPresentFullScreenContent_mF57FB8CBFE6CB68F701E2194D069004CA250CD70,
	AdManagerInterstitialClient_add_OnAdDidDismissFullScreenContent_m669360E9BA2847890C721D277998E67735822EEC,
	AdManagerInterstitialClient_remove_OnAdDidDismissFullScreenContent_mB8275B947032E309960128800D3867BD29DB4BEF,
	AdManagerInterstitialClient_add_OnAdDidRecordImpression_m2CDEB1BE44D7CA1A9B85CBCB4C96D30E85705548,
	AdManagerInterstitialClient_remove_OnAdDidRecordImpression_mEB7B224B690588A570D84D074E610A9955B8EB79,
	AdManagerInterstitialClient_add_OnPaidEvent_mD5C0CD5BB2C3B95467CD8BCC424399D5E43B47B4,
	AdManagerInterstitialClient_remove_OnPaidEvent_m3AB47794A10A8F8E596C834C12569BF52C5341AF,
	AdManagerInterstitialClient_add_OnAppEvent_m21410CF9FEF28DFCF41A9C2064982C90D659BD77,
	AdManagerInterstitialClient_remove_OnAppEvent_mAD112D67AA5E94F849B473FBB705A70338F4DBE4,
	AdManagerInterstitialClient_add_OnAdClicked_mDE2B9371C09753FDF9B28848E0B2D63863F2409B,
	AdManagerInterstitialClient_remove_OnAdClicked_m74BFD900CCCBFE2F5FE8FEE5B4AD6C5F71C2F967,
	AdManagerInterstitialClient_CreateInterstitialAd_m1D4B6FDDC565D9D60252F4FC9ACF613F31FC1D36,
	AdManagerInterstitialClient_LoadAd_mCF244AAA92D1DF85680AF00308FEB98E14333E7A,
	AdManagerInterstitialClient_Show_m9C4F6F7C30E4BC40A113EE46247CFFB5C1F44096,
	AdManagerInterstitialClient_DestroyInterstitial_mFE1359C56BB83F7AF8DE0F5834F8FCA31C869D77,
	AdManagerInterstitialClient_GetResponseInfoClient_m512A188956259C9C715BFCABB34A75A66CC31B3A,
	AdManagerInterstitialClient_onInterstitialAdLoaded_m924294020D4DB2C083C2E184B499471D88CC3CA4,
	AdManagerInterstitialClient_onInterstitialAdFailedToLoad_mD5902A9B8D5E0500F3E9FD21AF57EA20313DCC9A,
	AdManagerInterstitialClient_onAdFailedToShowFullScreenContent_mD1C7AA6C891AD45B656B52ACB53D7BC21514737B,
	AdManagerInterstitialClient_onAdShowedFullScreenContent_m94C2597BB3BE12252AE94A85687A706A0038BB4C,
	AdManagerInterstitialClient_onAdDismissedFullScreenContent_mCB75443DF7D8EE8DB1385D49FCB1E16994051A23,
	AdManagerInterstitialClient_onAdImpression_m087EB282BC848DA7269A56D8D1539C3351735DF0,
	AdManagerInterstitialClient_onAdClicked_mE614F6CC58B0928BA4F5BB6B690E339C1D15E8F9,
	AdManagerInterstitialClient_onPaidEvent_m3AE77BDEB8D3C996D835C95F060E10ECDEE232E0,
	AdManagerInterstitialClient_onAppEvent_mBDD85F848CEFB9C7CEF7277B490C7E8FD6001DDC,
	AdapterResponseInfoClient__ctor_m9C1DEB469244702C9D2BD01C5BF2BF31B1B2AB02,
	AdapterResponseInfoClient_get_AdapterClassName_mFB90D8DDAE89249421D3CAE08598273C1EDED93A,
	AdapterResponseInfoClient_get_AdSourceId_mF763B4972041647E78C566614871C704F625E3D2,
	AdapterResponseInfoClient_get_AdSourceName_m503C0FFC57DD9D20A5610AEEB24CE52F500C70B2,
	AdapterResponseInfoClient_get_AdSourceInstanceId_m8DDC39431E1C95F15997D1D67FF206B17822178B,
	AdapterResponseInfoClient_get_AdSourceInstanceName_mDCCC80E687B471116FEFD88E0B9937C4382DA7EA,
	AdapterResponseInfoClient_get_LatencyMillis_mD8507BA954B48C5F9FE03EAC10631A5B5196A957,
	AdapterResponseInfoClient_get_AdUnitMapping_m9B3E34798B6290296B5E9C5CFF55854FD2718EDF,
	AdapterResponseInfoClient_get_AdError_mA0EBEB424F7542218DCF3BEC6ACEC532F5D6521A,
	AdapterResponseInfoClient_ToString_m447C839089A58482F4483FE2AFDF4A289ADE20C9,
	AppOpenAdClient__ctor_m2FE29A763889491EE721163BE219EC4A0BAB3C8E,
	AppOpenAdClient_add_OnAdLoaded_m55C4EDB91560B609C65832375099C653CF8848C2,
	AppOpenAdClient_remove_OnAdLoaded_mC3C80D20FD5E4BB915BD3EA14A934695D9ABC6AE,
	AppOpenAdClient_add_OnAdFailedToLoad_m14C5FD77EE005760BCBADBE9436ACFF683B6DAE7,
	AppOpenAdClient_remove_OnAdFailedToLoad_mB0BB5B70E7E63366CCBF31A993D1034F32EF92A6,
	AppOpenAdClient_add_OnPaidEvent_mE0C068D8A71615592852E4CB415489F8A160B019,
	AppOpenAdClient_remove_OnPaidEvent_mD2FD65C1EB8FAEA4FE2392484CBAC3A6AC6A30FE,
	AppOpenAdClient_add_OnAdFailedToPresentFullScreenContent_m04BDB58DB44FEB01D9F4507F604184C8F7DAB03D,
	AppOpenAdClient_remove_OnAdFailedToPresentFullScreenContent_m3D779AECA71FFF5053BB02D9DF80D32A998AD207,
	AppOpenAdClient_add_OnAdDidPresentFullScreenContent_mC8C5F2905C777F286E320883622665F0289FAE7C,
	AppOpenAdClient_remove_OnAdDidPresentFullScreenContent_m878A705BE6E72279D149B2DA8E2A86D5896AE7D6,
	AppOpenAdClient_add_OnAdDidDismissFullScreenContent_m16722863B2BD3CF0A7D30EA5D0E8DEE002140391,
	AppOpenAdClient_remove_OnAdDidDismissFullScreenContent_mB1825BA289FF874FA07801E4F4E1CC907258FB31,
	AppOpenAdClient_add_OnAdDidRecordImpression_m57B1F640A8DC061EEF2DBA0F59AD878B0D089096,
	AppOpenAdClient_remove_OnAdDidRecordImpression_m778672C78A73E8CEB3F20D1517625081BB5CCD4B,
	AppOpenAdClient_add_OnAdClicked_m6744BAE84737CADA09E5035C710A6AD1D3A64017,
	AppOpenAdClient_remove_OnAdClicked_m2D7CBFCC8AFFF9E593A6447FB0E136ABE9A26E02,
	AppOpenAdClient_CreateAppOpenAd_m2D064C41A2A65816DA56A41A2E3D8162BF4642AC,
	AppOpenAdClient_LoadAd_m54DB68DDAA019DA821F3422A7FA8A26AA9BF4EE5,
	AppOpenAdClient_LoadAd_m9464F9C55BA776E94A8559B040B2647A18EE5B60,
	AppOpenAdClient_Show_m6B08D79E8F1711076D7DEA2A06527054C004A2BE,
	AppOpenAdClient_GetResponseInfoClient_mF9ED2B44E49BF513D7085592444833344504E45C,
	AppOpenAdClient_DestroyAppOpenAd_m5803B1B02343C71251040FB424529F5B87DA89EB,
	AppOpenAdClient_onAppOpenAdLoaded_m71E78205E603B5F64555937B0ACBD7F76803D188,
	AppOpenAdClient_onAppOpenAdFailedToLoad_m52F02F9C87D97EEE6A9F47DED0F9E4BAF412B9E5,
	AppOpenAdClient_onAdFailedToShowFullScreenContent_m469DEDE81E8D52318B2D24FBFA44631BE196BB94,
	AppOpenAdClient_onAdShowedFullScreenContent_m808D62CEDC30DD880073439E0F23243C87F4C855,
	AppOpenAdClient_onAdDismissedFullScreenContent_m5B38FC721954B9A68EF30BAE0B2C6397DA3F54AD,
	AppOpenAdClient_onAdImpression_mF7FA73A44C0AC225EBFF6DA946C74894F19C2171,
	AppOpenAdClient_onAdClicked_m7025EAEA368A79457E43F2664E298CAF44A21BB4,
	AppOpenAdClient_onPaidEvent_m6CC77FCB5F3983734C2F2146ED3F4CC99AC550DA,
	AppStateEventClient__ctor_m84C1FA2B7BE1EA3719AB469D885396421EEF7682,
	AppStateEventClient_add_appStateChanged_m1D4AA6489F8FFF06FF99BD83DAF1B6A3D44561AC,
	AppStateEventClient_remove_appStateChanged_m1632EDB3D054B68EC325F97A3144516D2C4322D3,
	AppStateEventClient_add_AppStateChanged_mB1A83171A7D5080940A3ECB29F15FCE6E2A7F060,
	AppStateEventClient_remove_AppStateChanged_mCC045A0237BCA34BC41AA2D34891715EE4BF8533,
	AppStateEventClient_onAppStateChanged_m6DC8D8DB51BB65A7888320EA0F4A82AD4FEAEF07,
	ApplicationPreferencesClient__ctor_mC8A7FB1D1BA82CCBC54110F6B0D4DA2F81EAC17E,
	ApplicationPreferencesClient_get_Instance_mA432E5B2FAE581F724D5D171409834DBB146BEF6,
	ApplicationPreferencesClient_SetInt_m74F20DD920A5B368A81C752C85BF1D76E28C0C10,
	ApplicationPreferencesClient_SetString_m19FFE0B821888927B942A90C840BE9BEEBBC7801,
	ApplicationPreferencesClient__cctor_mDE86FE5D3D1895E2DDBD05968EF650C9E486033D,
	BannerClient__ctor_mB1A47FD4831CEC323AF74B1C80A3FF2437218B68,
	BannerClient__ctor_m0ECA1B3BAF02E232C3C09A7ADACB45217A948888,
	BannerClient_add_OnAdLoaded_m8A6F35A33481C0A18D6AD2E2B6B5821C5F98CF05,
	BannerClient_remove_OnAdLoaded_m1BEF29D2D130947692079B0769AE5F22E6598FCF,
	BannerClient_add_OnAdFailedToLoad_mDC08D68E1C0125ED54CCA64D60B403563EA23799,
	BannerClient_remove_OnAdFailedToLoad_m91ADED11E06329DC9307EA1DF9F10161B3EF377E,
	BannerClient_add_OnAdOpening_m6489FB246A3D6F79CB11D0AB285A3716AFCF8F4E,
	BannerClient_remove_OnAdOpening_mAE1AFFD6E65DB39D4324751BB1679AF6FA0582BD,
	BannerClient_add_OnAdClosed_mF22894E78439D3937BF2EBF7A9063A39D7236751,
	BannerClient_remove_OnAdClosed_m582E84F2B6586E28C175C1F198D817553B76D73F,
	BannerClient_add_OnPaidEvent_mECD0F9BA85159990381AD8CEDDA7FFB671F46180,
	BannerClient_remove_OnPaidEvent_m0227B71145352BB5095148C6EA3FA0E45F569A45,
	BannerClient_add_OnAdClicked_m90F5259DB951CC17366A531C496BAAD8ECB98130,
	BannerClient_remove_OnAdClicked_m3A4D8DB0C5AEA358EE8B5EC33DC844FCA47341EC,
	BannerClient_add_OnAdImpressionRecorded_mAF8BCAFACA8D0DB4E7EEC37737D33059D40ADD72,
	BannerClient_remove_OnAdImpressionRecorded_mDDEDCD8C47CC5C4C67DEA57DB2FDCC9C67E59BE8,
	BannerClient_CreateBannerView_m41DA7317D40B1BE0B2A4654783F0CB2275FD45F9,
	BannerClient_CreateBannerView_m5D9AE92B9E2FB266AEA3903E4547A07C6BBA86EE,
	BannerClient_LoadAd_m496B17CC3B59F764463AA063051F2D315B14A845,
	BannerClient_ShowBannerView_m7F1A9B45DCBC108F256AE61DB213DAEDDCE71EF3,
	BannerClient_HideBannerView_m3FD46ABABB1C9BD5D42931DA713DF07A9AE7088C,
	BannerClient_DestroyBannerView_m7875BEEA3586EDCF18E0A8D2B4C29AC56E1FD5BA,
	BannerClient_GetHeightInPixels_m2252B24C6547FD265A53A46332A0C45637BC1552,
	BannerClient_GetWidthInPixels_mA0D3DD47C3EE9B4F6E1074BEE57FC529D583B827,
	BannerClient_SetPosition_mCF78647F2784591590B5D602D24F59F43B3B978D,
	BannerClient_SetPosition_m738BD54714459E92A44805D2368532236D652D55,
	BannerClient_GetResponseInfoClient_m29746BF2731086874ADA49C96999B509140D7995,
	BannerClient_onAdLoaded_m259743AAFBD13E11CD3DB18D6F93B6198E820666,
	BannerClient_onAdFailedToLoad_mE13729FFCDACC466C0B185C816414A15AEB34EAD,
	BannerClient_onAdOpened_m0B5950BE73736B90425E4D1195FF7971996C3372,
	BannerClient_onAdClosed_m4E1EA60395EFD69F37B66B810EE0D6E90D2991AC,
	BannerClient_onPaidEvent_m00EA9988B1027BE1B0B9CED0B96DBEE7EEE236F6,
	BannerClient_onAdClicked_mA24EF72C2E21A955F42ADEC9915E02BB945B9F49,
	BannerClient_onAdImpression_mE7036CE7CDC32B7E01B8F42D273B1E1959691B10,
	DisplayMetrics__ctor_m3B44CFB0F73A5483B365734EC3154E2C2ACF00EB,
	DisplayMetrics_get_Density_m5BBA9AAF1C438586D623B776DC62D21ADBDD01C3,
	DisplayMetrics_set_Density_m244A681967A7B69BB93B3931B59B0CA8FECF5CD9,
	DisplayMetrics_get_HeightPixels_m7BBC143C3B9FFF9F97C69D3E9B799BA764C7A615,
	DisplayMetrics_set_HeightPixels_m349A1286D37CC049FC26CAB58D7E4B96562F1D85,
	DisplayMetrics_get_WidthPixels_m9AB232432360FC5F26479DC473D20A76CFD27533,
	DisplayMetrics_set_WidthPixels_m39FDEBE0DF505CE4718746F1DDD63991FBD6A34E,
	GoogleMobileAdsClientFactory__ctor_m5E8C27566C48BA54D8926CC2E5F6008E49895B4C,
	GoogleMobileAdsClientFactory_BuildAppStateEventClient_mA98923F97363C7294470909CDF44D5623BE87647,
	GoogleMobileAdsClientFactory_BuildAppOpenAdClient_mC1F4DE1C3EAE901A63472DF5527CA3CAD5EA6EB1,
	GoogleMobileAdsClientFactory_BuildBannerClient_m4AE8AAA31BD80F66A293C160B315F780C4712F81,
	GoogleMobileAdsClientFactory_BuildAdManagerBannerClient_mB37CE8610A2B42235C7212BCDAA3AE748AE80179,
	GoogleMobileAdsClientFactory_BuildInterstitialClient_mF0DF1180F7661CF50EB2B98F610C479433594D0F,
	GoogleMobileAdsClientFactory_BuildAdManagerInterstitialClient_m4B99E4356EB390823F17EB4FDA2713EFBB28E6DD,
	GoogleMobileAdsClientFactory_BuildRewardedAdClient_m0656DF4184998CA421A5E7A7ACCFEE812E7AFEF8,
	GoogleMobileAdsClientFactory_BuildRewardedInterstitialAdClient_m6E1B63ADCE9A853B96683C3B3636E98DB514B61A,
	GoogleMobileAdsClientFactory_ApplicationPreferencesInstance_m14E57C1AD32C45EE7CE4E1B22DCCB5C8981B20FE,
	GoogleMobileAdsClientFactory_MobileAdsInstance_mAFD95F983A979855432E64FDED5DF4B49AA62634,
	InitializationStatusClient__ctor_m215EA3EF7CA95B30C4F753F3B6F746F2E8A5A3C8,
	InitializationStatusClient_getAdapterStatusForClassName_m2DE6AA537279EA8CAFE1EA00BCB4E141C917025E,
	InitializationStatusClient_getAdapterStatusMap_m2F5A27C28AE21CBF8626E4A1527E4AFA27C0EF8A,
	InitializationStatusClient_getKeys_m18748D88273B9FD9A2DDE7B52D8205A04B29C194,
	InterstitialClient__ctor_m5296112DD8099A52EB278FC271535A4D843980C5,
	InterstitialClient_add_OnAdLoaded_mA8F6907D1445BCC0A1DCABD7673542E48D04FC7C,
	InterstitialClient_remove_OnAdLoaded_m1341FAB9FEC34A4F6D42192EB2FB7D11811FA713,
	InterstitialClient_add_OnAdFailedToLoad_m9CD467FEA5E1DB8D096E4CB35363F3C8FCA910D1,
	InterstitialClient_remove_OnAdFailedToLoad_mE5C9132865B70FBB803BCF54331DE833BF632B71,
	InterstitialClient_add_OnAdFailedToPresentFullScreenContent_mD138928B39C07EB119ED7936FB5E27F7254C2B1A,
	InterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m3BB56D63D8206C6536F9D92CBC0A6DFA066F6297,
	InterstitialClient_add_OnAdDidPresentFullScreenContent_mD95B98CD4684DDEC3A6822AEDCEF01FE06D9DF1B,
	InterstitialClient_remove_OnAdDidPresentFullScreenContent_mA7FC82FA4C77CD11472685B44F4E6674A8DD1859,
	InterstitialClient_add_OnAdDidDismissFullScreenContent_m56A749F001A3FE5AE4AA5C74E69446E92F73012C,
	InterstitialClient_remove_OnAdDidDismissFullScreenContent_m6A809B3099F988FC08A57D2B8293C45FD79F0446,
	InterstitialClient_add_OnAdDidRecordImpression_mEAC60BD7C729C0DA8376A45CA8B21020D95F5C92,
	InterstitialClient_remove_OnAdDidRecordImpression_mB4B40C5986B4C06FDB0654905A4032C2A995E694,
	InterstitialClient_add_OnPaidEvent_m2DA4005EB9110EE21EA1B437ECEDE98A79408546,
	InterstitialClient_remove_OnPaidEvent_m3EFD77533D3B7A1EDB9AF5E86D75B366272A7F1B,
	InterstitialClient_add_OnAdClicked_m916D31652433DAAADA6D938BEA18F864EDC3400E,
	InterstitialClient_remove_OnAdClicked_m33AF5AB873523E0A3D6AF6709471F9B764B764D7,
	InterstitialClient_CreateInterstitialAd_m4AC3FF31C2F006B0426205541C689BC233F179E1,
	InterstitialClient_LoadAd_m3A071DB68F88B4D9402FFE5652ED5E5F2E8F5D79,
	InterstitialClient_Show_mE168F56BFE957491851C05792611C69EE77EB245,
	InterstitialClient_DestroyInterstitial_m72FBE89BF2109DBCD92CDABE0B7E259C15642D62,
	InterstitialClient_GetResponseInfoClient_m61FFFD585C208D470987D2849F3A97B269854127,
	InterstitialClient_onInterstitialAdLoaded_mFDB17A3D271A4FA823D402891948BAC580C45DD7,
	InterstitialClient_onInterstitialAdFailedToLoad_m530CA70B59AA2CD1E1B748111764029EFDFB37F0,
	InterstitialClient_onAdFailedToShowFullScreenContent_m8F8D50964DF7D94238A000177CCE7AB3E2F8E102,
	InterstitialClient_onAdShowedFullScreenContent_mE618C335A7E947367570640C935707FE0E99A6CB,
	InterstitialClient_onAdDismissedFullScreenContent_mDACE8063EB10A858C7CB47DE84B0AFF55342044B,
	InterstitialClient_onAdImpression_m40531DA92D406F0F5C872181D227BDDB1BBAC519,
	InterstitialClient_onAdClicked_m097A801795CD37191135AFCBF32B665A80FA4977,
	InterstitialClient_onPaidEvent_mA4B4FF210FC8C6FEA5196B297539BE01E8B0B9B6,
	LoadAdErrorClient__ctor_mCF892A9928A98CB198A43199D4C47F2319E34FD5,
	LoadAdErrorClient_GetCode_mF1EBB0640EB82C000AB4C5D9054F06EC033345C7,
	LoadAdErrorClient_GetDomain_m3AADB4656C201AC39EB80BECCB3E40D68FD63C44,
	LoadAdErrorClient_GetMessage_m828F8F37466E067D0D6F11DAF173968E5359F806,
	LoadAdErrorClient_GetCause_m3D8A6C3DAE436EE7157B14563F5033E3A7E61EF0,
	LoadAdErrorClient_GetResponseInfoClient_m22AB66521AE6F1CC8A6C508D4806FB91A36AAE2B,
	LoadAdErrorClient_ToString_m42D55FD1F1D257D2191E3D585293A55CF856F579,
	MobileAdsClient__ctor_mA2AA26902EFCECF018B57F4C272C928AD495251D,
	MobileAdsClient_get_Instance_mFB3C674CFE7A94D10717DD4209D485C2150CFF6F,
	MobileAdsClient_Initialize_m50D417F258672738BFD18D9AA82FC119A8A5C6C3,
	MobileAdsClient_SetApplicationVolume_m5784B6EE532DD3707A78CC38AEE9612FF87C5615,
	MobileAdsClient_DisableMediationInitialization_mABA1D5C78B657CDFB1B6AA839CE78C0C2FCF712D,
	MobileAdsClient_SetApplicationMuted_m4047866E2197341938CA7CEBEB0CD211C5119F6E,
	MobileAdsClient_SetRequestConfiguration_m33CEE71BC67F16BBC70176855B469263BDB5DB84,
	MobileAdsClient_GetRequestConfiguration_mA63130DF1D9F123408F7931E15422585A9D5FE89,
	MobileAdsClient_SetiOSAppPauseOnBackground_m2B3ADD9E6F33F2A1063E2BFACAAD42B362D8E239,
	MobileAdsClient_OpenAdInspector_m64441DF6E496BABD7492F7C9091C042D25AA19AA,
	MobileAdsClient_GetDeviceScale_m2F9BE9B5903BC925DAC196CD494B8ABD0FEBBADC,
	MobileAdsClient_GetDeviceSafeWidth_m61122BD9FDC76B65F02C21A937E99C2BB1C6AA09,
	MobileAdsClient_onInitializationComplete_m1A928CD8B564D5C32FF5733D852454B3DF10E1BD,
	MobileAdsClient__cctor_m968D47BBAD3C1307E7E90300F4651D6AB279EB8B,
	RequestConfigurationClient__ctor_m8C7864534714E56FC51B2195C788C0BD39FD7E3B,
	RequestConfigurationClient_BuildRequestConfiguration_m96D3A2D15C8F5084AE566565188066ED8FE9429C,
	RequestConfigurationClient_GetRequestConfiguration_m6BB0F11A46A56451F711F8765F86446EEF3B2C00,
	ResponseInfoClient__ctor_m6D3188882F6C1AFDBEFA44C5E59429CE5DBB1622,
	ResponseInfoClient_GetAdapterResponses_m7B10DDAC125319BC32C9C7218CB7846C62FEB91A,
	ResponseInfoClient_GetLoadedAdapterResponseInfo_m6E9941D5BEB6FF49735772C46609061458478D84,
	ResponseInfoClient_GetMediationAdapterClassName_m6A5AD6DB52511C02E5F8CDD6764313C31AB28306,
	ResponseInfoClient_GetResponseExtras_m688638F0E3E3E6B061C056647C00DA16BA6E290A,
	ResponseInfoClient_GetResponseId_mB902AA39B2FF2533E6E8345013B1546A5F9D2094,
	ResponseInfoClient_ToString_m151A096A0A090D4FBF5A116645D7C03C275BA0FE,
	RewardedAdClient__ctor_m41AEB4A67C8000CF5C394E7C37F22DF5D4D2DFBE,
	RewardedAdClient_add_OnAdLoaded_m07526AA3F62437BDBBCA1F2A3EA628AF60306636,
	RewardedAdClient_remove_OnAdLoaded_m233567FE8EDD78C794770F6F53C53B3BEA584FD9,
	RewardedAdClient_add_OnAdFailedToLoad_mCDB1A9951BC0E39B1E811447E42FC62CE1ACDCFF,
	RewardedAdClient_remove_OnAdFailedToLoad_m3FD61474E11B1A4AC0F9D127FB3E2E7CF526196D,
	RewardedAdClient_add_OnUserEarnedReward_m87FD0678E524E831427C5D6350F11660E6ACDE55,
	RewardedAdClient_remove_OnUserEarnedReward_mE52C36121C91D9F0053B1213F03189CCA7C4206B,
	RewardedAdClient_add_OnPaidEvent_mD9C2E353F2A459F62522EAEBE9B7C38380DF9715,
	RewardedAdClient_remove_OnPaidEvent_m27689B5B588DCCC18AB8A3FFFFAECF5D29119090,
	RewardedAdClient_add_OnAdFailedToPresentFullScreenContent_m0F5BBE757619DA7A5662B262C4601CB11ABCBA4C,
	RewardedAdClient_remove_OnAdFailedToPresentFullScreenContent_mD80340DF497C67C5554F9426A4C5FC6897CA3490,
	RewardedAdClient_add_OnAdDidPresentFullScreenContent_m3A6345BED2192A6D57BAF515C752DCE8CE865C78,
	RewardedAdClient_remove_OnAdDidPresentFullScreenContent_m85023D795C9950362F9403AD2F59112A1DAF6A9D,
	RewardedAdClient_add_OnAdDidDismissFullScreenContent_m9DED5F20A9C41AA5249529B14EAA689FAB741195,
	RewardedAdClient_remove_OnAdDidDismissFullScreenContent_m15A08C33F031B5338C52E1823F0F8ED6C60C1CA5,
	RewardedAdClient_add_OnAdDidRecordImpression_m6F170C1E856369720D2E1B9E65155E2384634089,
	RewardedAdClient_remove_OnAdDidRecordImpression_m07459DBF9E80064F44838808780AB35860646631,
	RewardedAdClient_add_OnAdClicked_m5DD2C1CB3BCF881F81567490E2463623272DCFB2,
	RewardedAdClient_remove_OnAdClicked_mBBDBC98DD877D452D28D618AA9C745E36C7B2D2C,
	RewardedAdClient_CreateRewardedAd_m7FBDF005EB747CF9D66FB0F4E9D8EFE1F0F47B12,
	RewardedAdClient_LoadAd_m762A2AD29641FDE4D43810634F7DEB91B89B1894,
	RewardedAdClient_Show_m496550B0E9BDF58FF3B3106E8D3C1DE449FFDEB4,
	RewardedAdClient_SetServerSideVerificationOptions_mCF1DA490F753D6D8E51B6D92828BAB43B8F96C43,
	RewardedAdClient_GetRewardItem_mD65E2B8E1684CA0DA3364990CB75CAB1D9A25B6C,
	RewardedAdClient_GetResponseInfoClient_mC1EF3E94F34DFED124A064FFB2D72CCFDF28A7BA,
	RewardedAdClient_DestroyRewardedAd_m6BD4840D8B346A06E925E43FA6556D00C569146D,
	RewardedAdClient_onRewardedAdLoaded_m600B9594686677C262132144A8D2F2FE4E95134D,
	RewardedAdClient_onRewardedAdFailedToLoad_m5CC9B905CAA413C988FAE2DDCE043766D798EA04,
	RewardedAdClient_onAdFailedToShowFullScreenContent_m8D1EDD2D192E0CBBF0B2994D51A1326F6A9551FC,
	RewardedAdClient_onAdShowedFullScreenContent_mB76B59614B39011ED4CFE37F64DAB33BB5F51FA1,
	RewardedAdClient_onAdDismissedFullScreenContent_m58005918F7A0942201310E2FF43193BA10D9F1D1,
	RewardedAdClient_onAdImpression_m61A310D933EAFA55720AEB5173D6EA2D18F12319,
	RewardedAdClient_onAdClicked_m2EA64B755E5A4BA2A37D665E2D81C85476531D45,
	RewardedAdClient_onUserEarnedReward_m99ED20CC533A6AEC3BE818E8733A8E5AB5BFD81B,
	RewardedAdClient_onPaidEvent_m9B0A0376003912071432C1E3E1ABA93D936FE523,
	RewardedInterstitialAdClient__ctor_m74FCA7CFA8C8D38F5F0C1B4D3171179B82FBD6DB,
	RewardedInterstitialAdClient_add_OnAdLoaded_m7497C801540548F4DCC4563672170CB0AFF5119E,
	RewardedInterstitialAdClient_remove_OnAdLoaded_m69199E4DFB945C91A4B23D6FF1A5AE2A004B6C7F,
	RewardedInterstitialAdClient_add_OnAdFailedToLoad_mAE481E9C7FF442E7057EF0D617FACFA5AE2DAD7E,
	RewardedInterstitialAdClient_remove_OnAdFailedToLoad_m19420BD5F20C960E1035CF701CBDD60E8C8BDCED,
	RewardedInterstitialAdClient_add_OnUserEarnedReward_m57EA995F940C547CF223F9C9BBA7C5FEAB2DE2A5,
	RewardedInterstitialAdClient_remove_OnUserEarnedReward_m4961DF438BE47F08BCF8C5DE8A374C98FD6F2C2E,
	RewardedInterstitialAdClient_add_OnPaidEvent_m0BC04064FBFB1F3567591B642DB8A51CDC9FB14D,
	RewardedInterstitialAdClient_remove_OnPaidEvent_m5FC98286CDC6675308D6A912470C20B6EA1A8582,
	RewardedInterstitialAdClient_add_OnAdFailedToPresentFullScreenContent_m9153B7D019C1B1B8205EBD884C0A62992D01D1D2,
	RewardedInterstitialAdClient_remove_OnAdFailedToPresentFullScreenContent_m431C8BAC0F5A005F3774C2A3085B6C19ECD0DFFF,
	RewardedInterstitialAdClient_add_OnAdDidPresentFullScreenContent_m760E279DA9401C2BADDC3463E67CD6A26BED310B,
	RewardedInterstitialAdClient_remove_OnAdDidPresentFullScreenContent_mA028A5E52BC8DAF9559A3DFEB3C76172E484CCC5,
	RewardedInterstitialAdClient_add_OnAdDidDismissFullScreenContent_mAFCAEF1A11EBEFF7F9FDAD4063E5874239500E54,
	RewardedInterstitialAdClient_remove_OnAdDidDismissFullScreenContent_mDA42064AFBC479BAB958045E049865580F6F5D6D,
	RewardedInterstitialAdClient_add_OnAdDidRecordImpression_m9E5EF225D4A68598F5190FADD0ED050B92BA3D5D,
	RewardedInterstitialAdClient_remove_OnAdDidRecordImpression_m75A89BA956B771F5440F2751C375A92A4A106D32,
	RewardedInterstitialAdClient_add_OnAdClicked_mE6A7025656686589845F35377E46EEA24EF7C8B2,
	RewardedInterstitialAdClient_remove_OnAdClicked_m67DE196EC623040CBC374AC4AED32C90D1724611,
	RewardedInterstitialAdClient_CreateRewardedInterstitialAd_mCC1AF2C202E7461D2D94EAEA3BFAE740A422EE7A,
	RewardedInterstitialAdClient_LoadAd_m834FF38F0C8CEF2975F7A3096D521BFE78F6E349,
	RewardedInterstitialAdClient_Show_mE7B0DE792CE9CE9DB80E563885222657DA724985,
	RewardedInterstitialAdClient_GetRewardItem_mB3F8C8457A9411D5FFA1F810B117CF0217E5AAE3,
	RewardedInterstitialAdClient_SetServerSideVerificationOptions_m4C78B0AC378E427695BBF9E07047EFF592B6AAA0,
	RewardedInterstitialAdClient_GetResponseInfoClient_m302751C7FE2D3A561C5747AAADD37F158BC1C930,
	RewardedInterstitialAdClient_DestroyRewardedInterstitialAd_m0CEE1307380907DAE5ADFC75E9362FE01DE62F20,
	RewardedInterstitialAdClient_onRewardedInterstitialAdLoaded_m5DC3E116C3881C48E81B139B592A373FF0690997,
	RewardedInterstitialAdClient_onRewardedInterstitialAdFailedToLoad_mD2712E0AA78D957251B5779A8F2D836CBBD74CC9,
	RewardedInterstitialAdClient_onAdFailedToShowFullScreenContent_m57691FE66D9AA5A707735EE2766D222A03FE8FA6,
	RewardedInterstitialAdClient_onAdShowedFullScreenContent_m245CE50E2AB77C6184AAD24034328E05C0A78229,
	RewardedInterstitialAdClient_onAdDismissedFullScreenContent_m4871CC12E70D023F101E7753593D03ECBA82BBC5,
	RewardedInterstitialAdClient_onAdImpression_m624653E10E386298DC18D4E211ACF238BD4B40B7,
	RewardedInterstitialAdClient_onAdClicked_m3C3705C6243570CD2600A77844E23771965C2DC9,
	RewardedInterstitialAdClient_onUserEarnedReward_mCDD3BA6C84008D94ADE87622E56D30A7C9FED506,
	RewardedInterstitialAdClient_onPaidEvent_m7C6390FBFA195B1401FB1B3CBDF952009EDB5C1E,
	Utils__ctor_m569C5D7514FBA4299E890AAEBEB042C9E779ADED,
	Utils_GetAdSizeJavaObject_mD29A1675DE25DC4E29F5A3283732ED145F7AF35D,
	Utils_GetAppOpenAdOrientation_m1B20D87B5E263D45B61988688E0BB4C6A515F521,
	Utils_GetDictionary_m343858B493463E56AA7C8AE42792149B22B162B8,
	Utils_GetScreenWidth_m34B29CCB9487724AF545597CDF60913CFBCCB058,
	Utils_GetAdRequestJavaObject_mD64364AB1CC93E8FA0C79E3F345B45B26D2C950D,
	Utils_GetAdManagerAdRequestJavaObject_mC080B7D28C353C3A5C319702687F91EB260F73D2,
	Utils_GetJavaListObject_mAD6D1C8F238330040F2E0C3106F08B1A5D74D6C0,
	Utils_GetCsTypeList_m6CBC1A7B651FAD9856463A3E74EF32329BE49F57,
	Utils_GetServerSideVerificationOptionsJavaObject_m2DD6ACBB2E7D85E82ECFFF00302945B515E25233,
};
static const int32_t s_InvokerIndices[298] = 
{
	1610,
	1886,
	1900,
	1900,
	1900,
	1900,
	1610,
	1610,
	1610,
	1935,
	1610,
	1610,
	1900,
	1610,
	1610,
	934,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1935,
	1900,
	1935,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	542,
	934,
	1610,
	1900,
	1900,
	1900,
	1900,
	1900,
	1887,
	1900,
	1900,
	1900,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	572,
	1935,
	1900,
	1935,
	1935,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	542,
	1935,
	1610,
	1610,
	1610,
	1610,
	1571,
	1935,
	3215,
	931,
	934,
	3231,
	1610,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	572,
	374,
	1610,
	1935,
	1935,
	1935,
	1921,
	1921,
	1597,
	871,
	1900,
	1935,
	1610,
	1935,
	1935,
	542,
	1935,
	1935,
	1935,
	1921,
	1628,
	1886,
	1597,
	1886,
	1597,
	1935,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1610,
	1431,
	1900,
	1900,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1935,
	1900,
	1935,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	542,
	1610,
	1886,
	1900,
	1900,
	1900,
	1900,
	1900,
	1935,
	3215,
	1610,
	1628,
	1935,
	1571,
	1610,
	1900,
	1571,
	1610,
	1921,
	1886,
	1610,
	3231,
	1935,
	3069,
	3069,
	880,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1610,
	1900,
	1900,
	1935,
	1935,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	936,
	542,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1900,
	1610,
	1900,
	1935,
	1935,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	936,
	542,
	1935,
	3069,
	3013,
	3069,
	3209,
	2795,
	2795,
	3069,
	3069,
	3069,
};
extern const CustomAttributesCacheGenerator g_GoogleMobileAds_Android_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Android_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Android_CodeGenModule = 
{
	"GoogleMobileAds.Android.dll",
	298,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_GoogleMobileAds_Android_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
