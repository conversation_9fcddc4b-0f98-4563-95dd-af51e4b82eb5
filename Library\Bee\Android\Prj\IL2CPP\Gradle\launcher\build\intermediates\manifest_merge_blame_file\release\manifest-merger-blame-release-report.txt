1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.smg.tractor.trolly.games.farming.game"
4    android:installLocation="preferExternal"
5    android:versionCode="24"
6    android:versionName="2.4" >
7
8    <uses-sdk
9        android:minSdkVersion="23"
9-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
10        android:targetSdkVersion="36" />
10-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
11
12    <supports-screens
12-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-163
13        android:anyDensity="true"
13-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:135-160
14        android:largeScreens="true"
14-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:78-105
15        android:normalScreens="true"
15-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:49-77
16        android:smallScreens="true"
16-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:21-48
17        android:xlargeScreens="true" />
17-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:106-134
18
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-79
19-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-76
20    <uses-permission android:name="android.permission.INTERNET" />
20-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-67
20-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:22-64
21
22    <uses-feature android:glEsVersion="0x00020000" />
22-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-54
22-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:19-51
23    <uses-feature
23-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-14:36
24        android:name="android.hardware.touchscreen"
24-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-52
25        android:required="false" />
25-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-33
26    <uses-feature
26-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:5-17:36
27        android:name="android.hardware.touchscreen.multitouch"
27-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:9-63
28        android:required="false" />
28-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-33
29    <uses-feature
29-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:5-20:36
30        android:name="android.hardware.touchscreen.multitouch.distinct"
30-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:9-72
31        android:required="false" />
31-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:9-33
32
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
33-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:22-76
34    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
34-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
34-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:22-79
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
35-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
35-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:22-85
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
36-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
36-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
37    <queries>
37-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
38
39        <!-- For browser content -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
41            <action android:name="android.intent.action.VIEW" />
41-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
41-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
42
43            <category android:name="android.intent.category.BROWSABLE" />
43-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
43-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
44
45            <data android:scheme="https" />
45-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
45-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
46        </intent>
47        <!-- End of browser content -->
48        <!-- For CustomTabsService -->
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
50            <action android:name="android.support.customtabs.action.CustomTabsService" />
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
51        </intent>
52        <!-- End of CustomTabsService -->
53        <!-- For MRAID capabilities -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
55            <action android:name="android.intent.action.INSERT" />
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
56
57            <data android:mimeType="vnd.android.cursor.dir/event" />
57-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
58        </intent>
59        <intent>
59-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
61
62            <data android:scheme="sms" />
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
63        </intent>
64        <intent>
64-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
65            <action android:name="android.intent.action.DIAL" />
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
66
67            <data android:path="tel:" />
67-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
68        </intent>
69        <!-- End of MRAID capabilities -->
70    </queries>
71
72    <uses-permission android:name="android.permission.WAKE_LOCK" />
72-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:24:5-68
72-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:24:22-65
73    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
73-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
73-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
74    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
74-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
74-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
75
76    <application
76-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-83
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
78        android:extractNativeLibs="true"
78-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:18-50
79        android:icon="@mipmap/app_icon"
79-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-80
80        android:label="@string/app_name" >
80-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:16-48
81        <activity
81-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:9-44:20
82            android:name="com.unity3d.player.UnityPlayerActivity"
82-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:24:13-66
83            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
83-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:13-194
84            android:exported="true"
84-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:26:13-36
85            android:hardwareAccelerated="false"
85-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-48
86            android:launchMode="singleTask"
86-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:13-44
87            android:resizeableActivity="false"
87-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:13-47
88            android:screenOrientation="userLandscape"
88-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:13-54
89            android:theme="@style/UnityThemeSelector" >
89-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:13-54
90            <intent-filter>
90-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-36:29
91                <action android:name="android.intent.action.MAIN" />
91-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:17-69
91-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:17-77
93-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:27-74
94            </intent-filter>
95
96            <meta-data
96-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:13-40:40
97                android:name="unityplayer.UnityActivity"
97-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:17-57
98                android:value="true" />
98-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:17-37
99            <meta-data
99-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:13-43:40
100                android:name="android.notch_support"
100-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:42:17-53
101                android:value="true" />
101-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:17-37
102        </activity>
103
104        <meta-data
104-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:9-48:33
105            android:name="unity.splash-mode"
105-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:47:13-45
106            android:value="2" />
106-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:48:13-30
107        <meta-data
107-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:9-51:36
108            android:name="unity.splash-enable"
108-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:13-47
109            android:value="True" />
109-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:13-33
110        <meta-data
110-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:52:9-54:36
111            android:name="unity.launch-fullscreen"
111-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:53:13-51
112            android:value="True" />
112-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:54:13-33
113        <meta-data
113-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:55:9-57:50
114            android:name="notch.config"
114-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:13-40
115            android:value="portrait|landscape" />
115-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:13-47
116        <meta-data
116-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:58:9-60:36
117            android:name="unity.auto-report-fully-drawn"
117-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:13-57
118            android:value="true" />
118-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:13-33
119
120        <activity
120-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
121            android:name="com.google.android.gms.common.api.GoogleApiActivity"
121-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:20:19-85
122            android:exported="false"
122-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:22:19-43
123            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
123-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:21:19-78
124        <activity
124-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
125            android:name="com.google.android.gms.ads.AdActivity"
125-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
126            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
126-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
127            android:exported="false"
127-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
128            android:theme="@android:style/Theme.Translucent" />
128-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
129
130        <provider
130-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
131            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
131-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
132            android:authorities="com.smg.tractor.trolly.games.farming.game.mobileadsinitprovider"
132-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
133            android:exported="false"
133-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
134            android:initOrder="100" />
134-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
135
136        <service
136-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
137            android:name="com.google.android.gms.ads.AdService"
137-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
138            android:enabled="true"
138-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
139            android:exported="false" />
139-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
140
141        <activity
141-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
142            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
142-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
143            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
143-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
144            android:exported="false" />
144-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
145        <activity
145-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
146            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
146-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
147            android:excludeFromRecents="true"
147-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
148            android:exported="false"
148-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
149            android:launchMode="singleTask"
149-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
150            android:taskAffinity=""
150-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
151            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
151-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
152
153        <meta-data
153-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
154            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
154-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
155            android:value="true" />
155-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
156        <meta-data
156-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
157            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
157-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
158            android:value="true" />
158-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
159
160        <service
160-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:28:9-34:19
161            android:name="com.google.firebase.components.ComponentDiscoveryService"
161-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:29:13-84
162            android:directBootAware="true"
162-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:34:13-43
163            android:exported="false" >
163-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:30:13-37
164            <meta-data
164-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
165                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
165-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
167            <meta-data
167-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:18:13-20:85
168                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
168-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:19:17-127
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:20:17-82
170        </service>
171
172        <provider
172-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:25:9-30:39
173            android:name="com.google.firebase.provider.FirebaseInitProvider"
173-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:26:13-77
174            android:authorities="com.smg.tractor.trolly.games.farming.game.firebaseinitprovider"
174-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:27:13-72
175            android:directBootAware="true"
175-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:28:13-43
176            android:exported="false"
176-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:29:13-37
177            android:initOrder="100" />
177-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:30:13-36
178
179        <receiver
179-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
180            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
180-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
181            android:enabled="true"
181-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
182            android:exported="false" >
182-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
183        </receiver>
184
185        <service
185-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
186            android:name="com.google.android.gms.measurement.AppMeasurementService"
186-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
187            android:enabled="true"
187-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
188            android:exported="false" />
188-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
189        <service
189-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
190            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
190-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
191            android:enabled="true"
191-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
192            android:exported="false"
192-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
193            android:permission="android.permission.BIND_JOB_SERVICE" />
193-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
194
195        <meta-data
195-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
196            android:name="com.google.android.gms.version"
196-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
197            android:value="@integer/google_play_services_version" />
197-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
198
199        <provider
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
200            android:name="androidx.startup.InitializationProvider"
200-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
201            android:authorities="com.smg.tractor.trolly.games.farming.game.androidx-startup"
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
203            <meta-data
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
204                android:name="androidx.work.WorkManagerInitializer"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
205                android:value="androidx.startup" />
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
206            <meta-data
206-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
207-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
208                android:value="androidx.startup" />
208-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
209            <meta-data
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
211                android:value="androidx.startup" />
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
212        </provider>
213
214        <service
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
215            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
217            android:enabled="@bool/enable_system_alarm_service_default"
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
218            android:exported="false" />
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
219        <service
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
220            android:name="androidx.work.impl.background.systemjob.SystemJobService"
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
222            android:enabled="@bool/enable_system_job_service_default"
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
223            android:exported="true"
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
224            android:permission="android.permission.BIND_JOB_SERVICE" />
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
225        <service
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
226            android:name="androidx.work.impl.foreground.SystemForegroundService"
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
228            android:enabled="@bool/enable_system_foreground_service_default"
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
229            android:exported="false" />
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
230
231        <receiver
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
232            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
233            android:directBootAware="false"
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
234            android:enabled="true"
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
235            android:exported="false" />
235-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
236        <receiver
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
237            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
239            android:enabled="false"
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
240            android:exported="false" >
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
241            <intent-filter>
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
242                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
243                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
244            </intent-filter>
245        </receiver>
246        <receiver
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
247            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
249            android:enabled="false"
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
250            android:exported="false" >
250-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
251            <intent-filter>
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
252                <action android:name="android.intent.action.BATTERY_OKAY" />
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
253                <action android:name="android.intent.action.BATTERY_LOW" />
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
254            </intent-filter>
255        </receiver>
256        <receiver
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
257            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
258            android:directBootAware="false"
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
259            android:enabled="false"
259-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
260            android:exported="false" >
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
261            <intent-filter>
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
262                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
263                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
264            </intent-filter>
265        </receiver>
266        <receiver
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
267            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
268            android:directBootAware="false"
268-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
269            android:enabled="false"
269-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
270            android:exported="false" >
270-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
271            <intent-filter>
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
272                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
273            </intent-filter>
274        </receiver>
275        <receiver
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
276            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
278            android:enabled="false"
278-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
279            android:exported="false" >
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
280            <intent-filter>
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
281                <action android:name="android.intent.action.BOOT_COMPLETED" />
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
282                <action android:name="android.intent.action.TIME_SET" />
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
283                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
284            </intent-filter>
285        </receiver>
286        <receiver
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
287            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
289            android:enabled="@bool/enable_system_alarm_service_default"
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
290            android:exported="false" >
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
291            <intent-filter>
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
292                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
293            </intent-filter>
294        </receiver>
295        <receiver
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
296            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
297            android:directBootAware="false"
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
298            android:enabled="true"
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
299            android:exported="true"
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
300            android:permission="android.permission.DUMP" >
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
301            <intent-filter>
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
302                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
303            </intent-filter>
304        </receiver>
305
306        <uses-library
306-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
307            android:name="android.ext.adservices"
307-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
308            android:required="false" />
308-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
309        <uses-library
309-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-12:40
310            android:name="org.apache.http.legacy"
310-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-50
311            android:required="false" />
311-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:13-37
312
313        <meta-data
313-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-16:70
314            android:name="com.google.android.gms.ads.APPLICATION_ID"
314-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-69
315            android:value="ca-app-pub-8347415490306764~4325096074" />
315-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:13-67
316        <meta-data
316-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-19:43
317            android:name="com.google.unity.ads.UNITY_VERSION"
317-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:13-62
318            android:value="2021.3.45f1" />
318-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:13-40
319
320        <receiver
320-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
321            android:name="androidx.profileinstaller.ProfileInstallReceiver"
321-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
322            android:directBootAware="false"
322-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
323            android:enabled="true"
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
324            android:exported="true"
324-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
325            android:permission="android.permission.DUMP" >
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
326            <intent-filter>
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
327                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
327-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
327-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
328            </intent-filter>
329            <intent-filter>
329-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
330                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
330-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
330-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
331            </intent-filter>
332            <intent-filter>
332-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
333                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
333-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
333-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
334            </intent-filter>
335            <intent-filter>
335-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
336                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
336-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
336-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
337            </intent-filter>
338        </receiver>
339
340        <service
340-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
341            android:name="androidx.room.MultiInstanceInvalidationService"
341-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
342            android:directBootAware="true"
342-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
343            android:exported="false" />
343-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
344    </application>
345
346</manifest>
