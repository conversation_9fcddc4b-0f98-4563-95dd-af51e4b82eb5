﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* FutureVoid_SWIG_CompletionDispatcher_m60ECAC83E9887F17ECCAFE1C1DE0340903FB5281_RuntimeMethod_var;
extern const RuntimeMethod* LogUtil_LogMessageFromCallback_m7B4B1B33C46B05A14DE066D91BB2110DF44B8107_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingApplicationException_mD6D4D05834648B9CE669A89E0AD6CD0D32A0EDEB_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentException_m1D154F67ADBC4A696102A0BAAAE4FF18BC2D8B1C_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentNullException_mCB01A40C26F2595EE0928F65A06D942BEF9F881D_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m355EE981BB1BAA35BB8E5C5EC2E90625C1C95166_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArithmeticException_m99B7D12BD99B0EAE887BC2C4B43366133758A5BA_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingDivideByZeroException_mD01159299A641E72ED233ACA5F9049D89B916FEC_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingIOException_mE89EF518A4630B7ACD06C6C2E31E6CA3FCB01774_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m917EAD3BCF29EFE9E6450F3420BE2917F60CC2F2_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingInvalidCastException_mF21809DB109F445315F916048C030A4F86843301_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingInvalidOperationException_m3C9E61FD1588B6CF1950AC28B4CC3599A7E4E16D_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingNullReferenceException_m8C550DB92EB91F9325BBAF8DDF2A868F082EABCD_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingOutOfMemoryException_mB184DCD107C95ED1930E66177083BB532FC6E037_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingOverflowException_mE247FE196CCC5A806A4941FC95F6270BAE75653F_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingSystemException_m0BFC9561278749A88CC8847749084DE5FBCC5E15_RuntimeMethod_var;
extern const RuntimeMethod* SWIGStringHelper_CreateString_m411964D0112A7FBA74A5AA7693C8AC07D24F13BC_RuntimeMethod_var;



// 0x00000001 System.Void Firebase.FirebaseException::.ctor(System.Int32,System.String)
extern void FirebaseException__ctor_mCB919E722DF4F366C4E0D9278716CBED5DD8907C (void);
// 0x00000002 System.Void Firebase.FirebaseException::set_ErrorCode(System.Int32)
extern void FirebaseException_set_ErrorCode_mE216C4C0EBAACC7ADA04ED328DEC1474680F9B5A (void);
// 0x00000003 Firebase.InitResult Firebase.InitializationException::get_InitResult()
extern void InitializationException_get_InitResult_m597B4C9A381618A9F73238F131D9AF3433880CF7 (void);
// 0x00000004 System.Void Firebase.InitializationException::set_InitResult(Firebase.InitResult)
extern void InitializationException_set_InitResult_mD6E68B41830F64CB54B3BF7FFE915263D8AD8E34 (void);
// 0x00000005 System.Void Firebase.InitializationException::.ctor(Firebase.InitResult,System.String)
extern void InitializationException__ctor_m4F5649529A9F0863B359E63E74F8B5331F138A14 (void);
// 0x00000006 System.Void Firebase.InitializationException::.ctor(Firebase.InitResult,System.String,System.Exception)
extern void InitializationException__ctor_m56641135A502F6D70F771A69157A0D674EB99DD3 (void);
// 0x00000007 System.String Firebase.ErrorMessages::get_DependencyNotFoundErrorMessage()
extern void ErrorMessages_get_DependencyNotFoundErrorMessage_m93703910D97FC22ECA3B89EEF8681C6F29989106 (void);
// 0x00000008 System.String Firebase.ErrorMessages::get_DllNotFoundExceptionErrorMessage()
extern void ErrorMessages_get_DllNotFoundExceptionErrorMessage_m493D1E37E274C2D633EB65991F5B26102E3F6595 (void);
// 0x00000009 System.Void Firebase.ErrorMessages::.cctor()
extern void ErrorMessages__cctor_mC64C25A9DFBF5FA4B8784803560444FA59FDD7DC (void);
// 0x0000000A System.Void Firebase.LogUtil::.cctor()
extern void LogUtil__cctor_m3A18E1D17E9D3E61E2A8B33C51742249F708BB0A (void);
// 0x0000000B System.Void Firebase.LogUtil::InitializeLogging()
extern void LogUtil_InitializeLogging_m05046FDF1759F0A851931F7FCF743FAA24BDB434 (void);
// 0x0000000C Firebase.Platform.PlatformLogLevel Firebase.LogUtil::ConvertLogLevel(Firebase.LogLevel)
extern void LogUtil_ConvertLogLevel_mD765D2120AA5F1681CFFCB822C189152704565FC (void);
// 0x0000000D System.Void Firebase.LogUtil::LogMessage(Firebase.LogLevel,System.String)
extern void LogUtil_LogMessage_mC36C5CCA27AFA6A1773D0A993DAC4A0C609F6C8B (void);
// 0x0000000E System.Void Firebase.LogUtil::LogMessageFromCallback(Firebase.LogLevel,System.String)
extern void LogUtil_LogMessageFromCallback_m7B4B1B33C46B05A14DE066D91BB2110DF44B8107 (void);
// 0x0000000F System.Void Firebase.LogUtil::.ctor()
extern void LogUtil__ctor_mE6F41CDC7EFF92D76E3D07B5F8350BF7D5A4983D (void);
// 0x00000010 System.Void Firebase.LogUtil::Finalize()
extern void LogUtil_Finalize_mD288B9870DD8D7AF744044513FCA62F9AB42A85C (void);
// 0x00000011 System.Void Firebase.LogUtil::Dispose()
extern void LogUtil_Dispose_mB12D003420083CAA79A613F44A635DA5418C989D (void);
// 0x00000012 System.Void Firebase.LogUtil::Dispose(System.Boolean)
extern void LogUtil_Dispose_m3E431D1105B6EBDC3183FA2B55AA7608607BBC6D (void);
// 0x00000013 System.Void Firebase.LogUtil::<.ctor>b__9_0(System.Object,System.EventArgs)
extern void LogUtil_U3C_ctorU3Eb__9_0_m3CCEC7958C25A61458C58F678BCD77AA29A551B6 (void);
// 0x00000014 System.Void Firebase.LogUtil/LogMessageDelegate::.ctor(System.Object,System.IntPtr)
extern void LogMessageDelegate__ctor_mEBA3FFB53CCE522DBB1B5571A5623A649E6643F0 (void);
// 0x00000015 System.Void Firebase.LogUtil/LogMessageDelegate::Invoke(Firebase.LogLevel,System.String)
extern void LogMessageDelegate_Invoke_mB54C38843065556AF65D1E42C9DDC9AFAFA5C5E8 (void);
// 0x00000016 System.IAsyncResult Firebase.LogUtil/LogMessageDelegate::BeginInvoke(Firebase.LogLevel,System.String,System.AsyncCallback,System.Object)
extern void LogMessageDelegate_BeginInvoke_m9A9B00026484A1266F2E8E1101699C83D2755654 (void);
// 0x00000017 System.Void Firebase.LogUtil/LogMessageDelegate::EndInvoke(System.IAsyncResult)
extern void LogMessageDelegate_EndInvoke_m13C568B3D481DF381F37AAEF2FA598025FDB8C95 (void);
// 0x00000018 System.Void Firebase.MonoPInvokeCallbackAttribute::.ctor(System.Type)
extern void MonoPInvokeCallbackAttribute__ctor_mD5BA102663DCE67244B79EF374E5641E765AB5CB (void);
// 0x00000019 System.Void Firebase.FutureBase::.ctor(System.IntPtr,System.Boolean)
extern void FutureBase__ctor_m69C88EC69B422C5752B2E249303D92F649B8C8AC (void);
// 0x0000001A System.Void Firebase.FutureBase::Finalize()
extern void FutureBase_Finalize_m02E7843DEC68FBDDCA2B009E905FE4657C2B04AC (void);
// 0x0000001B System.Void Firebase.FutureBase::Dispose()
extern void FutureBase_Dispose_m2C0FDC1F8EF2499A1E52D6CFEA94348388784BDB (void);
// 0x0000001C System.Void Firebase.FutureBase::Dispose(System.Boolean)
extern void FutureBase_Dispose_mD92D3FE1E216E3FFBE40723A1F3871452931B2AB (void);
// 0x0000001D Firebase.FutureStatus Firebase.FutureBase::status()
extern void FutureBase_status_m478C1E6AF62FB15C218A7C422CF5DC8CA1486CAA (void);
// 0x0000001E System.Int32 Firebase.FutureBase::error()
extern void FutureBase_error_mBA8200B272D3DB91D1EE78ECE0A10AAB84771C03 (void);
// 0x0000001F System.String Firebase.FutureBase::error_message()
extern void FutureBase_error_message_m6E9B30EF5EC5EE999B91077E60E3B96978DE4774 (void);
// 0x00000020 System.Void Firebase.StringStringMap::.ctor(System.IntPtr,System.Boolean)
extern void StringStringMap__ctor_mB3137F09FFB1EC3F5621EA75DBBEF82F9487D366 (void);
// 0x00000021 System.Runtime.InteropServices.HandleRef Firebase.StringStringMap::getCPtr(Firebase.StringStringMap)
extern void StringStringMap_getCPtr_m7DDDDE0EC47182907217F5620BDEDF5BC8018657 (void);
// 0x00000022 System.Void Firebase.StringStringMap::Finalize()
extern void StringStringMap_Finalize_m54B7DA5EEEDF3E469B05EC36A6FCE520DE2AF925 (void);
// 0x00000023 System.Void Firebase.StringStringMap::Dispose()
extern void StringStringMap_Dispose_mA58EFAC215289EDDB29347D7FB6EC3E9E84FADDF (void);
// 0x00000024 System.Void Firebase.StringStringMap::Dispose(System.Boolean)
extern void StringStringMap_Dispose_m33B2F2642A9D60E6A52283A65374B2A4BA868C1F (void);
// 0x00000025 System.String Firebase.StringStringMap::get_Item(System.String)
extern void StringStringMap_get_Item_mB384423BD033B98EE3457212BE65092712C56789 (void);
// 0x00000026 System.Void Firebase.StringStringMap::set_Item(System.String,System.String)
extern void StringStringMap_set_Item_m8A9BD489465331D5D800240D38CBB7965925F1F0 (void);
// 0x00000027 System.Int32 Firebase.StringStringMap::get_Count()
extern void StringStringMap_get_Count_m907D4DD769A90CAA0683591119FDC3728EEA5BB3 (void);
// 0x00000028 System.Boolean Firebase.StringStringMap::get_IsReadOnly()
extern void StringStringMap_get_IsReadOnly_m96FF99B82D36FD2E3BA738A31E98197987F9AA01 (void);
// 0x00000029 System.Collections.Generic.ICollection`1<System.String> Firebase.StringStringMap::get_Keys()
extern void StringStringMap_get_Keys_m913897400DEF518DFD7D6E0CCEB1FE2026A2B2D3 (void);
// 0x0000002A System.Void Firebase.StringStringMap::Add(System.Collections.Generic.KeyValuePair`2<System.String,System.String>)
extern void StringStringMap_Add_m47B75C347EDA853CA95EFE263E2B645A0241D430 (void);
// 0x0000002B System.Boolean Firebase.StringStringMap::Remove(System.Collections.Generic.KeyValuePair`2<System.String,System.String>)
extern void StringStringMap_Remove_m3443C8BF4781ADB1C64089E6DA44095D341329D2 (void);
// 0x0000002C System.Boolean Firebase.StringStringMap::Contains(System.Collections.Generic.KeyValuePair`2<System.String,System.String>)
extern void StringStringMap_Contains_mE32677A31CE147FDD041B47D9D48914312DB8AF4 (void);
// 0x0000002D System.Void Firebase.StringStringMap::CopyTo(System.Collections.Generic.KeyValuePair`2<System.String,System.String>[],System.Int32)
extern void StringStringMap_CopyTo_mD1D62132C4D6B43C716FBDD75A6628BD7DF618F8 (void);
// 0x0000002E System.Collections.Generic.IEnumerator`1<System.Collections.Generic.KeyValuePair`2<System.String,System.String>> Firebase.StringStringMap::global::System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<System.String,System.String>>.GetEnumerator()
extern void StringStringMap_globalU3AU3ASystem_Collections_Generic_IEnumerableU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CSystem_StringU3EU3E_GetEnumerator_mC7628FC9F836BA4B0C341737ED653595000DAFD4 (void);
// 0x0000002F System.Collections.IEnumerator Firebase.StringStringMap::global::System.Collections.IEnumerable.GetEnumerator()
extern void StringStringMap_globalU3AU3ASystem_Collections_IEnumerable_GetEnumerator_mE9D90D7B85A3208AE71B68D31C8BA56540BA8633 (void);
// 0x00000030 System.Void Firebase.StringStringMap::.ctor()
extern void StringStringMap__ctor_mDD72D1A9A58D226FEA04CB062E9240FBF4B0949E (void);
// 0x00000031 System.UInt32 Firebase.StringStringMap::size()
extern void StringStringMap_size_mBB463BF4F9C6625F91343E1E8F38418A790F1D00 (void);
// 0x00000032 System.Void Firebase.StringStringMap::Clear()
extern void StringStringMap_Clear_mB1FFA2B7D6635E95F3A55805C39BE0AD3B14F21E (void);
// 0x00000033 System.String Firebase.StringStringMap::getitem(System.String)
extern void StringStringMap_getitem_m9E38CEC82B11AC188D75FA0BBB8A8751AC30D6D4 (void);
// 0x00000034 System.Void Firebase.StringStringMap::setitem(System.String,System.String)
extern void StringStringMap_setitem_m17A4FFDF71A27F385C53EC0C18EC02BE031CEE0F (void);
// 0x00000035 System.Void Firebase.StringStringMap::Add(System.String,System.String)
extern void StringStringMap_Add_m9D2175007BBA9E8488671E9C13A08F247D5C2AED (void);
// 0x00000036 System.Boolean Firebase.StringStringMap::Remove(System.String)
extern void StringStringMap_Remove_m3E16E65DA11460219114402300F67FA76D87B408 (void);
// 0x00000037 System.IntPtr Firebase.StringStringMap::create_iterator_begin()
extern void StringStringMap_create_iterator_begin_m3F118062973941F86B02039418B2A2E5475F4377 (void);
// 0x00000038 System.String Firebase.StringStringMap::get_next_key(System.IntPtr)
extern void StringStringMap_get_next_key_m5748FA89B2C25838202E9E28D1DFB4FB8C33CE73 (void);
// 0x00000039 System.Void Firebase.StringStringMap::destroy_iterator(System.IntPtr)
extern void StringStringMap_destroy_iterator_m345F0B0BBC506B42F149223404B1A9F205232F87 (void);
// 0x0000003A System.Void Firebase.StringStringMap/StringStringMapEnumerator::.ctor(Firebase.StringStringMap)
extern void StringStringMapEnumerator__ctor_m765FC216E9A67EA91E8D360C32ED59B5EF59A43A (void);
// 0x0000003B System.Collections.Generic.KeyValuePair`2<System.String,System.String> Firebase.StringStringMap/StringStringMapEnumerator::get_Current()
extern void StringStringMapEnumerator_get_Current_m76DE586A735687200DC7ACF803FEF75527302F06 (void);
// 0x0000003C System.Object Firebase.StringStringMap/StringStringMapEnumerator::global::System.Collections.IEnumerator.get_Current()
extern void StringStringMapEnumerator_globalU3AU3ASystem_Collections_IEnumerator_get_Current_m5AC18877BA31B175CFB4CE0564BD6D59B835207C (void);
// 0x0000003D System.Boolean Firebase.StringStringMap/StringStringMapEnumerator::MoveNext()
extern void StringStringMapEnumerator_MoveNext_m22BD0722C730E02430014E65174344AA499F336F (void);
// 0x0000003E System.Void Firebase.StringStringMap/StringStringMapEnumerator::Reset()
extern void StringStringMapEnumerator_Reset_m71559FB9DF7506B0DBD523E327E02E6B4405A84E (void);
// 0x0000003F System.Void Firebase.StringStringMap/StringStringMapEnumerator::Dispose()
extern void StringStringMapEnumerator_Dispose_m1B7713DE735B05E182D29A076ED15B7720E91BD6 (void);
// 0x00000040 System.Void Firebase.FutureVoid::.ctor(System.IntPtr,System.Boolean)
extern void FutureVoid__ctor_m1360132BEDC4A7668F93C0D1EB79CE3E28E94597 (void);
// 0x00000041 System.Void Firebase.FutureVoid::Dispose(System.Boolean)
extern void FutureVoid_Dispose_mB88632A1075578EB4A185527EF0C9113111ECA9F (void);
// 0x00000042 System.Threading.Tasks.Task Firebase.FutureVoid::GetTask(Firebase.FutureVoid)
extern void FutureVoid_GetTask_m99313F8ED4D21B3440FA37F24EDE6206F6B0E3BF (void);
// 0x00000043 System.Void Firebase.FutureVoid::ThrowIfDisposed()
extern void FutureVoid_ThrowIfDisposed_mA7C1D4055AA1AC53502961F16F6449C4647792AF (void);
// 0x00000044 System.Void Firebase.FutureVoid::SetOnCompletionCallback(Firebase.FutureVoid/Action)
extern void FutureVoid_SetOnCompletionCallback_mA6429D52596940DA40C03180D7020560EAB0C14B (void);
// 0x00000045 System.Void Firebase.FutureVoid::SetCompletionData(System.IntPtr)
extern void FutureVoid_SetCompletionData_mF298BA528990E45DD85839CA0E91130876DAF491 (void);
// 0x00000046 System.Void Firebase.FutureVoid::SWIG_CompletionDispatcher(System.Int32)
extern void FutureVoid_SWIG_CompletionDispatcher_m60ECAC83E9887F17ECCAFE1C1DE0340903FB5281 (void);
// 0x00000047 System.IntPtr Firebase.FutureVoid::SWIG_OnCompletion(Firebase.FutureVoid/SWIG_CompletionDelegate,System.Int32)
extern void FutureVoid_SWIG_OnCompletion_m48F9542CC9CFAF8D061DB014E2791A8C4D6EEBF3 (void);
// 0x00000048 System.Void Firebase.FutureVoid::SWIG_FreeCompletionData(System.IntPtr)
extern void FutureVoid_SWIG_FreeCompletionData_mFEA68F96EA671BB05C45C11DDC981A965E7C4ACF (void);
// 0x00000049 System.Void Firebase.FutureVoid::.cctor()
extern void FutureVoid__cctor_mDCA344A3A0F77964E3A10A61E864E8DA62070ADD (void);
// 0x0000004A System.Void Firebase.FutureVoid/Action::.ctor(System.Object,System.IntPtr)
extern void Action__ctor_mBB9FA88AEDA150A5C03EF7CC69344846B1A4FD22 (void);
// 0x0000004B System.Void Firebase.FutureVoid/Action::Invoke()
extern void Action_Invoke_m8F270518E4B4FBDC595EA69BEC37C94DE19EBB3E (void);
// 0x0000004C System.IAsyncResult Firebase.FutureVoid/Action::BeginInvoke(System.AsyncCallback,System.Object)
extern void Action_BeginInvoke_m880F277B9978E98E5E80FB4B6F6B92085AA9DC10 (void);
// 0x0000004D System.Void Firebase.FutureVoid/Action::EndInvoke(System.IAsyncResult)
extern void Action_EndInvoke_m25D93B13687F5F2BF6E3C41FA7E3BC79CDA289AB (void);
// 0x0000004E System.Void Firebase.FutureVoid/SWIG_CompletionDelegate::.ctor(System.Object,System.IntPtr)
extern void SWIG_CompletionDelegate__ctor_m93C9C83EF65339E2A9134FA2E79AA1895FE881A2 (void);
// 0x0000004F System.Void Firebase.FutureVoid/SWIG_CompletionDelegate::Invoke(System.Int32)
extern void SWIG_CompletionDelegate_Invoke_m331EF656E6CAEB2B61DFD5CF6A68D4045F486CE5 (void);
// 0x00000050 System.IAsyncResult Firebase.FutureVoid/SWIG_CompletionDelegate::BeginInvoke(System.Int32,System.AsyncCallback,System.Object)
extern void SWIG_CompletionDelegate_BeginInvoke_mADCF9246A7BD1637ABAA9876C8C16EBFD318C78C (void);
// 0x00000051 System.Void Firebase.FutureVoid/SWIG_CompletionDelegate::EndInvoke(System.IAsyncResult)
extern void SWIG_CompletionDelegate_EndInvoke_m3D04ABBCBB71B86479A9252A5B3D39C7A15AF54C (void);
// 0x00000052 System.Void Firebase.FutureVoid/<>c__DisplayClass5_0::.ctor()
extern void U3CU3Ec__DisplayClass5_0__ctor_mD476161B45CC35BB800F3E7DFE1A32AE02C6A19D (void);
// 0x00000053 System.Void Firebase.FutureVoid/<>c__DisplayClass5_0::<GetTask>b__0()
extern void U3CU3Ec__DisplayClass5_0_U3CGetTaskU3Eb__0_m3182CE35DFFBB2B549FB2A7DDB7D84AF9B5C8A1B (void);
// 0x00000054 System.Void Firebase.FirebaseApp::.ctor(System.IntPtr,System.Boolean)
extern void FirebaseApp__ctor_mB94B4439B6474A9B3420A388F4C847153BB7DA2B (void);
// 0x00000055 System.Runtime.InteropServices.HandleRef Firebase.FirebaseApp::getCPtr(Firebase.FirebaseApp)
extern void FirebaseApp_getCPtr_mB9BA3F66814A2FC1CCF1355B112F2D1DD4E72E86 (void);
// 0x00000056 System.Void Firebase.FirebaseApp::Finalize()
extern void FirebaseApp_Finalize_mBA9B05FC454D571B021370352E3DAA24A927C964 (void);
// 0x00000057 System.Void Firebase.FirebaseApp::Dispose()
extern void FirebaseApp_Dispose_mD97452CFB97FC00105EB0369582537ED1C457A93 (void);
// 0x00000058 System.Void Firebase.FirebaseApp::Dispose(System.Boolean)
extern void FirebaseApp_Dispose_m05D2841662AD52390E0D09C5A81366D751FF8B38 (void);
// 0x00000059 System.Void Firebase.FirebaseApp::.cctor()
extern void FirebaseApp__cctor_m65C207A2EB72FE3DF40D35D1018C27881D66EB62 (void);
// 0x0000005A System.Void Firebase.FirebaseApp::TranslateDllNotFoundException(System.Action)
extern void FirebaseApp_TranslateDllNotFoundException_m7AA751F31022C03AA209E73B99646C4D0A17A90D (void);
// 0x0000005B Firebase.FirebaseApp Firebase.FirebaseApp::get_DefaultInstance()
extern void FirebaseApp_get_DefaultInstance_mCC924BAC33B68B03C851ACE856930B839F8267D7 (void);
// 0x0000005C Firebase.FirebaseApp Firebase.FirebaseApp::GetInstance(System.String)
extern void FirebaseApp_GetInstance_mED18364B5B6A567DEDDBFE38F5CE174FF8171196 (void);
// 0x0000005D Firebase.FirebaseApp Firebase.FirebaseApp::Create()
extern void FirebaseApp_Create_m9A391D6EC41EE45EAA3CCAE368F182EAD69791ED (void);
// 0x0000005E System.String Firebase.FirebaseApp::get_Name()
extern void FirebaseApp_get_Name_m5945BBCED21D565E2D871D4CC00D03BB6EDB60B8 (void);
// 0x0000005F Firebase.LogLevel Firebase.FirebaseApp::get_LogLevel()
extern void FirebaseApp_get_LogLevel_mF5210CA8F87660D4B3747792C00C98579142CAAF (void);
// 0x00000060 System.Void Firebase.FirebaseApp::AddReference()
extern void FirebaseApp_AddReference_m1B148D579E5DBB9E6608813E714769757F33142B (void);
// 0x00000061 System.Void Firebase.FirebaseApp::RemoveReference()
extern void FirebaseApp_RemoveReference_mF1E15EE01A41D7674FAFDAB777CF637071F907E0 (void);
// 0x00000062 System.Void Firebase.FirebaseApp::ThrowIfNull()
extern void FirebaseApp_ThrowIfNull_m408BCFBB4ED87F4FF412F374897F083E87897315 (void);
// 0x00000063 System.Void Firebase.FirebaseApp::InitializeAppUtilCallbacks()
extern void FirebaseApp_InitializeAppUtilCallbacks_m598537A6D9F6327DAA4E5398BB645C5B7CA81064 (void);
// 0x00000064 System.Void Firebase.FirebaseApp::OnAllAppsDestroyed()
extern void FirebaseApp_OnAllAppsDestroyed_m6D2ADF3AFBC105388CB7D63FA431CF5BAF9FA94D (void);
// 0x00000065 System.Boolean Firebase.FirebaseApp::InitializeCrashlyticsIfPresent()
extern void FirebaseApp_InitializeCrashlyticsIfPresent_m28C2D06F823206C421BF3D241335A8FA4D26A91B (void);
// 0x00000066 Firebase.FirebaseApp Firebase.FirebaseApp::CreateAndTrack(Firebase.FirebaseApp/CreateDelegate,Firebase.FirebaseApp)
extern void FirebaseApp_CreateAndTrack_m7FC684DE29AF92053981EB1323B38DBC4D34AC26 (void);
// 0x00000067 System.Void Firebase.FirebaseApp::SetCheckDependenciesThread(System.Int32)
extern void FirebaseApp_SetCheckDependenciesThread_m4DFA2531E30E77FFA43EBC45E23CD306E8BA8764 (void);
// 0x00000068 System.Void Firebase.FirebaseApp::ThrowIfCheckDependenciesRunning()
extern void FirebaseApp_ThrowIfCheckDependenciesRunning_m6452309AE10D2E53CCA8457A4D8A181B164D05EA (void);
// 0x00000069 System.Boolean Firebase.FirebaseApp::IsCheckDependenciesRunning()
extern void FirebaseApp_IsCheckDependenciesRunning_m35488E6B310C9CF1BC00E4464028166723C3F078 (void);
// 0x0000006A System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp::CheckDependenciesAsync()
extern void FirebaseApp_CheckDependenciesAsync_mB7A747897A064C84E4D84585DFEAF74A38D38F43 (void);
// 0x0000006B System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp::CheckAndFixDependenciesAsync()
extern void FirebaseApp_CheckAndFixDependenciesAsync_m268BF2FC002D4D4CC247EF2636CDF7EBFF043009 (void);
// 0x0000006C Firebase.DependencyStatus Firebase.FirebaseApp::CheckDependencies()
extern void FirebaseApp_CheckDependencies_mCD719D29867B855EBA5F74102286BB9A44FCB946 (void);
// 0x0000006D Firebase.DependencyStatus Firebase.FirebaseApp::CheckDependenciesInternal()
extern void FirebaseApp_CheckDependenciesInternal_m3C1FA1603F994655D1C49443FFF8053420F98509 (void);
// 0x0000006E System.Threading.Tasks.Task Firebase.FirebaseApp::FixDependenciesAsync()
extern void FirebaseApp_FixDependenciesAsync_mDBBC901E36336AE90E590E9DACD32FE1CCDB79E1 (void);
// 0x0000006F System.Void Firebase.FirebaseApp::ResetDefaultAppCPtr()
extern void FirebaseApp_ResetDefaultAppCPtr_m23794116100108FA137A11654DDF8C74A5C5F453 (void);
// 0x00000070 System.String Firebase.FirebaseApp::get_NameInternal()
extern void FirebaseApp_get_NameInternal_m89112573EBE5801863EA50B17FBAE20CBE855C75 (void);
// 0x00000071 Firebase.FirebaseApp Firebase.FirebaseApp::CreateInternal()
extern void FirebaseApp_CreateInternal_m7C9DA4F992AD0B435BE57A04D5BE3537AB2A0233 (void);
// 0x00000072 System.Void Firebase.FirebaseApp::ReleaseReferenceInternal(Firebase.FirebaseApp)
extern void FirebaseApp_ReleaseReferenceInternal_m251EC6B33F516335E844BC8CBC7531205D41C934 (void);
// 0x00000073 System.Void Firebase.FirebaseApp::RegisterLibrariesInternal(Firebase.StringStringMap)
extern void FirebaseApp_RegisterLibrariesInternal_m7994F3905C955E4DEAA729005F9E90D6FD23B7A8 (void);
// 0x00000074 System.Void Firebase.FirebaseApp::LogHeartbeatInternal(Firebase.FirebaseApp)
extern void FirebaseApp_LogHeartbeatInternal_m787662E07280C7D53C2F98DA855C6C467D6FDA13 (void);
// 0x00000075 System.Void Firebase.FirebaseApp::AppSetDefaultConfigPath(System.String)
extern void FirebaseApp_AppSetDefaultConfigPath_m55D859EEC14CA1FFEF3DC3422F5CE8C1328E097F (void);
// 0x00000076 System.String Firebase.FirebaseApp::get_DefaultName()
extern void FirebaseApp_get_DefaultName_mFAACACB5590298942D3A75F2A6BC53BD6531963C (void);
// 0x00000077 System.String Firebase.FirebaseApp/EnableModuleParams::get_CppModuleName()
extern void EnableModuleParams_get_CppModuleName_mE57521DAC3F8972C81AFBC72DB70FE79A9F946B3 (void);
// 0x00000078 System.Void Firebase.FirebaseApp/EnableModuleParams::set_CppModuleName(System.String)
extern void EnableModuleParams_set_CppModuleName_m780B77AD33765B83D0675C02876BB379B9EFCCEB (void);
// 0x00000079 System.String Firebase.FirebaseApp/EnableModuleParams::get_CSharpClassName()
extern void EnableModuleParams_get_CSharpClassName_m5C21BC47A020FE24984E1A282267CE62CF09080B (void);
// 0x0000007A System.Void Firebase.FirebaseApp/EnableModuleParams::set_CSharpClassName(System.String)
extern void EnableModuleParams_set_CSharpClassName_mB1413BCF93E8A3B658798ED556E586C47981F018 (void);
// 0x0000007B System.Boolean Firebase.FirebaseApp/EnableModuleParams::get_AlwaysEnable()
extern void EnableModuleParams_get_AlwaysEnable_m76B3B18100019E68E79EA0A0B320B1EAE0AB8260 (void);
// 0x0000007C System.Void Firebase.FirebaseApp/EnableModuleParams::set_AlwaysEnable(System.Boolean)
extern void EnableModuleParams_set_AlwaysEnable_m38C379905DD5810F629E35AD34DD0F677990ACFE (void);
// 0x0000007D System.Void Firebase.FirebaseApp/EnableModuleParams::.ctor(System.String,System.String,System.Boolean)
extern void EnableModuleParams__ctor_mC9619ED6B8BE82D305359BB53BDE6274A8A2D2C2 (void);
// 0x0000007E System.Void Firebase.FirebaseApp/CreateDelegate::.ctor(System.Object,System.IntPtr)
extern void CreateDelegate__ctor_m9B61AF9F4EFF9CCA9FC10B8BDB5E8AD7130E4DE1 (void);
// 0x0000007F Firebase.FirebaseApp Firebase.FirebaseApp/CreateDelegate::Invoke()
extern void CreateDelegate_Invoke_m9FC551133A4F9301FB4F107B90F7C98A66F95BE9 (void);
// 0x00000080 System.IAsyncResult Firebase.FirebaseApp/CreateDelegate::BeginInvoke(System.AsyncCallback,System.Object)
extern void CreateDelegate_BeginInvoke_mE55A9209FBD926992A08A1F0C0DCAC9083CC66A5 (void);
// 0x00000081 Firebase.FirebaseApp Firebase.FirebaseApp/CreateDelegate::EndInvoke(System.IAsyncResult)
extern void CreateDelegate_EndInvoke_mA8FE3700C59A150FAA37B17BDAA7A44319C1116E (void);
// 0x00000082 System.Void Firebase.FirebaseApp/<>c::.cctor()
extern void U3CU3Ec__cctor_m28462B4FF12E06FADDEB8D5A3FAC11B4D920FFC1 (void);
// 0x00000083 System.Void Firebase.FirebaseApp/<>c::.ctor()
extern void U3CU3Ec__ctor_m58F4F61972161EC4DA21A6B10024C4D7C71DC42A (void);
// 0x00000084 Firebase.FirebaseApp Firebase.FirebaseApp/<>c::<Create>b__15_0()
extern void U3CU3Ec_U3CCreateU3Eb__15_0_m88AD2B39B4B74F4D45D16198A1910301A7255079 (void);
// 0x00000085 System.Boolean Firebase.FirebaseApp/<>c::<CreateAndTrack>b__48_0()
extern void U3CU3Ec_U3CCreateAndTrackU3Eb__48_0_m0F69C58EFA46937CB73BD5245B64D5F8C18D0C17 (void);
// 0x00000086 Firebase.DependencyStatus Firebase.FirebaseApp/<>c::<CheckDependenciesAsync>b__56_0()
extern void U3CU3Ec_U3CCheckDependenciesAsyncU3Eb__56_0_m6A160DA6D9DD0D50C3B7CB6154EBEC780E60B16F (void);
// 0x00000087 System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp/<>c::<CheckAndFixDependenciesAsync>b__57_0(System.Threading.Tasks.Task`1<Firebase.DependencyStatus>)
extern void U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_0_mEF1FF5A1B2931F7DA90AB88D6DF6445776E66C9E (void);
// 0x00000088 System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp/<>c::<CheckAndFixDependenciesAsync>b__57_1(System.Threading.Tasks.Task)
extern void U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_1_m29647E233F76648112CEC303862A3884C9BD091C (void);
// 0x00000089 System.Void Firebase.FirebaseApp/<>c::<FixDependenciesAsync>b__60_1(System.Threading.Tasks.Task)
extern void U3CU3Ec_U3CFixDependenciesAsyncU3Eb__60_1_m4F62D52183948C02032FAE665CFFAB524D6045B0 (void);
// 0x0000008A System.Void Firebase.FirebaseApp/<>c__DisplayClass58_0::.ctor()
extern void U3CU3Ec__DisplayClass58_0__ctor_m565F7F01CA488A06B03680C4DFED5F65F6C69E17 (void);
// 0x0000008B System.Void Firebase.FirebaseApp/<>c__DisplayClass58_0::<CheckDependencies>b__0()
extern void U3CU3Ec__DisplayClass58_0_U3CCheckDependenciesU3Eb__0_m97B4EEB7056CE6B790CCC0E1CD6941437F6EDCB9 (void);
// 0x0000008C System.Void Firebase.FirebaseApp/<>c__DisplayClass60_0::.ctor()
extern void U3CU3Ec__DisplayClass60_0__ctor_m4C69617DCBD98CE10C1ED9C7E10740EFDCF63575 (void);
// 0x0000008D System.Void Firebase.FirebaseApp/<>c__DisplayClass60_0::<FixDependenciesAsync>b__0()
extern void U3CU3Ec__DisplayClass60_0_U3CFixDependenciesAsyncU3Eb__0_mE8201994F327DD8162D439F7D581BAB7D8EB7561 (void);
// 0x0000008E System.Void Firebase.AppUtilPINVOKE::.cctor()
extern void AppUtilPINVOKE__cctor_mB82716D517E855CA4815CA1B872B0599F1785B19 (void);
// 0x0000008F System.Void Firebase.AppUtilPINVOKE::delete_FutureBase(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_delete_FutureBase_m05F2F373729A515D05275F4EDE8C244C5FD43FCC (void);
// 0x00000090 System.Int32 Firebase.AppUtilPINVOKE::FutureBase_status(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FutureBase_status_mAADCE6BBF81EA978FD0AE85FB933E459B7F85604 (void);
// 0x00000091 System.Int32 Firebase.AppUtilPINVOKE::FutureBase_error(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FutureBase_error_m2D1B3DA922FE2A7A45D3C947B10CEF6D2D357533 (void);
// 0x00000092 System.String Firebase.AppUtilPINVOKE::FutureBase_error_message(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FutureBase_error_message_m8B286D44C72F4B764A402DBB4F3A14F6EBF92FF1 (void);
// 0x00000093 System.IntPtr Firebase.AppUtilPINVOKE::new_StringStringMap__SWIG_0()
extern void AppUtilPINVOKE_new_StringStringMap__SWIG_0_mA6EA891DA3D8FEB5638E135ABAD2AC0DC431ED36 (void);
// 0x00000094 System.UInt32 Firebase.AppUtilPINVOKE::StringStringMap_size(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_StringStringMap_size_m4E63887FDF8B0E1D3DB6E84815E85BF5B4D290CE (void);
// 0x00000095 System.Void Firebase.AppUtilPINVOKE::StringStringMap_Clear(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_StringStringMap_Clear_m2C6F7439A94CAE20846174ED69FBA2F9066E7A1D (void);
// 0x00000096 System.String Firebase.AppUtilPINVOKE::StringStringMap_getitem(System.Runtime.InteropServices.HandleRef,System.String)
extern void AppUtilPINVOKE_StringStringMap_getitem_m2EB0B1A9766EBBCABF6CFECEC8F22E1091AE9F7D (void);
// 0x00000097 System.Void Firebase.AppUtilPINVOKE::StringStringMap_setitem(System.Runtime.InteropServices.HandleRef,System.String,System.String)
extern void AppUtilPINVOKE_StringStringMap_setitem_m3961F9DFD0085CFF6D6C5D6393960E98D7C31629 (void);
// 0x00000098 System.Void Firebase.AppUtilPINVOKE::StringStringMap_Add(System.Runtime.InteropServices.HandleRef,System.String,System.String)
extern void AppUtilPINVOKE_StringStringMap_Add_m3527846D771DE697566F691022E6AE3809139D2B (void);
// 0x00000099 System.Boolean Firebase.AppUtilPINVOKE::StringStringMap_Remove(System.Runtime.InteropServices.HandleRef,System.String)
extern void AppUtilPINVOKE_StringStringMap_Remove_m0C85EE5015CC8052BDA51D49D336DF5170A69DE5 (void);
// 0x0000009A System.IntPtr Firebase.AppUtilPINVOKE::StringStringMap_create_iterator_begin(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_StringStringMap_create_iterator_begin_mFFFF3528977EDCCEAD0B111C6255900D627FCE34 (void);
// 0x0000009B System.String Firebase.AppUtilPINVOKE::StringStringMap_get_next_key(System.Runtime.InteropServices.HandleRef,System.IntPtr)
extern void AppUtilPINVOKE_StringStringMap_get_next_key_mC1E6FBC6B8E837D49B3B51048846EFBEFEEB455B (void);
// 0x0000009C System.Void Firebase.AppUtilPINVOKE::StringStringMap_destroy_iterator(System.Runtime.InteropServices.HandleRef,System.IntPtr)
extern void AppUtilPINVOKE_StringStringMap_destroy_iterator_m40AFDE67ED8D7A4FBC965EEE1B0DA9E15DEB66CC (void);
// 0x0000009D System.Void Firebase.AppUtilPINVOKE::delete_StringStringMap(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_delete_StringStringMap_m8EAC4B5F1B2198D47AA2245074DD2C4B73F2DE0A (void);
// 0x0000009E System.IntPtr Firebase.AppUtilPINVOKE::FutureVoid_SWIG_OnCompletion(System.Runtime.InteropServices.HandleRef,Firebase.FutureVoid/SWIG_CompletionDelegate,System.Int32)
extern void AppUtilPINVOKE_FutureVoid_SWIG_OnCompletion_m8ECE4C8E41A5F9C81C5FBF6739AF216985DA369B (void);
// 0x0000009F System.Void Firebase.AppUtilPINVOKE::FutureVoid_SWIG_FreeCompletionData(System.IntPtr)
extern void AppUtilPINVOKE_FutureVoid_SWIG_FreeCompletionData_m6F255A098D3B9CA3ABAA838700DE34F483A80C67 (void);
// 0x000000A0 System.Void Firebase.AppUtilPINVOKE::delete_FutureVoid(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_delete_FutureVoid_m23422CC33D67D0B3ACBA292B975FC62633FA5D90 (void);
// 0x000000A1 System.String Firebase.AppUtilPINVOKE::FirebaseApp_NameInternal_get(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_NameInternal_get_m7A1FC05B47A670C5EF6228A284F2830D5F966AAF (void);
// 0x000000A2 System.IntPtr Firebase.AppUtilPINVOKE::FirebaseApp_CreateInternal__SWIG_0()
extern void AppUtilPINVOKE_FirebaseApp_CreateInternal__SWIG_0_mE02354511523AA50E810D1C60FA97DF31F050081 (void);
// 0x000000A3 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_ReleaseReferenceInternal(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_ReleaseReferenceInternal_m83E641C7626685F4E5018D332B214A4497F4437C (void);
// 0x000000A4 System.Int32 Firebase.AppUtilPINVOKE::FirebaseApp_GetLogLevelInternal()
extern void AppUtilPINVOKE_FirebaseApp_GetLogLevelInternal_m32950055F0CCA12C52B9948446AB55C4FFBF79D3 (void);
// 0x000000A5 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_RegisterLibrariesInternal(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_RegisterLibrariesInternal_m4EF07686F8D26220CB0EBAB65C4ADC3AFC28D227 (void);
// 0x000000A6 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_LogHeartbeatInternal(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_LogHeartbeatInternal_mFFFD1ED22D411FBC4C2BEAB561A7E67C7CB6D7CE (void);
// 0x000000A7 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_AppSetDefaultConfigPath(System.String)
extern void AppUtilPINVOKE_FirebaseApp_AppSetDefaultConfigPath_m991D5A7289A762B01357BD1585E7C94270177C09 (void);
// 0x000000A8 System.String Firebase.AppUtilPINVOKE::FirebaseApp_DefaultName_get()
extern void AppUtilPINVOKE_FirebaseApp_DefaultName_get_mBAEE6B135FCB729CB583A3CA4597DB952D48C585 (void);
// 0x000000A9 System.Void Firebase.AppUtilPINVOKE::PollCallbacks()
extern void AppUtilPINVOKE_PollCallbacks_mBFD4A04F81A7B15308F5B4CEB0C1D584CAF1EC71 (void);
// 0x000000AA System.Void Firebase.AppUtilPINVOKE::AppEnableLogCallback(System.Boolean)
extern void AppUtilPINVOKE_AppEnableLogCallback_mB2B3CFF17912BDBF2A0EADD54BF5A68BE0078160 (void);
// 0x000000AB System.Void Firebase.AppUtilPINVOKE::SetEnabledAllAppCallbacks(System.Boolean)
extern void AppUtilPINVOKE_SetEnabledAllAppCallbacks_mA4D2C483C70D7FA772D58523B4656A088EB1E0C7 (void);
// 0x000000AC System.Void Firebase.AppUtilPINVOKE::SetEnabledAppCallbackByName(System.String,System.Boolean)
extern void AppUtilPINVOKE_SetEnabledAppCallbackByName_m0E27DC635F167FBE31CC908C881E76E59F369183 (void);
// 0x000000AD System.Boolean Firebase.AppUtilPINVOKE::GetEnabledAppCallbackByName(System.String)
extern void AppUtilPINVOKE_GetEnabledAppCallbackByName_mB8121629E6C43D1A22E1773F92A74391C2E48F44 (void);
// 0x000000AE System.Void Firebase.AppUtilPINVOKE::SetLogFunction(Firebase.LogUtil/LogMessageDelegate)
extern void AppUtilPINVOKE_SetLogFunction_mC95A68C1B570C234A5DEDAB0B78ABAFC60CABC0C (void);
// 0x000000AF System.Int32 Firebase.AppUtilPINVOKE::CheckAndroidDependencies()
extern void AppUtilPINVOKE_CheckAndroidDependencies_mF626F790D17BBB6B55308DFE7306CCFF1148C210 (void);
// 0x000000B0 System.IntPtr Firebase.AppUtilPINVOKE::FixAndroidDependencies()
extern void AppUtilPINVOKE_FixAndroidDependencies_mA7454C1CECC9D0FFB05F60F0064E5D95E16BD699 (void);
// 0x000000B1 System.Void Firebase.AppUtilPINVOKE::InitializePlayServicesInternal()
extern void AppUtilPINVOKE_InitializePlayServicesInternal_m6CDF2BCDF9BDBA87CBE0D803B3B6CD16D495CC40 (void);
// 0x000000B2 System.Void Firebase.AppUtilPINVOKE::TerminatePlayServicesInternal()
extern void AppUtilPINVOKE_TerminatePlayServicesInternal_mA808F30F3DEFF00997E5042805C0E717D7725F32 (void);
// 0x000000B3 System.IntPtr Firebase.AppUtilPINVOKE::FutureVoid_SWIGUpcast(System.IntPtr)
extern void AppUtilPINVOKE_FutureVoid_SWIGUpcast_m34C07BD600DBA8A05C4DA0B553472CBC407C55FF (void);
// 0x000000B4 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SWIGRegisterExceptionCallbacks_AppUtil(Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate)
extern void SWIGExceptionHelper_SWIGRegisterExceptionCallbacks_AppUtil_m73FDAC85B33251A5207C88493A40F2F24D634676 (void);
// 0x000000B5 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SWIGRegisterExceptionCallbacksArgument_AppUtil(Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate)
extern void SWIGExceptionHelper_SWIGRegisterExceptionCallbacksArgument_AppUtil_m8F9C20ECB599940EF0CFCFF5598BD3C3222E2B37 (void);
// 0x000000B6 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingApplicationException(System.String)
extern void SWIGExceptionHelper_SetPendingApplicationException_mD6D4D05834648B9CE669A89E0AD6CD0D32A0EDEB (void);
// 0x000000B7 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArithmeticException(System.String)
extern void SWIGExceptionHelper_SetPendingArithmeticException_m99B7D12BD99B0EAE887BC2C4B43366133758A5BA (void);
// 0x000000B8 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingDivideByZeroException(System.String)
extern void SWIGExceptionHelper_SetPendingDivideByZeroException_mD01159299A641E72ED233ACA5F9049D89B916FEC (void);
// 0x000000B9 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingIndexOutOfRangeException(System.String)
extern void SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m917EAD3BCF29EFE9E6450F3420BE2917F60CC2F2 (void);
// 0x000000BA System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingInvalidCastException(System.String)
extern void SWIGExceptionHelper_SetPendingInvalidCastException_mF21809DB109F445315F916048C030A4F86843301 (void);
// 0x000000BB System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingInvalidOperationException(System.String)
extern void SWIGExceptionHelper_SetPendingInvalidOperationException_m3C9E61FD1588B6CF1950AC28B4CC3599A7E4E16D (void);
// 0x000000BC System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingIOException(System.String)
extern void SWIGExceptionHelper_SetPendingIOException_mE89EF518A4630B7ACD06C6C2E31E6CA3FCB01774 (void);
// 0x000000BD System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingNullReferenceException(System.String)
extern void SWIGExceptionHelper_SetPendingNullReferenceException_m8C550DB92EB91F9325BBAF8DDF2A868F082EABCD (void);
// 0x000000BE System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingOutOfMemoryException(System.String)
extern void SWIGExceptionHelper_SetPendingOutOfMemoryException_mB184DCD107C95ED1930E66177083BB532FC6E037 (void);
// 0x000000BF System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingOverflowException(System.String)
extern void SWIGExceptionHelper_SetPendingOverflowException_mE247FE196CCC5A806A4941FC95F6270BAE75653F (void);
// 0x000000C0 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingSystemException(System.String)
extern void SWIGExceptionHelper_SetPendingSystemException_m0BFC9561278749A88CC8847749084DE5FBCC5E15 (void);
// 0x000000C1 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArgumentException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentException_m1D154F67ADBC4A696102A0BAAAE4FF18BC2D8B1C (void);
// 0x000000C2 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArgumentNullException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentNullException_mCB01A40C26F2595EE0928F65A06D942BEF9F881D (void);
// 0x000000C3 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArgumentOutOfRangeException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m355EE981BB1BAA35BB8E5C5EC2E90625C1C95166 (void);
// 0x000000C4 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::.cctor()
extern void SWIGExceptionHelper__cctor_m88D96246E0C2DD75CFC4054F09FA9044A5B6FA90 (void);
// 0x000000C5 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::.ctor()
extern void SWIGExceptionHelper__ctor_m06C48C4611CDA458CA1AF651ED06BF7FF7EDF536 (void);
// 0x000000C6 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate::.ctor(System.Object,System.IntPtr)
extern void ExceptionDelegate__ctor_m4E04BD56501AA698F333F3189D232E0DD8BE66A0 (void);
// 0x000000C7 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate::Invoke(System.String)
extern void ExceptionDelegate_Invoke_mE907915DC5B6A911DE7F253DF0E0D82F63B23A06 (void);
// 0x000000C8 System.IAsyncResult Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate::BeginInvoke(System.String,System.AsyncCallback,System.Object)
extern void ExceptionDelegate_BeginInvoke_m72D31AEE58624296E481B8F6C28EDF28C445F92B (void);
// 0x000000C9 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate::EndInvoke(System.IAsyncResult)
extern void ExceptionDelegate_EndInvoke_mCD778A944D0755D6227785C17547B6F3FCCB9D59 (void);
// 0x000000CA System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::.ctor(System.Object,System.IntPtr)
extern void ExceptionArgumentDelegate__ctor_m9B64B0E9472C1DDAA639843324FD57FBCCE07E08 (void);
// 0x000000CB System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::Invoke(System.String,System.String)
extern void ExceptionArgumentDelegate_Invoke_mD10622418D792C1CDA2D02B0117C251187C52D74 (void);
// 0x000000CC System.IAsyncResult Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::BeginInvoke(System.String,System.String,System.AsyncCallback,System.Object)
extern void ExceptionArgumentDelegate_BeginInvoke_m0410594AB6ABF10A9740F06B324A5A6C059E39B9 (void);
// 0x000000CD System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::EndInvoke(System.IAsyncResult)
extern void ExceptionArgumentDelegate_EndInvoke_mE24172C5085232AD4E4A4EAC36FBF77A79A93C31 (void);
// 0x000000CE System.Boolean Firebase.AppUtilPINVOKE/SWIGPendingException::get_Pending()
extern void SWIGPendingException_get_Pending_m57F7C179B5EFB37003896A5F25F4FBED7DA3D2AD (void);
// 0x000000CF System.Void Firebase.AppUtilPINVOKE/SWIGPendingException::Set(System.Exception)
extern void SWIGPendingException_Set_m3016052808B54728D457EB1D4E8E7306D806098A (void);
// 0x000000D0 System.Exception Firebase.AppUtilPINVOKE/SWIGPendingException::Retrieve()
extern void SWIGPendingException_Retrieve_m62D9AC53AD2901040C0DF7F7800858C07617B6CD (void);
// 0x000000D1 System.Void Firebase.AppUtilPINVOKE/SWIGPendingException::.cctor()
extern void SWIGPendingException__cctor_m0ECBAFFCFA5AA7F4EF02F7400DD059E373CB3291 (void);
// 0x000000D2 System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper::SWIGRegisterStringCallback_AppUtil(Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate)
extern void SWIGStringHelper_SWIGRegisterStringCallback_AppUtil_m72D311A8F2D513C5602B6F4E7936C2910DBECA15 (void);
// 0x000000D3 System.String Firebase.AppUtilPINVOKE/SWIGStringHelper::CreateString(System.String)
extern void SWIGStringHelper_CreateString_m411964D0112A7FBA74A5AA7693C8AC07D24F13BC (void);
// 0x000000D4 System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper::.cctor()
extern void SWIGStringHelper__cctor_m8EE8BC5E0ABB17F0F9D0A2F615EE4987FD86F3B0 (void);
// 0x000000D5 System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper::.ctor()
extern void SWIGStringHelper__ctor_m9F305BAB06F185B49FD5AC05A407928C69D672F6 (void);
// 0x000000D6 System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate::.ctor(System.Object,System.IntPtr)
extern void SWIGStringDelegate__ctor_mED39AF7AB0675F58D7C5E732BB50C419BF321299 (void);
// 0x000000D7 System.String Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate::Invoke(System.String)
extern void SWIGStringDelegate_Invoke_mE2D5B14F87E5528B7095C2B08CFD4B10A4926BDF (void);
// 0x000000D8 System.IAsyncResult Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate::BeginInvoke(System.String,System.AsyncCallback,System.Object)
extern void SWIGStringDelegate_BeginInvoke_m071087F52EF4FF4FB5914D2D986CD4607DFDE8E7 (void);
// 0x000000D9 System.String Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate::EndInvoke(System.IAsyncResult)
extern void SWIGStringDelegate_EndInvoke_m4A38AD7D0C3965603592F826BC582D78A7FB7AA0 (void);
// 0x000000DA System.Void Firebase.AppUtil::PollCallbacks()
extern void AppUtil_PollCallbacks_m75E222C3BCE3563C9C27265D3AE011E3E342E527 (void);
// 0x000000DB System.Void Firebase.AppUtil::AppEnableLogCallback(System.Boolean)
extern void AppUtil_AppEnableLogCallback_m01A441841A004A6048FCFC012F083BFAA3581C66 (void);
// 0x000000DC System.Void Firebase.AppUtil::SetEnabledAllAppCallbacks(System.Boolean)
extern void AppUtil_SetEnabledAllAppCallbacks_m40DACCE1222844931485D67E3885D9ACD1C31FF2 (void);
// 0x000000DD System.Void Firebase.AppUtil::SetEnabledAppCallbackByName(System.String,System.Boolean)
extern void AppUtil_SetEnabledAppCallbackByName_m59AF9169D18540D471ECB1A999A5F7B67D0B63BC (void);
// 0x000000DE System.Boolean Firebase.AppUtil::GetEnabledAppCallbackByName(System.String)
extern void AppUtil_GetEnabledAppCallbackByName_m4E31F50E6B3C90FF204A4BC57293B0A1C5F81513 (void);
// 0x000000DF System.Void Firebase.AppUtil::SetLogFunction(Firebase.LogUtil/LogMessageDelegate)
extern void AppUtil_SetLogFunction_m9B6FD50FE9307EC41EB21DD82665887C9F6514BB (void);
// 0x000000E0 Firebase.GooglePlayServicesAvailability Firebase.AppUtil::CheckAndroidDependencies()
extern void AppUtil_CheckAndroidDependencies_mF6F1264E5DA034CDB3B4A20715419FB36BB09FED (void);
// 0x000000E1 System.Threading.Tasks.Task Firebase.AppUtil::FixAndroidDependenciesAsync()
extern void AppUtil_FixAndroidDependenciesAsync_m4E0A5F5046D85FBCEEB21963516269A404D9ACA7 (void);
// 0x000000E2 System.Void Firebase.AppUtil::InitializePlayServicesInternal()
extern void AppUtil_InitializePlayServicesInternal_mFDAD6568DBE745E692B38FC49EA137C6FBCB2850 (void);
// 0x000000E3 System.Void Firebase.AppUtil::TerminatePlayServicesInternal()
extern void AppUtil_TerminatePlayServicesInternal_mEEDE73F4D1E3F490FFE1329DBD9255A944830B57 (void);
// 0x000000E4 System.String Firebase.VersionInfo::get_SdkVersion()
extern void VersionInfo_get_SdkVersion_m98A33172434D6EA468C4FF32AC941045D88DCF8B (void);
// 0x000000E5 System.String Firebase.VersionInfo::get_BuildSource()
extern void VersionInfo_get_BuildSource_m6FA5048FBD9FF01F4AA5811F1209868D080ADD2D (void);
// 0x000000E6 Firebase.Platform.FirebaseAppUtils Firebase.Platform.FirebaseAppUtils::get_Instance()
extern void FirebaseAppUtils_get_Instance_m79B79F13BD6329A947B640DB886DC398439A90BF (void);
// 0x000000E7 System.Void Firebase.Platform.FirebaseAppUtils::TranslateDllNotFoundException(System.Action)
extern void FirebaseAppUtils_TranslateDllNotFoundException_m6E4E3109BD0827EE0E675EE9CD50E9C6EE343356 (void);
// 0x000000E8 System.Void Firebase.Platform.FirebaseAppUtils::PollCallbacks()
extern void FirebaseAppUtils_PollCallbacks_m0A88A069EC5477A1CC9BFCEDC55DED431EE64E91 (void);
// 0x000000E9 Firebase.Platform.PlatformLogLevel Firebase.Platform.FirebaseAppUtils::GetLogLevel()
extern void FirebaseAppUtils_GetLogLevel_m9E036F13631BCE81463AA50592FBCD54E9CB09D7 (void);
// 0x000000EA System.Void Firebase.Platform.FirebaseAppUtils::.ctor()
extern void FirebaseAppUtils__ctor_m69CA1CBCD58CB3128DF35E9560A3D1D38005845F (void);
// 0x000000EB System.Void Firebase.Platform.FirebaseAppUtils::.cctor()
extern void FirebaseAppUtils__cctor_mD68087540805827D81757968316C3971FF48B537 (void);
static Il2CppMethodPointer s_methodPointers[235] = 
{
	FirebaseException__ctor_mCB919E722DF4F366C4E0D9278716CBED5DD8907C,
	FirebaseException_set_ErrorCode_mE216C4C0EBAACC7ADA04ED328DEC1474680F9B5A,
	InitializationException_get_InitResult_m597B4C9A381618A9F73238F131D9AF3433880CF7,
	InitializationException_set_InitResult_mD6E68B41830F64CB54B3BF7FFE915263D8AD8E34,
	InitializationException__ctor_m4F5649529A9F0863B359E63E74F8B5331F138A14,
	InitializationException__ctor_m56641135A502F6D70F771A69157A0D674EB99DD3,
	ErrorMessages_get_DependencyNotFoundErrorMessage_m93703910D97FC22ECA3B89EEF8681C6F29989106,
	ErrorMessages_get_DllNotFoundExceptionErrorMessage_m493D1E37E274C2D633EB65991F5B26102E3F6595,
	ErrorMessages__cctor_mC64C25A9DFBF5FA4B8784803560444FA59FDD7DC,
	LogUtil__cctor_m3A18E1D17E9D3E61E2A8B33C51742249F708BB0A,
	LogUtil_InitializeLogging_m05046FDF1759F0A851931F7FCF743FAA24BDB434,
	LogUtil_ConvertLogLevel_mD765D2120AA5F1681CFFCB822C189152704565FC,
	LogUtil_LogMessage_mC36C5CCA27AFA6A1773D0A993DAC4A0C609F6C8B,
	LogUtil_LogMessageFromCallback_m7B4B1B33C46B05A14DE066D91BB2110DF44B8107,
	LogUtil__ctor_mE6F41CDC7EFF92D76E3D07B5F8350BF7D5A4983D,
	LogUtil_Finalize_mD288B9870DD8D7AF744044513FCA62F9AB42A85C,
	LogUtil_Dispose_mB12D003420083CAA79A613F44A635DA5418C989D,
	LogUtil_Dispose_m3E431D1105B6EBDC3183FA2B55AA7608607BBC6D,
	LogUtil_U3C_ctorU3Eb__9_0_m3CCEC7958C25A61458C58F678BCD77AA29A551B6,
	LogMessageDelegate__ctor_mEBA3FFB53CCE522DBB1B5571A5623A649E6643F0,
	LogMessageDelegate_Invoke_mB54C38843065556AF65D1E42C9DDC9AFAFA5C5E8,
	LogMessageDelegate_BeginInvoke_m9A9B00026484A1266F2E8E1101699C83D2755654,
	LogMessageDelegate_EndInvoke_m13C568B3D481DF381F37AAEF2FA598025FDB8C95,
	MonoPInvokeCallbackAttribute__ctor_mD5BA102663DCE67244B79EF374E5641E765AB5CB,
	FutureBase__ctor_m69C88EC69B422C5752B2E249303D92F649B8C8AC,
	FutureBase_Finalize_m02E7843DEC68FBDDCA2B009E905FE4657C2B04AC,
	FutureBase_Dispose_m2C0FDC1F8EF2499A1E52D6CFEA94348388784BDB,
	FutureBase_Dispose_mD92D3FE1E216E3FFBE40723A1F3871452931B2AB,
	FutureBase_status_m478C1E6AF62FB15C218A7C422CF5DC8CA1486CAA,
	FutureBase_error_mBA8200B272D3DB91D1EE78ECE0A10AAB84771C03,
	FutureBase_error_message_m6E9B30EF5EC5EE999B91077E60E3B96978DE4774,
	StringStringMap__ctor_mB3137F09FFB1EC3F5621EA75DBBEF82F9487D366,
	StringStringMap_getCPtr_m7DDDDE0EC47182907217F5620BDEDF5BC8018657,
	StringStringMap_Finalize_m54B7DA5EEEDF3E469B05EC36A6FCE520DE2AF925,
	StringStringMap_Dispose_mA58EFAC215289EDDB29347D7FB6EC3E9E84FADDF,
	StringStringMap_Dispose_m33B2F2642A9D60E6A52283A65374B2A4BA868C1F,
	StringStringMap_get_Item_mB384423BD033B98EE3457212BE65092712C56789,
	StringStringMap_set_Item_m8A9BD489465331D5D800240D38CBB7965925F1F0,
	StringStringMap_get_Count_m907D4DD769A90CAA0683591119FDC3728EEA5BB3,
	StringStringMap_get_IsReadOnly_m96FF99B82D36FD2E3BA738A31E98197987F9AA01,
	StringStringMap_get_Keys_m913897400DEF518DFD7D6E0CCEB1FE2026A2B2D3,
	StringStringMap_Add_m47B75C347EDA853CA95EFE263E2B645A0241D430,
	StringStringMap_Remove_m3443C8BF4781ADB1C64089E6DA44095D341329D2,
	StringStringMap_Contains_mE32677A31CE147FDD041B47D9D48914312DB8AF4,
	StringStringMap_CopyTo_mD1D62132C4D6B43C716FBDD75A6628BD7DF618F8,
	StringStringMap_globalU3AU3ASystem_Collections_Generic_IEnumerableU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CSystem_StringU3EU3E_GetEnumerator_mC7628FC9F836BA4B0C341737ED653595000DAFD4,
	StringStringMap_globalU3AU3ASystem_Collections_IEnumerable_GetEnumerator_mE9D90D7B85A3208AE71B68D31C8BA56540BA8633,
	StringStringMap__ctor_mDD72D1A9A58D226FEA04CB062E9240FBF4B0949E,
	StringStringMap_size_mBB463BF4F9C6625F91343E1E8F38418A790F1D00,
	StringStringMap_Clear_mB1FFA2B7D6635E95F3A55805C39BE0AD3B14F21E,
	StringStringMap_getitem_m9E38CEC82B11AC188D75FA0BBB8A8751AC30D6D4,
	StringStringMap_setitem_m17A4FFDF71A27F385C53EC0C18EC02BE031CEE0F,
	StringStringMap_Add_m9D2175007BBA9E8488671E9C13A08F247D5C2AED,
	StringStringMap_Remove_m3E16E65DA11460219114402300F67FA76D87B408,
	StringStringMap_create_iterator_begin_m3F118062973941F86B02039418B2A2E5475F4377,
	StringStringMap_get_next_key_m5748FA89B2C25838202E9E28D1DFB4FB8C33CE73,
	StringStringMap_destroy_iterator_m345F0B0BBC506B42F149223404B1A9F205232F87,
	StringStringMapEnumerator__ctor_m765FC216E9A67EA91E8D360C32ED59B5EF59A43A,
	StringStringMapEnumerator_get_Current_m76DE586A735687200DC7ACF803FEF75527302F06,
	StringStringMapEnumerator_globalU3AU3ASystem_Collections_IEnumerator_get_Current_m5AC18877BA31B175CFB4CE0564BD6D59B835207C,
	StringStringMapEnumerator_MoveNext_m22BD0722C730E02430014E65174344AA499F336F,
	StringStringMapEnumerator_Reset_m71559FB9DF7506B0DBD523E327E02E6B4405A84E,
	StringStringMapEnumerator_Dispose_m1B7713DE735B05E182D29A076ED15B7720E91BD6,
	FutureVoid__ctor_m1360132BEDC4A7668F93C0D1EB79CE3E28E94597,
	FutureVoid_Dispose_mB88632A1075578EB4A185527EF0C9113111ECA9F,
	FutureVoid_GetTask_m99313F8ED4D21B3440FA37F24EDE6206F6B0E3BF,
	FutureVoid_ThrowIfDisposed_mA7C1D4055AA1AC53502961F16F6449C4647792AF,
	FutureVoid_SetOnCompletionCallback_mA6429D52596940DA40C03180D7020560EAB0C14B,
	FutureVoid_SetCompletionData_mF298BA528990E45DD85839CA0E91130876DAF491,
	FutureVoid_SWIG_CompletionDispatcher_m60ECAC83E9887F17ECCAFE1C1DE0340903FB5281,
	FutureVoid_SWIG_OnCompletion_m48F9542CC9CFAF8D061DB014E2791A8C4D6EEBF3,
	FutureVoid_SWIG_FreeCompletionData_mFEA68F96EA671BB05C45C11DDC981A965E7C4ACF,
	FutureVoid__cctor_mDCA344A3A0F77964E3A10A61E864E8DA62070ADD,
	Action__ctor_mBB9FA88AEDA150A5C03EF7CC69344846B1A4FD22,
	Action_Invoke_m8F270518E4B4FBDC595EA69BEC37C94DE19EBB3E,
	Action_BeginInvoke_m880F277B9978E98E5E80FB4B6F6B92085AA9DC10,
	Action_EndInvoke_m25D93B13687F5F2BF6E3C41FA7E3BC79CDA289AB,
	SWIG_CompletionDelegate__ctor_m93C9C83EF65339E2A9134FA2E79AA1895FE881A2,
	SWIG_CompletionDelegate_Invoke_m331EF656E6CAEB2B61DFD5CF6A68D4045F486CE5,
	SWIG_CompletionDelegate_BeginInvoke_mADCF9246A7BD1637ABAA9876C8C16EBFD318C78C,
	SWIG_CompletionDelegate_EndInvoke_m3D04ABBCBB71B86479A9252A5B3D39C7A15AF54C,
	U3CU3Ec__DisplayClass5_0__ctor_mD476161B45CC35BB800F3E7DFE1A32AE02C6A19D,
	U3CU3Ec__DisplayClass5_0_U3CGetTaskU3Eb__0_m3182CE35DFFBB2B549FB2A7DDB7D84AF9B5C8A1B,
	FirebaseApp__ctor_mB94B4439B6474A9B3420A388F4C847153BB7DA2B,
	FirebaseApp_getCPtr_mB9BA3F66814A2FC1CCF1355B112F2D1DD4E72E86,
	FirebaseApp_Finalize_mBA9B05FC454D571B021370352E3DAA24A927C964,
	FirebaseApp_Dispose_mD97452CFB97FC00105EB0369582537ED1C457A93,
	FirebaseApp_Dispose_m05D2841662AD52390E0D09C5A81366D751FF8B38,
	FirebaseApp__cctor_m65C207A2EB72FE3DF40D35D1018C27881D66EB62,
	FirebaseApp_TranslateDllNotFoundException_m7AA751F31022C03AA209E73B99646C4D0A17A90D,
	FirebaseApp_get_DefaultInstance_mCC924BAC33B68B03C851ACE856930B839F8267D7,
	FirebaseApp_GetInstance_mED18364B5B6A567DEDDBFE38F5CE174FF8171196,
	FirebaseApp_Create_m9A391D6EC41EE45EAA3CCAE368F182EAD69791ED,
	FirebaseApp_get_Name_m5945BBCED21D565E2D871D4CC00D03BB6EDB60B8,
	FirebaseApp_get_LogLevel_mF5210CA8F87660D4B3747792C00C98579142CAAF,
	FirebaseApp_AddReference_m1B148D579E5DBB9E6608813E714769757F33142B,
	FirebaseApp_RemoveReference_mF1E15EE01A41D7674FAFDAB777CF637071F907E0,
	FirebaseApp_ThrowIfNull_m408BCFBB4ED87F4FF412F374897F083E87897315,
	FirebaseApp_InitializeAppUtilCallbacks_m598537A6D9F6327DAA4E5398BB645C5B7CA81064,
	FirebaseApp_OnAllAppsDestroyed_m6D2ADF3AFBC105388CB7D63FA431CF5BAF9FA94D,
	FirebaseApp_InitializeCrashlyticsIfPresent_m28C2D06F823206C421BF3D241335A8FA4D26A91B,
	FirebaseApp_CreateAndTrack_m7FC684DE29AF92053981EB1323B38DBC4D34AC26,
	FirebaseApp_SetCheckDependenciesThread_m4DFA2531E30E77FFA43EBC45E23CD306E8BA8764,
	FirebaseApp_ThrowIfCheckDependenciesRunning_m6452309AE10D2E53CCA8457A4D8A181B164D05EA,
	FirebaseApp_IsCheckDependenciesRunning_m35488E6B310C9CF1BC00E4464028166723C3F078,
	FirebaseApp_CheckDependenciesAsync_mB7A747897A064C84E4D84585DFEAF74A38D38F43,
	FirebaseApp_CheckAndFixDependenciesAsync_m268BF2FC002D4D4CC247EF2636CDF7EBFF043009,
	FirebaseApp_CheckDependencies_mCD719D29867B855EBA5F74102286BB9A44FCB946,
	FirebaseApp_CheckDependenciesInternal_m3C1FA1603F994655D1C49443FFF8053420F98509,
	FirebaseApp_FixDependenciesAsync_mDBBC901E36336AE90E590E9DACD32FE1CCDB79E1,
	FirebaseApp_ResetDefaultAppCPtr_m23794116100108FA137A11654DDF8C74A5C5F453,
	FirebaseApp_get_NameInternal_m89112573EBE5801863EA50B17FBAE20CBE855C75,
	FirebaseApp_CreateInternal_m7C9DA4F992AD0B435BE57A04D5BE3537AB2A0233,
	FirebaseApp_ReleaseReferenceInternal_m251EC6B33F516335E844BC8CBC7531205D41C934,
	FirebaseApp_RegisterLibrariesInternal_m7994F3905C955E4DEAA729005F9E90D6FD23B7A8,
	FirebaseApp_LogHeartbeatInternal_m787662E07280C7D53C2F98DA855C6C467D6FDA13,
	FirebaseApp_AppSetDefaultConfigPath_m55D859EEC14CA1FFEF3DC3422F5CE8C1328E097F,
	FirebaseApp_get_DefaultName_mFAACACB5590298942D3A75F2A6BC53BD6531963C,
	EnableModuleParams_get_CppModuleName_mE57521DAC3F8972C81AFBC72DB70FE79A9F946B3,
	EnableModuleParams_set_CppModuleName_m780B77AD33765B83D0675C02876BB379B9EFCCEB,
	EnableModuleParams_get_CSharpClassName_m5C21BC47A020FE24984E1A282267CE62CF09080B,
	EnableModuleParams_set_CSharpClassName_mB1413BCF93E8A3B658798ED556E586C47981F018,
	EnableModuleParams_get_AlwaysEnable_m76B3B18100019E68E79EA0A0B320B1EAE0AB8260,
	EnableModuleParams_set_AlwaysEnable_m38C379905DD5810F629E35AD34DD0F677990ACFE,
	EnableModuleParams__ctor_mC9619ED6B8BE82D305359BB53BDE6274A8A2D2C2,
	CreateDelegate__ctor_m9B61AF9F4EFF9CCA9FC10B8BDB5E8AD7130E4DE1,
	CreateDelegate_Invoke_m9FC551133A4F9301FB4F107B90F7C98A66F95BE9,
	CreateDelegate_BeginInvoke_mE55A9209FBD926992A08A1F0C0DCAC9083CC66A5,
	CreateDelegate_EndInvoke_mA8FE3700C59A150FAA37B17BDAA7A44319C1116E,
	U3CU3Ec__cctor_m28462B4FF12E06FADDEB8D5A3FAC11B4D920FFC1,
	U3CU3Ec__ctor_m58F4F61972161EC4DA21A6B10024C4D7C71DC42A,
	U3CU3Ec_U3CCreateU3Eb__15_0_m88AD2B39B4B74F4D45D16198A1910301A7255079,
	U3CU3Ec_U3CCreateAndTrackU3Eb__48_0_m0F69C58EFA46937CB73BD5245B64D5F8C18D0C17,
	U3CU3Ec_U3CCheckDependenciesAsyncU3Eb__56_0_m6A160DA6D9DD0D50C3B7CB6154EBEC780E60B16F,
	U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_0_mEF1FF5A1B2931F7DA90AB88D6DF6445776E66C9E,
	U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_1_m29647E233F76648112CEC303862A3884C9BD091C,
	U3CU3Ec_U3CFixDependenciesAsyncU3Eb__60_1_m4F62D52183948C02032FAE665CFFAB524D6045B0,
	U3CU3Ec__DisplayClass58_0__ctor_m565F7F01CA488A06B03680C4DFED5F65F6C69E17,
	U3CU3Ec__DisplayClass58_0_U3CCheckDependenciesU3Eb__0_m97B4EEB7056CE6B790CCC0E1CD6941437F6EDCB9,
	U3CU3Ec__DisplayClass60_0__ctor_m4C69617DCBD98CE10C1ED9C7E10740EFDCF63575,
	U3CU3Ec__DisplayClass60_0_U3CFixDependenciesAsyncU3Eb__0_mE8201994F327DD8162D439F7D581BAB7D8EB7561,
	AppUtilPINVOKE__cctor_mB82716D517E855CA4815CA1B872B0599F1785B19,
	AppUtilPINVOKE_delete_FutureBase_m05F2F373729A515D05275F4EDE8C244C5FD43FCC,
	AppUtilPINVOKE_FutureBase_status_mAADCE6BBF81EA978FD0AE85FB933E459B7F85604,
	AppUtilPINVOKE_FutureBase_error_m2D1B3DA922FE2A7A45D3C947B10CEF6D2D357533,
	AppUtilPINVOKE_FutureBase_error_message_m8B286D44C72F4B764A402DBB4F3A14F6EBF92FF1,
	AppUtilPINVOKE_new_StringStringMap__SWIG_0_mA6EA891DA3D8FEB5638E135ABAD2AC0DC431ED36,
	AppUtilPINVOKE_StringStringMap_size_m4E63887FDF8B0E1D3DB6E84815E85BF5B4D290CE,
	AppUtilPINVOKE_StringStringMap_Clear_m2C6F7439A94CAE20846174ED69FBA2F9066E7A1D,
	AppUtilPINVOKE_StringStringMap_getitem_m2EB0B1A9766EBBCABF6CFECEC8F22E1091AE9F7D,
	AppUtilPINVOKE_StringStringMap_setitem_m3961F9DFD0085CFF6D6C5D6393960E98D7C31629,
	AppUtilPINVOKE_StringStringMap_Add_m3527846D771DE697566F691022E6AE3809139D2B,
	AppUtilPINVOKE_StringStringMap_Remove_m0C85EE5015CC8052BDA51D49D336DF5170A69DE5,
	AppUtilPINVOKE_StringStringMap_create_iterator_begin_mFFFF3528977EDCCEAD0B111C6255900D627FCE34,
	AppUtilPINVOKE_StringStringMap_get_next_key_mC1E6FBC6B8E837D49B3B51048846EFBEFEEB455B,
	AppUtilPINVOKE_StringStringMap_destroy_iterator_m40AFDE67ED8D7A4FBC965EEE1B0DA9E15DEB66CC,
	AppUtilPINVOKE_delete_StringStringMap_m8EAC4B5F1B2198D47AA2245074DD2C4B73F2DE0A,
	AppUtilPINVOKE_FutureVoid_SWIG_OnCompletion_m8ECE4C8E41A5F9C81C5FBF6739AF216985DA369B,
	AppUtilPINVOKE_FutureVoid_SWIG_FreeCompletionData_m6F255A098D3B9CA3ABAA838700DE34F483A80C67,
	AppUtilPINVOKE_delete_FutureVoid_m23422CC33D67D0B3ACBA292B975FC62633FA5D90,
	AppUtilPINVOKE_FirebaseApp_NameInternal_get_m7A1FC05B47A670C5EF6228A284F2830D5F966AAF,
	AppUtilPINVOKE_FirebaseApp_CreateInternal__SWIG_0_mE02354511523AA50E810D1C60FA97DF31F050081,
	AppUtilPINVOKE_FirebaseApp_ReleaseReferenceInternal_m83E641C7626685F4E5018D332B214A4497F4437C,
	AppUtilPINVOKE_FirebaseApp_GetLogLevelInternal_m32950055F0CCA12C52B9948446AB55C4FFBF79D3,
	AppUtilPINVOKE_FirebaseApp_RegisterLibrariesInternal_m4EF07686F8D26220CB0EBAB65C4ADC3AFC28D227,
	AppUtilPINVOKE_FirebaseApp_LogHeartbeatInternal_mFFFD1ED22D411FBC4C2BEAB561A7E67C7CB6D7CE,
	AppUtilPINVOKE_FirebaseApp_AppSetDefaultConfigPath_m991D5A7289A762B01357BD1585E7C94270177C09,
	AppUtilPINVOKE_FirebaseApp_DefaultName_get_mBAEE6B135FCB729CB583A3CA4597DB952D48C585,
	AppUtilPINVOKE_PollCallbacks_mBFD4A04F81A7B15308F5B4CEB0C1D584CAF1EC71,
	AppUtilPINVOKE_AppEnableLogCallback_mB2B3CFF17912BDBF2A0EADD54BF5A68BE0078160,
	AppUtilPINVOKE_SetEnabledAllAppCallbacks_mA4D2C483C70D7FA772D58523B4656A088EB1E0C7,
	AppUtilPINVOKE_SetEnabledAppCallbackByName_m0E27DC635F167FBE31CC908C881E76E59F369183,
	AppUtilPINVOKE_GetEnabledAppCallbackByName_mB8121629E6C43D1A22E1773F92A74391C2E48F44,
	AppUtilPINVOKE_SetLogFunction_mC95A68C1B570C234A5DEDAB0B78ABAFC60CABC0C,
	AppUtilPINVOKE_CheckAndroidDependencies_mF626F790D17BBB6B55308DFE7306CCFF1148C210,
	AppUtilPINVOKE_FixAndroidDependencies_mA7454C1CECC9D0FFB05F60F0064E5D95E16BD699,
	AppUtilPINVOKE_InitializePlayServicesInternal_m6CDF2BCDF9BDBA87CBE0D803B3B6CD16D495CC40,
	AppUtilPINVOKE_TerminatePlayServicesInternal_mA808F30F3DEFF00997E5042805C0E717D7725F32,
	AppUtilPINVOKE_FutureVoid_SWIGUpcast_m34C07BD600DBA8A05C4DA0B553472CBC407C55FF,
	SWIGExceptionHelper_SWIGRegisterExceptionCallbacks_AppUtil_m73FDAC85B33251A5207C88493A40F2F24D634676,
	SWIGExceptionHelper_SWIGRegisterExceptionCallbacksArgument_AppUtil_m8F9C20ECB599940EF0CFCFF5598BD3C3222E2B37,
	SWIGExceptionHelper_SetPendingApplicationException_mD6D4D05834648B9CE669A89E0AD6CD0D32A0EDEB,
	SWIGExceptionHelper_SetPendingArithmeticException_m99B7D12BD99B0EAE887BC2C4B43366133758A5BA,
	SWIGExceptionHelper_SetPendingDivideByZeroException_mD01159299A641E72ED233ACA5F9049D89B916FEC,
	SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m917EAD3BCF29EFE9E6450F3420BE2917F60CC2F2,
	SWIGExceptionHelper_SetPendingInvalidCastException_mF21809DB109F445315F916048C030A4F86843301,
	SWIGExceptionHelper_SetPendingInvalidOperationException_m3C9E61FD1588B6CF1950AC28B4CC3599A7E4E16D,
	SWIGExceptionHelper_SetPendingIOException_mE89EF518A4630B7ACD06C6C2E31E6CA3FCB01774,
	SWIGExceptionHelper_SetPendingNullReferenceException_m8C550DB92EB91F9325BBAF8DDF2A868F082EABCD,
	SWIGExceptionHelper_SetPendingOutOfMemoryException_mB184DCD107C95ED1930E66177083BB532FC6E037,
	SWIGExceptionHelper_SetPendingOverflowException_mE247FE196CCC5A806A4941FC95F6270BAE75653F,
	SWIGExceptionHelper_SetPendingSystemException_m0BFC9561278749A88CC8847749084DE5FBCC5E15,
	SWIGExceptionHelper_SetPendingArgumentException_m1D154F67ADBC4A696102A0BAAAE4FF18BC2D8B1C,
	SWIGExceptionHelper_SetPendingArgumentNullException_mCB01A40C26F2595EE0928F65A06D942BEF9F881D,
	SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m355EE981BB1BAA35BB8E5C5EC2E90625C1C95166,
	SWIGExceptionHelper__cctor_m88D96246E0C2DD75CFC4054F09FA9044A5B6FA90,
	SWIGExceptionHelper__ctor_m06C48C4611CDA458CA1AF651ED06BF7FF7EDF536,
	ExceptionDelegate__ctor_m4E04BD56501AA698F333F3189D232E0DD8BE66A0,
	ExceptionDelegate_Invoke_mE907915DC5B6A911DE7F253DF0E0D82F63B23A06,
	ExceptionDelegate_BeginInvoke_m72D31AEE58624296E481B8F6C28EDF28C445F92B,
	ExceptionDelegate_EndInvoke_mCD778A944D0755D6227785C17547B6F3FCCB9D59,
	ExceptionArgumentDelegate__ctor_m9B64B0E9472C1DDAA639843324FD57FBCCE07E08,
	ExceptionArgumentDelegate_Invoke_mD10622418D792C1CDA2D02B0117C251187C52D74,
	ExceptionArgumentDelegate_BeginInvoke_m0410594AB6ABF10A9740F06B324A5A6C059E39B9,
	ExceptionArgumentDelegate_EndInvoke_mE24172C5085232AD4E4A4EAC36FBF77A79A93C31,
	SWIGPendingException_get_Pending_m57F7C179B5EFB37003896A5F25F4FBED7DA3D2AD,
	SWIGPendingException_Set_m3016052808B54728D457EB1D4E8E7306D806098A,
	SWIGPendingException_Retrieve_m62D9AC53AD2901040C0DF7F7800858C07617B6CD,
	SWIGPendingException__cctor_m0ECBAFFCFA5AA7F4EF02F7400DD059E373CB3291,
	SWIGStringHelper_SWIGRegisterStringCallback_AppUtil_m72D311A8F2D513C5602B6F4E7936C2910DBECA15,
	SWIGStringHelper_CreateString_m411964D0112A7FBA74A5AA7693C8AC07D24F13BC,
	SWIGStringHelper__cctor_m8EE8BC5E0ABB17F0F9D0A2F615EE4987FD86F3B0,
	SWIGStringHelper__ctor_m9F305BAB06F185B49FD5AC05A407928C69D672F6,
	SWIGStringDelegate__ctor_mED39AF7AB0675F58D7C5E732BB50C419BF321299,
	SWIGStringDelegate_Invoke_mE2D5B14F87E5528B7095C2B08CFD4B10A4926BDF,
	SWIGStringDelegate_BeginInvoke_m071087F52EF4FF4FB5914D2D986CD4607DFDE8E7,
	SWIGStringDelegate_EndInvoke_m4A38AD7D0C3965603592F826BC582D78A7FB7AA0,
	AppUtil_PollCallbacks_m75E222C3BCE3563C9C27265D3AE011E3E342E527,
	AppUtil_AppEnableLogCallback_m01A441841A004A6048FCFC012F083BFAA3581C66,
	AppUtil_SetEnabledAllAppCallbacks_m40DACCE1222844931485D67E3885D9ACD1C31FF2,
	AppUtil_SetEnabledAppCallbackByName_m59AF9169D18540D471ECB1A999A5F7B67D0B63BC,
	AppUtil_GetEnabledAppCallbackByName_m4E31F50E6B3C90FF204A4BC57293B0A1C5F81513,
	AppUtil_SetLogFunction_m9B6FD50FE9307EC41EB21DD82665887C9F6514BB,
	AppUtil_CheckAndroidDependencies_mF6F1264E5DA034CDB3B4A20715419FB36BB09FED,
	AppUtil_FixAndroidDependenciesAsync_m4E0A5F5046D85FBCEEB21963516269A404D9ACA7,
	AppUtil_InitializePlayServicesInternal_mFDAD6568DBE745E692B38FC49EA137C6FBCB2850,
	AppUtil_TerminatePlayServicesInternal_mEEDE73F4D1E3F490FFE1329DBD9255A944830B57,
	VersionInfo_get_SdkVersion_m98A33172434D6EA468C4FF32AC941045D88DCF8B,
	VersionInfo_get_BuildSource_m6FA5048FBD9FF01F4AA5811F1209868D080ADD2D,
	FirebaseAppUtils_get_Instance_m79B79F13BD6329A947B640DB886DC398439A90BF,
	FirebaseAppUtils_TranslateDllNotFoundException_m6E4E3109BD0827EE0E675EE9CD50E9C6EE343356,
	FirebaseAppUtils_PollCallbacks_m0A88A069EC5477A1CC9BFCEDC55DED431EE64E91,
	FirebaseAppUtils_GetLogLevel_m9E036F13631BCE81463AA50592FBCD54E9CB09D7,
	FirebaseAppUtils__ctor_m69CA1CBCD58CB3128DF35E9560A3D1D38005845F,
	FirebaseAppUtils__cctor_mD68087540805827D81757968316C3971FF48B537,
};
static const int32_t s_InvokerIndices[235] = 
{
	880,
	1597,
	1886,
	1597,
	880,
	545,
	3215,
	3215,
	3231,
	3231,
	3231,
	3013,
	2902,
	2902,
	1935,
	1935,
	1935,
	1571,
	934,
	933,
	880,
	287,
	1610,
	1610,
	918,
	1935,
	1935,
	1571,
	1886,
	1886,
	1900,
	918,
	2995,
	1935,
	1935,
	1571,
	1431,
	934,
	1886,
	1866,
	1900,
	1541,
	1095,
	1095,
	931,
	1900,
	1900,
	1935,
	1929,
	1935,
	1431,
	934,
	934,
	1182,
	1888,
	1429,
	1599,
	1610,
	1831,
	1900,
	1866,
	1935,
	1935,
	918,
	1571,
	3069,
	1935,
	1610,
	1599,
	3185,
	730,
	3187,
	3231,
	933,
	1935,
	748,
	1610,
	933,
	1597,
	476,
	1610,
	1935,
	1935,
	918,
	2995,
	1935,
	1935,
	1571,
	3231,
	3189,
	3215,
	3069,
	3215,
	1900,
	3209,
	1935,
	1935,
	1935,
	3231,
	3231,
	3203,
	2795,
	3185,
	3231,
	3203,
	3215,
	3215,
	3209,
	3209,
	3215,
	3231,
	1900,
	3215,
	3189,
	3189,
	3189,
	3189,
	3215,
	1900,
	1610,
	1900,
	1610,
	1866,
	1571,
	571,
	933,
	1900,
	748,
	1431,
	3231,
	1935,
	1900,
	1866,
	1886,
	1431,
	1431,
	1610,
	1935,
	1935,
	1935,
	1935,
	3231,
	3184,
	3011,
	3011,
	3064,
	3211,
	3136,
	3184,
	2779,
	2554,
	2554,
	2658,
	3040,
	2778,
	2897,
	3184,
	2458,
	3187,
	3184,
	3064,
	3211,
	3184,
	3209,
	3184,
	3184,
	3189,
	3215,
	3231,
	3181,
	3181,
	2908,
	2946,
	3189,
	3209,
	3211,
	3231,
	3231,
	3043,
	1962,
	2606,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	2915,
	2915,
	2915,
	3231,
	1935,
	933,
	1610,
	490,
	1610,
	933,
	934,
	301,
	1610,
	3203,
	3189,
	3215,
	3231,
	3189,
	3069,
	3231,
	1935,
	933,
	1431,
	490,
	1431,
	3231,
	3181,
	3181,
	2908,
	2946,
	3189,
	3209,
	3215,
	3231,
	3231,
	3215,
	3215,
	3215,
	1610,
	1935,
	1886,
	1935,
	3231,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[17] = 
{
	{ 0x0600000E, 1,  (void**)&LogUtil_LogMessageFromCallback_m7B4B1B33C46B05A14DE066D91BB2110DF44B8107_RuntimeMethod_var, 0 },
	{ 0x06000046, 0,  (void**)&FutureVoid_SWIG_CompletionDispatcher_m60ECAC83E9887F17ECCAFE1C1DE0340903FB5281_RuntimeMethod_var, 0 },
	{ 0x060000B6, 3,  (void**)&SWIGExceptionHelper_SetPendingApplicationException_mD6D4D05834648B9CE669A89E0AD6CD0D32A0EDEB_RuntimeMethod_var, 0 },
	{ 0x060000B7, 7,  (void**)&SWIGExceptionHelper_SetPendingArithmeticException_m99B7D12BD99B0EAE887BC2C4B43366133758A5BA_RuntimeMethod_var, 0 },
	{ 0x060000B8, 8,  (void**)&SWIGExceptionHelper_SetPendingDivideByZeroException_mD01159299A641E72ED233ACA5F9049D89B916FEC_RuntimeMethod_var, 0 },
	{ 0x060000B9, 10,  (void**)&SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m917EAD3BCF29EFE9E6450F3420BE2917F60CC2F2_RuntimeMethod_var, 0 },
	{ 0x060000BA, 11,  (void**)&SWIGExceptionHelper_SetPendingInvalidCastException_mF21809DB109F445315F916048C030A4F86843301_RuntimeMethod_var, 0 },
	{ 0x060000BB, 12,  (void**)&SWIGExceptionHelper_SetPendingInvalidOperationException_m3C9E61FD1588B6CF1950AC28B4CC3599A7E4E16D_RuntimeMethod_var, 0 },
	{ 0x060000BC, 9,  (void**)&SWIGExceptionHelper_SetPendingIOException_mE89EF518A4630B7ACD06C6C2E31E6CA3FCB01774_RuntimeMethod_var, 0 },
	{ 0x060000BD, 13,  (void**)&SWIGExceptionHelper_SetPendingNullReferenceException_m8C550DB92EB91F9325BBAF8DDF2A868F082EABCD_RuntimeMethod_var, 0 },
	{ 0x060000BE, 14,  (void**)&SWIGExceptionHelper_SetPendingOutOfMemoryException_mB184DCD107C95ED1930E66177083BB532FC6E037_RuntimeMethod_var, 0 },
	{ 0x060000BF, 15,  (void**)&SWIGExceptionHelper_SetPendingOverflowException_mE247FE196CCC5A806A4941FC95F6270BAE75653F_RuntimeMethod_var, 0 },
	{ 0x060000C0, 16,  (void**)&SWIGExceptionHelper_SetPendingSystemException_m0BFC9561278749A88CC8847749084DE5FBCC5E15_RuntimeMethod_var, 0 },
	{ 0x060000C1, 4,  (void**)&SWIGExceptionHelper_SetPendingArgumentException_m1D154F67ADBC4A696102A0BAAAE4FF18BC2D8B1C_RuntimeMethod_var, 0 },
	{ 0x060000C2, 5,  (void**)&SWIGExceptionHelper_SetPendingArgumentNullException_mCB01A40C26F2595EE0928F65A06D942BEF9F881D_RuntimeMethod_var, 0 },
	{ 0x060000C3, 6,  (void**)&SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m355EE981BB1BAA35BB8E5C5EC2E90625C1C95166_RuntimeMethod_var, 0 },
	{ 0x060000D3, 17,  (void**)&SWIGStringHelper_CreateString_m411964D0112A7FBA74A5AA7693C8AC07D24F13BC_RuntimeMethod_var, 0 },
};
extern const CustomAttributesCacheGenerator g_Firebase_App_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Firebase_App_CodeGenModule;
const Il2CppCodeGenModule g_Firebase_App_CodeGenModule = 
{
	"Firebase.App.dll",
	235,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	17,
	s_reversePInvokeIndices,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_Firebase_App_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
