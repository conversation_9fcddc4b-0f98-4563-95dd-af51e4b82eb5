{"root": [{"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}]}