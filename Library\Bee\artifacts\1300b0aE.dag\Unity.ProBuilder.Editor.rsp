-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.ref.dll"
-define:UNITY_2021_3_45
-define:UNITY_2021_3
-define:UNITY_2021
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_RUNTIME_PERMISSIONS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_ANDROID
-define:TEXTCORE_1_0_OR_NEWER
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:BCG_RCC
-define:Admob_Simple_Rizwan
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.IOSResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.JarResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.PackageManagerResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.VersionHandlerImpl.dll"
-r:"Assets/ExternalDependencyManager/Editor/Google.VersionHandler.dll"
-r:"Assets/Firebase/Editor/Firebase.Editor.dll"
-r:"Assets/Firebase/Plugins/Firebase.Analytics.dll"
-r:"Assets/Firebase/Plugins/Firebase.App.dll"
-r:"Assets/Firebase/Plugins/Firebase.Platform.dll"
-r:"Assets/Firebase/Plugins/Firebase.TaskExtension.dll"
-r:"Assets/Firebase/Plugins/Google.MiniJson.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Common.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Core.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.Unity.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Unity.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Csg.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Stl.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/AssemblyInfo.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/AutoUVEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/BezierSplineEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/BooleanEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ConfigurableWindow.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ContextClickManipulator.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/CutTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/DestroyListener.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/DimensionsEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/DrawShapeTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorEnum.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorGUILayout.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorGUIUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorHandleDrawing.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorHandleDrawingScopes.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorHandleUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorMaterialUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorMeshUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorSceneViewPicker.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorShapeUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorSnapping.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorStyles.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorToolbarLoader.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorToolbarMenuItems.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorToolsContexts.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditorUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EditShapeTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EntityEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EntityUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/EntityVisibility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/Experimental.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/FileUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/HandleGUI.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/HierarchyListener.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/IconUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/IHasPreferences.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/LicenseEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/Lightmapping.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/LightmapUVEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/LogEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MainToolbar.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MaterialEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MaterialPalette.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MenuAction.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MenuActionShortcutAttribute.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MenuItems.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MenuOption.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MenuToolToggle.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MeshAndElementSelection.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MeshDebugTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/MeshSelection.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/Model.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ObjExporter.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/pb_ObjectArray.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PlyExporter.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PolyShapeEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PolyShapeTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PositionTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/Pref.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PreferenceDictionaryEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PreferencesInternal.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/PreferencesUpdater.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderAnalytics.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderEditorShortcuts.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderMenuActionAttribute.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderMeshEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderMeshPreview.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProbuilderMoveTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProbuilderRotateTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProbuilderScaleTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderSettings.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderSettingsProvider.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderShapeEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProBuilderToolManager.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ProGridsInterface.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ReflectionUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/RepairActions.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/SceneDragAndDropListener.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ScriptingSymbolManager.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/SelectionUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ShapeMenuItems.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/SmoothGroupEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/StripProBuilderScripts.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/TextureMoveTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/TextureRotateTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/TextureScaleTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/TextureTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/ToolbarGroup.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/TooltipContent.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/TooltipEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/UndoUtility.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/UnityScenePostProcessor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/UnwrapParametersEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/UVEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/UVRenderOptions.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/Version.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/VersionValidator.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/VertexColorPalette.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/VertexManipulationTool.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/VertexManipulationToolSettings.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/VertexOnFaceEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/EditorCore/VertexPositionEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/NewBezierShape.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/NewPolyShapeToggle.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/NewShapeToggle.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/OpenLightmapUVEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/OpenMaterialEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/OpenSmoothingEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/OpenUVEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/OpenVertexColorEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Editors/OpenVertexPositionEditor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Export/Export.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Export/ExportAsset.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Export/ExportObj.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Export/ExportPly.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Export/ExportStlAscii.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Export/ExportStlBinary.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/BevelEdges.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/BridgeEdges.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/CollapseVertices.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/ConformFaceNormals.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/ConnectEdges.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/ConnectVertices.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/DeleteFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/DetachFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/DuplicateFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/Extrude.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/ExtrudeEdges.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/ExtrudeFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/FillHole.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/FlipFaceEdge.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/FlipFaceNormals.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/InsertEdgeLoop.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/MergeFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/OffsetElements.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/SetPivotToSelection.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/SmartConnect.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/SmartSubdivide.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/SplitVertices.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/SubdivideEdges.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/SubdivideFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/TriangulateFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Geometry/WeldVertices.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Interaction/ToggleDragRectMode.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Interaction/ToggleDragSelectionMode.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Interaction/ToggleHandleOrientation.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Interaction/ToggleHandlePivotPoint.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Interaction/ToggleSelectBackFaces.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Interaction/ToggleXRay.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/CenterPivot.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/ConformObjectNormals.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/FlipObjectNormals.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/FreezeTransform.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/GenerateUV2.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/MergeObjects.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/MirrorObjects.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/ProBuilderize.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/SetCollider.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/SetTrigger.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/SubdivideObject.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Object/TriangulateObject.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/GrowSelection.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectEdgeLoop.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectEdgeRing.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectFaceLoop.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectFaceRing.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectHole.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectLoop.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectMaterial.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectRing.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectSmoothingGroup.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/SelectVertexColor.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuActions/Selection/ShrinkSelection.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/MenuToggles/Geometry/CutToolToggle.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/StateMachines/ShapeState.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/StateMachines/ShapeState_DrawBaseShape.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/StateMachines/ShapeState_DrawHeightShape.cs"
"Library/PackageCache/com.unity.probuilder@5.2.3/Editor/StateMachines/ShapeState_InitShape.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.AdditionalFile.txt"