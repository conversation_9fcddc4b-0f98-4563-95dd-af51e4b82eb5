{"logs": [{"outputFile": "com.smg.tractor.trolly.games.farming.game.launcher-mergeReleaseResources-33:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7257", "endColumns": "100", "endOffsets": "7353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1b2a8be1f7c20051c0ee2c7353491a0\\transformed\\jetified-play-services-base-18.2.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2800,2906,3079,3209,3315,3487,3619,3739,3992,4177,4286,4461,4596,4767,4942,5009,5073", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "2901,3074,3204,3310,3482,3614,3734,3841,4172,4281,4456,4591,4762,4937,5004,5068,5151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af425f2533cb03e8373310f0c6b921bc\\transformed\\appcompat-1.2.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,7176", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,7252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3846", "endColumns": "145", "endOffsets": "3987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c864c6bd1e296df4f88b5762f75d1381\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "47,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "5156,5261,5361,5480", "endColumns": "104,99,118,101", "endOffsets": "5256,5356,5475,5577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f7c1a76f7439ca27df3c52327d1c9ad\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,291,347,413,486,592,654,789,915,1048,1098,1151,1279,1373,1412,1490,1524,1557,1604,1670,1709", "endColumns": "45,45,55,65,72,105,61,134,125,132,49,52,127,93,38,77,33,32,46,65,38,55", "endOffsets": "244,290,346,412,485,591,653,788,914,1047,1097,1150,1278,1372,1411,1489,1523,1556,1603,1669,1708,1764"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5582,5632,5682,5742,5812,5889,5999,6065,6204,6334,6471,6525,6582,6714,6812,6855,6937,6975,7012,7063,7133,7358", "endColumns": "49,49,59,69,76,109,65,138,129,136,53,56,131,97,42,81,37,36,50,69,42,59", "endOffsets": "5627,5677,5737,5807,5884,5994,6060,6199,6329,6466,6520,6577,6709,6807,6850,6932,6970,7007,7058,7128,7171,7413"}}]}]}