﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>


template <typename R>
struct VirtFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

// System.Collections.Generic.Dictionary`2<System.Object,System.Object>
struct Dictionary_2_tBD1E3221EBD04CEBDA49B84779912E91F56B958D;
// System.Collections.Generic.Dictionary`2<System.String,System.String>
struct Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5;
// System.Collections.Generic.HashSet`1<System.Object>
struct HashSet_1_t680119C7ED8D82AED56CDB83DF6F0E9149852A9B;
// System.Collections.Generic.HashSet`1<System.String>
struct HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229;
// System.Collections.Generic.IEqualityComparer`1<System.String>
struct IEqualityComparer_1_tE6A65C5E45E33FD7D9849FD0914DE3AD32B68050;
// System.Collections.Generic.Dictionary`2/KeyCollection<System.String,System.String>
struct KeyCollection_t52C81163A051BCD87A36FEF95F736DD600E2305D;
// System.Collections.Generic.List`1<GoogleMobileAds.Api.Mediation.MediationExtras>
struct List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A;
// System.Collections.Generic.List`1<System.Object>
struct List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5;
// System.Collections.Generic.List`1<System.String>
struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3;
// System.Collections.Generic.Dictionary`2/ValueCollection<System.String,System.String>
struct ValueCollection_t9161A5C97376D261665798FA27DAFD5177305C81;
// System.Collections.Generic.Dictionary`2/Entry<System.String,System.String>[]
struct EntryU5BU5D_t52A654EA9927D1B5F56CA05CF209F2E4393C4510;
// System.Collections.Generic.HashSet`1/Slot<System.String>[]
struct SlotU5BU5D_t0AE906AEB021E70A8C465C39ADD28C2B4884604D;
// System.Byte[]
struct ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726;
// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Int32[]
struct Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32;
// GoogleMobileAds.Api.Mediation.MediationExtras[]
struct MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B;
// System.String[]
struct StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A;
// System.Type[]
struct TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755;
// GoogleMobileAds.Api.AdManager.AdManagerAdRequest
struct AdManagerAdRequest_t96CCD47947FF0CD69515505C8D3C25B0E5C67671;
// GoogleMobileAds.Api.AdRequest
struct AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE;
// GoogleMobileAds.Api.AdSize
struct AdSize_t03060093E09AF371C66F52156884405E04436209;
// GoogleMobileAds.Api.AdValue
struct AdValue_t806DD55342062227A4009E24834AE96A38A57F28;
// GoogleMobileAds.Api.AdValueEventArgs
struct AdValueEventArgs_t370093BE304A6D5E913DDA88544E062BE0B3D91E;
// GoogleMobileAds.Api.AdapterStatus
struct AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45;
// GoogleMobileAds.Api.AdManager.AppEvent
struct AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A;
// System.Reflection.AssemblyName
struct AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824;
// System.Reflection.Binder
struct Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30;
// System.Globalization.CultureInfo
struct CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98;
// System.EventArgs
struct EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA;
// GoogleMobileAds.Api.MaxAdContentRating
struct MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239;
// GoogleMobileAds.Api.Mediation.MediationExtras
struct MediationExtras_t29FA501CE520EDBF8C61516FC547ADD575699C3B;
// System.Reflection.MemberFilter
struct MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81;
// GoogleMobileAds.Api.RequestConfiguration
struct RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951;
// GoogleMobileAds.Api.Reward
struct Reward_t53574C3E2B34B29EE3862B9EAE8B8334C0BC2CDB;
// System.Runtime.Serialization.SerializationInfo
struct SerializationInfo_t097DA64D9DB49ED7F2458E964BE8CCCF63FC67C1;
// GoogleMobileAds.Api.ServerSideVerificationOptions
struct ServerSideVerificationOptions_t2FF28841A6B538771FB349BCCD3517E399362E49;
// System.String
struct String_t;
// System.Text.StringBuilder
struct StringBuilder_t;
// System.Reflection.StrongNameKeyPair
struct StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF;
// System.Type
struct Type_t;
// System.Version
struct Version_tBDAEDED25425A1D09910468B8BD1759115646E3C;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;
// System.Reflection.Assembly/ResolveEventHolder
struct ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C;
// GoogleMobileAds.Api.RequestConfiguration/Builder
struct Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE;

IL2CPP_EXTERN_C RuntimeClass* AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringBuilder_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral5F66A1EC0B47E429CB0A3F7C531F0674F96F49F3;
IL2CPP_EXTERN_C String_t* _stringLiteral8244FF4469CD24AECA8F7B1B293CA6DF261B2861;
IL2CPP_EXTERN_C String_t* _stringLiteralB2501285DA6895C43F81A426A8C28EF7BA40D44E;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m83D00E76233F0160C86CF5C7D15286CD14A44A64_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_0_0_0_var;
struct CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshaled_com;
struct CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_t1D3937E03744E90DA195553D49F400C20A4AC7D2 
{
public:

public:
};


// System.Object


// System.Collections.Generic.Dictionary`2<System.String,System.String>
struct Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5  : public RuntimeObject
{
public:
	// System.Int32[] System.Collections.Generic.Dictionary`2::buckets
	Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32* ___buckets_0;
	// System.Collections.Generic.Dictionary`2/Entry<TKey,TValue>[] System.Collections.Generic.Dictionary`2::entries
	EntryU5BU5D_t52A654EA9927D1B5F56CA05CF209F2E4393C4510* ___entries_1;
	// System.Int32 System.Collections.Generic.Dictionary`2::count
	int32_t ___count_2;
	// System.Int32 System.Collections.Generic.Dictionary`2::version
	int32_t ___version_3;
	// System.Int32 System.Collections.Generic.Dictionary`2::freeList
	int32_t ___freeList_4;
	// System.Int32 System.Collections.Generic.Dictionary`2::freeCount
	int32_t ___freeCount_5;
	// System.Collections.Generic.IEqualityComparer`1<TKey> System.Collections.Generic.Dictionary`2::comparer
	RuntimeObject* ___comparer_6;
	// System.Collections.Generic.Dictionary`2/KeyCollection<TKey,TValue> System.Collections.Generic.Dictionary`2::keys
	KeyCollection_t52C81163A051BCD87A36FEF95F736DD600E2305D * ___keys_7;
	// System.Collections.Generic.Dictionary`2/ValueCollection<TKey,TValue> System.Collections.Generic.Dictionary`2::values
	ValueCollection_t9161A5C97376D261665798FA27DAFD5177305C81 * ___values_8;
	// System.Object System.Collections.Generic.Dictionary`2::_syncRoot
	RuntimeObject * ____syncRoot_9;

public:
	inline static int32_t get_offset_of_buckets_0() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___buckets_0)); }
	inline Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32* get_buckets_0() const { return ___buckets_0; }
	inline Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32** get_address_of_buckets_0() { return &___buckets_0; }
	inline void set_buckets_0(Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32* value)
	{
		___buckets_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___buckets_0), (void*)value);
	}

	inline static int32_t get_offset_of_entries_1() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___entries_1)); }
	inline EntryU5BU5D_t52A654EA9927D1B5F56CA05CF209F2E4393C4510* get_entries_1() const { return ___entries_1; }
	inline EntryU5BU5D_t52A654EA9927D1B5F56CA05CF209F2E4393C4510** get_address_of_entries_1() { return &___entries_1; }
	inline void set_entries_1(EntryU5BU5D_t52A654EA9927D1B5F56CA05CF209F2E4393C4510* value)
	{
		___entries_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___entries_1), (void*)value);
	}

	inline static int32_t get_offset_of_count_2() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___count_2)); }
	inline int32_t get_count_2() const { return ___count_2; }
	inline int32_t* get_address_of_count_2() { return &___count_2; }
	inline void set_count_2(int32_t value)
	{
		___count_2 = value;
	}

	inline static int32_t get_offset_of_version_3() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___version_3)); }
	inline int32_t get_version_3() const { return ___version_3; }
	inline int32_t* get_address_of_version_3() { return &___version_3; }
	inline void set_version_3(int32_t value)
	{
		___version_3 = value;
	}

	inline static int32_t get_offset_of_freeList_4() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___freeList_4)); }
	inline int32_t get_freeList_4() const { return ___freeList_4; }
	inline int32_t* get_address_of_freeList_4() { return &___freeList_4; }
	inline void set_freeList_4(int32_t value)
	{
		___freeList_4 = value;
	}

	inline static int32_t get_offset_of_freeCount_5() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___freeCount_5)); }
	inline int32_t get_freeCount_5() const { return ___freeCount_5; }
	inline int32_t* get_address_of_freeCount_5() { return &___freeCount_5; }
	inline void set_freeCount_5(int32_t value)
	{
		___freeCount_5 = value;
	}

	inline static int32_t get_offset_of_comparer_6() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___comparer_6)); }
	inline RuntimeObject* get_comparer_6() const { return ___comparer_6; }
	inline RuntimeObject** get_address_of_comparer_6() { return &___comparer_6; }
	inline void set_comparer_6(RuntimeObject* value)
	{
		___comparer_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___comparer_6), (void*)value);
	}

	inline static int32_t get_offset_of_keys_7() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___keys_7)); }
	inline KeyCollection_t52C81163A051BCD87A36FEF95F736DD600E2305D * get_keys_7() const { return ___keys_7; }
	inline KeyCollection_t52C81163A051BCD87A36FEF95F736DD600E2305D ** get_address_of_keys_7() { return &___keys_7; }
	inline void set_keys_7(KeyCollection_t52C81163A051BCD87A36FEF95F736DD600E2305D * value)
	{
		___keys_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___keys_7), (void*)value);
	}

	inline static int32_t get_offset_of_values_8() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ___values_8)); }
	inline ValueCollection_t9161A5C97376D261665798FA27DAFD5177305C81 * get_values_8() const { return ___values_8; }
	inline ValueCollection_t9161A5C97376D261665798FA27DAFD5177305C81 ** get_address_of_values_8() { return &___values_8; }
	inline void set_values_8(ValueCollection_t9161A5C97376D261665798FA27DAFD5177305C81 * value)
	{
		___values_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___values_8), (void*)value);
	}

	inline static int32_t get_offset_of__syncRoot_9() { return static_cast<int32_t>(offsetof(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5, ____syncRoot_9)); }
	inline RuntimeObject * get__syncRoot_9() const { return ____syncRoot_9; }
	inline RuntimeObject ** get_address_of__syncRoot_9() { return &____syncRoot_9; }
	inline void set__syncRoot_9(RuntimeObject * value)
	{
		____syncRoot_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____syncRoot_9), (void*)value);
	}
};


// System.Collections.Generic.HashSet`1<System.String>
struct HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229  : public RuntimeObject
{
public:
	// System.Int32[] System.Collections.Generic.HashSet`1::_buckets
	Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32* ____buckets_7;
	// System.Collections.Generic.HashSet`1/Slot<T>[] System.Collections.Generic.HashSet`1::_slots
	SlotU5BU5D_t0AE906AEB021E70A8C465C39ADD28C2B4884604D* ____slots_8;
	// System.Int32 System.Collections.Generic.HashSet`1::_count
	int32_t ____count_9;
	// System.Int32 System.Collections.Generic.HashSet`1::_lastIndex
	int32_t ____lastIndex_10;
	// System.Int32 System.Collections.Generic.HashSet`1::_freeList
	int32_t ____freeList_11;
	// System.Collections.Generic.IEqualityComparer`1<T> System.Collections.Generic.HashSet`1::_comparer
	RuntimeObject* ____comparer_12;
	// System.Int32 System.Collections.Generic.HashSet`1::_version
	int32_t ____version_13;
	// System.Runtime.Serialization.SerializationInfo System.Collections.Generic.HashSet`1::_siInfo
	SerializationInfo_t097DA64D9DB49ED7F2458E964BE8CCCF63FC67C1 * ____siInfo_14;

public:
	inline static int32_t get_offset_of__buckets_7() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____buckets_7)); }
	inline Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32* get__buckets_7() const { return ____buckets_7; }
	inline Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32** get_address_of__buckets_7() { return &____buckets_7; }
	inline void set__buckets_7(Int32U5BU5D_t70F1BDC14B1786481B176D6139A5E3B87DC54C32* value)
	{
		____buckets_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____buckets_7), (void*)value);
	}

	inline static int32_t get_offset_of__slots_8() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____slots_8)); }
	inline SlotU5BU5D_t0AE906AEB021E70A8C465C39ADD28C2B4884604D* get__slots_8() const { return ____slots_8; }
	inline SlotU5BU5D_t0AE906AEB021E70A8C465C39ADD28C2B4884604D** get_address_of__slots_8() { return &____slots_8; }
	inline void set__slots_8(SlotU5BU5D_t0AE906AEB021E70A8C465C39ADD28C2B4884604D* value)
	{
		____slots_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____slots_8), (void*)value);
	}

	inline static int32_t get_offset_of__count_9() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____count_9)); }
	inline int32_t get__count_9() const { return ____count_9; }
	inline int32_t* get_address_of__count_9() { return &____count_9; }
	inline void set__count_9(int32_t value)
	{
		____count_9 = value;
	}

	inline static int32_t get_offset_of__lastIndex_10() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____lastIndex_10)); }
	inline int32_t get__lastIndex_10() const { return ____lastIndex_10; }
	inline int32_t* get_address_of__lastIndex_10() { return &____lastIndex_10; }
	inline void set__lastIndex_10(int32_t value)
	{
		____lastIndex_10 = value;
	}

	inline static int32_t get_offset_of__freeList_11() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____freeList_11)); }
	inline int32_t get__freeList_11() const { return ____freeList_11; }
	inline int32_t* get_address_of__freeList_11() { return &____freeList_11; }
	inline void set__freeList_11(int32_t value)
	{
		____freeList_11 = value;
	}

	inline static int32_t get_offset_of__comparer_12() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____comparer_12)); }
	inline RuntimeObject* get__comparer_12() const { return ____comparer_12; }
	inline RuntimeObject** get_address_of__comparer_12() { return &____comparer_12; }
	inline void set__comparer_12(RuntimeObject* value)
	{
		____comparer_12 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____comparer_12), (void*)value);
	}

	inline static int32_t get_offset_of__version_13() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____version_13)); }
	inline int32_t get__version_13() const { return ____version_13; }
	inline int32_t* get_address_of__version_13() { return &____version_13; }
	inline void set__version_13(int32_t value)
	{
		____version_13 = value;
	}

	inline static int32_t get_offset_of__siInfo_14() { return static_cast<int32_t>(offsetof(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229, ____siInfo_14)); }
	inline SerializationInfo_t097DA64D9DB49ED7F2458E964BE8CCCF63FC67C1 * get__siInfo_14() const { return ____siInfo_14; }
	inline SerializationInfo_t097DA64D9DB49ED7F2458E964BE8CCCF63FC67C1 ** get_address_of__siInfo_14() { return &____siInfo_14; }
	inline void set__siInfo_14(SerializationInfo_t097DA64D9DB49ED7F2458E964BE8CCCF63FC67C1 * value)
	{
		____siInfo_14 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____siInfo_14), (void*)value);
	}
};


// System.Collections.Generic.List`1<GoogleMobileAds.Api.Mediation.MediationExtras>
struct List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A  : public RuntimeObject
{
public:
	// T[] System.Collections.Generic.List`1::_items
	MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject * ____syncRoot_4;

public:
	inline static int32_t get_offset_of__items_1() { return static_cast<int32_t>(offsetof(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A, ____items_1)); }
	inline MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B* get__items_1() const { return ____items_1; }
	inline MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B** get_address_of__items_1() { return &____items_1; }
	inline void set__items_1(MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B* value)
	{
		____items_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____items_1), (void*)value);
	}

	inline static int32_t get_offset_of__size_2() { return static_cast<int32_t>(offsetof(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A, ____size_2)); }
	inline int32_t get__size_2() const { return ____size_2; }
	inline int32_t* get_address_of__size_2() { return &____size_2; }
	inline void set__size_2(int32_t value)
	{
		____size_2 = value;
	}

	inline static int32_t get_offset_of__version_3() { return static_cast<int32_t>(offsetof(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A, ____version_3)); }
	inline int32_t get__version_3() const { return ____version_3; }
	inline int32_t* get_address_of__version_3() { return &____version_3; }
	inline void set__version_3(int32_t value)
	{
		____version_3 = value;
	}

	inline static int32_t get_offset_of__syncRoot_4() { return static_cast<int32_t>(offsetof(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A, ____syncRoot_4)); }
	inline RuntimeObject * get__syncRoot_4() const { return ____syncRoot_4; }
	inline RuntimeObject ** get_address_of__syncRoot_4() { return &____syncRoot_4; }
	inline void set__syncRoot_4(RuntimeObject * value)
	{
		____syncRoot_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____syncRoot_4), (void*)value);
	}
};

struct List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A_StaticFields
{
public:
	// T[] System.Collections.Generic.List`1::_emptyArray
	MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B* ____emptyArray_5;

public:
	inline static int32_t get_offset_of__emptyArray_5() { return static_cast<int32_t>(offsetof(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A_StaticFields, ____emptyArray_5)); }
	inline MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B* get__emptyArray_5() const { return ____emptyArray_5; }
	inline MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B** get_address_of__emptyArray_5() { return &____emptyArray_5; }
	inline void set__emptyArray_5(MediationExtrasU5BU5D_t40102247C40333D07CBE655D184D2D056C050B1B* value)
	{
		____emptyArray_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____emptyArray_5), (void*)value);
	}
};


// System.Collections.Generic.List`1<System.String>
struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3  : public RuntimeObject
{
public:
	// T[] System.Collections.Generic.List`1::_items
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject * ____syncRoot_4;

public:
	inline static int32_t get_offset_of__items_1() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____items_1)); }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* get__items_1() const { return ____items_1; }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A** get_address_of__items_1() { return &____items_1; }
	inline void set__items_1(StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* value)
	{
		____items_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____items_1), (void*)value);
	}

	inline static int32_t get_offset_of__size_2() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____size_2)); }
	inline int32_t get__size_2() const { return ____size_2; }
	inline int32_t* get_address_of__size_2() { return &____size_2; }
	inline void set__size_2(int32_t value)
	{
		____size_2 = value;
	}

	inline static int32_t get_offset_of__version_3() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____version_3)); }
	inline int32_t get__version_3() const { return ____version_3; }
	inline int32_t* get_address_of__version_3() { return &____version_3; }
	inline void set__version_3(int32_t value)
	{
		____version_3 = value;
	}

	inline static int32_t get_offset_of__syncRoot_4() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____syncRoot_4)); }
	inline RuntimeObject * get__syncRoot_4() const { return ____syncRoot_4; }
	inline RuntimeObject ** get_address_of__syncRoot_4() { return &____syncRoot_4; }
	inline void set__syncRoot_4(RuntimeObject * value)
	{
		____syncRoot_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____syncRoot_4), (void*)value);
	}
};

struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_StaticFields
{
public:
	// T[] System.Collections.Generic.List`1::_emptyArray
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* ____emptyArray_5;

public:
	inline static int32_t get_offset_of__emptyArray_5() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_StaticFields, ____emptyArray_5)); }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* get__emptyArray_5() const { return ____emptyArray_5; }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A** get_address_of__emptyArray_5() { return &____emptyArray_5; }
	inline void set__emptyArray_5(StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* value)
	{
		____emptyArray_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____emptyArray_5), (void*)value);
	}
};


// GoogleMobileAds.Api.AdRequest
struct AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE  : public RuntimeObject
{
public:
	// System.Collections.Generic.HashSet`1<System.String> GoogleMobileAds.Api.AdRequest::Keywords
	HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * ___Keywords_1;
	// System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.AdRequest::Extras
	Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * ___Extras_2;
	// System.Collections.Generic.List`1<GoogleMobileAds.Api.Mediation.MediationExtras> GoogleMobileAds.Api.AdRequest::MediationExtras
	List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A * ___MediationExtras_3;

public:
	inline static int32_t get_offset_of_Keywords_1() { return static_cast<int32_t>(offsetof(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE, ___Keywords_1)); }
	inline HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * get_Keywords_1() const { return ___Keywords_1; }
	inline HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 ** get_address_of_Keywords_1() { return &___Keywords_1; }
	inline void set_Keywords_1(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * value)
	{
		___Keywords_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Keywords_1), (void*)value);
	}

	inline static int32_t get_offset_of_Extras_2() { return static_cast<int32_t>(offsetof(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE, ___Extras_2)); }
	inline Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * get_Extras_2() const { return ___Extras_2; }
	inline Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 ** get_address_of_Extras_2() { return &___Extras_2; }
	inline void set_Extras_2(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * value)
	{
		___Extras_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Extras_2), (void*)value);
	}

	inline static int32_t get_offset_of_MediationExtras_3() { return static_cast<int32_t>(offsetof(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE, ___MediationExtras_3)); }
	inline List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A * get_MediationExtras_3() const { return ___MediationExtras_3; }
	inline List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A ** get_address_of_MediationExtras_3() { return &___MediationExtras_3; }
	inline void set_MediationExtras_3(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A * value)
	{
		___MediationExtras_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___MediationExtras_3), (void*)value);
	}
};

struct AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_StaticFields
{
public:
	// System.String GoogleMobileAds.Api.AdRequest::<Version>k__BackingField
	String_t* ___U3CVersionU3Ek__BackingField_0;

public:
	inline static int32_t get_offset_of_U3CVersionU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_StaticFields, ___U3CVersionU3Ek__BackingField_0)); }
	inline String_t* get_U3CVersionU3Ek__BackingField_0() const { return ___U3CVersionU3Ek__BackingField_0; }
	inline String_t** get_address_of_U3CVersionU3Ek__BackingField_0() { return &___U3CVersionU3Ek__BackingField_0; }
	inline void set_U3CVersionU3Ek__BackingField_0(String_t* value)
	{
		___U3CVersionU3Ek__BackingField_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CVersionU3Ek__BackingField_0), (void*)value);
	}
};


// GoogleMobileAds.Api.AdManager.AppEvent
struct AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A  : public RuntimeObject
{
public:
	// System.String GoogleMobileAds.Api.AdManager.AppEvent::<Name>k__BackingField
	String_t* ___U3CNameU3Ek__BackingField_0;
	// System.String GoogleMobileAds.Api.AdManager.AppEvent::<Data>k__BackingField
	String_t* ___U3CDataU3Ek__BackingField_1;

public:
	inline static int32_t get_offset_of_U3CNameU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A, ___U3CNameU3Ek__BackingField_0)); }
	inline String_t* get_U3CNameU3Ek__BackingField_0() const { return ___U3CNameU3Ek__BackingField_0; }
	inline String_t** get_address_of_U3CNameU3Ek__BackingField_0() { return &___U3CNameU3Ek__BackingField_0; }
	inline void set_U3CNameU3Ek__BackingField_0(String_t* value)
	{
		___U3CNameU3Ek__BackingField_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CNameU3Ek__BackingField_0), (void*)value);
	}

	inline static int32_t get_offset_of_U3CDataU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A, ___U3CDataU3Ek__BackingField_1)); }
	inline String_t* get_U3CDataU3Ek__BackingField_1() const { return ___U3CDataU3Ek__BackingField_1; }
	inline String_t** get_address_of_U3CDataU3Ek__BackingField_1() { return &___U3CDataU3Ek__BackingField_1; }
	inline void set_U3CDataU3Ek__BackingField_1(String_t* value)
	{
		___U3CDataU3Ek__BackingField_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CDataU3Ek__BackingField_1), (void*)value);
	}
};

struct Il2CppArrayBounds;

// System.Array


// System.EventArgs
struct EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA  : public RuntimeObject
{
public:

public:
};

struct EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_StaticFields
{
public:
	// System.EventArgs System.EventArgs::Empty
	EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA * ___Empty_0;

public:
	inline static int32_t get_offset_of_Empty_0() { return static_cast<int32_t>(offsetof(EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_StaticFields, ___Empty_0)); }
	inline EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA * get_Empty_0() const { return ___Empty_0; }
	inline EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA ** get_address_of_Empty_0() { return &___Empty_0; }
	inline void set_Empty_0(EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA * value)
	{
		___Empty_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_0), (void*)value);
	}
};


// GoogleMobileAds.Api.MaxAdContentRating
struct MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239  : public RuntimeObject
{
public:
	// System.String GoogleMobileAds.Api.MaxAdContentRating::<Value>k__BackingField
	String_t* ___U3CValueU3Ek__BackingField_0;

public:
	inline static int32_t get_offset_of_U3CValueU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239, ___U3CValueU3Ek__BackingField_0)); }
	inline String_t* get_U3CValueU3Ek__BackingField_0() const { return ___U3CValueU3Ek__BackingField_0; }
	inline String_t** get_address_of_U3CValueU3Ek__BackingField_0() { return &___U3CValueU3Ek__BackingField_0; }
	inline void set_U3CValueU3Ek__BackingField_0(String_t* value)
	{
		___U3CValueU3Ek__BackingField_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CValueU3Ek__BackingField_0), (void*)value);
	}
};


// GoogleMobileAds.Api.Mediation.MediationExtras
struct MediationExtras_t29FA501CE520EDBF8C61516FC547ADD575699C3B  : public RuntimeObject
{
public:
	// System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.Mediation.MediationExtras::<Extras>k__BackingField
	Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * ___U3CExtrasU3Ek__BackingField_0;

public:
	inline static int32_t get_offset_of_U3CExtrasU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(MediationExtras_t29FA501CE520EDBF8C61516FC547ADD575699C3B, ___U3CExtrasU3Ek__BackingField_0)); }
	inline Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * get_U3CExtrasU3Ek__BackingField_0() const { return ___U3CExtrasU3Ek__BackingField_0; }
	inline Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 ** get_address_of_U3CExtrasU3Ek__BackingField_0() { return &___U3CExtrasU3Ek__BackingField_0; }
	inline void set_U3CExtrasU3Ek__BackingField_0(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * value)
	{
		___U3CExtrasU3Ek__BackingField_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CExtrasU3Ek__BackingField_0), (void*)value);
	}
};


// System.Reflection.MemberInfo
struct MemberInfo_t  : public RuntimeObject
{
public:

public:
};


// GoogleMobileAds.Api.ServerSideVerificationOptions
struct ServerSideVerificationOptions_t2FF28841A6B538771FB349BCCD3517E399362E49  : public RuntimeObject
{
public:
	// System.String GoogleMobileAds.Api.ServerSideVerificationOptions::UserId
	String_t* ___UserId_0;
	// System.String GoogleMobileAds.Api.ServerSideVerificationOptions::CustomData
	String_t* ___CustomData_1;

public:
	inline static int32_t get_offset_of_UserId_0() { return static_cast<int32_t>(offsetof(ServerSideVerificationOptions_t2FF28841A6B538771FB349BCCD3517E399362E49, ___UserId_0)); }
	inline String_t* get_UserId_0() const { return ___UserId_0; }
	inline String_t** get_address_of_UserId_0() { return &___UserId_0; }
	inline void set_UserId_0(String_t* value)
	{
		___UserId_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___UserId_0), (void*)value);
	}

	inline static int32_t get_offset_of_CustomData_1() { return static_cast<int32_t>(offsetof(ServerSideVerificationOptions_t2FF28841A6B538771FB349BCCD3517E399362E49, ___CustomData_1)); }
	inline String_t* get_CustomData_1() const { return ___CustomData_1; }
	inline String_t** get_address_of_CustomData_1() { return &___CustomData_1; }
	inline void set_CustomData_1(String_t* value)
	{
		___CustomData_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___CustomData_1), (void*)value);
	}
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.Text.StringBuilder
struct StringBuilder_t  : public RuntimeObject
{
public:
	// System.Char[] System.Text.StringBuilder::m_ChunkChars
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___m_ChunkChars_0;
	// System.Text.StringBuilder System.Text.StringBuilder::m_ChunkPrevious
	StringBuilder_t * ___m_ChunkPrevious_1;
	// System.Int32 System.Text.StringBuilder::m_ChunkLength
	int32_t ___m_ChunkLength_2;
	// System.Int32 System.Text.StringBuilder::m_ChunkOffset
	int32_t ___m_ChunkOffset_3;
	// System.Int32 System.Text.StringBuilder::m_MaxCapacity
	int32_t ___m_MaxCapacity_4;

public:
	inline static int32_t get_offset_of_m_ChunkChars_0() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkChars_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_m_ChunkChars_0() const { return ___m_ChunkChars_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_m_ChunkChars_0() { return &___m_ChunkChars_0; }
	inline void set_m_ChunkChars_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___m_ChunkChars_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_ChunkChars_0), (void*)value);
	}

	inline static int32_t get_offset_of_m_ChunkPrevious_1() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkPrevious_1)); }
	inline StringBuilder_t * get_m_ChunkPrevious_1() const { return ___m_ChunkPrevious_1; }
	inline StringBuilder_t ** get_address_of_m_ChunkPrevious_1() { return &___m_ChunkPrevious_1; }
	inline void set_m_ChunkPrevious_1(StringBuilder_t * value)
	{
		___m_ChunkPrevious_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_ChunkPrevious_1), (void*)value);
	}

	inline static int32_t get_offset_of_m_ChunkLength_2() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkLength_2)); }
	inline int32_t get_m_ChunkLength_2() const { return ___m_ChunkLength_2; }
	inline int32_t* get_address_of_m_ChunkLength_2() { return &___m_ChunkLength_2; }
	inline void set_m_ChunkLength_2(int32_t value)
	{
		___m_ChunkLength_2 = value;
	}

	inline static int32_t get_offset_of_m_ChunkOffset_3() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkOffset_3)); }
	inline int32_t get_m_ChunkOffset_3() const { return ___m_ChunkOffset_3; }
	inline int32_t* get_address_of_m_ChunkOffset_3() { return &___m_ChunkOffset_3; }
	inline void set_m_ChunkOffset_3(int32_t value)
	{
		___m_ChunkOffset_3 = value;
	}

	inline static int32_t get_offset_of_m_MaxCapacity_4() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_MaxCapacity_4)); }
	inline int32_t get_m_MaxCapacity_4() const { return ___m_MaxCapacity_4; }
	inline int32_t* get_address_of_m_MaxCapacity_4() { return &___m_MaxCapacity_4; }
	inline void set_m_MaxCapacity_4(int32_t value)
	{
		___m_MaxCapacity_4 = value;
	}
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// System.Version
struct Version_tBDAEDED25425A1D09910468B8BD1759115646E3C  : public RuntimeObject
{
public:
	// System.Int32 System.Version::_Major
	int32_t ____Major_0;
	// System.Int32 System.Version::_Minor
	int32_t ____Minor_1;
	// System.Int32 System.Version::_Build
	int32_t ____Build_2;
	// System.Int32 System.Version::_Revision
	int32_t ____Revision_3;

public:
	inline static int32_t get_offset_of__Major_0() { return static_cast<int32_t>(offsetof(Version_tBDAEDED25425A1D09910468B8BD1759115646E3C, ____Major_0)); }
	inline int32_t get__Major_0() const { return ____Major_0; }
	inline int32_t* get_address_of__Major_0() { return &____Major_0; }
	inline void set__Major_0(int32_t value)
	{
		____Major_0 = value;
	}

	inline static int32_t get_offset_of__Minor_1() { return static_cast<int32_t>(offsetof(Version_tBDAEDED25425A1D09910468B8BD1759115646E3C, ____Minor_1)); }
	inline int32_t get__Minor_1() const { return ____Minor_1; }
	inline int32_t* get_address_of__Minor_1() { return &____Minor_1; }
	inline void set__Minor_1(int32_t value)
	{
		____Minor_1 = value;
	}

	inline static int32_t get_offset_of__Build_2() { return static_cast<int32_t>(offsetof(Version_tBDAEDED25425A1D09910468B8BD1759115646E3C, ____Build_2)); }
	inline int32_t get__Build_2() const { return ____Build_2; }
	inline int32_t* get_address_of__Build_2() { return &____Build_2; }
	inline void set__Build_2(int32_t value)
	{
		____Build_2 = value;
	}

	inline static int32_t get_offset_of__Revision_3() { return static_cast<int32_t>(offsetof(Version_tBDAEDED25425A1D09910468B8BD1759115646E3C, ____Revision_3)); }
	inline int32_t get__Revision_3() const { return ____Revision_3; }
	inline int32_t* get_address_of__Revision_3() { return &____Revision_3; }
	inline void set__Revision_3(int32_t value)
	{
		____Revision_3 = value;
	}
};

struct Version_tBDAEDED25425A1D09910468B8BD1759115646E3C_StaticFields
{
public:
	// System.Char[] System.Version::SeparatorsArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___SeparatorsArray_4;

public:
	inline static int32_t get_offset_of_SeparatorsArray_4() { return static_cast<int32_t>(offsetof(Version_tBDAEDED25425A1D09910468B8BD1759115646E3C_StaticFields, ___SeparatorsArray_4)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_SeparatorsArray_4() const { return ___SeparatorsArray_4; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_SeparatorsArray_4() { return &___SeparatorsArray_4; }
	inline void set_SeparatorsArray_4(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___SeparatorsArray_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___SeparatorsArray_4), (void*)value);
	}
};


// System.Nullable`1<System.Boolean>
struct Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3 
{
public:
	// T System.Nullable`1::value
	bool ___value_0;
	// System.Boolean System.Nullable`1::has_value
	bool ___has_value_1;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3, ___value_0)); }
	inline bool get_value_0() const { return ___value_0; }
	inline bool* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(bool value)
	{
		___value_0 = value;
	}

	inline static int32_t get_offset_of_has_value_1() { return static_cast<int32_t>(offsetof(Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3, ___has_value_1)); }
	inline bool get_has_value_1() const { return ___has_value_1; }
	inline bool* get_address_of_has_value_1() { return &___has_value_1; }
	inline void set_has_value_1(bool value)
	{
		___has_value_1 = value;
	}
};


// GoogleMobileAds.Api.AdManager.AdManagerAdRequest
struct AdManagerAdRequest_t96CCD47947FF0CD69515505C8D3C25B0E5C67671  : public AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE
{
public:
	// System.String GoogleMobileAds.Api.AdManager.AdManagerAdRequest::PublisherProvidedId
	String_t* ___PublisherProvidedId_4;
	// System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.AdManager.AdManagerAdRequest::CustomTargeting
	Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * ___CustomTargeting_5;
	// System.Collections.Generic.HashSet`1<System.String> GoogleMobileAds.Api.AdManager.AdManagerAdRequest::CategoryExclusions
	HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * ___CategoryExclusions_6;

public:
	inline static int32_t get_offset_of_PublisherProvidedId_4() { return static_cast<int32_t>(offsetof(AdManagerAdRequest_t96CCD47947FF0CD69515505C8D3C25B0E5C67671, ___PublisherProvidedId_4)); }
	inline String_t* get_PublisherProvidedId_4() const { return ___PublisherProvidedId_4; }
	inline String_t** get_address_of_PublisherProvidedId_4() { return &___PublisherProvidedId_4; }
	inline void set_PublisherProvidedId_4(String_t* value)
	{
		___PublisherProvidedId_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___PublisherProvidedId_4), (void*)value);
	}

	inline static int32_t get_offset_of_CustomTargeting_5() { return static_cast<int32_t>(offsetof(AdManagerAdRequest_t96CCD47947FF0CD69515505C8D3C25B0E5C67671, ___CustomTargeting_5)); }
	inline Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * get_CustomTargeting_5() const { return ___CustomTargeting_5; }
	inline Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 ** get_address_of_CustomTargeting_5() { return &___CustomTargeting_5; }
	inline void set_CustomTargeting_5(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * value)
	{
		___CustomTargeting_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___CustomTargeting_5), (void*)value);
	}

	inline static int32_t get_offset_of_CategoryExclusions_6() { return static_cast<int32_t>(offsetof(AdManagerAdRequest_t96CCD47947FF0CD69515505C8D3C25B0E5C67671, ___CategoryExclusions_6)); }
	inline HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * get_CategoryExclusions_6() const { return ___CategoryExclusions_6; }
	inline HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 ** get_address_of_CategoryExclusions_6() { return &___CategoryExclusions_6; }
	inline void set_CategoryExclusions_6(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * value)
	{
		___CategoryExclusions_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___CategoryExclusions_6), (void*)value);
	}
};


// GoogleMobileAds.Api.AdValueEventArgs
struct AdValueEventArgs_t370093BE304A6D5E913DDA88544E062BE0B3D91E  : public EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA
{
public:
	// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.AdValueEventArgs::<AdValue>k__BackingField
	AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * ___U3CAdValueU3Ek__BackingField_1;

public:
	inline static int32_t get_offset_of_U3CAdValueU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(AdValueEventArgs_t370093BE304A6D5E913DDA88544E062BE0B3D91E, ___U3CAdValueU3Ek__BackingField_1)); }
	inline AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * get_U3CAdValueU3Ek__BackingField_1() const { return ___U3CAdValueU3Ek__BackingField_1; }
	inline AdValue_t806DD55342062227A4009E24834AE96A38A57F28 ** get_address_of_U3CAdValueU3Ek__BackingField_1() { return &___U3CAdValueU3Ek__BackingField_1; }
	inline void set_U3CAdValueU3Ek__BackingField_1(AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * value)
	{
		___U3CAdValueU3Ek__BackingField_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CAdValueU3Ek__BackingField_1), (void*)value);
	}
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// System.Int32
struct Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046 
{
public:
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046, ___m_value_0)); }
	inline int32_t get_m_value_0() const { return ___m_value_0; }
	inline int32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int32_t value)
	{
		___m_value_0 = value;
	}
};


// System.Int64
struct Int64_t378EE0D608BD3107E77238E85F30D2BBD46981F3 
{
public:
	// System.Int64 System.Int64::m_value
	int64_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int64_t378EE0D608BD3107E77238E85F30D2BBD46981F3, ___m_value_0)); }
	inline int64_t get_m_value_0() const { return ___m_value_0; }
	inline int64_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int64_t value)
	{
		___m_value_0 = value;
	}
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// GoogleMobileAds.Api.Reward
struct Reward_t53574C3E2B34B29EE3862B9EAE8B8334C0BC2CDB  : public EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA
{
public:
	// System.String GoogleMobileAds.Api.Reward::Type
	String_t* ___Type_1;
	// System.Double GoogleMobileAds.Api.Reward::Amount
	double ___Amount_2;

public:
	inline static int32_t get_offset_of_Type_1() { return static_cast<int32_t>(offsetof(Reward_t53574C3E2B34B29EE3862B9EAE8B8334C0BC2CDB, ___Type_1)); }
	inline String_t* get_Type_1() const { return ___Type_1; }
	inline String_t** get_address_of_Type_1() { return &___Type_1; }
	inline void set_Type_1(String_t* value)
	{
		___Type_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Type_1), (void*)value);
	}

	inline static int32_t get_offset_of_Amount_2() { return static_cast<int32_t>(offsetof(Reward_t53574C3E2B34B29EE3862B9EAE8B8334C0BC2CDB, ___Amount_2)); }
	inline double get_Amount_2() const { return ___Amount_2; }
	inline double* get_address_of_Amount_2() { return &___Amount_2; }
	inline void set_Amount_2(double value)
	{
		___Amount_2 = value;
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// GoogleMobileAds.Api.AdPosition
struct AdPosition_t6527C1E7E4E676E6DFA0A6823F828FAFEF0E4AED 
{
public:
	// System.Int32 GoogleMobileAds.Api.AdPosition::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(AdPosition_t6527C1E7E4E676E6DFA0A6823F828FAFEF0E4AED, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Api.AdapterState
struct AdapterState_tCFB90921284AD43DE4E80890DDD286CA1CB7BEDA 
{
public:
	// System.Int32 GoogleMobileAds.Api.AdapterState::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(AdapterState_tCFB90921284AD43DE4E80890DDD286CA1CB7BEDA, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Reflection.Assembly
struct Assembly_t  : public RuntimeObject
{
public:
	// System.IntPtr System.Reflection.Assembly::_mono_assembly
	intptr_t ____mono_assembly_0;
	// System.Reflection.Assembly/ResolveEventHolder System.Reflection.Assembly::resolve_event_holder
	ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C * ___resolve_event_holder_1;
	// System.Object System.Reflection.Assembly::_evidence
	RuntimeObject * ____evidence_2;
	// System.Object System.Reflection.Assembly::_minimum
	RuntimeObject * ____minimum_3;
	// System.Object System.Reflection.Assembly::_optional
	RuntimeObject * ____optional_4;
	// System.Object System.Reflection.Assembly::_refuse
	RuntimeObject * ____refuse_5;
	// System.Object System.Reflection.Assembly::_granted
	RuntimeObject * ____granted_6;
	// System.Object System.Reflection.Assembly::_denied
	RuntimeObject * ____denied_7;
	// System.Boolean System.Reflection.Assembly::fromByteArray
	bool ___fromByteArray_8;
	// System.String System.Reflection.Assembly::assemblyName
	String_t* ___assemblyName_9;

public:
	inline static int32_t get_offset_of__mono_assembly_0() { return static_cast<int32_t>(offsetof(Assembly_t, ____mono_assembly_0)); }
	inline intptr_t get__mono_assembly_0() const { return ____mono_assembly_0; }
	inline intptr_t* get_address_of__mono_assembly_0() { return &____mono_assembly_0; }
	inline void set__mono_assembly_0(intptr_t value)
	{
		____mono_assembly_0 = value;
	}

	inline static int32_t get_offset_of_resolve_event_holder_1() { return static_cast<int32_t>(offsetof(Assembly_t, ___resolve_event_holder_1)); }
	inline ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C * get_resolve_event_holder_1() const { return ___resolve_event_holder_1; }
	inline ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C ** get_address_of_resolve_event_holder_1() { return &___resolve_event_holder_1; }
	inline void set_resolve_event_holder_1(ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C * value)
	{
		___resolve_event_holder_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___resolve_event_holder_1), (void*)value);
	}

	inline static int32_t get_offset_of__evidence_2() { return static_cast<int32_t>(offsetof(Assembly_t, ____evidence_2)); }
	inline RuntimeObject * get__evidence_2() const { return ____evidence_2; }
	inline RuntimeObject ** get_address_of__evidence_2() { return &____evidence_2; }
	inline void set__evidence_2(RuntimeObject * value)
	{
		____evidence_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____evidence_2), (void*)value);
	}

	inline static int32_t get_offset_of__minimum_3() { return static_cast<int32_t>(offsetof(Assembly_t, ____minimum_3)); }
	inline RuntimeObject * get__minimum_3() const { return ____minimum_3; }
	inline RuntimeObject ** get_address_of__minimum_3() { return &____minimum_3; }
	inline void set__minimum_3(RuntimeObject * value)
	{
		____minimum_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____minimum_3), (void*)value);
	}

	inline static int32_t get_offset_of__optional_4() { return static_cast<int32_t>(offsetof(Assembly_t, ____optional_4)); }
	inline RuntimeObject * get__optional_4() const { return ____optional_4; }
	inline RuntimeObject ** get_address_of__optional_4() { return &____optional_4; }
	inline void set__optional_4(RuntimeObject * value)
	{
		____optional_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____optional_4), (void*)value);
	}

	inline static int32_t get_offset_of__refuse_5() { return static_cast<int32_t>(offsetof(Assembly_t, ____refuse_5)); }
	inline RuntimeObject * get__refuse_5() const { return ____refuse_5; }
	inline RuntimeObject ** get_address_of__refuse_5() { return &____refuse_5; }
	inline void set__refuse_5(RuntimeObject * value)
	{
		____refuse_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____refuse_5), (void*)value);
	}

	inline static int32_t get_offset_of__granted_6() { return static_cast<int32_t>(offsetof(Assembly_t, ____granted_6)); }
	inline RuntimeObject * get__granted_6() const { return ____granted_6; }
	inline RuntimeObject ** get_address_of__granted_6() { return &____granted_6; }
	inline void set__granted_6(RuntimeObject * value)
	{
		____granted_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____granted_6), (void*)value);
	}

	inline static int32_t get_offset_of__denied_7() { return static_cast<int32_t>(offsetof(Assembly_t, ____denied_7)); }
	inline RuntimeObject * get__denied_7() const { return ____denied_7; }
	inline RuntimeObject ** get_address_of__denied_7() { return &____denied_7; }
	inline void set__denied_7(RuntimeObject * value)
	{
		____denied_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____denied_7), (void*)value);
	}

	inline static int32_t get_offset_of_fromByteArray_8() { return static_cast<int32_t>(offsetof(Assembly_t, ___fromByteArray_8)); }
	inline bool get_fromByteArray_8() const { return ___fromByteArray_8; }
	inline bool* get_address_of_fromByteArray_8() { return &___fromByteArray_8; }
	inline void set_fromByteArray_8(bool value)
	{
		___fromByteArray_8 = value;
	}

	inline static int32_t get_offset_of_assemblyName_9() { return static_cast<int32_t>(offsetof(Assembly_t, ___assemblyName_9)); }
	inline String_t* get_assemblyName_9() const { return ___assemblyName_9; }
	inline String_t** get_address_of_assemblyName_9() { return &___assemblyName_9; }
	inline void set_assemblyName_9(String_t* value)
	{
		___assemblyName_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___assemblyName_9), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Reflection.Assembly
struct Assembly_t_marshaled_pinvoke
{
	intptr_t ____mono_assembly_0;
	ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C * ___resolve_event_holder_1;
	Il2CppIUnknown* ____evidence_2;
	Il2CppIUnknown* ____minimum_3;
	Il2CppIUnknown* ____optional_4;
	Il2CppIUnknown* ____refuse_5;
	Il2CppIUnknown* ____granted_6;
	Il2CppIUnknown* ____denied_7;
	int32_t ___fromByteArray_8;
	char* ___assemblyName_9;
};
// Native definition for COM marshalling of System.Reflection.Assembly
struct Assembly_t_marshaled_com
{
	intptr_t ____mono_assembly_0;
	ResolveEventHolder_tA37081FAEBE21D83D216225B4489BA8A37B4E13C * ___resolve_event_holder_1;
	Il2CppIUnknown* ____evidence_2;
	Il2CppIUnknown* ____minimum_3;
	Il2CppIUnknown* ____optional_4;
	Il2CppIUnknown* ____refuse_5;
	Il2CppIUnknown* ____granted_6;
	Il2CppIUnknown* ____denied_7;
	int32_t ___fromByteArray_8;
	Il2CppChar* ___assemblyName_9;
};

// System.Reflection.AssemblyContentType
struct AssemblyContentType_t3D610214A4025EDAEA27C569340C2AC5B0B828AE 
{
public:
	// System.Int32 System.Reflection.AssemblyContentType::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(AssemblyContentType_t3D610214A4025EDAEA27C569340C2AC5B0B828AE, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Configuration.Assemblies.AssemblyHashAlgorithm
struct AssemblyHashAlgorithm_tAC2C042FAE3F5BCF6BEFA05671C2BE09A85D6E66 
{
public:
	// System.Int32 System.Configuration.Assemblies.AssemblyHashAlgorithm::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(AssemblyHashAlgorithm_tAC2C042FAE3F5BCF6BEFA05671C2BE09A85D6E66, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Reflection.AssemblyNameFlags
struct AssemblyNameFlags_t18020151897CB7FD3FA390EE3999ECCA3FEA7622 
{
public:
	// System.Int32 System.Reflection.AssemblyNameFlags::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(AssemblyNameFlags_t18020151897CB7FD3FA390EE3999ECCA3FEA7622, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Configuration.Assemblies.AssemblyVersionCompatibility
struct AssemblyVersionCompatibility_t686857D4C42019A45D4309AB80A2517E3D34BEDD 
{
public:
	// System.Int32 System.Configuration.Assemblies.AssemblyVersionCompatibility::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(AssemblyVersionCompatibility_t686857D4C42019A45D4309AB80A2517E3D34BEDD, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Reflection.BindingFlags
struct BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733 
{
public:
	// System.Int32 System.Reflection.BindingFlags::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Api.Orientation
struct Orientation_t2752841DEAB0C4F1ACBCFA2D389BEA7C1358BB16 
{
public:
	// System.Int32 GoogleMobileAds.Api.Orientation::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(Orientation_t2752841DEAB0C4F1ACBCFA2D389BEA7C1358BB16, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Reflection.ProcessorArchitecture
struct ProcessorArchitecture_t80DDC787E34DBB9769E1CA90689FDB0131D60AAB 
{
public:
	// System.Int32 System.Reflection.ProcessorArchitecture::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(ProcessorArchitecture_t80DDC787E34DBB9769E1CA90689FDB0131D60AAB, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.RuntimeTypeHandle
struct RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 
{
public:
	// System.IntPtr System.RuntimeTypeHandle::value
	intptr_t ___value_0;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9, ___value_0)); }
	inline intptr_t get_value_0() const { return ___value_0; }
	inline intptr_t* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(intptr_t value)
	{
		___value_0 = value;
	}
};


// GoogleMobileAds.Api.TagForChildDirectedTreatment
struct TagForChildDirectedTreatment_tAFC7EDB1DD023934BCBB792B00E548C0CEAC1D2B 
{
public:
	// System.Int32 GoogleMobileAds.Api.TagForChildDirectedTreatment::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(TagForChildDirectedTreatment_tAFC7EDB1DD023934BCBB792B00E548C0CEAC1D2B, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Api.TagForUnderAgeOfConsent
struct TagForUnderAgeOfConsent_tCC8E7566E738DB77C28E74ABD6B6338885E0007C 
{
public:
	// System.Int32 GoogleMobileAds.Api.TagForUnderAgeOfConsent::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(TagForUnderAgeOfConsent_tCC8E7566E738DB77C28E74ABD6B6338885E0007C, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Api.AdSize/Type
struct Type_tB8BB3893F257F5E09C9A62E3C895096DD5A5356B 
{
public:
	// System.Int32 GoogleMobileAds.Api.AdSize/Type::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(Type_tB8BB3893F257F5E09C9A62E3C895096DD5A5356B, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Api.AdValue/PrecisionType
struct PrecisionType_t467BB0058BDA0CFAB1146C47CF04BCB7BE2F8A14 
{
public:
	// System.Int32 GoogleMobileAds.Api.AdValue/PrecisionType::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(PrecisionType_t467BB0058BDA0CFAB1146C47CF04BCB7BE2F8A14, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>
struct Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B 
{
public:
	// T System.Nullable`1::value
	int32_t ___value_0;
	// System.Boolean System.Nullable`1::has_value
	bool ___has_value_1;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B, ___value_0)); }
	inline int32_t get_value_0() const { return ___value_0; }
	inline int32_t* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(int32_t value)
	{
		___value_0 = value;
	}

	inline static int32_t get_offset_of_has_value_1() { return static_cast<int32_t>(offsetof(Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B, ___has_value_1)); }
	inline bool get_has_value_1() const { return ___has_value_1; }
	inline bool* get_address_of_has_value_1() { return &___has_value_1; }
	inline void set_has_value_1(bool value)
	{
		___has_value_1 = value;
	}
};


// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>
struct Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7 
{
public:
	// T System.Nullable`1::value
	int32_t ___value_0;
	// System.Boolean System.Nullable`1::has_value
	bool ___has_value_1;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7, ___value_0)); }
	inline int32_t get_value_0() const { return ___value_0; }
	inline int32_t* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(int32_t value)
	{
		___value_0 = value;
	}

	inline static int32_t get_offset_of_has_value_1() { return static_cast<int32_t>(offsetof(Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7, ___has_value_1)); }
	inline bool get_has_value_1() const { return ___has_value_1; }
	inline bool* get_address_of_has_value_1() { return &___has_value_1; }
	inline void set_has_value_1(bool value)
	{
		___has_value_1 = value;
	}
};


// GoogleMobileAds.Api.AdSize
struct AdSize_t03060093E09AF371C66F52156884405E04436209  : public RuntimeObject
{
public:
	// GoogleMobileAds.Api.AdSize/Type GoogleMobileAds.Api.AdSize::_type
	int32_t ____type_0;
	// GoogleMobileAds.Api.Orientation GoogleMobileAds.Api.AdSize::_orientation
	int32_t ____orientation_1;
	// System.Int32 GoogleMobileAds.Api.AdSize::_width
	int32_t ____width_2;
	// System.Int32 GoogleMobileAds.Api.AdSize::_height
	int32_t ____height_3;

public:
	inline static int32_t get_offset_of__type_0() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209, ____type_0)); }
	inline int32_t get__type_0() const { return ____type_0; }
	inline int32_t* get_address_of__type_0() { return &____type_0; }
	inline void set__type_0(int32_t value)
	{
		____type_0 = value;
	}

	inline static int32_t get_offset_of__orientation_1() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209, ____orientation_1)); }
	inline int32_t get__orientation_1() const { return ____orientation_1; }
	inline int32_t* get_address_of__orientation_1() { return &____orientation_1; }
	inline void set__orientation_1(int32_t value)
	{
		____orientation_1 = value;
	}

	inline static int32_t get_offset_of__width_2() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209, ____width_2)); }
	inline int32_t get__width_2() const { return ____width_2; }
	inline int32_t* get_address_of__width_2() { return &____width_2; }
	inline void set__width_2(int32_t value)
	{
		____width_2 = value;
	}

	inline static int32_t get_offset_of__height_3() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209, ____height_3)); }
	inline int32_t get__height_3() const { return ____height_3; }
	inline int32_t* get_address_of__height_3() { return &____height_3; }
	inline void set__height_3(int32_t value)
	{
		____height_3 = value;
	}
};

struct AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields
{
public:
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::Banner
	AdSize_t03060093E09AF371C66F52156884405E04436209 * ___Banner_4;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::MediumRectangle
	AdSize_t03060093E09AF371C66F52156884405E04436209 * ___MediumRectangle_5;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::IABBanner
	AdSize_t03060093E09AF371C66F52156884405E04436209 * ___IABBanner_6;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::Leaderboard
	AdSize_t03060093E09AF371C66F52156884405E04436209 * ___Leaderboard_7;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::SmartBanner
	AdSize_t03060093E09AF371C66F52156884405E04436209 * ___SmartBanner_8;
	// System.Int32 GoogleMobileAds.Api.AdSize::FullWidth
	int32_t ___FullWidth_9;

public:
	inline static int32_t get_offset_of_Banner_4() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields, ___Banner_4)); }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 * get_Banner_4() const { return ___Banner_4; }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 ** get_address_of_Banner_4() { return &___Banner_4; }
	inline void set_Banner_4(AdSize_t03060093E09AF371C66F52156884405E04436209 * value)
	{
		___Banner_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Banner_4), (void*)value);
	}

	inline static int32_t get_offset_of_MediumRectangle_5() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields, ___MediumRectangle_5)); }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 * get_MediumRectangle_5() const { return ___MediumRectangle_5; }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 ** get_address_of_MediumRectangle_5() { return &___MediumRectangle_5; }
	inline void set_MediumRectangle_5(AdSize_t03060093E09AF371C66F52156884405E04436209 * value)
	{
		___MediumRectangle_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___MediumRectangle_5), (void*)value);
	}

	inline static int32_t get_offset_of_IABBanner_6() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields, ___IABBanner_6)); }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 * get_IABBanner_6() const { return ___IABBanner_6; }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 ** get_address_of_IABBanner_6() { return &___IABBanner_6; }
	inline void set_IABBanner_6(AdSize_t03060093E09AF371C66F52156884405E04436209 * value)
	{
		___IABBanner_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___IABBanner_6), (void*)value);
	}

	inline static int32_t get_offset_of_Leaderboard_7() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields, ___Leaderboard_7)); }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 * get_Leaderboard_7() const { return ___Leaderboard_7; }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 ** get_address_of_Leaderboard_7() { return &___Leaderboard_7; }
	inline void set_Leaderboard_7(AdSize_t03060093E09AF371C66F52156884405E04436209 * value)
	{
		___Leaderboard_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Leaderboard_7), (void*)value);
	}

	inline static int32_t get_offset_of_SmartBanner_8() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields, ___SmartBanner_8)); }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 * get_SmartBanner_8() const { return ___SmartBanner_8; }
	inline AdSize_t03060093E09AF371C66F52156884405E04436209 ** get_address_of_SmartBanner_8() { return &___SmartBanner_8; }
	inline void set_SmartBanner_8(AdSize_t03060093E09AF371C66F52156884405E04436209 * value)
	{
		___SmartBanner_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___SmartBanner_8), (void*)value);
	}

	inline static int32_t get_offset_of_FullWidth_9() { return static_cast<int32_t>(offsetof(AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields, ___FullWidth_9)); }
	inline int32_t get_FullWidth_9() const { return ___FullWidth_9; }
	inline int32_t* get_address_of_FullWidth_9() { return &___FullWidth_9; }
	inline void set_FullWidth_9(int32_t value)
	{
		___FullWidth_9 = value;
	}
};


// GoogleMobileAds.Api.AdValue
struct AdValue_t806DD55342062227A4009E24834AE96A38A57F28  : public RuntimeObject
{
public:
	// GoogleMobileAds.Api.AdValue/PrecisionType GoogleMobileAds.Api.AdValue::<Precision>k__BackingField
	int32_t ___U3CPrecisionU3Ek__BackingField_0;
	// System.Int64 GoogleMobileAds.Api.AdValue::<Value>k__BackingField
	int64_t ___U3CValueU3Ek__BackingField_1;
	// System.String GoogleMobileAds.Api.AdValue::<CurrencyCode>k__BackingField
	String_t* ___U3CCurrencyCodeU3Ek__BackingField_2;

public:
	inline static int32_t get_offset_of_U3CPrecisionU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(AdValue_t806DD55342062227A4009E24834AE96A38A57F28, ___U3CPrecisionU3Ek__BackingField_0)); }
	inline int32_t get_U3CPrecisionU3Ek__BackingField_0() const { return ___U3CPrecisionU3Ek__BackingField_0; }
	inline int32_t* get_address_of_U3CPrecisionU3Ek__BackingField_0() { return &___U3CPrecisionU3Ek__BackingField_0; }
	inline void set_U3CPrecisionU3Ek__BackingField_0(int32_t value)
	{
		___U3CPrecisionU3Ek__BackingField_0 = value;
	}

	inline static int32_t get_offset_of_U3CValueU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(AdValue_t806DD55342062227A4009E24834AE96A38A57F28, ___U3CValueU3Ek__BackingField_1)); }
	inline int64_t get_U3CValueU3Ek__BackingField_1() const { return ___U3CValueU3Ek__BackingField_1; }
	inline int64_t* get_address_of_U3CValueU3Ek__BackingField_1() { return &___U3CValueU3Ek__BackingField_1; }
	inline void set_U3CValueU3Ek__BackingField_1(int64_t value)
	{
		___U3CValueU3Ek__BackingField_1 = value;
	}

	inline static int32_t get_offset_of_U3CCurrencyCodeU3Ek__BackingField_2() { return static_cast<int32_t>(offsetof(AdValue_t806DD55342062227A4009E24834AE96A38A57F28, ___U3CCurrencyCodeU3Ek__BackingField_2)); }
	inline String_t* get_U3CCurrencyCodeU3Ek__BackingField_2() const { return ___U3CCurrencyCodeU3Ek__BackingField_2; }
	inline String_t** get_address_of_U3CCurrencyCodeU3Ek__BackingField_2() { return &___U3CCurrencyCodeU3Ek__BackingField_2; }
	inline void set_U3CCurrencyCodeU3Ek__BackingField_2(String_t* value)
	{
		___U3CCurrencyCodeU3Ek__BackingField_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CCurrencyCodeU3Ek__BackingField_2), (void*)value);
	}
};


// GoogleMobileAds.Api.AdapterStatus
struct AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45  : public RuntimeObject
{
public:
	// GoogleMobileAds.Api.AdapterState GoogleMobileAds.Api.AdapterStatus::<InitializationState>k__BackingField
	int32_t ___U3CInitializationStateU3Ek__BackingField_0;
	// System.String GoogleMobileAds.Api.AdapterStatus::<Description>k__BackingField
	String_t* ___U3CDescriptionU3Ek__BackingField_1;
	// System.Int32 GoogleMobileAds.Api.AdapterStatus::<Latency>k__BackingField
	int32_t ___U3CLatencyU3Ek__BackingField_2;

public:
	inline static int32_t get_offset_of_U3CInitializationStateU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45, ___U3CInitializationStateU3Ek__BackingField_0)); }
	inline int32_t get_U3CInitializationStateU3Ek__BackingField_0() const { return ___U3CInitializationStateU3Ek__BackingField_0; }
	inline int32_t* get_address_of_U3CInitializationStateU3Ek__BackingField_0() { return &___U3CInitializationStateU3Ek__BackingField_0; }
	inline void set_U3CInitializationStateU3Ek__BackingField_0(int32_t value)
	{
		___U3CInitializationStateU3Ek__BackingField_0 = value;
	}

	inline static int32_t get_offset_of_U3CDescriptionU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45, ___U3CDescriptionU3Ek__BackingField_1)); }
	inline String_t* get_U3CDescriptionU3Ek__BackingField_1() const { return ___U3CDescriptionU3Ek__BackingField_1; }
	inline String_t** get_address_of_U3CDescriptionU3Ek__BackingField_1() { return &___U3CDescriptionU3Ek__BackingField_1; }
	inline void set_U3CDescriptionU3Ek__BackingField_1(String_t* value)
	{
		___U3CDescriptionU3Ek__BackingField_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CDescriptionU3Ek__BackingField_1), (void*)value);
	}

	inline static int32_t get_offset_of_U3CLatencyU3Ek__BackingField_2() { return static_cast<int32_t>(offsetof(AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45, ___U3CLatencyU3Ek__BackingField_2)); }
	inline int32_t get_U3CLatencyU3Ek__BackingField_2() const { return ___U3CLatencyU3Ek__BackingField_2; }
	inline int32_t* get_address_of_U3CLatencyU3Ek__BackingField_2() { return &___U3CLatencyU3Ek__BackingField_2; }
	inline void set_U3CLatencyU3Ek__BackingField_2(int32_t value)
	{
		___U3CLatencyU3Ek__BackingField_2 = value;
	}
};


// System.Reflection.AssemblyName
struct AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824  : public RuntimeObject
{
public:
	// System.String System.Reflection.AssemblyName::name
	String_t* ___name_0;
	// System.String System.Reflection.AssemblyName::codebase
	String_t* ___codebase_1;
	// System.Int32 System.Reflection.AssemblyName::major
	int32_t ___major_2;
	// System.Int32 System.Reflection.AssemblyName::minor
	int32_t ___minor_3;
	// System.Int32 System.Reflection.AssemblyName::build
	int32_t ___build_4;
	// System.Int32 System.Reflection.AssemblyName::revision
	int32_t ___revision_5;
	// System.Globalization.CultureInfo System.Reflection.AssemblyName::cultureinfo
	CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98 * ___cultureinfo_6;
	// System.Reflection.AssemblyNameFlags System.Reflection.AssemblyName::flags
	int32_t ___flags_7;
	// System.Configuration.Assemblies.AssemblyHashAlgorithm System.Reflection.AssemblyName::hashalg
	int32_t ___hashalg_8;
	// System.Reflection.StrongNameKeyPair System.Reflection.AssemblyName::keypair
	StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF * ___keypair_9;
	// System.Byte[] System.Reflection.AssemblyName::publicKey
	ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___publicKey_10;
	// System.Byte[] System.Reflection.AssemblyName::keyToken
	ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___keyToken_11;
	// System.Configuration.Assemblies.AssemblyVersionCompatibility System.Reflection.AssemblyName::versioncompat
	int32_t ___versioncompat_12;
	// System.Version System.Reflection.AssemblyName::version
	Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * ___version_13;
	// System.Reflection.ProcessorArchitecture System.Reflection.AssemblyName::processor_architecture
	int32_t ___processor_architecture_14;
	// System.Reflection.AssemblyContentType System.Reflection.AssemblyName::contentType
	int32_t ___contentType_15;

public:
	inline static int32_t get_offset_of_name_0() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___name_0)); }
	inline String_t* get_name_0() const { return ___name_0; }
	inline String_t** get_address_of_name_0() { return &___name_0; }
	inline void set_name_0(String_t* value)
	{
		___name_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___name_0), (void*)value);
	}

	inline static int32_t get_offset_of_codebase_1() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___codebase_1)); }
	inline String_t* get_codebase_1() const { return ___codebase_1; }
	inline String_t** get_address_of_codebase_1() { return &___codebase_1; }
	inline void set_codebase_1(String_t* value)
	{
		___codebase_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___codebase_1), (void*)value);
	}

	inline static int32_t get_offset_of_major_2() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___major_2)); }
	inline int32_t get_major_2() const { return ___major_2; }
	inline int32_t* get_address_of_major_2() { return &___major_2; }
	inline void set_major_2(int32_t value)
	{
		___major_2 = value;
	}

	inline static int32_t get_offset_of_minor_3() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___minor_3)); }
	inline int32_t get_minor_3() const { return ___minor_3; }
	inline int32_t* get_address_of_minor_3() { return &___minor_3; }
	inline void set_minor_3(int32_t value)
	{
		___minor_3 = value;
	}

	inline static int32_t get_offset_of_build_4() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___build_4)); }
	inline int32_t get_build_4() const { return ___build_4; }
	inline int32_t* get_address_of_build_4() { return &___build_4; }
	inline void set_build_4(int32_t value)
	{
		___build_4 = value;
	}

	inline static int32_t get_offset_of_revision_5() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___revision_5)); }
	inline int32_t get_revision_5() const { return ___revision_5; }
	inline int32_t* get_address_of_revision_5() { return &___revision_5; }
	inline void set_revision_5(int32_t value)
	{
		___revision_5 = value;
	}

	inline static int32_t get_offset_of_cultureinfo_6() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___cultureinfo_6)); }
	inline CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98 * get_cultureinfo_6() const { return ___cultureinfo_6; }
	inline CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98 ** get_address_of_cultureinfo_6() { return &___cultureinfo_6; }
	inline void set_cultureinfo_6(CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98 * value)
	{
		___cultureinfo_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___cultureinfo_6), (void*)value);
	}

	inline static int32_t get_offset_of_flags_7() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___flags_7)); }
	inline int32_t get_flags_7() const { return ___flags_7; }
	inline int32_t* get_address_of_flags_7() { return &___flags_7; }
	inline void set_flags_7(int32_t value)
	{
		___flags_7 = value;
	}

	inline static int32_t get_offset_of_hashalg_8() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___hashalg_8)); }
	inline int32_t get_hashalg_8() const { return ___hashalg_8; }
	inline int32_t* get_address_of_hashalg_8() { return &___hashalg_8; }
	inline void set_hashalg_8(int32_t value)
	{
		___hashalg_8 = value;
	}

	inline static int32_t get_offset_of_keypair_9() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___keypair_9)); }
	inline StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF * get_keypair_9() const { return ___keypair_9; }
	inline StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF ** get_address_of_keypair_9() { return &___keypair_9; }
	inline void set_keypair_9(StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF * value)
	{
		___keypair_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___keypair_9), (void*)value);
	}

	inline static int32_t get_offset_of_publicKey_10() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___publicKey_10)); }
	inline ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* get_publicKey_10() const { return ___publicKey_10; }
	inline ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726** get_address_of_publicKey_10() { return &___publicKey_10; }
	inline void set_publicKey_10(ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* value)
	{
		___publicKey_10 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___publicKey_10), (void*)value);
	}

	inline static int32_t get_offset_of_keyToken_11() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___keyToken_11)); }
	inline ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* get_keyToken_11() const { return ___keyToken_11; }
	inline ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726** get_address_of_keyToken_11() { return &___keyToken_11; }
	inline void set_keyToken_11(ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* value)
	{
		___keyToken_11 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___keyToken_11), (void*)value);
	}

	inline static int32_t get_offset_of_versioncompat_12() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___versioncompat_12)); }
	inline int32_t get_versioncompat_12() const { return ___versioncompat_12; }
	inline int32_t* get_address_of_versioncompat_12() { return &___versioncompat_12; }
	inline void set_versioncompat_12(int32_t value)
	{
		___versioncompat_12 = value;
	}

	inline static int32_t get_offset_of_version_13() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___version_13)); }
	inline Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * get_version_13() const { return ___version_13; }
	inline Version_tBDAEDED25425A1D09910468B8BD1759115646E3C ** get_address_of_version_13() { return &___version_13; }
	inline void set_version_13(Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * value)
	{
		___version_13 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___version_13), (void*)value);
	}

	inline static int32_t get_offset_of_processor_architecture_14() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___processor_architecture_14)); }
	inline int32_t get_processor_architecture_14() const { return ___processor_architecture_14; }
	inline int32_t* get_address_of_processor_architecture_14() { return &___processor_architecture_14; }
	inline void set_processor_architecture_14(int32_t value)
	{
		___processor_architecture_14 = value;
	}

	inline static int32_t get_offset_of_contentType_15() { return static_cast<int32_t>(offsetof(AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824, ___contentType_15)); }
	inline int32_t get_contentType_15() const { return ___contentType_15; }
	inline int32_t* get_address_of_contentType_15() { return &___contentType_15; }
	inline void set_contentType_15(int32_t value)
	{
		___contentType_15 = value;
	}
};

// Native definition for P/Invoke marshalling of System.Reflection.AssemblyName
struct AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshaled_pinvoke
{
	char* ___name_0;
	char* ___codebase_1;
	int32_t ___major_2;
	int32_t ___minor_3;
	int32_t ___build_4;
	int32_t ___revision_5;
	CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshaled_pinvoke* ___cultureinfo_6;
	int32_t ___flags_7;
	int32_t ___hashalg_8;
	StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF * ___keypair_9;
	Il2CppSafeArray/*NONE*/* ___publicKey_10;
	Il2CppSafeArray/*NONE*/* ___keyToken_11;
	int32_t ___versioncompat_12;
	Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * ___version_13;
	int32_t ___processor_architecture_14;
	int32_t ___contentType_15;
};
// Native definition for COM marshalling of System.Reflection.AssemblyName
struct AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshaled_com
{
	Il2CppChar* ___name_0;
	Il2CppChar* ___codebase_1;
	int32_t ___major_2;
	int32_t ___minor_3;
	int32_t ___build_4;
	int32_t ___revision_5;
	CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshaled_com* ___cultureinfo_6;
	int32_t ___flags_7;
	int32_t ___hashalg_8;
	StrongNameKeyPair_tCA4C0AB8B98C6C03134BC8AB17DD4C76D8091FDF * ___keypair_9;
	Il2CppSafeArray/*NONE*/* ___publicKey_10;
	Il2CppSafeArray/*NONE*/* ___keyToken_11;
	int32_t ___versioncompat_12;
	Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * ___version_13;
	int32_t ___processor_architecture_14;
	int32_t ___contentType_15;
};

// System.Type
struct Type_t  : public MemberInfo_t
{
public:
	// System.RuntimeTypeHandle System.Type::_impl
	RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  ____impl_9;

public:
	inline static int32_t get_offset_of__impl_9() { return static_cast<int32_t>(offsetof(Type_t, ____impl_9)); }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  get__impl_9() const { return ____impl_9; }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 * get_address_of__impl_9() { return &____impl_9; }
	inline void set__impl_9(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  value)
	{
		____impl_9 = value;
	}
};

struct Type_t_StaticFields
{
public:
	// System.Reflection.MemberFilter System.Type::FilterAttribute
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterAttribute_0;
	// System.Reflection.MemberFilter System.Type::FilterName
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterName_1;
	// System.Reflection.MemberFilter System.Type::FilterNameIgnoreCase
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterNameIgnoreCase_2;
	// System.Object System.Type::Missing
	RuntimeObject * ___Missing_3;
	// System.Char System.Type::Delimiter
	Il2CppChar ___Delimiter_4;
	// System.Type[] System.Type::EmptyTypes
	TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* ___EmptyTypes_5;
	// System.Reflection.Binder System.Type::defaultBinder
	Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * ___defaultBinder_6;

public:
	inline static int32_t get_offset_of_FilterAttribute_0() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterAttribute_0)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterAttribute_0() const { return ___FilterAttribute_0; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterAttribute_0() { return &___FilterAttribute_0; }
	inline void set_FilterAttribute_0(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterAttribute_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterAttribute_0), (void*)value);
	}

	inline static int32_t get_offset_of_FilterName_1() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterName_1)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterName_1() const { return ___FilterName_1; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterName_1() { return &___FilterName_1; }
	inline void set_FilterName_1(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterName_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterName_1), (void*)value);
	}

	inline static int32_t get_offset_of_FilterNameIgnoreCase_2() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterNameIgnoreCase_2)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterNameIgnoreCase_2() const { return ___FilterNameIgnoreCase_2; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterNameIgnoreCase_2() { return &___FilterNameIgnoreCase_2; }
	inline void set_FilterNameIgnoreCase_2(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterNameIgnoreCase_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterNameIgnoreCase_2), (void*)value);
	}

	inline static int32_t get_offset_of_Missing_3() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Missing_3)); }
	inline RuntimeObject * get_Missing_3() const { return ___Missing_3; }
	inline RuntimeObject ** get_address_of_Missing_3() { return &___Missing_3; }
	inline void set_Missing_3(RuntimeObject * value)
	{
		___Missing_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Missing_3), (void*)value);
	}

	inline static int32_t get_offset_of_Delimiter_4() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Delimiter_4)); }
	inline Il2CppChar get_Delimiter_4() const { return ___Delimiter_4; }
	inline Il2CppChar* get_address_of_Delimiter_4() { return &___Delimiter_4; }
	inline void set_Delimiter_4(Il2CppChar value)
	{
		___Delimiter_4 = value;
	}

	inline static int32_t get_offset_of_EmptyTypes_5() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___EmptyTypes_5)); }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* get_EmptyTypes_5() const { return ___EmptyTypes_5; }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755** get_address_of_EmptyTypes_5() { return &___EmptyTypes_5; }
	inline void set_EmptyTypes_5(TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* value)
	{
		___EmptyTypes_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___EmptyTypes_5), (void*)value);
	}

	inline static int32_t get_offset_of_defaultBinder_6() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___defaultBinder_6)); }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * get_defaultBinder_6() const { return ___defaultBinder_6; }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 ** get_address_of_defaultBinder_6() { return &___defaultBinder_6; }
	inline void set_defaultBinder_6(Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * value)
	{
		___defaultBinder_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___defaultBinder_6), (void*)value);
	}
};


// GoogleMobileAds.Api.RequestConfiguration
struct RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951  : public RuntimeObject
{
public:
	// GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.RequestConfiguration::MaxAdContentRating
	MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * ___MaxAdContentRating_0;
	// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment> GoogleMobileAds.Api.RequestConfiguration::TagForChildDirectedTreatment
	Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  ___TagForChildDirectedTreatment_1;
	// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent> GoogleMobileAds.Api.RequestConfiguration::TagForUnderAgeOfConsent
	Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  ___TagForUnderAgeOfConsent_2;
	// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Api.RequestConfiguration::TestDeviceIds
	List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___TestDeviceIds_3;
	// System.Nullable`1<System.Boolean> GoogleMobileAds.Api.RequestConfiguration::SameAppKeyEnabled
	Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  ___SameAppKeyEnabled_4;

public:
	inline static int32_t get_offset_of_MaxAdContentRating_0() { return static_cast<int32_t>(offsetof(RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951, ___MaxAdContentRating_0)); }
	inline MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * get_MaxAdContentRating_0() const { return ___MaxAdContentRating_0; }
	inline MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 ** get_address_of_MaxAdContentRating_0() { return &___MaxAdContentRating_0; }
	inline void set_MaxAdContentRating_0(MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * value)
	{
		___MaxAdContentRating_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___MaxAdContentRating_0), (void*)value);
	}

	inline static int32_t get_offset_of_TagForChildDirectedTreatment_1() { return static_cast<int32_t>(offsetof(RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951, ___TagForChildDirectedTreatment_1)); }
	inline Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  get_TagForChildDirectedTreatment_1() const { return ___TagForChildDirectedTreatment_1; }
	inline Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B * get_address_of_TagForChildDirectedTreatment_1() { return &___TagForChildDirectedTreatment_1; }
	inline void set_TagForChildDirectedTreatment_1(Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  value)
	{
		___TagForChildDirectedTreatment_1 = value;
	}

	inline static int32_t get_offset_of_TagForUnderAgeOfConsent_2() { return static_cast<int32_t>(offsetof(RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951, ___TagForUnderAgeOfConsent_2)); }
	inline Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  get_TagForUnderAgeOfConsent_2() const { return ___TagForUnderAgeOfConsent_2; }
	inline Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7 * get_address_of_TagForUnderAgeOfConsent_2() { return &___TagForUnderAgeOfConsent_2; }
	inline void set_TagForUnderAgeOfConsent_2(Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  value)
	{
		___TagForUnderAgeOfConsent_2 = value;
	}

	inline static int32_t get_offset_of_TestDeviceIds_3() { return static_cast<int32_t>(offsetof(RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951, ___TestDeviceIds_3)); }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * get_TestDeviceIds_3() const { return ___TestDeviceIds_3; }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 ** get_address_of_TestDeviceIds_3() { return &___TestDeviceIds_3; }
	inline void set_TestDeviceIds_3(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * value)
	{
		___TestDeviceIds_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TestDeviceIds_3), (void*)value);
	}

	inline static int32_t get_offset_of_SameAppKeyEnabled_4() { return static_cast<int32_t>(offsetof(RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951, ___SameAppKeyEnabled_4)); }
	inline Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  get_SameAppKeyEnabled_4() const { return ___SameAppKeyEnabled_4; }
	inline Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3 * get_address_of_SameAppKeyEnabled_4() { return &___SameAppKeyEnabled_4; }
	inline void set_SameAppKeyEnabled_4(Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  value)
	{
		___SameAppKeyEnabled_4 = value;
	}
};


// GoogleMobileAds.Api.RequestConfiguration/Builder
struct Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE  : public RuntimeObject
{
public:
	// GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.RequestConfiguration/Builder::<MaxAdContentRating>k__BackingField
	MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * ___U3CMaxAdContentRatingU3Ek__BackingField_0;
	// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment> GoogleMobileAds.Api.RequestConfiguration/Builder::<TagForChildDirectedTreatment>k__BackingField
	Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  ___U3CTagForChildDirectedTreatmentU3Ek__BackingField_1;
	// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent> GoogleMobileAds.Api.RequestConfiguration/Builder::<TagForUnderAgeOfConsent>k__BackingField
	Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  ___U3CTagForUnderAgeOfConsentU3Ek__BackingField_2;
	// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Api.RequestConfiguration/Builder::<TestDeviceIds>k__BackingField
	List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___U3CTestDeviceIdsU3Ek__BackingField_3;
	// System.Nullable`1<System.Boolean> GoogleMobileAds.Api.RequestConfiguration/Builder::<SameAppKeyEnabled>k__BackingField
	Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  ___U3CSameAppKeyEnabledU3Ek__BackingField_4;

public:
	inline static int32_t get_offset_of_U3CMaxAdContentRatingU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE, ___U3CMaxAdContentRatingU3Ek__BackingField_0)); }
	inline MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * get_U3CMaxAdContentRatingU3Ek__BackingField_0() const { return ___U3CMaxAdContentRatingU3Ek__BackingField_0; }
	inline MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 ** get_address_of_U3CMaxAdContentRatingU3Ek__BackingField_0() { return &___U3CMaxAdContentRatingU3Ek__BackingField_0; }
	inline void set_U3CMaxAdContentRatingU3Ek__BackingField_0(MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * value)
	{
		___U3CMaxAdContentRatingU3Ek__BackingField_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CMaxAdContentRatingU3Ek__BackingField_0), (void*)value);
	}

	inline static int32_t get_offset_of_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE, ___U3CTagForChildDirectedTreatmentU3Ek__BackingField_1)); }
	inline Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  get_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1() const { return ___U3CTagForChildDirectedTreatmentU3Ek__BackingField_1; }
	inline Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B * get_address_of_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1() { return &___U3CTagForChildDirectedTreatmentU3Ek__BackingField_1; }
	inline void set_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1(Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  value)
	{
		___U3CTagForChildDirectedTreatmentU3Ek__BackingField_1 = value;
	}

	inline static int32_t get_offset_of_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2() { return static_cast<int32_t>(offsetof(Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE, ___U3CTagForUnderAgeOfConsentU3Ek__BackingField_2)); }
	inline Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  get_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2() const { return ___U3CTagForUnderAgeOfConsentU3Ek__BackingField_2; }
	inline Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7 * get_address_of_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2() { return &___U3CTagForUnderAgeOfConsentU3Ek__BackingField_2; }
	inline void set_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2(Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  value)
	{
		___U3CTagForUnderAgeOfConsentU3Ek__BackingField_2 = value;
	}

	inline static int32_t get_offset_of_U3CTestDeviceIdsU3Ek__BackingField_3() { return static_cast<int32_t>(offsetof(Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE, ___U3CTestDeviceIdsU3Ek__BackingField_3)); }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * get_U3CTestDeviceIdsU3Ek__BackingField_3() const { return ___U3CTestDeviceIdsU3Ek__BackingField_3; }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 ** get_address_of_U3CTestDeviceIdsU3Ek__BackingField_3() { return &___U3CTestDeviceIdsU3Ek__BackingField_3; }
	inline void set_U3CTestDeviceIdsU3Ek__BackingField_3(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * value)
	{
		___U3CTestDeviceIdsU3Ek__BackingField_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CTestDeviceIdsU3Ek__BackingField_3), (void*)value);
	}

	inline static int32_t get_offset_of_U3CSameAppKeyEnabledU3Ek__BackingField_4() { return static_cast<int32_t>(offsetof(Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE, ___U3CSameAppKeyEnabledU3Ek__BackingField_4)); }
	inline Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  get_U3CSameAppKeyEnabledU3Ek__BackingField_4() const { return ___U3CSameAppKeyEnabledU3Ek__BackingField_4; }
	inline Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3 * get_address_of_U3CSameAppKeyEnabledU3Ek__BackingField_4() { return &___U3CSameAppKeyEnabledU3Ek__BackingField_4; }
	inline void set_U3CSameAppKeyEnabledU3Ek__BackingField_4(Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  value)
	{
		___U3CSameAppKeyEnabledU3Ek__BackingField_4 = value;
	}
};

#ifdef __clang__
#pragma clang diagnostic pop
#endif


// System.Void System.Collections.Generic.Dictionary`2<System.Object,System.Object>::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m2C8EE5C13636D67F6C451C4935049F534AEC658F_gshared (Dictionary_2_tBD1E3221EBD04CEBDA49B84779912E91F56B958D * __this, const RuntimeMethod* method);
// System.Void System.Collections.Generic.HashSet`1<System.Object>::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HashSet_1__ctor_m2CDA40DEC2900A9CB00F8348FF386DF44ABD0EC7_gshared (HashSet_1_t680119C7ED8D82AED56CDB83DF6F0E9149852A9B * __this, const RuntimeMethod* method);
// System.Void System.Collections.Generic.List`1<System.Object>::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m0F0E00088CF56FEACC9E32D8B7D91B93D91DAA3B_gshared (List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * __this, const RuntimeMethod* method);

// System.Void System.Collections.Generic.Dictionary`2<System.String,System.String>::.ctor()
inline void Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666 (Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 *, const RuntimeMethod*))Dictionary_2__ctor_m2C8EE5C13636D67F6C451C4935049F534AEC658F_gshared)(__this, method);
}
// System.Void System.Collections.Generic.HashSet`1<System.String>::.ctor()
inline void HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818 (HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * __this, const RuntimeMethod* method)
{
	((  void (*) (HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 *, const RuntimeMethod*))HashSet_1__ctor_m2CDA40DEC2900A9CB00F8348FF386DF44ABD0EC7_gshared)(__this, method);
}
// System.Void GoogleMobileAds.Api.AdRequest::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdRequest__ctor_mFA778383D6EE57A62A19BE2F446F898CB178B7E8 (AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE * __this, const RuntimeMethod* method);
// System.Type System.Type::GetTypeFromHandle(System.RuntimeTypeHandle)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t * Type_GetTypeFromHandle_m8BB57524FF7F9DB1803BC561D2B3A4DBACEB385E (RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  ___handle0, const RuntimeMethod* method);
// System.Version System.Reflection.AssemblyName::get_Version()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * AssemblyName_get_Version_m1E5978822709B7B59BEB504A8BC567823766497D_inline (AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824 * __this, const RuntimeMethod* method);
// System.Int32 System.Version::get_Major()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Version_get_Major_mBDD414863C4A05FADE87F8C39C8CE8ED6DE6C460_inline (Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * __this, const RuntimeMethod* method);
// System.Int32 System.Version::get_Minor()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Version_get_Minor_m8FCC5D46616E2E54B213EDF31CF3EB57EC998BCE_inline (Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * __this, const RuntimeMethod* method);
// System.Int32 System.Version::get_Build()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Version_get_Build_mF4D316F7F919B539F41467DD4A91839E42456584_inline (Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * __this, const RuntimeMethod* method);
// System.String System.String::Format(System.String,System.Object,System.Object,System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_m039737CCD992C5BFC8D16DFD681F5E8786E87FA6 (String_t* ___format0, RuntimeObject * ___arg01, RuntimeObject * ___arg12, RuntimeObject * ___arg23, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.AdRequest::set_Version(System.String)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdRequest_set_Version_m938C94CD619E6DAB2EAA5860BD8EE98A802DF82F_inline (String_t* ___value0, const RuntimeMethod* method);
// System.Void System.Collections.Generic.List`1<GoogleMobileAds.Api.Mediation.MediationExtras>::.ctor()
inline void List_1__ctor_m83D00E76233F0160C86CF5C7D15286CD14A44A64 (List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A * __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A *, const RuntimeMethod*))List_1__ctor_m0F0E00088CF56FEACC9E32D8B7D91B93D91DAA3B_gshared)(__this, method);
}
// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405 (RuntimeObject * __this, const RuntimeMethod* method);
// System.Void System.Text.StringBuilder::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder__ctor_m9305A36F9CF53EDD80D132428999934C68904C77 (StringBuilder_t * __this, String_t* ___value0, const RuntimeMethod* method);
// System.String GoogleMobileAds.Api.AdRequest::get_Version()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AdRequest_get_Version_m0140F2D9E5FE5EB8A076CD376660C34DB55C10F9_inline (const RuntimeMethod* method);
// System.Text.StringBuilder System.Text.StringBuilder::Append(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t * StringBuilder_Append_mD02AB0C74C6F55E3E330818C77EC147E22096FB1 (StringBuilder_t * __this, String_t* ___value0, const RuntimeMethod* method);
// System.Boolean System.String::IsNullOrEmpty(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_IsNullOrEmpty_m9AFBB5335B441B94E884B8A9D4A27AD60E3D7F7C (String_t* ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, int32_t ___width0, int32_t ___height1, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32,GoogleMobileAds.Api.AdSize/Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdSize__ctor_m0D17F8059B3EF0FC19B2E9AE90E06857D2880792 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, int32_t ___width0, int32_t ___height1, int32_t ___type2, const RuntimeMethod* method);
// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::CreateAnchoredAdaptiveAdSize(System.Int32,GoogleMobileAds.Api.Orientation)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AdSize_t03060093E09AF371C66F52156884405E04436209 * AdSize_CreateAnchoredAdaptiveAdSize_mB4718A1E214DA7FBF8806046D159F60C876868C6 (int32_t ___width0, int32_t ___orientation1, const RuntimeMethod* method);
// System.Type System.Object::GetType()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t * Object_GetType_m571FE8360C10B98C23AAF1F066D92C08CC94F45B (RuntimeObject * __this, const RuntimeMethod* method);
// System.Int32 System.Int32::GetHashCode()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Int32_GetHashCode_mEDD3F492A5F7CF021125AE3F38E2B8F8743FC667 (int32_t* __this, const RuntimeMethod* method);
// System.Void System.EventArgs::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventArgs__ctor_m5ECB9A8ED0A9E2DBB1ED999BAC1CB44F4354E571 (EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.AdapterStatus::set_InitializationState(GoogleMobileAds.Api.AdapterState)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdapterStatus_set_InitializationState_m8D23EB6FAA5DC25A033006B82E4640E5E0FCF4DE_inline (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.AdapterStatus::set_Description(System.String)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdapterStatus_set_Description_m12EECD98F5BE85D58D7C1632F6D80FEF1A3BA733_inline (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, String_t* ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.AdapterStatus::set_Latency(System.Int32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdapterStatus_set_Latency_m6AFDC90CA78BE3A71352878AD5B16B86C9290A2B_inline (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.MaxAdContentRating::set_Value(System.String)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MaxAdContentRating_set_Value_m87B495BE0B1BB9D04CE8B144A4D99A2A5ADD9646_inline (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * __this, String_t* ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.MaxAdContentRating::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaxAdContentRating__ctor_m61B9B7976E688768408525833B8F0416837E72BB (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * __this, String_t* ___value0, const RuntimeMethod* method);
// System.Void System.Collections.Generic.List`1<System.String>::.ctor()
inline void List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9 (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *, const RuntimeMethod*))List_1__ctor_m0F0E00088CF56FEACC9E32D8B7D91B93D91DAA3B_gshared)(__this, method);
}
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_MaxAdContentRating(GoogleMobileAds.Api.MaxAdContentRating)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TagForChildDirectedTreatment(System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TagForUnderAgeOfConsent(System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TestDeviceIds(System.Collections.Generic.List`1<System.String>)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_SameAppKeyEnabled(System.Nullable`1<System.Boolean>)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_SameAppKeyEnabled_m4F22919DC10A3690CB48160BF601A27DEE973C1A_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.RequestConfiguration::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RequestConfiguration__ctor_mB474168D61CAEE374CA2497A21DD2FF1FB337E99 (RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * __this, const RuntimeMethod* method);
// GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.RequestConfiguration/Builder::get_MaxAdContentRating()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * Builder_get_MaxAdContentRating_m9A3592507C10CC11286B697201CC6465ABC79671_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method);
// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TagForChildDirectedTreatment()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  Builder_get_TagForChildDirectedTreatment_mCD38E6703E8325EB13CE37474C0A347AC206FDCF_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method);
// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TagForUnderAgeOfConsent()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  Builder_get_TagForUnderAgeOfConsent_m44813E5169846F8DB5D7C2D840C828DB225B60CF_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method);
// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TestDeviceIds()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * Builder_get_TestDeviceIds_m4963727F9F2E889F7E0B5C8CFCDB5256C1F456AE_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method);
// System.Nullable`1<System.Boolean> GoogleMobileAds.Api.RequestConfiguration/Builder::get_SameAppKeyEnabled()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  Builder_get_SameAppKeyEnabled_m6B799BA968F08CA2F07E3B1F6375EFFD2C7F17C1_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method);
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdManager.AdManagerAdRequest::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdManagerAdRequest__ctor_m34E1A34DBDE358F53E6B6535773D61C2C2E99EA6 (AdManagerAdRequest_t96CCD47947FF0CD69515505C8D3C25B0E5C67671 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * L_0 = (Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 *)il2cpp_codegen_object_new(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666(L_0, /*hidden argument*/Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666_RuntimeMethod_var);
		__this->set_CustomTargeting_5(L_0);
		HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * L_1 = (HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 *)il2cpp_codegen_object_new(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229_il2cpp_TypeInfo_var);
		HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818(L_1, /*hidden argument*/HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818_RuntimeMethod_var);
		__this->set_CategoryExclusions_6(L_1);
		IL2CPP_RUNTIME_CLASS_INIT(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		AdRequest__ctor_mFA778383D6EE57A62A19BE2F446F898CB178B7E8(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdRequest::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdRequest__cctor_mB567C9044ECBDFE38B504F9C4960B6E6995510D0 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8244FF4469CD24AECA8F7B1B293CA6DF261B2861);
		s_Il2CppMethodInitialized = true;
	}
	Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * V_0 = NULL;
	{
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_0 = { reinterpret_cast<intptr_t> (AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_1;
		L_1 = Type_GetTypeFromHandle_m8BB57524FF7F9DB1803BC561D2B3A4DBACEB385E(L_0, /*hidden argument*/NULL);
		NullCheck(L_1);
		Assembly_t * L_2;
		L_2 = VirtFuncInvoker0< Assembly_t * >::Invoke(23 /* System.Reflection.Assembly System.Type::get_Assembly() */, L_1);
		NullCheck(L_2);
		AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824 * L_3;
		L_3 = VirtFuncInvoker0< AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824 * >::Invoke(16 /* System.Reflection.AssemblyName System.Reflection.Assembly::GetName() */, L_2);
		NullCheck(L_3);
		Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * L_4;
		L_4 = AssemblyName_get_Version_m1E5978822709B7B59BEB504A8BC567823766497D_inline(L_3, /*hidden argument*/NULL);
		V_0 = L_4;
		Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * L_5 = V_0;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = Version_get_Major_mBDD414863C4A05FADE87F8C39C8CE8ED6DE6C460_inline(L_5, /*hidden argument*/NULL);
		int32_t L_7 = L_6;
		RuntimeObject * L_8 = Box(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var, &L_7);
		Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * L_9 = V_0;
		NullCheck(L_9);
		int32_t L_10;
		L_10 = Version_get_Minor_m8FCC5D46616E2E54B213EDF31CF3EB57EC998BCE_inline(L_9, /*hidden argument*/NULL);
		int32_t L_11 = L_10;
		RuntimeObject * L_12 = Box(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var, &L_11);
		Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * L_13 = V_0;
		NullCheck(L_13);
		int32_t L_14;
		L_14 = Version_get_Build_mF4D316F7F919B539F41467DD4A91839E42456584_inline(L_13, /*hidden argument*/NULL);
		int32_t L_15 = L_14;
		RuntimeObject * L_16 = Box(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var, &L_15);
		String_t* L_17;
		L_17 = String_Format_m039737CCD992C5BFC8D16DFD681F5E8786E87FA6(_stringLiteral8244FF4469CD24AECA8F7B1B293CA6DF261B2861, L_8, L_12, L_16, /*hidden argument*/NULL);
		AdRequest_set_Version_m938C94CD619E6DAB2EAA5860BD8EE98A802DF82F_inline(L_17, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdRequest::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdRequest__ctor_mFA778383D6EE57A62A19BE2F446F898CB178B7E8 (AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m83D00E76233F0160C86CF5C7D15286CD14A44A64_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 * L_0 = (HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229 *)il2cpp_codegen_object_new(HashSet_1_t45F75268054D01D9E70EB33D7F6D2FA609DB9229_il2cpp_TypeInfo_var);
		HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818(L_0, /*hidden argument*/HashSet_1__ctor_mCC4A4964EEA7915C5CABFACB64E6A9AD82700818_RuntimeMethod_var);
		__this->set_Keywords_1(L_0);
		Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * L_1 = (Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 *)il2cpp_codegen_object_new(Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666(L_1, /*hidden argument*/Dictionary_2__ctor_mA6747E78BD4DF1D09D9091C1B3EBAE0FDB200666_RuntimeMethod_var);
		__this->set_Extras_2(L_1);
		List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A * L_2 = (List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A *)il2cpp_codegen_object_new(List_1_t3BE2E70C31BF7B6E3FE9457A64789A9D94AE308A_il2cpp_TypeInfo_var);
		List_1__ctor_m83D00E76233F0160C86CF5C7D15286CD14A44A64(L_2, /*hidden argument*/List_1__ctor_m83D00E76233F0160C86CF5C7D15286CD14A44A64_RuntimeMethod_var);
		__this->set_MediationExtras_3(L_2);
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.String GoogleMobileAds.Api.AdRequest::get_Version()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AdRequest_get_Version_m0140F2D9E5FE5EB8A076CD376660C34DB55C10F9 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		String_t* L_0 = ((AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_StaticFields*)il2cpp_codegen_static_fields_for(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var))->get_U3CVersionU3Ek__BackingField_0();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.AdRequest::set_Version(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdRequest_set_Version_m938C94CD619E6DAB2EAA5860BD8EE98A802DF82F (String_t* ___value0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___value0;
		IL2CPP_RUNTIME_CLASS_INIT(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		((AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_StaticFields*)il2cpp_codegen_static_fields_for(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var))->set_U3CVersionU3Ek__BackingField_0(L_0);
		return;
	}
}
// System.String GoogleMobileAds.Api.AdRequest::BuildVersionString(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AdRequest_BuildVersionString_m23168963AC9A833D3CD7F43AEBB20D638DDEDD09 (String_t* ___nativePluginVersion0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5F66A1EC0B47E429CB0A3F7C531F0674F96F49F3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB2501285DA6895C43F81A426A8C28EF7BA40D44E);
		s_Il2CppMethodInitialized = true;
	}
	StringBuilder_t * V_0 = NULL;
	{
		StringBuilder_t * L_0 = (StringBuilder_t *)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m9305A36F9CF53EDD80D132428999934C68904C77(L_0, _stringLiteralB2501285DA6895C43F81A426A8C28EF7BA40D44E, /*hidden argument*/NULL);
		V_0 = L_0;
		StringBuilder_t * L_1 = V_0;
		IL2CPP_RUNTIME_CLASS_INIT(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		String_t* L_2;
		L_2 = AdRequest_get_Version_m0140F2D9E5FE5EB8A076CD376660C34DB55C10F9_inline(/*hidden argument*/NULL);
		NullCheck(L_1);
		StringBuilder_t * L_3;
		L_3 = StringBuilder_Append_mD02AB0C74C6F55E3E330818C77EC147E22096FB1(L_1, L_2, /*hidden argument*/NULL);
		String_t* L_4 = ___nativePluginVersion0;
		bool L_5;
		L_5 = String_IsNullOrEmpty_m9AFBB5335B441B94E884B8A9D4A27AD60E3D7F7C(L_4, /*hidden argument*/NULL);
		if (L_5)
		{
			goto IL_0036;
		}
	}
	{
		StringBuilder_t * L_6 = V_0;
		NullCheck(L_6);
		StringBuilder_t * L_7;
		L_7 = StringBuilder_Append_mD02AB0C74C6F55E3E330818C77EC147E22096FB1(L_6, _stringLiteral5F66A1EC0B47E429CB0A3F7C531F0674F96F49F3, /*hidden argument*/NULL);
		StringBuilder_t * L_8 = V_0;
		String_t* L_9 = ___nativePluginVersion0;
		NullCheck(L_8);
		StringBuilder_t * L_10;
		L_10 = StringBuilder_Append_mD02AB0C74C6F55E3E330818C77EC147E22096FB1(L_8, L_9, /*hidden argument*/NULL);
	}

IL_0036:
	{
		StringBuilder_t * L_11 = V_0;
		NullCheck(L_11);
		String_t* L_12;
		L_12 = VirtFuncInvoker0< String_t* >::Invoke(3 /* System.String System.Object::ToString() */, L_11);
		return L_12;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, int32_t ___width0, int32_t ___height1, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		__this->set__type_0(0);
		int32_t L_0 = ___width0;
		__this->set__width_2(L_0);
		int32_t L_1 = ___height1;
		__this->set__height_3(L_1);
		__this->set__orientation_1(0);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32,GoogleMobileAds.Api.AdSize/Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdSize__ctor_m0D17F8059B3EF0FC19B2E9AE90E06857D2880792 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, int32_t ___width0, int32_t ___height1, int32_t ___type2, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___width0;
		int32_t L_1 = ___height1;
		AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929(__this, L_0, L_1, /*hidden argument*/NULL);
		int32_t L_2 = ___type2;
		__this->set__type_0(L_2);
		return;
	}
}
// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::CreateAnchoredAdaptiveAdSize(System.Int32,GoogleMobileAds.Api.Orientation)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AdSize_t03060093E09AF371C66F52156884405E04436209 * AdSize_CreateAnchoredAdaptiveAdSize_mB4718A1E214DA7FBF8806046D159F60C876868C6 (int32_t ___width0, int32_t ___orientation1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AdSize_t03060093E09AF371C66F52156884405E04436209 * V_0 = NULL;
	{
		int32_t L_0 = ___width0;
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_1 = (AdSize_t03060093E09AF371C66F52156884405E04436209 *)il2cpp_codegen_object_new(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize__ctor_m0D17F8059B3EF0FC19B2E9AE90E06857D2880792(L_1, L_0, 0, 2, /*hidden argument*/NULL);
		V_0 = L_1;
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_2 = V_0;
		int32_t L_3 = ___orientation1;
		NullCheck(L_2);
		L_2->set__orientation_1(L_3);
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_4 = V_0;
		return L_4;
	}
}
// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AdSize_t03060093E09AF371C66F52156884405E04436209 * AdSize_GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth_m397FAC78A9C9E87A5D8808145567068138BF3FFE (int32_t ___width0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___width0;
		IL2CPP_RUNTIME_CLASS_INIT(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_1;
		L_1 = AdSize_CreateAnchoredAdaptiveAdSize_mB4718A1E214DA7FBF8806046D159F60C876868C6(L_0, 0, /*hidden argument*/NULL);
		return L_1;
	}
}
// System.Int32 GoogleMobileAds.Api.AdSize::get_Width()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AdSize_get_Width_m3BEB8032410BE3D663C957BFCF01E6A59F208B31 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__width_2();
		return L_0;
	}
}
// System.Int32 GoogleMobileAds.Api.AdSize::get_Height()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AdSize_get_Height_m5D9A50F40AE5D1ADE624BC788141DB38BD999455 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__height_3();
		return L_0;
	}
}
// GoogleMobileAds.Api.AdSize/Type GoogleMobileAds.Api.AdSize::get_AdType()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AdSize_get_AdType_m1B1093C98B765BA1EB2E238CD241366915D33967 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__type_0();
		return L_0;
	}
}
// GoogleMobileAds.Api.Orientation GoogleMobileAds.Api.AdSize::get_Orientation()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AdSize_get_Orientation_m128E54BB23D60EC047E3ABCCF14039599A901A18 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__orientation_1();
		return L_0;
	}
}
// System.Boolean GoogleMobileAds.Api.AdSize::Equals(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AdSize_Equals_mEDEC9BFF9CB8BAA2BFD25A9A8151894B9E34AC51 (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, RuntimeObject * ___obj0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AdSize_t03060093E09AF371C66F52156884405E04436209 * V_0 = NULL;
	int32_t G_B8_0 = 0;
	{
		RuntimeObject * L_0 = ___obj0;
		if (!L_0)
		{
			goto IL_0017;
		}
	}
	{
		Type_t * L_1;
		L_1 = Object_GetType_m571FE8360C10B98C23AAF1F066D92C08CC94F45B(__this, /*hidden argument*/NULL);
		RuntimeObject * L_2 = ___obj0;
		NullCheck(L_2);
		Type_t * L_3;
		L_3 = Object_GetType_m571FE8360C10B98C23AAF1F066D92C08CC94F45B(L_2, /*hidden argument*/NULL);
		if ((((RuntimeObject*)(Type_t *)L_1) == ((RuntimeObject*)(Type_t *)L_3)))
		{
			goto IL_0019;
		}
	}

IL_0017:
	{
		return (bool)0;
	}

IL_0019:
	{
		RuntimeObject * L_4 = ___obj0;
		V_0 = ((AdSize_t03060093E09AF371C66F52156884405E04436209 *)CastclassClass((RuntimeObject*)L_4, AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var));
		int32_t L_5 = __this->get__width_2();
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_6 = V_0;
		NullCheck(L_6);
		int32_t L_7 = L_6->get__width_2();
		if ((!(((uint32_t)L_5) == ((uint32_t)L_7))))
		{
			goto IL_0063;
		}
	}
	{
		int32_t L_8 = __this->get__height_3();
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_9 = V_0;
		NullCheck(L_9);
		int32_t L_10 = L_9->get__height_3();
		if ((!(((uint32_t)L_8) == ((uint32_t)L_10))))
		{
			goto IL_0063;
		}
	}
	{
		int32_t L_11 = __this->get__type_0();
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_12 = V_0;
		NullCheck(L_12);
		int32_t L_13 = L_12->get__type_0();
		if ((!(((uint32_t)L_11) == ((uint32_t)L_13))))
		{
			goto IL_0063;
		}
	}
	{
		int32_t L_14 = __this->get__orientation_1();
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_15 = V_0;
		NullCheck(L_15);
		int32_t L_16 = L_15->get__orientation_1();
		G_B8_0 = ((((int32_t)L_14) == ((int32_t)L_16))? 1 : 0);
		goto IL_0064;
	}

IL_0063:
	{
		G_B8_0 = 0;
	}

IL_0064:
	{
		return (bool)G_B8_0;
	}
}
// System.Int32 GoogleMobileAds.Api.AdSize::GetHashCode()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AdSize_GetHashCode_m69600B06087C9871F8B2596DA604B592BCB1283E (AdSize_t03060093E09AF371C66F52156884405E04436209 * __this, const RuntimeMethod* method)
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	{
		V_0 = ((int32_t)71);
		V_1 = ((int32_t)11);
		int32_t L_0 = V_0;
		V_2 = L_0;
		int32_t L_1 = V_2;
		int32_t L_2 = V_1;
		int32_t* L_3 = __this->get_address_of__width_2();
		int32_t L_4;
		L_4 = Int32_GetHashCode_mEDD3F492A5F7CF021125AE3F38E2B8F8743FC667((int32_t*)L_3, /*hidden argument*/NULL);
		V_2 = ((int32_t)((int32_t)((int32_t)il2cpp_codegen_multiply((int32_t)L_1, (int32_t)L_2))^(int32_t)L_4));
		int32_t L_5 = V_2;
		int32_t L_6 = V_1;
		int32_t* L_7 = __this->get_address_of__height_3();
		int32_t L_8;
		L_8 = Int32_GetHashCode_mEDD3F492A5F7CF021125AE3F38E2B8F8743FC667((int32_t*)L_7, /*hidden argument*/NULL);
		V_2 = ((int32_t)((int32_t)((int32_t)il2cpp_codegen_multiply((int32_t)L_5, (int32_t)L_6))^(int32_t)L_8));
		int32_t L_9 = V_2;
		int32_t L_10 = V_1;
		int32_t* L_11 = __this->get_address_of__type_0();
		int32_t L_12;
		L_12 = Int32_GetHashCode_mEDD3F492A5F7CF021125AE3F38E2B8F8743FC667((int32_t*)L_11, /*hidden argument*/NULL);
		V_2 = ((int32_t)((int32_t)((int32_t)il2cpp_codegen_multiply((int32_t)L_9, (int32_t)L_10))^(int32_t)L_12));
		int32_t L_13 = V_2;
		int32_t L_14 = V_1;
		int32_t* L_15 = __this->get_address_of__orientation_1();
		int32_t L_16;
		L_16 = Int32_GetHashCode_mEDD3F492A5F7CF021125AE3F38E2B8F8743FC667((int32_t*)L_15, /*hidden argument*/NULL);
		V_2 = ((int32_t)((int32_t)((int32_t)il2cpp_codegen_multiply((int32_t)L_13, (int32_t)L_14))^(int32_t)L_16));
		int32_t L_17 = V_2;
		return L_17;
	}
}
// System.Void GoogleMobileAds.Api.AdSize::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdSize__cctor_mCF933C938E0761B4033174E15EB117B05D813C3C (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_0 = (AdSize_t03060093E09AF371C66F52156884405E04436209 *)il2cpp_codegen_object_new(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929(L_0, ((int32_t)320), ((int32_t)50), /*hidden argument*/NULL);
		((AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields*)il2cpp_codegen_static_fields_for(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var))->set_Banner_4(L_0);
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_1 = (AdSize_t03060093E09AF371C66F52156884405E04436209 *)il2cpp_codegen_object_new(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929(L_1, ((int32_t)300), ((int32_t)250), /*hidden argument*/NULL);
		((AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields*)il2cpp_codegen_static_fields_for(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var))->set_MediumRectangle_5(L_1);
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_2 = (AdSize_t03060093E09AF371C66F52156884405E04436209 *)il2cpp_codegen_object_new(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929(L_2, ((int32_t)468), ((int32_t)60), /*hidden argument*/NULL);
		((AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields*)il2cpp_codegen_static_fields_for(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var))->set_IABBanner_6(L_2);
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_3 = (AdSize_t03060093E09AF371C66F52156884405E04436209 *)il2cpp_codegen_object_new(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929(L_3, ((int32_t)728), ((int32_t)90), /*hidden argument*/NULL);
		((AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields*)il2cpp_codegen_static_fields_for(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var))->set_Leaderboard_7(L_3);
		AdSize_t03060093E09AF371C66F52156884405E04436209 * L_4 = (AdSize_t03060093E09AF371C66F52156884405E04436209 *)il2cpp_codegen_object_new(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var);
		AdSize__ctor_m0D17F8059B3EF0FC19B2E9AE90E06857D2880792(L_4, 0, 0, 1, /*hidden argument*/NULL);
		((AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields*)il2cpp_codegen_static_fields_for(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var))->set_SmartBanner_8(L_4);
		((AdSize_t03060093E09AF371C66F52156884405E04436209_StaticFields*)il2cpp_codegen_static_fields_for(AdSize_t03060093E09AF371C66F52156884405E04436209_il2cpp_TypeInfo_var))->set_FullWidth_9((-1));
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdValue::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdValue__ctor_m37CBA3EBBF99BEBC2ADFD28630D1EE6A3071395B (AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdValue::set_Precision(GoogleMobileAds.Api.AdValue/PrecisionType)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdValue_set_Precision_m41801AC0C15990FC28445C401A818279D6734FEC (AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CPrecisionU3Ek__BackingField_0(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdValue::set_Value(System.Int64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdValue_set_Value_m253FBF7AB640E038912EDE5A26E097B6503713C1 (AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * __this, int64_t ___value0, const RuntimeMethod* method)
{
	{
		int64_t L_0 = ___value0;
		__this->set_U3CValueU3Ek__BackingField_1(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdValue::set_CurrencyCode(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdValue_set_CurrencyCode_m0B1E6921005D7C14D7DEDB5716B62884773560B0 (AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CCurrencyCodeU3Ek__BackingField_2(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdValueEventArgs::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdValueEventArgs__ctor_mF6CEC05465A21DFA6AC1305763E27E4989E11F32 (AdValueEventArgs_t370093BE304A6D5E913DDA88544E062BE0B3D91E * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_il2cpp_TypeInfo_var);
		EventArgs__ctor_m5ECB9A8ED0A9E2DBB1ED999BAC1CB44F4354E571(__this, /*hidden argument*/NULL);
		return;
	}
}
// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.AdValueEventArgs::get_AdValue()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * AdValueEventArgs_get_AdValue_m47E07F15E141D9E7E4599AD72935E6CCCBFE047C (AdValueEventArgs_t370093BE304A6D5E913DDA88544E062BE0B3D91E * __this, const RuntimeMethod* method)
{
	{
		AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * L_0 = __this->get_U3CAdValueU3Ek__BackingField_1();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.AdValueEventArgs::set_AdValue(GoogleMobileAds.Api.AdValue)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdValueEventArgs_set_AdValue_m68EFEA232807581D77002A5E00A88CBB2D287591 (AdValueEventArgs_t370093BE304A6D5E913DDA88544E062BE0B3D91E * __this, AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * ___value0, const RuntimeMethod* method)
{
	{
		AdValue_t806DD55342062227A4009E24834AE96A38A57F28 * L_0 = ___value0;
		__this->set_U3CAdValueU3Ek__BackingField_1(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdapterStatus::.ctor(GoogleMobileAds.Api.AdapterState,System.String,System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdapterStatus__ctor_m464B6C58733F1FC6FC8171A3AC4B27C97B5F81CA (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___state0, String_t* ___description1, int32_t ___latency2, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		int32_t L_0 = ___state0;
		AdapterStatus_set_InitializationState_m8D23EB6FAA5DC25A033006B82E4640E5E0FCF4DE_inline(__this, L_0, /*hidden argument*/NULL);
		String_t* L_1 = ___description1;
		AdapterStatus_set_Description_m12EECD98F5BE85D58D7C1632F6D80FEF1A3BA733_inline(__this, L_1, /*hidden argument*/NULL);
		int32_t L_2 = ___latency2;
		AdapterStatus_set_Latency_m6AFDC90CA78BE3A71352878AD5B16B86C9290A2B_inline(__this, L_2, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdapterStatus::set_InitializationState(GoogleMobileAds.Api.AdapterState)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdapterStatus_set_InitializationState_m8D23EB6FAA5DC25A033006B82E4640E5E0FCF4DE (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CInitializationStateU3Ek__BackingField_0(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdapterStatus::set_Description(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdapterStatus_set_Description_m12EECD98F5BE85D58D7C1632F6D80FEF1A3BA733 (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CDescriptionU3Ek__BackingField_1(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdapterStatus::set_Latency(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdapterStatus_set_Latency_m6AFDC90CA78BE3A71352878AD5B16B86C9290A2B (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CLatencyU3Ek__BackingField_2(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdManager.AppEvent::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppEvent__ctor_m01D295C1FC52F4EDE3A6456CC5071FC3EAE34ECF (AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdManager.AppEvent::set_Name(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppEvent_set_Name_m607FC3BF917696813C1CF88E0B1355A717E22115 (AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CNameU3Ek__BackingField_0(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AdManager.AppEvent::set_Data(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppEvent_set_Data_m1B6BDC68337061F10E7C61AAD9672E7C9E22652E (AppEvent_tEBB00894F5B96344280D4A96AB8AA1909770C56A * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CDataU3Ek__BackingField_1(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.MaxAdContentRating::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaxAdContentRating__ctor_m61B9B7976E688768408525833B8F0416837E72BB (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		String_t* L_0 = ___value0;
		MaxAdContentRating_set_Value_m87B495BE0B1BB9D04CE8B144A4D99A2A5ADD9646_inline(__this, L_0, /*hidden argument*/NULL);
		return;
	}
}
// GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.MaxAdContentRating::ToMaxAdContentRating(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * MaxAdContentRating_ToMaxAdContentRating_mD30E1DF2AB94EE009F7CB1EA0D02A3A7976F6887 (String_t* ___value0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___value0;
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_1 = (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 *)il2cpp_codegen_object_new(MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239_il2cpp_TypeInfo_var);
		MaxAdContentRating__ctor_m61B9B7976E688768408525833B8F0416837E72BB(L_1, L_0, /*hidden argument*/NULL);
		return L_1;
	}
}
// System.String GoogleMobileAds.Api.MaxAdContentRating::get_Value()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* MaxAdContentRating_get_Value_m7BFF3E65CC7A4C015DC31B26AF20C6C332FC85CA (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * __this, const RuntimeMethod* method)
{
	{
		String_t* L_0 = __this->get_U3CValueU3Ek__BackingField_0();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.MaxAdContentRating::set_Value(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaxAdContentRating_set_Value_m87B495BE0B1BB9D04CE8B144A4D99A2A5ADD9646 (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CValueU3Ek__BackingField_0(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.Mediation.MediationExtras::get_Extras()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * MediationExtras_get_Extras_m053815293202950C6CF255BE08D4FD7F783C1BEB (MediationExtras_t29FA501CE520EDBF8C61516FC547ADD575699C3B * __this, const RuntimeMethod* method)
{
	{
		Dictionary_2_tDE3227CA5E7A32F5070BD24C69F42204A3ADE9D5 * L_0 = __this->get_U3CExtrasU3Ek__BackingField_0();
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RequestConfiguration::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RequestConfiguration__ctor_mB474168D61CAEE374CA2497A21DD2FF1FB337E99 (RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *)il2cpp_codegen_object_new(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var);
		List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9(L_0, /*hidden argument*/List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var);
		__this->set_TestDeviceIds_3(L_0);
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.Reward::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Reward__ctor_mE06181CF7816ECBE92B2B7B000C75F12721C2DFA (Reward_t53574C3E2B34B29EE3862B9EAE8B8334C0BC2CDB * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(EventArgs_tBCAACA538A5195B6D6C8DFCC3524A2A4A67FD8BA_il2cpp_TypeInfo_var);
		EventArgs__ctor_m5ECB9A8ED0A9E2DBB1ED999BAC1CB44F4354E571(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.ServerSideVerificationOptions::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServerSideVerificationOptions__ctor_m7F3CA9D05B947139B90C70BBDFC312CBF2A3F9E2 (ServerSideVerificationOptions_t2FF28841A6B538771FB349BCCD3517E399362E49 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Builder__ctor_m421D9BBC603B8E15090C742C5B714B8260609ABB (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  V_0;
	memset((&V_0), 0, sizeof(V_0));
	Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  V_1;
	memset((&V_1), 0, sizeof(V_1));
	Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C_inline(__this, (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 *)NULL, /*hidden argument*/NULL);
		il2cpp_codegen_initobj((&V_0), sizeof(Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B ));
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_0 = V_0;
		Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA_inline(__this, L_0, /*hidden argument*/NULL);
		il2cpp_codegen_initobj((&V_1), sizeof(Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7 ));
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_1 = V_1;
		Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF_inline(__this, L_1, /*hidden argument*/NULL);
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_2 = (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *)il2cpp_codegen_object_new(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var);
		List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9(L_2, /*hidden argument*/List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var);
		Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F_inline(__this, L_2, /*hidden argument*/NULL);
		il2cpp_codegen_initobj((&V_2), sizeof(Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3 ));
		Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  L_3 = V_2;
		Builder_set_SameAppKeyEnabled_m4F22919DC10A3690CB48160BF601A27DEE973C1A_inline(__this, L_3, /*hidden argument*/NULL);
		return;
	}
}
// GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.RequestConfiguration/Builder::get_MaxAdContentRating()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * Builder_get_MaxAdContentRating_m9A3592507C10CC11286B697201CC6465ABC79671 (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_0 = __this->get_U3CMaxAdContentRatingU3Ek__BackingField_0();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_MaxAdContentRating(GoogleMobileAds.Api.MaxAdContentRating)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * ___value0, const RuntimeMethod* method)
{
	{
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_0 = ___value0;
		__this->set_U3CMaxAdContentRatingU3Ek__BackingField_0(L_0);
		return;
	}
}
// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TagForChildDirectedTreatment()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  Builder_get_TagForChildDirectedTreatment_mCD38E6703E8325EB13CE37474C0A347AC206FDCF (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_0 = __this->get_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TagForChildDirectedTreatment(System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  ___value0, const RuntimeMethod* method)
{
	{
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_0 = ___value0;
		__this->set_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1(L_0);
		return;
	}
}
// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TagForUnderAgeOfConsent()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  Builder_get_TagForUnderAgeOfConsent_m44813E5169846F8DB5D7C2D840C828DB225B60CF (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_0 = __this->get_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TagForUnderAgeOfConsent(System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  ___value0, const RuntimeMethod* method)
{
	{
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_0 = ___value0;
		__this->set_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2(L_0);
		return;
	}
}
// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TestDeviceIds()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * Builder_get_TestDeviceIds_m4963727F9F2E889F7E0B5C8CFCDB5256C1F456AE (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = __this->get_U3CTestDeviceIdsU3Ek__BackingField_3();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TestDeviceIds(System.Collections.Generic.List`1<System.String>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___value0, const RuntimeMethod* method)
{
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = ___value0;
		__this->set_U3CTestDeviceIdsU3Ek__BackingField_3(L_0);
		return;
	}
}
// System.Nullable`1<System.Boolean> GoogleMobileAds.Api.RequestConfiguration/Builder::get_SameAppKeyEnabled()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  Builder_get_SameAppKeyEnabled_m6B799BA968F08CA2F07E3B1F6375EFFD2C7F17C1 (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  L_0 = __this->get_U3CSameAppKeyEnabledU3Ek__BackingField_4();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_SameAppKeyEnabled(System.Nullable`1<System.Boolean>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Builder_set_SameAppKeyEnabled_m4F22919DC10A3690CB48160BF601A27DEE973C1A (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  ___value0, const RuntimeMethod* method)
{
	{
		Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  L_0 = ___value0;
		__this->set_U3CSameAppKeyEnabledU3Ek__BackingField_4(L_0);
		return;
	}
}
// GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetMaxAdContentRating(GoogleMobileAds.Api.MaxAdContentRating)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * Builder_SetMaxAdContentRating_mC248A6EA37CD3C767C58A6C544A1D716A51AB4D0 (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * ___maxAdContentRating0, const RuntimeMethod* method)
{
	{
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_0 = ___maxAdContentRating0;
		Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C_inline(__this, L_0, /*hidden argument*/NULL);
		return __this;
	}
}
// GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetTagForChildDirectedTreatment(System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * Builder_SetTagForChildDirectedTreatment_m9D68DD5CD57DDA251CF26C4C62BB939C3725A09C (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  ___tagForChildDirectedTreatment0, const RuntimeMethod* method)
{
	{
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_0 = ___tagForChildDirectedTreatment0;
		Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA_inline(__this, L_0, /*hidden argument*/NULL);
		return __this;
	}
}
// GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetTagForUnderAgeOfConsent(System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * Builder_SetTagForUnderAgeOfConsent_m596B502201DDB50C6D52BA1DB44CE36A6D99320D (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  ___tagForUnderAgeOfConsent0, const RuntimeMethod* method)
{
	{
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_0 = ___tagForUnderAgeOfConsent0;
		Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF_inline(__this, L_0, /*hidden argument*/NULL);
		return __this;
	}
}
// GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetTestDeviceIds(System.Collections.Generic.List`1<System.String>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * Builder_SetTestDeviceIds_mC86FC178F26606663E63411D30FBB4956024B98E (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___testDeviceIds0, const RuntimeMethod* method)
{
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = ___testDeviceIds0;
		Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F_inline(__this, L_0, /*hidden argument*/NULL);
		return __this;
	}
}
// GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Api.RequestConfiguration/Builder::build()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * Builder_build_m167E7DDB110F1F7DD752424FA257F5802E77E4DA (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * V_0 = NULL;
	RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * V_1 = NULL;
	{
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_0 = (RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 *)il2cpp_codegen_object_new(RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951_il2cpp_TypeInfo_var);
		RequestConfiguration__ctor_mB474168D61CAEE374CA2497A21DD2FF1FB337E99(L_0, /*hidden argument*/NULL);
		V_1 = L_0;
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_1 = V_1;
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_2;
		L_2 = Builder_get_MaxAdContentRating_m9A3592507C10CC11286B697201CC6465ABC79671_inline(__this, /*hidden argument*/NULL);
		NullCheck(L_1);
		L_1->set_MaxAdContentRating_0(L_2);
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_3 = V_1;
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_4;
		L_4 = Builder_get_TagForChildDirectedTreatment_mCD38E6703E8325EB13CE37474C0A347AC206FDCF_inline(__this, /*hidden argument*/NULL);
		NullCheck(L_3);
		L_3->set_TagForChildDirectedTreatment_1(L_4);
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_5 = V_1;
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_6;
		L_6 = Builder_get_TagForUnderAgeOfConsent_m44813E5169846F8DB5D7C2D840C828DB225B60CF_inline(__this, /*hidden argument*/NULL);
		NullCheck(L_5);
		L_5->set_TagForUnderAgeOfConsent_2(L_6);
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_7 = V_1;
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_8;
		L_8 = Builder_get_TestDeviceIds_m4963727F9F2E889F7E0B5C8CFCDB5256C1F456AE_inline(__this, /*hidden argument*/NULL);
		NullCheck(L_7);
		L_7->set_TestDeviceIds_3(L_8);
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_9 = V_1;
		Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  L_10;
		L_10 = Builder_get_SameAppKeyEnabled_m6B799BA968F08CA2F07E3B1F6375EFFD2C7F17C1_inline(__this, /*hidden argument*/NULL);
		NullCheck(L_9);
		L_9->set_SameAppKeyEnabled_4(L_10);
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_11 = V_1;
		V_0 = L_11;
		RequestConfiguration_tF81C86729E113F66C28E2D553D22666459FD7951 * L_12 = V_0;
		return L_12;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * AssemblyName_get_Version_m1E5978822709B7B59BEB504A8BC567823766497D_inline (AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824 * __this, const RuntimeMethod* method)
{
	{
		Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * L_0 = __this->get_version_13();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Version_get_Major_mBDD414863C4A05FADE87F8C39C8CE8ED6DE6C460_inline (Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__Major_0();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Version_get_Minor_m8FCC5D46616E2E54B213EDF31CF3EB57EC998BCE_inline (Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__Minor_1();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Version_get_Build_mF4D316F7F919B539F41467DD4A91839E42456584_inline (Version_tBDAEDED25425A1D09910468B8BD1759115646E3C * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get__Build_2();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdRequest_set_Version_m938C94CD619E6DAB2EAA5860BD8EE98A802DF82F_inline (String_t* ___value0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___value0;
		IL2CPP_RUNTIME_CLASS_INIT(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		((AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_StaticFields*)il2cpp_codegen_static_fields_for(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var))->set_U3CVersionU3Ek__BackingField_0(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AdRequest_get_Version_m0140F2D9E5FE5EB8A076CD376660C34DB55C10F9_inline (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var);
		String_t* L_0 = ((AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_StaticFields*)il2cpp_codegen_static_fields_for(AdRequest_t32DDA912B1EEA224F107BDD96FD925AA97A6E9FE_il2cpp_TypeInfo_var))->get_U3CVersionU3Ek__BackingField_0();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdapterStatus_set_InitializationState_m8D23EB6FAA5DC25A033006B82E4640E5E0FCF4DE_inline (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CInitializationStateU3Ek__BackingField_0(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdapterStatus_set_Description_m12EECD98F5BE85D58D7C1632F6D80FEF1A3BA733_inline (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CDescriptionU3Ek__BackingField_1(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AdapterStatus_set_Latency_m6AFDC90CA78BE3A71352878AD5B16B86C9290A2B_inline (AdapterStatus_t8EB3B69F0D73A948898E86A260F3C46AFF5E6A45 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CLatencyU3Ek__BackingField_2(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MaxAdContentRating_set_Value_m87B495BE0B1BB9D04CE8B144A4D99A2A5ADD9646_inline (MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CValueU3Ek__BackingField_0(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * ___value0, const RuntimeMethod* method)
{
	{
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_0 = ___value0;
		__this->set_U3CMaxAdContentRatingU3Ek__BackingField_0(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  ___value0, const RuntimeMethod* method)
{
	{
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_0 = ___value0;
		__this->set_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  ___value0, const RuntimeMethod* method)
{
	{
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_0 = ___value0;
		__this->set_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___value0, const RuntimeMethod* method)
{
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = ___value0;
		__this->set_U3CTestDeviceIdsU3Ek__BackingField_3(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Builder_set_SameAppKeyEnabled_m4F22919DC10A3690CB48160BF601A27DEE973C1A_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  ___value0, const RuntimeMethod* method)
{
	{
		Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  L_0 = ___value0;
		__this->set_U3CSameAppKeyEnabledU3Ek__BackingField_4(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * Builder_get_MaxAdContentRating_m9A3592507C10CC11286B697201CC6465ABC79671_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		MaxAdContentRating_tBF9F97DD312A32D5B59E71DB468BBCD36DD33239 * L_0 = __this->get_U3CMaxAdContentRatingU3Ek__BackingField_0();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  Builder_get_TagForChildDirectedTreatment_mCD38E6703E8325EB13CE37474C0A347AC206FDCF_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		Nullable_1_t233A3C7E61996ACB06DCEE64C535FEEB333AD70B  L_0 = __this->get_U3CTagForChildDirectedTreatmentU3Ek__BackingField_1();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  Builder_get_TagForUnderAgeOfConsent_m44813E5169846F8DB5D7C2D840C828DB225B60CF_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		Nullable_1_tD94DD5B11CAF30DA314E1E469CF14FC1BD60E2C7  L_0 = __this->get_U3CTagForUnderAgeOfConsentU3Ek__BackingField_2();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * Builder_get_TestDeviceIds_m4963727F9F2E889F7E0B5C8CFCDB5256C1F456AE_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = __this->get_U3CTestDeviceIdsU3Ek__BackingField_3();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  Builder_get_SameAppKeyEnabled_m6B799BA968F08CA2F07E3B1F6375EFFD2C7F17C1_inline (Builder_t64D5F06F55654642F0B71A2606A5B46186F9EBEE * __this, const RuntimeMethod* method)
{
	{
		Nullable_1_t1D1CD146BFCBDC2E53E1F700889F8C5C21063EF3  L_0 = __this->get_U3CSameAppKeyEnabledU3Ek__BackingField_4();
		return L_0;
	}
}
