using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class GrassCubeprocessormachine : MonoBehaviour
{
    [Header("Prefabs and Transforms")]
    public GameObject BundlePrefab;
    public Transform SpawnPoint;
    public Transform LastLocalPosition;

    [Header("Scale Settings")]
    public Vector3 startScale = new Vector3(0.207168981f, 0.110515535f, 0.0942983702f);
    public Vector3 endScale = new Vector3(0.9432604f, 0.503188f, 0.4293497f);

    [Header("Animation Settings")]
    public float scaleSpeed = 3f;
    public float moveSpeed = 4f;
    public AnimationCurve scaleCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    public AnimationCurve moveCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

    [Header("Dynamic Effects")]
    public float bounceIntensity = 0.1f;
    public float rotationSpeed = 45f;
    public bool enableParticleEffects = true;
    public ParticleSystem processingParticles;
    public ParticleSystem completionParticles;

    [Header("Audio")]
    public AudioSource processingSound;
    public AudioSource completionSound;
    public float pitchVariation = 0.2f;

    [Header("UI Elements")]
    public Slider progressSlider;
    public Text percentageText;
    public GameObject completePanel;

    [Header("Level Settings")]
    public int targetBundles = 10;
    public int maxTriggersPerBundle = 8;

    [Header("Trigger Settings")]
    public float triggerDelayTime = 5f;

    [Header("Scale Monitoring")]
    public float minScaleThreshold = 0.2f;
    public float scaleCheckInterval = 0.1f;

    // Private variables
    private Dictionary<GameObject, int> bundleTriggerCount = new Dictionary<GameObject, int>();
    private Dictionary<GameObject, Coroutine> bundleProcessingCoroutines = new Dictionary<GameObject, Coroutine>();
    private List<GameObject> processedBundles = new List<GameObject>();
    private int completedBundles = 0;
    private bool isLevelComplete = false;
    
    void Start()
    {
        // Initialize UI with simple system - start at 0%
        completedBundles = 0;
        UpdateProgressSimple();

        // Make sure complete panel is hidden at start
        if (completePanel != null)
        {
            completePanel.SetActive(false);
        }

        // Initialize animation curves if not set
        if (scaleCurve.keys.Length == 0)
            scaleCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        if (moveCurve.keys.Length == 0)
            moveCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);


    }

    void OnTriggerEnter(Collider other)
    {
        if (isLevelComplete) return;

        if (other.gameObject.CompareTag("GrassBundle"))
        {
            // Initialize trigger count for new bundles
            if (!bundleTriggerCount.ContainsKey(other.gameObject))
            {
                bundleTriggerCount[other.gameObject] = 0;
            }

            bundleTriggerCount[other.gameObject]++;
            int triggerCount = bundleTriggerCount[other.gameObject];

            // Stop any existing processing coroutine for this bundle
            if (bundleProcessingCoroutines.ContainsKey(other.gameObject))
            {
                if (bundleProcessingCoroutines[other.gameObject] != null)
                    StopCoroutine(bundleProcessingCoroutines[other.gameObject]);
            }

            // Start new processing coroutine
            bundleProcessingCoroutines[other.gameObject] = StartCoroutine(ProcessGrassBundle(other.gameObject, triggerCount));

            // Play processing sound with pitch variation
            PlayProcessingSound();

            // Trigger particle effects
            if (enableParticleEffects && processingParticles != null)
            {
                processingParticles.transform.position = other.transform.position;
                processingParticles.Play();
            }
        }
    }
    
    IEnumerator ProcessGrassBundle(GameObject bundle, int triggerCount)
    {
        // Direct approach - always use endScale after first trigger
        Vector3 targetScale = endScale;

        // Debug logs to see what's happening
        Debug.Log($"ProcessGrassBundle - TriggerCount: {triggerCount}");
        Debug.Log($"EndScale from inspector: {endScale}");
        Debug.Log($"TargetScale being used: {targetScale}");
        Debug.Log($"Current bundle scale: {bundle.transform.localScale}");

        // Store original values for animation
        Vector3 currentScale = bundle.transform.localScale;
        Vector3 originalPosition = bundle.transform.position;
        Quaternion originalRotation = bundle.transform.rotation;

        // Add bounce effect during scaling
        Vector3 bounceScale = targetScale + Vector3.one * bounceIntensity;

        float scaleTime = 0f;
        float scaleDuration = 1f / scaleSpeed;

        // First phase: Scale up with bounce
        while (scaleTime < 0.7f)
        {
            scaleTime += Time.deltaTime * scaleSpeed;
            float curveValue = scaleCurve.Evaluate(scaleTime / 0.7f);

            // Scale animation with bounce
            Vector3 currentTargetScale = Vector3.Lerp(currentScale, bounceScale, curveValue);
            bundle.transform.localScale = currentTargetScale;

            // Add subtle rotation during processing
            float rotationAmount = Mathf.Sin(scaleTime * rotationSpeed) * 2f;
            bundle.transform.rotation = originalRotation * Quaternion.Euler(0, rotationAmount, 0);

            // Add slight vertical bounce
            float bounceOffset = Mathf.Sin(scaleTime * 10f) * bounceIntensity * 0.1f;
            bundle.transform.position = originalPosition + Vector3.up * bounceOffset;

            yield return null;
        }

        // Second phase: Settle to target scale
        float settleTime = 0f;
        while (settleTime < 0.3f)
        {
            settleTime += Time.deltaTime * scaleSpeed;
            float curveValue = scaleCurve.Evaluate(settleTime / 0.3f);

            bundle.transform.localScale = Vector3.Lerp(bounceScale, targetScale, curveValue);

            // Return to original rotation and position
            bundle.transform.rotation = Quaternion.Lerp(bundle.transform.rotation, originalRotation, curveValue);
            bundle.transform.position = Vector3.Lerp(bundle.transform.position, originalPosition, curveValue);

            yield return null;
        }

        // Ensure final values
        bundle.transform.localScale = targetScale;
        bundle.transform.rotation = originalRotation;
        bundle.transform.position = originalPosition;

        // Debug log final scale
        Debug.Log($"Final scale applied: {bundle.transform.localScale}");

        // If this bundle has been triggered enough times, move it to last position
        if (triggerCount >= 2 && !processedBundles.Contains(bundle))
        {
            processedBundles.Add(bundle);
            yield return StartCoroutine(MoveToLastPosition(bundle));
        }
        else
        {
            // Spawn new bundle only if not moving to last position
            SpawnNewBundle();
        }

        // Clean up coroutine reference
        if (bundleProcessingCoroutines.ContainsKey(bundle))
            bundleProcessingCoroutines.Remove(bundle);
    }
    
    IEnumerator MoveToLastPosition(GameObject bundle)
    {
        // Store original values
        Vector3 startPos = bundle.transform.position;
        Vector3 targetPos = LastLocalPosition.position;
        Quaternion startRot = bundle.transform.rotation;
        Vector3 startScale = bundle.transform.localScale;

        // Calculate arc movement for more dynamic motion
        Vector3 midPoint = (startPos + targetPos) * 0.5f;
        midPoint.y += 2f; // Add height for arc movement

        float moveTime = 0f;
        float moveDuration = 1f / moveSpeed;

        // Smooth movement animation with arc and rotation
        while (moveTime < 1f)
        {
            moveTime += Time.deltaTime * moveSpeed;
            float curveValue = moveCurve.Evaluate(moveTime);

            // Arc movement using quadratic bezier curve
            Vector3 currentPos = CalculateBezierPoint(startPos, midPoint, targetPos, curveValue);
            bundle.transform.position = currentPos;

            // Add spinning rotation during movement
            float spinAmount = moveTime * 360f * 2f; // 2 full rotations
            bundle.transform.rotation = startRot * Quaternion.Euler(spinAmount, spinAmount * 0.5f, 0);

            // Slight scale pulsing during movement
            float pulseScale = 1f + Mathf.Sin(moveTime * 15f) * 0.05f;
            bundle.transform.localScale = startScale * pulseScale;

            yield return null;
        }

        // Ensure final position and reset rotation/scale
        bundle.transform.position = targetPos;
        bundle.transform.rotation = Quaternion.identity;
        bundle.transform.localScale = startScale;

        // Parent to last position for organization
        bundle.transform.SetParent(LastLocalPosition);

        // Add rigidbody with some initial force for dynamic effect
        Rigidbody rb = bundle.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = bundle.AddComponent<Rigidbody>();
        }

        // Add slight random force for natural settling
        Vector3 randomForce = new Vector3(
            Random.Range(-1f, 1f),
            Random.Range(0.5f, 1.5f),
            Random.Range(-1f, 1f)
        ) * 2f;
        rb.AddForce(randomForce, ForceMode.Impulse);

        // Play completion effects
        PlayCompletionEffects(bundle.transform.position);



        // Spawn new bundle (this will update progress)
        SpawnNewBundle();
    }
    
    // Helper method for bezier curve calculation
    Vector3 CalculateBezierPoint(Vector3 p0, Vector3 p1, Vector3 p2, float t)
    {
        float u = 1 - t;
        float tt = t * t;
        float uu = u * u;

        Vector3 point = uu * p0;
        point += 2 * u * t * p1;
        point += tt * p2;

        return point;
    }

    // Spawn new bundle with slight delay for smoother flow
    void SpawnNewBundle()
    {
        StartCoroutine(DelayedSpawn());
    }

    IEnumerator DelayedSpawn()
    {
        yield return new WaitForSeconds(0.1f);
        GameObject newBundle = Instantiate(BundlePrefab, SpawnPoint.position, Quaternion.identity);

        // PROGRESS UPDATE: Each instantiate = 10%
        completedBundles++;
        UpdateProgressSimple();

        // First set the correct scale so we can calculate proper collider size
        newBundle.transform.localScale = startScale;

        // Add BoxCollider to spawned bundle if it doesn't exist
        if (newBundle.GetComponent<BoxCollider>() == null)
        {
            BoxCollider boxCollider = newBundle.AddComponent<BoxCollider>();
            // Set collider as trigger for processing detection
            boxCollider.isTrigger = false; // Set to false for physics collision

            // Set the exact size and center values as specified
            boxCollider.center = new Vector3(0f, 0f, 0f);
            boxCollider.size = new Vector3(3.000001f, 3.000001f, 3.000002f);
        }

        // Add rigidbody to spawned bundle if it doesn't exist
        if (newBundle.GetComponent<Rigidbody>() == null)
        {
            Rigidbody rb = newBundle.AddComponent<Rigidbody>();
            // Set some default rigidbody properties for better physics
            rb.mass = 1f;
            rb.drag = 0.5f;
            rb.angularDrag = 0.5f;
        }

        // Now start the spawn animation from zero to start scale
        newBundle.transform.localScale = Vector3.zero;
        StartCoroutine(AnimateSpawn(newBundle, startScale));

        // Start coroutine to enable trigger after specified delay
        StartCoroutine(EnableTriggerAfterDelay(newBundle, triggerDelayTime));

        // Start monitoring scale for auto-deactivation
        StartCoroutine(MonitorScaleForDeactivation(newBundle));
    }

    IEnumerator AnimateSpawn(GameObject bundle, Vector3 targetScale)
    {
        float spawnTime = 0f;

        // Temporarily disable rigidbody during spawn animation to prevent physics interference
        Rigidbody rb = bundle.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.isKinematic = true;
        }

        while (spawnTime < 1f)
        {
            spawnTime += Time.deltaTime * 3f;
            float curveValue = scaleCurve.Evaluate(spawnTime);
            bundle.transform.localScale = Vector3.Lerp(Vector3.zero, targetScale, curveValue);

            // Add slight rotation during spawn
            bundle.transform.Rotate(0, Time.deltaTime * 180f, 0);

            yield return null;
        }

        bundle.transform.localScale = targetScale;

        // Re-enable rigidbody physics after spawn animation
        if (rb != null)
        {
            rb.isKinematic = false;
        }
    }

    // Enable trigger on BoxCollider and set rigidbody to kinematic after specified delay
    IEnumerator EnableTriggerAfterDelay(GameObject bundle, float delay)
    {
        yield return new WaitForSeconds(delay);

        // Check if bundle still exists (might have been destroyed)
        if (bundle != null)
        {
            // Set BoxCollider to trigger
            BoxCollider boxCollider = bundle.GetComponent<BoxCollider>();
            if (boxCollider != null)
            {
                boxCollider.isTrigger = true;
            }

            // Set Rigidbody to kinematic (disable physics)
            Rigidbody rb = bundle.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.isKinematic = true;
            }


        }
    }

    // Monitor scale and deactivate object when it reaches minimum threshold
    IEnumerator MonitorScaleForDeactivation(GameObject bundle)
    {
        // Wait until bundle is properly initialized
        yield return new WaitForSeconds(0.5f);

        while (bundle != null && bundle.activeInHierarchy)
        {
            // Check if bundle still has GrassBundle tag
            if (bundle.CompareTag("GrassBundle"))
            {
                // Get the maximum scale component (use the largest dimension)
                Vector3 currentScale = bundle.transform.localScale;
                float maxScale = Mathf.Max(currentScale.x, currentScale.y, currentScale.z);

                // If scale has decreased to threshold or below, deactivate the object
                if (maxScale <= minScaleThreshold)
                {
                    // Check if this bundle hasn't been processed yet
                    if (!processedBundles.Contains(bundle))
                    {
                        processedBundles.Add(bundle);

                        // Spawn new bundle to replace the deactivated one (this will update progress)
                        SpawnNewBundle();
                    }

                    bundle.SetActive(false);

                    // Clean up any references
                    if (bundleTriggerCount.ContainsKey(bundle))
                        bundleTriggerCount.Remove(bundle);
                    if (bundleProcessingCoroutines.ContainsKey(bundle))
                        bundleProcessingCoroutines.Remove(bundle);

                    yield break; // Exit the coroutine
                }

            }
            else
            {
                // If tag changed, stop monitoring
                yield break;
            }

            // Wait before next check
            yield return new WaitForSeconds(scaleCheckInterval);
        }
    }

    // Audio methods
    void PlayProcessingSound()
    {
        if (processingSound != null)
        {
            processingSound.pitch = 1f + Random.Range(-pitchVariation, pitchVariation);
            processingSound.Play();
        }
    }

    void PlayCompletionEffects(Vector3 position)
    {
        // Play completion sound
        if (completionSound != null)
        {
            completionSound.pitch = 1f + Random.Range(-pitchVariation * 0.5f, pitchVariation * 0.5f);
            completionSound.Play();
        }

        // Play completion particles
        if (enableParticleEffects && completionParticles != null)
        {
            completionParticles.transform.position = position;
            completionParticles.Play();
        }
    }

    // Smooth progress update animation
    IEnumerator AnimateProgressUpdate()
    {
        if (progressSlider == null) yield break;

        float startValue = progressSlider.value;
        float targetValue = (float)completedBundles / targetBundles;
        float animTime = 0f;

        while (animTime < 1f)
        {
            animTime += Time.deltaTime * 2f;
            progressSlider.value = Mathf.Lerp(startValue, targetValue, animTime);
            UpdatePercentageText();
            yield return null;
        }

        progressSlider.value = targetValue;
        UpdatePercentageText();
    }

    // Simple progress update: Each bundle = 10%
    void UpdateProgressSimple()
    {
        int progressPercentage = completedBundles * 10; // Each bundle = 10%

        // Update slider (0 to 1 range)
        if (progressSlider != null)
        {
            progressSlider.value = progressPercentage / 100f;
        }

        // Update text
        if (percentageText != null)
        {
            percentageText.text = progressPercentage + "%";
        }



        // Check for completion
        if (progressPercentage >= 100)
        {
            ShowCompletePanel();
        }
    }

    // Show complete panel when 100% reached
    void ShowCompletePanel()
    {
        if (completePanel != null)
        {
            completePanel.SetActive(true);
        }

        isLevelComplete = true;
    }

    void UpdatePercentageText()
    {
        if (percentageText != null)
        {
            float percentage = (float)completedBundles / targetBundles * 100f;
            percentageText.text = Mathf.RoundToInt(percentage) + "%";
        }
    }

    // Manual progress update method (can be called directly)
    public void UpdateProgressUI()
    {
        if (progressSlider != null)
        {
            float targetValue = (float)completedBundles / targetBundles;
            progressSlider.value = targetValue;
        }
        UpdatePercentageText();
    }

    // Test method to manually spawn bundle (for debugging)
    [ContextMenu("Test Spawn Bundle")]
    public void TestSpawnBundle()
    {
        if (completedBundles < 10)
        {
            SpawnNewBundle();
        }
    }

    void LevelComplete()
    {
        isLevelComplete = true;

        // Play final completion effects
        if (enableParticleEffects && completionParticles != null)
        {
            completionParticles.transform.position = LastLocalPosition.position;
            completionParticles.Play();
        }

        if (completionSound != null)
        {
            completionSound.pitch = 1f;
            completionSound.Play();
        }



        // Add screen shake or other dramatic effects here
        StartCoroutine(CelebrationEffect());
    }

    IEnumerator CelebrationEffect()
    {
        // Simple celebration effect - could be expanded
        float celebrationTime = 0f;
        Vector3 originalCameraPos = Camera.main.transform.position;

        while (celebrationTime < 2f)
        {
            celebrationTime += Time.deltaTime;

            // Subtle camera shake
            Vector3 shakeOffset = Random.insideUnitSphere * 0.1f;
            shakeOffset.z = 0; // Keep camera on same Z plane
            Camera.main.transform.position = originalCameraPos + shakeOffset;

            yield return null;
        }

        Camera.main.transform.position = originalCameraPos;
    }
}
