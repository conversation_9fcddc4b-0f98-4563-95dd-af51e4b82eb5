﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>


template <typename R>
struct VirtFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

// System.Action`1<UnityEngine.AndroidJavaObject>
struct Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B;
// System.Action`1<GoogleMobileAds.Ump.Api.FormError>
struct Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD;
// System.Action`1<System.Object>
struct Action_1_tD9663D9715FAA4E62035CFCF1AD4D094EE7872DC;
// System.Collections.Generic.List`1<System.Object>
struct List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5;
// System.Collections.Generic.List`1<System.String>
struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3;
// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Delegate[]
struct DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8;
// System.IntPtr[]
struct IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6;
// System.Object[]
struct ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE;
// System.Diagnostics.StackTrace[]
struct StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971;
// System.String[]
struct StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A;
// System.Action
struct Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6;
// UnityEngine.AndroidJavaClass
struct AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4;
// UnityEngine.AndroidJavaObject
struct AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E;
// UnityEngine.AndroidJavaProxy
struct AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF;
// UnityEngine.AndroidJavaRunnable
struct AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60;
// System.AsyncCallback
struct AsyncCallback_tA7921BEF974919C46FF8F9D9867C567B200BB0EA;
// GoogleMobileAds.Ump.Api.ConsentDebugSettings
struct ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0;
// GoogleMobileAds.Ump.Android.ConsentFormClient
struct ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA;
// GoogleMobileAds.Ump.Android.ConsentInformationClient
struct ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE;
// GoogleMobileAds.Ump.Api.ConsentRequestParameters
struct ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17;
// System.DelegateData
struct DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288;
// GoogleMobileAds.Ump.Api.FormError
struct FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3;
// UnityEngine.GlobalJavaObjectRef
struct GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289;
// System.IAsyncResult
struct IAsyncResult_tC9F97BF36FCF122D29D3101D80642278297BF370;
// GoogleMobileAds.Ump.Common.IConsentFormClient
struct IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4;
// GoogleMobileAds.Ump.Common.IConsentInformationClient
struct IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951;
// System.Collections.IDictionary
struct IDictionary_t99871C56B8EC2452AC5C4CF3831695E617B89D3A;
// System.InvalidOperationException
struct InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB;
// System.Reflection.MethodInfo
struct MethodInfo_t;
// GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener
struct OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84;
// GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener
struct OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D;
// GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener
struct OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42;
// GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener
struct OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1;
// GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener
struct OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7;
// System.Runtime.Serialization.SafeSerializationManager
struct SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F;
// System.String
struct String_t;
// GoogleMobileAds.Ump.Android.UmpClientFactory
struct UmpClientFactory_tEB81174E5F666DA098ACA4B4E4C00DEF9BC5081A;
// GoogleMobileAds.Ump.Android.Utils
struct Utils_tD5EFE985A2E725E51C06BAF94E0D25A7A31806EB;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;
// GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834;

IL2CPP_EXTERN_C RuntimeClass* Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral03A8B37AD1D0955D7622AC6CD210F9E6E1AD40D7;
IL2CPP_EXTERN_C String_t* _stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A;
IL2CPP_EXTERN_C String_t* _stringLiteral0FB8683E34C0E832D7B6B99C752834988560A605;
IL2CPP_EXTERN_C String_t* _stringLiteral110E0A2D48A545A1D32B314C250A0161599F2C9B;
IL2CPP_EXTERN_C String_t* _stringLiteral16D6B99AEBC4C36DEE7AF021A4F103C19802543C;
IL2CPP_EXTERN_C String_t* _stringLiteral244940E192616C6FDCE2CD494CC5BE9AA60F5CD8;
IL2CPP_EXTERN_C String_t* _stringLiteral2D4ED83444630924C3A223A95C183373672AFA3A;
IL2CPP_EXTERN_C String_t* _stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998;
IL2CPP_EXTERN_C String_t* _stringLiteral375FAFE344F4C2DEFCE90CB6F7DAC4DA51729E3F;
IL2CPP_EXTERN_C String_t* _stringLiteral3F0D13AB774CBD4B95456A54A6A0F9BE25A12554;
IL2CPP_EXTERN_C String_t* _stringLiteral41B244E6AA749FD04A6C56697A4AFF71754B6BC5;
IL2CPP_EXTERN_C String_t* _stringLiteral42737D67B57D1AED1E18C475494BB867AD0AA192;
IL2CPP_EXTERN_C String_t* _stringLiteral45DDA05892176F58A7B35CE2ED91EC900D9D2067;
IL2CPP_EXTERN_C String_t* _stringLiteral4AAD8F8A579371AB1C26B13B652DA8BA29CC7926;
IL2CPP_EXTERN_C String_t* _stringLiteral4AC0D82903EC38AEDFDD6B6F68456C96D5DCD877;
IL2CPP_EXTERN_C String_t* _stringLiteral4B5590C684CEA428C87D4AF085DE91F2CEFF57C2;
IL2CPP_EXTERN_C String_t* _stringLiteral4D613657609485AE586A3379BA0E3FC13C1E1078;
IL2CPP_EXTERN_C String_t* _stringLiteral51F15D564436CDC5F4CA0943FE275B8092368A12;
IL2CPP_EXTERN_C String_t* _stringLiteral6E4657CA6250CD99B3E629C68F25D97249F44C22;
IL2CPP_EXTERN_C String_t* _stringLiteral777AE62A4AAF93B33F90DF3E12489F43ABA14E39;
IL2CPP_EXTERN_C String_t* _stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D;
IL2CPP_EXTERN_C String_t* _stringLiteral7FD3121F912C0E6F15526759C7D703A870317033;
IL2CPP_EXTERN_C String_t* _stringLiteral802F23DDB5D2A0B7807EBAADD5E1DCC85F33F3D3;
IL2CPP_EXTERN_C String_t* _stringLiteralA42C5DDC073620888C073D79151E73091416273D;
IL2CPP_EXTERN_C String_t* _stringLiteralA733C7FC19A8317471D21AD091D1A9A6F973A728;
IL2CPP_EXTERN_C String_t* _stringLiteralA815C138A9BCEB494E0655DE56D11FA37230A46B;
IL2CPP_EXTERN_C String_t* _stringLiteralAAA25E8902DA4A760B857FFC74176D9BB9EB25D2;
IL2CPP_EXTERN_C String_t* _stringLiteralB3588DE9307F0F69133E3EB2F32C1E05E8719923;
IL2CPP_EXTERN_C String_t* _stringLiteralB54E8601CF18631B8097B2949990B5D8DD33F197;
IL2CPP_EXTERN_C String_t* _stringLiteralB5DDAB11F0FE2E649C78FA5A86258A2B3653B3CD;
IL2CPP_EXTERN_C String_t* _stringLiteralB95E94A0154D0393CEC86372990C5EC19EB9E045;
IL2CPP_EXTERN_C String_t* _stringLiteralBE7CF21EBC79CE3BF042EB192A59E7131D2D576B;
IL2CPP_EXTERN_C String_t* _stringLiteralC6F2712DA56297B977518F6DEDB64B4367DECAB3;
IL2CPP_EXTERN_C String_t* _stringLiteralC9CD9BE5636542BD3CFAC8C3719D519A618BC894;
IL2CPP_EXTERN_C String_t* _stringLiteralCAB0CB77D616B0B7305968B4EA47AD2DCF10E56D;
IL2CPP_EXTERN_C String_t* _stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0;
IL2CPP_EXTERN_C String_t* _stringLiteralE39CFBC2A5BA78BC0A2504F2FD6185ACF03D38A9;
IL2CPP_EXTERN_C String_t* _stringLiteralE742A0CC8947EA498A5F70DC274F54915AF90F21;
IL2CPP_EXTERN_C String_t* _stringLiteralED905D79B2EFE56922FF3A3AE7CE4B8680E9789C;
IL2CPP_EXTERN_C String_t* _stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006;
IL2CPP_EXTERN_C String_t* _stringLiteralF4691AD5F8DF3030E11F162E7F2F52C353D6D08C;
IL2CPP_EXTERN_C String_t* _stringLiteralF78C185BAABEDEF7DF7B666CE38F4A6E6A86F1C0;
IL2CPP_EXTERN_C String_t* _stringLiteralF9E201A19431397F0E527CE1CBEFB38F0D1AA5EA;
IL2CPP_EXTERN_C String_t* _stringLiteralFB4AE4F77150C3A8E8E4F8B23E734E0C7277B7D9;
IL2CPP_EXTERN_C const RuntimeMethod* Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Action_1_Invoke_mCA8DB3C200C032B1F12A4EF6C54B0D3DC27DA5D6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Action_1__ctor_m0CC0E4D1D61626822ACA46CAE3121E0E9FE989C9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AndroidJavaObject_CallStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mAD48C38D66AB67D0F0274D195F4A99CB7AB589F2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_mDD267BEE3AD41D6B78540E8E2C2C95C84BA3E367_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m1569F1485B135B4E9B7466B4A95A10A35FE1A703_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentFormClient_U3CShowU3Em__0_m21708BA8AE24D8FA97F7B8BE9A0EF4A2F02D606D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentInformationClient_CanRequestAds_mCAABCD8029F40C78E0167549811E25B623056757_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentInformationClient_GetConsentStatus_mD6C92990E65197BFCAE0F13614F2718B242D9446_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m3C8615C525A07D949F95EFD3AF4A0C5D82DCDC33_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentInformationClient_IsConsentFormAvailable_m5B6E6AACDF70DC9925C9AF4ED8C2C7F9281C4D56_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConsentInformationClient_Reset_m40DD537DE51F4E3E67FF90598C8760C730480D61_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m65A91D17CADA79F187F4D68980A9C8640B6C9FC7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_mCE70417061695048D84E473D50556E46B8630F54_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m9B0E356FA9FCFB9B1BECC6D7C5DF5C03309251AA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m35388695226DE2F7B0B5D0A07016716D6AD9CAEF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m199DB87BCE947106FBA38E19FDFE80CB65B61144_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC644795AFB1E0FCDD4A4D5FE30D4EE0F3EBDA497_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m83019FB280AA8BBBF7202F1110851479163653FE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UmpClientFactory_ConsentFormClient_m3AEC3CD62E7D9DF830B4ABEB6B5A63F772CA03C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UmpClientFactory_ConsentInformationClient_mCB353E67E65957F579BF7896716EB47A99AC77F4_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_t76A4E770837AF396798DF9BA18DE49C0DB398C59 
{
public:

public:
};


// System.Object


// System.Collections.Generic.List`1<System.Object>
struct List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5  : public RuntimeObject
{
public:
	// T[] System.Collections.Generic.List`1::_items
	ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject * ____syncRoot_4;

public:
	inline static int32_t get_offset_of__items_1() { return static_cast<int32_t>(offsetof(List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5, ____items_1)); }
	inline ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* get__items_1() const { return ____items_1; }
	inline ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE** get_address_of__items_1() { return &____items_1; }
	inline void set__items_1(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* value)
	{
		____items_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____items_1), (void*)value);
	}

	inline static int32_t get_offset_of__size_2() { return static_cast<int32_t>(offsetof(List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5, ____size_2)); }
	inline int32_t get__size_2() const { return ____size_2; }
	inline int32_t* get_address_of__size_2() { return &____size_2; }
	inline void set__size_2(int32_t value)
	{
		____size_2 = value;
	}

	inline static int32_t get_offset_of__version_3() { return static_cast<int32_t>(offsetof(List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5, ____version_3)); }
	inline int32_t get__version_3() const { return ____version_3; }
	inline int32_t* get_address_of__version_3() { return &____version_3; }
	inline void set__version_3(int32_t value)
	{
		____version_3 = value;
	}

	inline static int32_t get_offset_of__syncRoot_4() { return static_cast<int32_t>(offsetof(List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5, ____syncRoot_4)); }
	inline RuntimeObject * get__syncRoot_4() const { return ____syncRoot_4; }
	inline RuntimeObject ** get_address_of__syncRoot_4() { return &____syncRoot_4; }
	inline void set__syncRoot_4(RuntimeObject * value)
	{
		____syncRoot_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____syncRoot_4), (void*)value);
	}
};

struct List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5_StaticFields
{
public:
	// T[] System.Collections.Generic.List`1::_emptyArray
	ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ____emptyArray_5;

public:
	inline static int32_t get_offset_of__emptyArray_5() { return static_cast<int32_t>(offsetof(List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5_StaticFields, ____emptyArray_5)); }
	inline ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* get__emptyArray_5() const { return ____emptyArray_5; }
	inline ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE** get_address_of__emptyArray_5() { return &____emptyArray_5; }
	inline void set__emptyArray_5(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* value)
	{
		____emptyArray_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____emptyArray_5), (void*)value);
	}
};


// System.Collections.Generic.List`1<System.String>
struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3  : public RuntimeObject
{
public:
	// T[] System.Collections.Generic.List`1::_items
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject * ____syncRoot_4;

public:
	inline static int32_t get_offset_of__items_1() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____items_1)); }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* get__items_1() const { return ____items_1; }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A** get_address_of__items_1() { return &____items_1; }
	inline void set__items_1(StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* value)
	{
		____items_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____items_1), (void*)value);
	}

	inline static int32_t get_offset_of__size_2() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____size_2)); }
	inline int32_t get__size_2() const { return ____size_2; }
	inline int32_t* get_address_of__size_2() { return &____size_2; }
	inline void set__size_2(int32_t value)
	{
		____size_2 = value;
	}

	inline static int32_t get_offset_of__version_3() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____version_3)); }
	inline int32_t get__version_3() const { return ____version_3; }
	inline int32_t* get_address_of__version_3() { return &____version_3; }
	inline void set__version_3(int32_t value)
	{
		____version_3 = value;
	}

	inline static int32_t get_offset_of__syncRoot_4() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____syncRoot_4)); }
	inline RuntimeObject * get__syncRoot_4() const { return ____syncRoot_4; }
	inline RuntimeObject ** get_address_of__syncRoot_4() { return &____syncRoot_4; }
	inline void set__syncRoot_4(RuntimeObject * value)
	{
		____syncRoot_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____syncRoot_4), (void*)value);
	}
};

struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_StaticFields
{
public:
	// T[] System.Collections.Generic.List`1::_emptyArray
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* ____emptyArray_5;

public:
	inline static int32_t get_offset_of__emptyArray_5() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_StaticFields, ____emptyArray_5)); }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* get__emptyArray_5() const { return ____emptyArray_5; }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A** get_address_of__emptyArray_5() { return &____emptyArray_5; }
	inline void set__emptyArray_5(StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* value)
	{
		____emptyArray_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____emptyArray_5), (void*)value);
	}
};


// UnityEngine.AndroidJavaObject
struct AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E  : public RuntimeObject
{
public:
	// UnityEngine.GlobalJavaObjectRef UnityEngine.AndroidJavaObject::m_jobject
	GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * ___m_jobject_1;
	// UnityEngine.GlobalJavaObjectRef UnityEngine.AndroidJavaObject::m_jclass
	GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * ___m_jclass_2;

public:
	inline static int32_t get_offset_of_m_jobject_1() { return static_cast<int32_t>(offsetof(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E, ___m_jobject_1)); }
	inline GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * get_m_jobject_1() const { return ___m_jobject_1; }
	inline GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 ** get_address_of_m_jobject_1() { return &___m_jobject_1; }
	inline void set_m_jobject_1(GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * value)
	{
		___m_jobject_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_jobject_1), (void*)value);
	}

	inline static int32_t get_offset_of_m_jclass_2() { return static_cast<int32_t>(offsetof(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E, ___m_jclass_2)); }
	inline GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * get_m_jclass_2() const { return ___m_jclass_2; }
	inline GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 ** get_address_of_m_jclass_2() { return &___m_jclass_2; }
	inline void set_m_jclass_2(GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * value)
	{
		___m_jclass_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_jclass_2), (void*)value);
	}
};

struct AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_StaticFields
{
public:
	// System.Boolean UnityEngine.AndroidJavaObject::enableDebugPrints
	bool ___enableDebugPrints_0;

public:
	inline static int32_t get_offset_of_enableDebugPrints_0() { return static_cast<int32_t>(offsetof(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_StaticFields, ___enableDebugPrints_0)); }
	inline bool get_enableDebugPrints_0() const { return ___enableDebugPrints_0; }
	inline bool* get_address_of_enableDebugPrints_0() { return &___enableDebugPrints_0; }
	inline void set_enableDebugPrints_0(bool value)
	{
		___enableDebugPrints_0 = value;
	}
};

struct Il2CppArrayBounds;

// System.Array


// GoogleMobileAds.Ump.Android.ConsentInformationClient
struct ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener GoogleMobileAds.Ump.Android.ConsentInformationClient::_onSuccess
	OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * ____onSuccess_0;
	// GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener GoogleMobileAds.Ump.Android.ConsentInformationClient::_onFailure
	OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * ____onFailure_1;
	// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.ConsentInformationClient::_consentInformation
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ____consentInformation_3;
	// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.ConsentInformationClient::_activity
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ____activity_4;

public:
	inline static int32_t get_offset_of__onSuccess_0() { return static_cast<int32_t>(offsetof(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE, ____onSuccess_0)); }
	inline OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * get__onSuccess_0() const { return ____onSuccess_0; }
	inline OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 ** get_address_of__onSuccess_0() { return &____onSuccess_0; }
	inline void set__onSuccess_0(OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * value)
	{
		____onSuccess_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onSuccess_0), (void*)value);
	}

	inline static int32_t get_offset_of__onFailure_1() { return static_cast<int32_t>(offsetof(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE, ____onFailure_1)); }
	inline OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * get__onFailure_1() const { return ____onFailure_1; }
	inline OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 ** get_address_of__onFailure_1() { return &____onFailure_1; }
	inline void set__onFailure_1(OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * value)
	{
		____onFailure_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onFailure_1), (void*)value);
	}

	inline static int32_t get_offset_of__consentInformation_3() { return static_cast<int32_t>(offsetof(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE, ____consentInformation_3)); }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * get__consentInformation_3() const { return ____consentInformation_3; }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E ** get_address_of__consentInformation_3() { return &____consentInformation_3; }
	inline void set__consentInformation_3(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * value)
	{
		____consentInformation_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____consentInformation_3), (void*)value);
	}

	inline static int32_t get_offset_of__activity_4() { return static_cast<int32_t>(offsetof(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE, ____activity_4)); }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * get__activity_4() const { return ____activity_4; }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E ** get_address_of__activity_4() { return &____activity_4; }
	inline void set__activity_4(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * value)
	{
		____activity_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____activity_4), (void*)value);
	}
};

struct ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_StaticFields
{
public:
	// GoogleMobileAds.Ump.Android.ConsentInformationClient GoogleMobileAds.Ump.Android.ConsentInformationClient::_instance
	ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * ____instance_2;

public:
	inline static int32_t get_offset_of__instance_2() { return static_cast<int32_t>(offsetof(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_StaticFields, ____instance_2)); }
	inline ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * get__instance_2() const { return ____instance_2; }
	inline ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE ** get_address_of__instance_2() { return &____instance_2; }
	inline void set__instance_2(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * value)
	{
		____instance_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____instance_2), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentRequestParameters
struct ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17  : public RuntimeObject
{
public:
	// System.Boolean GoogleMobileAds.Ump.Api.ConsentRequestParameters::TagForUnderAgeOfConsent
	bool ___TagForUnderAgeOfConsent_0;
	// GoogleMobileAds.Ump.Api.ConsentDebugSettings GoogleMobileAds.Ump.Api.ConsentRequestParameters::ConsentDebugSettings
	ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * ___ConsentDebugSettings_1;

public:
	inline static int32_t get_offset_of_TagForUnderAgeOfConsent_0() { return static_cast<int32_t>(offsetof(ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17, ___TagForUnderAgeOfConsent_0)); }
	inline bool get_TagForUnderAgeOfConsent_0() const { return ___TagForUnderAgeOfConsent_0; }
	inline bool* get_address_of_TagForUnderAgeOfConsent_0() { return &___TagForUnderAgeOfConsent_0; }
	inline void set_TagForUnderAgeOfConsent_0(bool value)
	{
		___TagForUnderAgeOfConsent_0 = value;
	}

	inline static int32_t get_offset_of_ConsentDebugSettings_1() { return static_cast<int32_t>(offsetof(ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17, ___ConsentDebugSettings_1)); }
	inline ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * get_ConsentDebugSettings_1() const { return ___ConsentDebugSettings_1; }
	inline ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 ** get_address_of_ConsentDebugSettings_1() { return &___ConsentDebugSettings_1; }
	inline void set_ConsentDebugSettings_1(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * value)
	{
		___ConsentDebugSettings_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___ConsentDebugSettings_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.FormError
struct FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3  : public RuntimeObject
{
public:
	// System.Int32 GoogleMobileAds.Ump.Api.FormError::<ErrorCode>k__BackingField
	int32_t ___U3CErrorCodeU3Ek__BackingField_0;
	// System.String GoogleMobileAds.Ump.Api.FormError::<Message>k__BackingField
	String_t* ___U3CMessageU3Ek__BackingField_1;

public:
	inline static int32_t get_offset_of_U3CErrorCodeU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3, ___U3CErrorCodeU3Ek__BackingField_0)); }
	inline int32_t get_U3CErrorCodeU3Ek__BackingField_0() const { return ___U3CErrorCodeU3Ek__BackingField_0; }
	inline int32_t* get_address_of_U3CErrorCodeU3Ek__BackingField_0() { return &___U3CErrorCodeU3Ek__BackingField_0; }
	inline void set_U3CErrorCodeU3Ek__BackingField_0(int32_t value)
	{
		___U3CErrorCodeU3Ek__BackingField_0 = value;
	}

	inline static int32_t get_offset_of_U3CMessageU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3, ___U3CMessageU3Ek__BackingField_1)); }
	inline String_t* get_U3CMessageU3Ek__BackingField_1() const { return ___U3CMessageU3Ek__BackingField_1; }
	inline String_t** get_address_of_U3CMessageU3Ek__BackingField_1() { return &___U3CMessageU3Ek__BackingField_1; }
	inline void set_U3CMessageU3Ek__BackingField_1(String_t* value)
	{
		___U3CMessageU3Ek__BackingField_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CMessageU3Ek__BackingField_1), (void*)value);
	}
};


// System.Reflection.MemberInfo
struct MemberInfo_t  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// GoogleMobileAds.Ump.Android.UmpClientFactory
struct UmpClientFactory_tEB81174E5F666DA098ACA4B4E4C00DEF9BC5081A  : public RuntimeObject
{
public:

public:
};


// GoogleMobileAds.Ump.Android.Utils
struct Utils_tD5EFE985A2E725E51C06BAF94E0D25A7A31806EB  : public RuntimeObject
{
public:

public:
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834  : public RuntimeObject
{
public:
	// System.Action GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::onFormLoaded
	Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ___onFormLoaded_0;
	// GoogleMobileAds.Ump.Android.ConsentFormClient GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::$this
	ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * ___U24this_1;

public:
	inline static int32_t get_offset_of_onFormLoaded_0() { return static_cast<int32_t>(offsetof(U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834, ___onFormLoaded_0)); }
	inline Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * get_onFormLoaded_0() const { return ___onFormLoaded_0; }
	inline Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 ** get_address_of_onFormLoaded_0() { return &___onFormLoaded_0; }
	inline void set_onFormLoaded_0(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * value)
	{
		___onFormLoaded_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___onFormLoaded_0), (void*)value);
	}

	inline static int32_t get_offset_of_U24this_1() { return static_cast<int32_t>(offsetof(U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834, ___U24this_1)); }
	inline ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * get_U24this_1() const { return ___U24this_1; }
	inline ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA ** get_address_of_U24this_1() { return &___U24this_1; }
	inline void set_U24this_1(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * value)
	{
		___U24this_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U24this_1), (void*)value);
	}
};


// System.Collections.Generic.List`1/Enumerator<System.Object>
struct Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6 
{
public:
	// System.Collections.Generic.List`1<T> System.Collections.Generic.List`1/Enumerator::list
	List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * ___list_0;
	// System.Int32 System.Collections.Generic.List`1/Enumerator::index
	int32_t ___index_1;
	// System.Int32 System.Collections.Generic.List`1/Enumerator::version
	int32_t ___version_2;
	// T System.Collections.Generic.List`1/Enumerator::current
	RuntimeObject * ___current_3;

public:
	inline static int32_t get_offset_of_list_0() { return static_cast<int32_t>(offsetof(Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6, ___list_0)); }
	inline List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * get_list_0() const { return ___list_0; }
	inline List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 ** get_address_of_list_0() { return &___list_0; }
	inline void set_list_0(List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * value)
	{
		___list_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___list_0), (void*)value);
	}

	inline static int32_t get_offset_of_index_1() { return static_cast<int32_t>(offsetof(Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6, ___index_1)); }
	inline int32_t get_index_1() const { return ___index_1; }
	inline int32_t* get_address_of_index_1() { return &___index_1; }
	inline void set_index_1(int32_t value)
	{
		___index_1 = value;
	}

	inline static int32_t get_offset_of_version_2() { return static_cast<int32_t>(offsetof(Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6, ___version_2)); }
	inline int32_t get_version_2() const { return ___version_2; }
	inline int32_t* get_address_of_version_2() { return &___version_2; }
	inline void set_version_2(int32_t value)
	{
		___version_2 = value;
	}

	inline static int32_t get_offset_of_current_3() { return static_cast<int32_t>(offsetof(Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6, ___current_3)); }
	inline RuntimeObject * get_current_3() const { return ___current_3; }
	inline RuntimeObject ** get_address_of_current_3() { return &___current_3; }
	inline void set_current_3(RuntimeObject * value)
	{
		___current_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___current_3), (void*)value);
	}
};


// System.Collections.Generic.List`1/Enumerator<System.String>
struct Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B 
{
public:
	// System.Collections.Generic.List`1<T> System.Collections.Generic.List`1/Enumerator::list
	List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___list_0;
	// System.Int32 System.Collections.Generic.List`1/Enumerator::index
	int32_t ___index_1;
	// System.Int32 System.Collections.Generic.List`1/Enumerator::version
	int32_t ___version_2;
	// T System.Collections.Generic.List`1/Enumerator::current
	String_t* ___current_3;

public:
	inline static int32_t get_offset_of_list_0() { return static_cast<int32_t>(offsetof(Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B, ___list_0)); }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * get_list_0() const { return ___list_0; }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 ** get_address_of_list_0() { return &___list_0; }
	inline void set_list_0(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * value)
	{
		___list_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___list_0), (void*)value);
	}

	inline static int32_t get_offset_of_index_1() { return static_cast<int32_t>(offsetof(Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B, ___index_1)); }
	inline int32_t get_index_1() const { return ___index_1; }
	inline int32_t* get_address_of_index_1() { return &___index_1; }
	inline void set_index_1(int32_t value)
	{
		___index_1 = value;
	}

	inline static int32_t get_offset_of_version_2() { return static_cast<int32_t>(offsetof(Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B, ___version_2)); }
	inline int32_t get_version_2() const { return ___version_2; }
	inline int32_t* get_address_of_version_2() { return &___version_2; }
	inline void set_version_2(int32_t value)
	{
		___version_2 = value;
	}

	inline static int32_t get_offset_of_current_3() { return static_cast<int32_t>(offsetof(Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B, ___current_3)); }
	inline String_t* get_current_3() const { return ___current_3; }
	inline String_t** get_address_of_current_3() { return &___current_3; }
	inline void set_current_3(String_t* value)
	{
		___current_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___current_3), (void*)value);
	}
};


// UnityEngine.AndroidJavaClass
struct AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4  : public AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E
{
public:

public:
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// System.Int32
struct Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046 
{
public:
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046, ___m_value_0)); }
	inline int32_t get_m_value_0() const { return ___m_value_0; }
	inline int32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int32_t value)
	{
		___m_value_0 = value;
	}
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// System.Reflection.MethodBase
struct MethodBase_t  : public MemberInfo_t
{
public:

public:
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// UnityEngine.AndroidJavaProxy
struct AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF  : public RuntimeObject
{
public:
	// UnityEngine.AndroidJavaClass UnityEngine.AndroidJavaProxy::javaInterface
	AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * ___javaInterface_0;
	// System.IntPtr UnityEngine.AndroidJavaProxy::proxyObject
	intptr_t ___proxyObject_1;

public:
	inline static int32_t get_offset_of_javaInterface_0() { return static_cast<int32_t>(offsetof(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF, ___javaInterface_0)); }
	inline AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * get_javaInterface_0() const { return ___javaInterface_0; }
	inline AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 ** get_address_of_javaInterface_0() { return &___javaInterface_0; }
	inline void set_javaInterface_0(AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * value)
	{
		___javaInterface_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___javaInterface_0), (void*)value);
	}

	inline static int32_t get_offset_of_proxyObject_1() { return static_cast<int32_t>(offsetof(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF, ___proxyObject_1)); }
	inline intptr_t get_proxyObject_1() const { return ___proxyObject_1; }
	inline intptr_t* get_address_of_proxyObject_1() { return &___proxyObject_1; }
	inline void set_proxyObject_1(intptr_t value)
	{
		___proxyObject_1 = value;
	}
};

struct AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_StaticFields
{
public:
	// UnityEngine.GlobalJavaObjectRef UnityEngine.AndroidJavaProxy::s_JavaLangSystemClass
	GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * ___s_JavaLangSystemClass_2;
	// System.IntPtr UnityEngine.AndroidJavaProxy::s_HashCodeMethodID
	intptr_t ___s_HashCodeMethodID_3;

public:
	inline static int32_t get_offset_of_s_JavaLangSystemClass_2() { return static_cast<int32_t>(offsetof(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_StaticFields, ___s_JavaLangSystemClass_2)); }
	inline GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * get_s_JavaLangSystemClass_2() const { return ___s_JavaLangSystemClass_2; }
	inline GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 ** get_address_of_s_JavaLangSystemClass_2() { return &___s_JavaLangSystemClass_2; }
	inline void set_s_JavaLangSystemClass_2(GlobalJavaObjectRef_t04A7D04EB0317C286F089E4DB4444EC4F2D78289 * value)
	{
		___s_JavaLangSystemClass_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___s_JavaLangSystemClass_2), (void*)value);
	}

	inline static int32_t get_offset_of_s_HashCodeMethodID_3() { return static_cast<int32_t>(offsetof(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_StaticFields, ___s_HashCodeMethodID_3)); }
	inline intptr_t get_s_HashCodeMethodID_3() const { return ___s_HashCodeMethodID_3; }
	inline intptr_t* get_address_of_s_HashCodeMethodID_3() { return &___s_HashCodeMethodID_3; }
	inline void set_s_HashCodeMethodID_3(intptr_t value)
	{
		___s_HashCodeMethodID_3 = value;
	}
};


// GoogleMobileAds.Ump.Api.DebugGeography
struct DebugGeography_t17CE8783791AC778B9B84042AFFE7247DE5AF70D 
{
public:
	// System.Int32 GoogleMobileAds.Ump.Api.DebugGeography::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(DebugGeography_t17CE8783791AC778B9B84042AFFE7247DE5AF70D, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Delegate
struct Delegate_t  : public RuntimeObject
{
public:
	// System.IntPtr System.Delegate::method_ptr
	Il2CppMethodPointer ___method_ptr_0;
	// System.IntPtr System.Delegate::invoke_impl
	intptr_t ___invoke_impl_1;
	// System.Object System.Delegate::m_target
	RuntimeObject * ___m_target_2;
	// System.IntPtr System.Delegate::method
	intptr_t ___method_3;
	// System.IntPtr System.Delegate::delegate_trampoline
	intptr_t ___delegate_trampoline_4;
	// System.IntPtr System.Delegate::extra_arg
	intptr_t ___extra_arg_5;
	// System.IntPtr System.Delegate::method_code
	intptr_t ___method_code_6;
	// System.Reflection.MethodInfo System.Delegate::method_info
	MethodInfo_t * ___method_info_7;
	// System.Reflection.MethodInfo System.Delegate::original_method_info
	MethodInfo_t * ___original_method_info_8;
	// System.DelegateData System.Delegate::data
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	// System.Boolean System.Delegate::method_is_virtual
	bool ___method_is_virtual_10;

public:
	inline static int32_t get_offset_of_method_ptr_0() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_ptr_0)); }
	inline Il2CppMethodPointer get_method_ptr_0() const { return ___method_ptr_0; }
	inline Il2CppMethodPointer* get_address_of_method_ptr_0() { return &___method_ptr_0; }
	inline void set_method_ptr_0(Il2CppMethodPointer value)
	{
		___method_ptr_0 = value;
	}

	inline static int32_t get_offset_of_invoke_impl_1() { return static_cast<int32_t>(offsetof(Delegate_t, ___invoke_impl_1)); }
	inline intptr_t get_invoke_impl_1() const { return ___invoke_impl_1; }
	inline intptr_t* get_address_of_invoke_impl_1() { return &___invoke_impl_1; }
	inline void set_invoke_impl_1(intptr_t value)
	{
		___invoke_impl_1 = value;
	}

	inline static int32_t get_offset_of_m_target_2() { return static_cast<int32_t>(offsetof(Delegate_t, ___m_target_2)); }
	inline RuntimeObject * get_m_target_2() const { return ___m_target_2; }
	inline RuntimeObject ** get_address_of_m_target_2() { return &___m_target_2; }
	inline void set_m_target_2(RuntimeObject * value)
	{
		___m_target_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_target_2), (void*)value);
	}

	inline static int32_t get_offset_of_method_3() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_3)); }
	inline intptr_t get_method_3() const { return ___method_3; }
	inline intptr_t* get_address_of_method_3() { return &___method_3; }
	inline void set_method_3(intptr_t value)
	{
		___method_3 = value;
	}

	inline static int32_t get_offset_of_delegate_trampoline_4() { return static_cast<int32_t>(offsetof(Delegate_t, ___delegate_trampoline_4)); }
	inline intptr_t get_delegate_trampoline_4() const { return ___delegate_trampoline_4; }
	inline intptr_t* get_address_of_delegate_trampoline_4() { return &___delegate_trampoline_4; }
	inline void set_delegate_trampoline_4(intptr_t value)
	{
		___delegate_trampoline_4 = value;
	}

	inline static int32_t get_offset_of_extra_arg_5() { return static_cast<int32_t>(offsetof(Delegate_t, ___extra_arg_5)); }
	inline intptr_t get_extra_arg_5() const { return ___extra_arg_5; }
	inline intptr_t* get_address_of_extra_arg_5() { return &___extra_arg_5; }
	inline void set_extra_arg_5(intptr_t value)
	{
		___extra_arg_5 = value;
	}

	inline static int32_t get_offset_of_method_code_6() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_code_6)); }
	inline intptr_t get_method_code_6() const { return ___method_code_6; }
	inline intptr_t* get_address_of_method_code_6() { return &___method_code_6; }
	inline void set_method_code_6(intptr_t value)
	{
		___method_code_6 = value;
	}

	inline static int32_t get_offset_of_method_info_7() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_info_7)); }
	inline MethodInfo_t * get_method_info_7() const { return ___method_info_7; }
	inline MethodInfo_t ** get_address_of_method_info_7() { return &___method_info_7; }
	inline void set_method_info_7(MethodInfo_t * value)
	{
		___method_info_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___method_info_7), (void*)value);
	}

	inline static int32_t get_offset_of_original_method_info_8() { return static_cast<int32_t>(offsetof(Delegate_t, ___original_method_info_8)); }
	inline MethodInfo_t * get_original_method_info_8() const { return ___original_method_info_8; }
	inline MethodInfo_t ** get_address_of_original_method_info_8() { return &___original_method_info_8; }
	inline void set_original_method_info_8(MethodInfo_t * value)
	{
		___original_method_info_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___original_method_info_8), (void*)value);
	}

	inline static int32_t get_offset_of_data_9() { return static_cast<int32_t>(offsetof(Delegate_t, ___data_9)); }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * get_data_9() const { return ___data_9; }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 ** get_address_of_data_9() { return &___data_9; }
	inline void set_data_9(DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * value)
	{
		___data_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___data_9), (void*)value);
	}

	inline static int32_t get_offset_of_method_is_virtual_10() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_is_virtual_10)); }
	inline bool get_method_is_virtual_10() const { return ___method_is_virtual_10; }
	inline bool* get_address_of_method_is_virtual_10() { return &___method_is_virtual_10; }
	inline void set_method_is_virtual_10(bool value)
	{
		___method_is_virtual_10 = value;
	}
};

// Native definition for P/Invoke marshalling of System.Delegate
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};
// Native definition for COM marshalling of System.Delegate
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};

// System.Exception
struct Exception_t  : public RuntimeObject
{
public:
	// System.String System.Exception::_className
	String_t* ____className_1;
	// System.String System.Exception::_message
	String_t* ____message_2;
	// System.Collections.IDictionary System.Exception::_data
	RuntimeObject* ____data_3;
	// System.Exception System.Exception::_innerException
	Exception_t * ____innerException_4;
	// System.String System.Exception::_helpURL
	String_t* ____helpURL_5;
	// System.Object System.Exception::_stackTrace
	RuntimeObject * ____stackTrace_6;
	// System.String System.Exception::_stackTraceString
	String_t* ____stackTraceString_7;
	// System.String System.Exception::_remoteStackTraceString
	String_t* ____remoteStackTraceString_8;
	// System.Int32 System.Exception::_remoteStackIndex
	int32_t ____remoteStackIndex_9;
	// System.Object System.Exception::_dynamicMethods
	RuntimeObject * ____dynamicMethods_10;
	// System.Int32 System.Exception::_HResult
	int32_t ____HResult_11;
	// System.String System.Exception::_source
	String_t* ____source_12;
	// System.Runtime.Serialization.SafeSerializationManager System.Exception::_safeSerializationManager
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	// System.Diagnostics.StackTrace[] System.Exception::captured_traces
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	// System.IntPtr[] System.Exception::native_trace_ips
	IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* ___native_trace_ips_15;

public:
	inline static int32_t get_offset_of__className_1() { return static_cast<int32_t>(offsetof(Exception_t, ____className_1)); }
	inline String_t* get__className_1() const { return ____className_1; }
	inline String_t** get_address_of__className_1() { return &____className_1; }
	inline void set__className_1(String_t* value)
	{
		____className_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____className_1), (void*)value);
	}

	inline static int32_t get_offset_of__message_2() { return static_cast<int32_t>(offsetof(Exception_t, ____message_2)); }
	inline String_t* get__message_2() const { return ____message_2; }
	inline String_t** get_address_of__message_2() { return &____message_2; }
	inline void set__message_2(String_t* value)
	{
		____message_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____message_2), (void*)value);
	}

	inline static int32_t get_offset_of__data_3() { return static_cast<int32_t>(offsetof(Exception_t, ____data_3)); }
	inline RuntimeObject* get__data_3() const { return ____data_3; }
	inline RuntimeObject** get_address_of__data_3() { return &____data_3; }
	inline void set__data_3(RuntimeObject* value)
	{
		____data_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____data_3), (void*)value);
	}

	inline static int32_t get_offset_of__innerException_4() { return static_cast<int32_t>(offsetof(Exception_t, ____innerException_4)); }
	inline Exception_t * get__innerException_4() const { return ____innerException_4; }
	inline Exception_t ** get_address_of__innerException_4() { return &____innerException_4; }
	inline void set__innerException_4(Exception_t * value)
	{
		____innerException_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____innerException_4), (void*)value);
	}

	inline static int32_t get_offset_of__helpURL_5() { return static_cast<int32_t>(offsetof(Exception_t, ____helpURL_5)); }
	inline String_t* get__helpURL_5() const { return ____helpURL_5; }
	inline String_t** get_address_of__helpURL_5() { return &____helpURL_5; }
	inline void set__helpURL_5(String_t* value)
	{
		____helpURL_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____helpURL_5), (void*)value);
	}

	inline static int32_t get_offset_of__stackTrace_6() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTrace_6)); }
	inline RuntimeObject * get__stackTrace_6() const { return ____stackTrace_6; }
	inline RuntimeObject ** get_address_of__stackTrace_6() { return &____stackTrace_6; }
	inline void set__stackTrace_6(RuntimeObject * value)
	{
		____stackTrace_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTrace_6), (void*)value);
	}

	inline static int32_t get_offset_of__stackTraceString_7() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTraceString_7)); }
	inline String_t* get__stackTraceString_7() const { return ____stackTraceString_7; }
	inline String_t** get_address_of__stackTraceString_7() { return &____stackTraceString_7; }
	inline void set__stackTraceString_7(String_t* value)
	{
		____stackTraceString_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTraceString_7), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackTraceString_8() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackTraceString_8)); }
	inline String_t* get__remoteStackTraceString_8() const { return ____remoteStackTraceString_8; }
	inline String_t** get_address_of__remoteStackTraceString_8() { return &____remoteStackTraceString_8; }
	inline void set__remoteStackTraceString_8(String_t* value)
	{
		____remoteStackTraceString_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____remoteStackTraceString_8), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackIndex_9() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackIndex_9)); }
	inline int32_t get__remoteStackIndex_9() const { return ____remoteStackIndex_9; }
	inline int32_t* get_address_of__remoteStackIndex_9() { return &____remoteStackIndex_9; }
	inline void set__remoteStackIndex_9(int32_t value)
	{
		____remoteStackIndex_9 = value;
	}

	inline static int32_t get_offset_of__dynamicMethods_10() { return static_cast<int32_t>(offsetof(Exception_t, ____dynamicMethods_10)); }
	inline RuntimeObject * get__dynamicMethods_10() const { return ____dynamicMethods_10; }
	inline RuntimeObject ** get_address_of__dynamicMethods_10() { return &____dynamicMethods_10; }
	inline void set__dynamicMethods_10(RuntimeObject * value)
	{
		____dynamicMethods_10 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____dynamicMethods_10), (void*)value);
	}

	inline static int32_t get_offset_of__HResult_11() { return static_cast<int32_t>(offsetof(Exception_t, ____HResult_11)); }
	inline int32_t get__HResult_11() const { return ____HResult_11; }
	inline int32_t* get_address_of__HResult_11() { return &____HResult_11; }
	inline void set__HResult_11(int32_t value)
	{
		____HResult_11 = value;
	}

	inline static int32_t get_offset_of__source_12() { return static_cast<int32_t>(offsetof(Exception_t, ____source_12)); }
	inline String_t* get__source_12() const { return ____source_12; }
	inline String_t** get_address_of__source_12() { return &____source_12; }
	inline void set__source_12(String_t* value)
	{
		____source_12 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____source_12), (void*)value);
	}

	inline static int32_t get_offset_of__safeSerializationManager_13() { return static_cast<int32_t>(offsetof(Exception_t, ____safeSerializationManager_13)); }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * get__safeSerializationManager_13() const { return ____safeSerializationManager_13; }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F ** get_address_of__safeSerializationManager_13() { return &____safeSerializationManager_13; }
	inline void set__safeSerializationManager_13(SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * value)
	{
		____safeSerializationManager_13 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____safeSerializationManager_13), (void*)value);
	}

	inline static int32_t get_offset_of_captured_traces_14() { return static_cast<int32_t>(offsetof(Exception_t, ___captured_traces_14)); }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* get_captured_traces_14() const { return ___captured_traces_14; }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971** get_address_of_captured_traces_14() { return &___captured_traces_14; }
	inline void set_captured_traces_14(StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* value)
	{
		___captured_traces_14 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___captured_traces_14), (void*)value);
	}

	inline static int32_t get_offset_of_native_trace_ips_15() { return static_cast<int32_t>(offsetof(Exception_t, ___native_trace_ips_15)); }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* get_native_trace_ips_15() const { return ___native_trace_ips_15; }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6** get_address_of_native_trace_ips_15() { return &___native_trace_ips_15; }
	inline void set_native_trace_ips_15(IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* value)
	{
		___native_trace_ips_15 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___native_trace_ips_15), (void*)value);
	}
};

struct Exception_t_StaticFields
{
public:
	// System.Object System.Exception::s_EDILock
	RuntimeObject * ___s_EDILock_0;

public:
	inline static int32_t get_offset_of_s_EDILock_0() { return static_cast<int32_t>(offsetof(Exception_t_StaticFields, ___s_EDILock_0)); }
	inline RuntimeObject * get_s_EDILock_0() const { return ___s_EDILock_0; }
	inline RuntimeObject ** get_address_of_s_EDILock_0() { return &___s_EDILock_0; }
	inline void set_s_EDILock_0(RuntimeObject * value)
	{
		___s_EDILock_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___s_EDILock_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Exception
struct Exception_t_marshaled_pinvoke
{
	char* ____className_1;
	char* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_pinvoke* ____innerException_4;
	char* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	char* ____stackTraceString_7;
	char* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	char* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};
// Native definition for COM marshalling of System.Exception
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className_1;
	Il2CppChar* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_com* ____innerException_4;
	Il2CppChar* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	Il2CppChar* ____stackTraceString_7;
	Il2CppChar* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	Il2CppChar* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};

// UnityEngine.RuntimePlatform
struct RuntimePlatform_tB8798C800FD9810C0FE2B7D2F2A0A3979D239065 
{
public:
	// System.Int32 UnityEngine.RuntimePlatform::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(RuntimePlatform_tB8798C800FD9810C0FE2B7D2F2A0A3979D239065, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Ump.Api.ConsentDebugSettings
struct ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Api.DebugGeography GoogleMobileAds.Ump.Api.ConsentDebugSettings::DebugGeography
	int32_t ___DebugGeography_0;
	// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Ump.Api.ConsentDebugSettings::TestDeviceHashedIds
	List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___TestDeviceHashedIds_1;

public:
	inline static int32_t get_offset_of_DebugGeography_0() { return static_cast<int32_t>(offsetof(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0, ___DebugGeography_0)); }
	inline int32_t get_DebugGeography_0() const { return ___DebugGeography_0; }
	inline int32_t* get_address_of_DebugGeography_0() { return &___DebugGeography_0; }
	inline void set_DebugGeography_0(int32_t value)
	{
		___DebugGeography_0 = value;
	}

	inline static int32_t get_offset_of_TestDeviceHashedIds_1() { return static_cast<int32_t>(offsetof(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0, ___TestDeviceHashedIds_1)); }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * get_TestDeviceHashedIds_1() const { return ___TestDeviceHashedIds_1; }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 ** get_address_of_TestDeviceHashedIds_1() { return &___TestDeviceHashedIds_1; }
	inline void set_TestDeviceHashedIds_1(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * value)
	{
		___TestDeviceHashedIds_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TestDeviceHashedIds_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Android.ConsentFormClient
struct ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA  : public AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF
{
public:
	// GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener GoogleMobileAds.Ump.Android.ConsentFormClient::_onSuccess
	OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * ____onSuccess_4;
	// GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener GoogleMobileAds.Ump.Android.ConsentFormClient::_onFailure
	OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * ____onFailure_5;
	// GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener GoogleMobileAds.Ump.Android.ConsentFormClient::_onDismissed
	OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84 * ____onDismissed_6;
	// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.ConsentFormClient::_activity
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ____activity_8;
	// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.ConsentFormClient::_userMessagingPlatformClass
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ____userMessagingPlatformClass_9;
	// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.ConsentFormClient::_consentForm
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ____consentForm_10;
	// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.ConsentFormClient::_unityConsentForm
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ____unityConsentForm_11;
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Android.ConsentFormClient::_onConsentFormDismissed
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ____onConsentFormDismissed_12;

public:
	inline static int32_t get_offset_of__onSuccess_4() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____onSuccess_4)); }
	inline OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * get__onSuccess_4() const { return ____onSuccess_4; }
	inline OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 ** get_address_of__onSuccess_4() { return &____onSuccess_4; }
	inline void set__onSuccess_4(OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * value)
	{
		____onSuccess_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onSuccess_4), (void*)value);
	}

	inline static int32_t get_offset_of__onFailure_5() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____onFailure_5)); }
	inline OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * get__onFailure_5() const { return ____onFailure_5; }
	inline OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D ** get_address_of__onFailure_5() { return &____onFailure_5; }
	inline void set__onFailure_5(OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * value)
	{
		____onFailure_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onFailure_5), (void*)value);
	}

	inline static int32_t get_offset_of__onDismissed_6() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____onDismissed_6)); }
	inline OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84 * get__onDismissed_6() const { return ____onDismissed_6; }
	inline OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84 ** get_address_of__onDismissed_6() { return &____onDismissed_6; }
	inline void set__onDismissed_6(OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84 * value)
	{
		____onDismissed_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onDismissed_6), (void*)value);
	}

	inline static int32_t get_offset_of__activity_8() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____activity_8)); }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * get__activity_8() const { return ____activity_8; }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E ** get_address_of__activity_8() { return &____activity_8; }
	inline void set__activity_8(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * value)
	{
		____activity_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____activity_8), (void*)value);
	}

	inline static int32_t get_offset_of__userMessagingPlatformClass_9() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____userMessagingPlatformClass_9)); }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * get__userMessagingPlatformClass_9() const { return ____userMessagingPlatformClass_9; }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E ** get_address_of__userMessagingPlatformClass_9() { return &____userMessagingPlatformClass_9; }
	inline void set__userMessagingPlatformClass_9(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * value)
	{
		____userMessagingPlatformClass_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____userMessagingPlatformClass_9), (void*)value);
	}

	inline static int32_t get_offset_of__consentForm_10() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____consentForm_10)); }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * get__consentForm_10() const { return ____consentForm_10; }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E ** get_address_of__consentForm_10() { return &____consentForm_10; }
	inline void set__consentForm_10(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * value)
	{
		____consentForm_10 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____consentForm_10), (void*)value);
	}

	inline static int32_t get_offset_of__unityConsentForm_11() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____unityConsentForm_11)); }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * get__unityConsentForm_11() const { return ____unityConsentForm_11; }
	inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E ** get_address_of__unityConsentForm_11() { return &____unityConsentForm_11; }
	inline void set__unityConsentForm_11(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * value)
	{
		____unityConsentForm_11 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____unityConsentForm_11), (void*)value);
	}

	inline static int32_t get_offset_of__onConsentFormDismissed_12() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA, ____onConsentFormDismissed_12)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get__onConsentFormDismissed_12() const { return ____onConsentFormDismissed_12; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of__onConsentFormDismissed_12() { return &____onConsentFormDismissed_12; }
	inline void set__onConsentFormDismissed_12(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		____onConsentFormDismissed_12 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onConsentFormDismissed_12), (void*)value);
	}
};

struct ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_StaticFields
{
public:
	// GoogleMobileAds.Ump.Android.ConsentFormClient GoogleMobileAds.Ump.Android.ConsentFormClient::_instance
	ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * ____instance_7;

public:
	inline static int32_t get_offset_of__instance_7() { return static_cast<int32_t>(offsetof(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_StaticFields, ____instance_7)); }
	inline ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * get__instance_7() const { return ____instance_7; }
	inline ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA ** get_address_of__instance_7() { return &____instance_7; }
	inline void set__instance_7(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * value)
	{
		____instance_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____instance_7), (void*)value);
	}
};


// System.MulticastDelegate
struct MulticastDelegate_t  : public Delegate_t
{
public:
	// System.Delegate[] System.MulticastDelegate::delegates
	DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* ___delegates_11;

public:
	inline static int32_t get_offset_of_delegates_11() { return static_cast<int32_t>(offsetof(MulticastDelegate_t, ___delegates_11)); }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* get_delegates_11() const { return ___delegates_11; }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8** get_address_of_delegates_11() { return &___delegates_11; }
	inline void set_delegates_11(DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* value)
	{
		___delegates_11 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___delegates_11), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates_11;
};
// Native definition for COM marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates_11;
};

// GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener
struct OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84  : public AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF
{
public:
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::_onConsentFormDismissedAction
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ____onConsentFormDismissedAction_4;

public:
	inline static int32_t get_offset_of__onConsentFormDismissedAction_4() { return static_cast<int32_t>(offsetof(OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84, ____onConsentFormDismissedAction_4)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get__onConsentFormDismissedAction_4() const { return ____onConsentFormDismissedAction_4; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of__onConsentFormDismissedAction_4() { return &____onConsentFormDismissedAction_4; }
	inline void set__onConsentFormDismissedAction_4(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		____onConsentFormDismissedAction_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onConsentFormDismissedAction_4), (void*)value);
	}
};


// GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener
struct OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D  : public AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF
{
public:
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::_onConsentFormLoadFailureAction
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ____onConsentFormLoadFailureAction_4;

public:
	inline static int32_t get_offset_of__onConsentFormLoadFailureAction_4() { return static_cast<int32_t>(offsetof(OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D, ____onConsentFormLoadFailureAction_4)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get__onConsentFormLoadFailureAction_4() const { return ____onConsentFormLoadFailureAction_4; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of__onConsentFormLoadFailureAction_4() { return &____onConsentFormLoadFailureAction_4; }
	inline void set__onConsentFormLoadFailureAction_4(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		____onConsentFormLoadFailureAction_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onConsentFormLoadFailureAction_4), (void*)value);
	}
};


// GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener
struct OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42  : public AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF
{
public:
	// System.Action`1<UnityEngine.AndroidJavaObject> GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::_onConsentFormLoadSuccessAction
	Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * ____onConsentFormLoadSuccessAction_4;

public:
	inline static int32_t get_offset_of__onConsentFormLoadSuccessAction_4() { return static_cast<int32_t>(offsetof(OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42, ____onConsentFormLoadSuccessAction_4)); }
	inline Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * get__onConsentFormLoadSuccessAction_4() const { return ____onConsentFormLoadSuccessAction_4; }
	inline Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B ** get_address_of__onConsentFormLoadSuccessAction_4() { return &____onConsentFormLoadSuccessAction_4; }
	inline void set__onConsentFormLoadSuccessAction_4(Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * value)
	{
		____onConsentFormLoadSuccessAction_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onConsentFormLoadSuccessAction_4), (void*)value);
	}
};


// GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener
struct OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1  : public AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF
{
public:
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::_onConsentInfoUpdateFailureAction
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ____onConsentInfoUpdateFailureAction_4;

public:
	inline static int32_t get_offset_of__onConsentInfoUpdateFailureAction_4() { return static_cast<int32_t>(offsetof(OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1, ____onConsentInfoUpdateFailureAction_4)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get__onConsentInfoUpdateFailureAction_4() const { return ____onConsentInfoUpdateFailureAction_4; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of__onConsentInfoUpdateFailureAction_4() { return &____onConsentInfoUpdateFailureAction_4; }
	inline void set__onConsentInfoUpdateFailureAction_4(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		____onConsentInfoUpdateFailureAction_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onConsentInfoUpdateFailureAction_4), (void*)value);
	}
};


// GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener
struct OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7  : public AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF
{
public:
	// System.Action GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::_onConsentInfoUpdateSuccessAction
	Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ____onConsentInfoUpdateSuccessAction_4;

public:
	inline static int32_t get_offset_of__onConsentInfoUpdateSuccessAction_4() { return static_cast<int32_t>(offsetof(OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7, ____onConsentInfoUpdateSuccessAction_4)); }
	inline Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * get__onConsentInfoUpdateSuccessAction_4() const { return ____onConsentInfoUpdateSuccessAction_4; }
	inline Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 ** get_address_of__onConsentInfoUpdateSuccessAction_4() { return &____onConsentInfoUpdateSuccessAction_4; }
	inline void set__onConsentInfoUpdateSuccessAction_4(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * value)
	{
		____onConsentInfoUpdateSuccessAction_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____onConsentInfoUpdateSuccessAction_4), (void*)value);
	}
};


// System.SystemException
struct SystemException_tC551B4D6EE3772B5F32C71EE8C719F4B43ECCC62  : public Exception_t
{
public:

public:
};


// System.Action`1<UnityEngine.AndroidJavaObject>
struct Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B  : public MulticastDelegate_t
{
public:

public:
};


// System.Action`1<GoogleMobileAds.Ump.Api.FormError>
struct Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD  : public MulticastDelegate_t
{
public:

public:
};


// System.Action
struct Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6  : public MulticastDelegate_t
{
public:

public:
};


// UnityEngine.AndroidJavaRunnable
struct AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60  : public MulticastDelegate_t
{
public:

public:
};


// System.InvalidOperationException
struct InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB  : public SystemException_tC551B4D6EE3772B5F32C71EE8C719F4B43ECCC62
{
public:

public:
};

#ifdef __clang__
#pragma clang diagnostic pop
#endif
// System.Object[]
struct ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) RuntimeObject * m_Items[1];

public:
	inline RuntimeObject * GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject ** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject * value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject * GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject ** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject * value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


// !!0 UnityEngine.AndroidJavaObject::GetStatic<System.Object>(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject * AndroidJavaObject_GetStatic_TisRuntimeObject_mEC743ECF275CB896DE039A9FC1E5672B30C8B3D0_gshared (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___fieldName0, const RuntimeMethod* method);
// System.Void System.Action`1<System.Object>::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mA671E933C9D3DAE4E3F71D34FDDA971739618158_gshared (Action_1_tD9663D9715FAA4E62035CFCF1AD4D094EE7872DC * __this, RuntimeObject * ___object0, intptr_t ___method1, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::Call<System.Int32>(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_gshared (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::Call<System.Object>(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject * AndroidJavaObject_Call_TisRuntimeObject_mB802C2A6B0365A1670402D283EB7FA241C326B0E_gshared (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
// System.Void System.Action`1<System.Object>::Invoke(!0)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1_Invoke_m587509C88BB83721D7918D89DF07606BB752D744_gshared (Action_1_tD9663D9715FAA4E62035CFCF1AD4D094EE7872DC * __this, RuntimeObject * ___obj0, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::CallStatic<System.Object>(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject * AndroidJavaObject_CallStatic_TisRuntimeObject_m29BD05B7A29F937D71B746DFFE889B90E1142509_gshared (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::Call<System.Boolean>(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_gshared (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
// System.Int32 System.Collections.Generic.List`1<System.Object>::get_Count()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m5D847939ABB9A78203B062CAFFE975792174D00F_gshared_inline (List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * __this, const RuntimeMethod* method);
// System.Collections.Generic.List`1/Enumerator<!0> System.Collections.Generic.List`1<System.Object>::GetEnumerator()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6  List_1_GetEnumerator_m1739A5E25DF502A6984F9B98CFCAC2D3FABCF233_gshared (List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * __this, const RuntimeMethod* method);
// !0 System.Collections.Generic.List`1/Enumerator<System.Object>::get_Current()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject * Enumerator_get_Current_m9C4EBBD2108B51885E750F927D7936290C8E20EE_gshared_inline (Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6 * __this, const RuntimeMethod* method);
// System.Boolean System.Collections.Generic.List`1/Enumerator<System.Object>::MoveNext()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_m2E56233762839CE55C67E00AC8DD3D4D3F6C0DF0_gshared (Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6 * __this, const RuntimeMethod* method);
// System.Void System.Collections.Generic.List`1/Enumerator<System.Object>::Dispose()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mCFB225D9E5E597A1CC8F958E53BEA1367D8AC7B8_gshared (Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6 * __this, const RuntimeMethod* method);

// System.Void UnityEngine.AndroidJavaProxy::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238 (AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF * __this, String_t* ___javaInterface0, const RuntimeMethod* method);
// UnityEngine.RuntimePlatform UnityEngine.Application::get_platform()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4 (const RuntimeMethod* method);
// System.Void UnityEngine.AndroidJavaClass::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541 (AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * __this, String_t* ___className0, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::GetStatic<UnityEngine.AndroidJavaObject>(System.String)
inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153 (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___fieldName0, const RuntimeMethod* method)
{
	return ((  AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * (*) (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, String_t*, const RuntimeMethod*))AndroidJavaObject_GetStatic_TisRuntimeObject_mEC743ECF275CB896DE039A9FC1E5672B30C8B3D0_gshared)(__this, ___fieldName0, method);
}
// System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AndroidJavaObject__ctor_m6146DBD19BCFFDB3D4F42C8D38491F354B58B001 (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___className0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m2481275D945C664C823EBE378B801BD39050CA8D (U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * __this, const RuntimeMethod* method);
// System.Void System.Action`1<UnityEngine.AndroidJavaObject>::.ctor(System.Object,System.IntPtr)
inline void Action_1__ctor_m0CC0E4D1D61626822ACA46CAE3121E0E9FE989C9 (Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * __this, RuntimeObject * ___object0, intptr_t ___method1, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B *, RuntimeObject *, intptr_t, const RuntimeMethod*))Action_1__ctor_mA671E933C9D3DAE4E3F71D34FDDA971739618158_gshared)(__this, ___object0, ___method1, method);
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::.ctor(System.Action`1<UnityEngine.AndroidJavaObject>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormLoadSuccessListener__ctor_m7D259C6D3148F10E17344FFB032D87D6144F6806 (OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * __this, Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * ___onConsentFormLoadSuccessAction0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormLoadFailureListener__ctor_mE6F632602335283E28633423C49A21306A6051AB (OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onConsentFormLoadFailureAction0, const RuntimeMethod* method);
// System.Void UnityEngine.AndroidJavaRunnable::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727 (AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 * __this, RuntimeObject * ___object0, intptr_t ___method1, const RuntimeMethod* method);
// System.Void UnityEngine.AndroidJavaObject::Call(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::Call<System.Int32>(System.String,System.Object[])
inline int32_t AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method)
{
	return ((  int32_t (*) (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, String_t*, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*, const RuntimeMethod*))AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_gshared)(__this, ___methodName0, ___args1, method);
}
// !!0 UnityEngine.AndroidJavaObject::Call<System.String>(System.String,System.Object[])
inline String_t* AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method)
{
	return ((  String_t* (*) (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, String_t*, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*, const RuntimeMethod*))AndroidJavaObject_Call_TisRuntimeObject_mB802C2A6B0365A1670402D283EB7FA241C326B0E_gshared)(__this, ___methodName0, ___args1, method);
}
// System.Void GoogleMobileAds.Ump.Api.FormError::.ctor(System.Int32,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, int32_t ___errorCode0, String_t* ___message1, const RuntimeMethod* method);
// System.Void System.Action`1<GoogleMobileAds.Ump.Api.FormError>::Invoke(!0)
inline void Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827 (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * __this, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___obj0, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *, const RuntimeMethod*))Action_1_Invoke_m587509C88BB83721D7918D89DF07606BB752D744_gshared)(__this, ___obj0, method);
}
// System.Void UnityEngine.Debug::LogError(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogError_m8850D65592770A364D494025FF3A73E8D4D70485 (RuntimeObject * ___message0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient__ctor_m02CF8A6E9764BF469C72F734FD1D1D429EAAE39B (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, const RuntimeMethod* method);
// System.String System.String::Concat(System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m4B4AB72618348C5DFBFBA8DED84B9E2EBDB55E1B (String_t* ___str00, String_t* ___str11, const RuntimeMethod* method);
// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405 (RuntimeObject * __this, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::CallStatic<UnityEngine.AndroidJavaObject>(System.String,System.Object[])
inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * AndroidJavaObject_CallStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mAD48C38D66AB67D0F0274D195F4A99CB7AB589F2 (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method)
{
	return ((  AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * (*) (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, String_t*, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*, const RuntimeMethod*))AndroidJavaObject_CallStatic_TisRuntimeObject_m29BD05B7A29F937D71B746DFFE889B90E1142509_gshared)(__this, ___methodName0, ___args1, method);
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::.ctor(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentInfoUpdateSuccessListener__ctor_mB868212ADBE76EA0C1807201067C5801C333A44A (OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * __this, Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ___onConsentInfoUpdateSuccessAction0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentInfoUpdateFailureListener__ctor_mAA13AA008BA8709FC4035B6F0ECA7CFEEA1CC370 (OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onConsentInfoUpdateFailureAction0, const RuntimeMethod* method);
// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.Utils::GetConsentRequestParametersJavaObject(GoogleMobileAds.Ump.Api.ConsentRequestParameters,UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * Utils_GetConsentRequestParametersJavaObject_m9EACC27838E61BF36A6BD835CDB189B3BA0CEA4B (ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * ___consentRequestParameters0, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___activity1, const RuntimeMethod* method);
// System.String System.String::Concat(System.String,System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44 (String_t* ___str00, String_t* ___str11, String_t* ___str22, const RuntimeMethod* method);
// System.Void System.InvalidOperationException::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * __this, String_t* ___message0, const RuntimeMethod* method);
// !!0 UnityEngine.AndroidJavaObject::Call<UnityEngine.AndroidJavaObject>(System.String,System.Object[])
inline AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412 (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method)
{
	return ((  AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * (*) (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, String_t*, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*, const RuntimeMethod*))AndroidJavaObject_Call_TisRuntimeObject_mB802C2A6B0365A1670402D283EB7FA241C326B0E_gshared)(__this, ___methodName0, ___args1, method);
}
// !!0 UnityEngine.AndroidJavaObject::Call<System.Boolean>(System.String,System.Object[])
inline bool AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method)
{
	return ((  bool (*) (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, String_t*, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*, const RuntimeMethod*))AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_gshared)(__this, ___methodName0, ___args1, method);
}
// System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentInformationClient__ctor_m77D7E2D66558FBB1A82C44FB7534567AB1930B18 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method);
// System.Void UnityEngine.Debug::Log(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_Log_mC26E5AD0D8D156C7FFD173AA15827F69225E9DB8 (RuntimeObject * ___message0, const RuntimeMethod* method);
// System.Void System.Action`1<UnityEngine.AndroidJavaObject>::Invoke(!0)
inline void Action_1_Invoke_mCA8DB3C200C032B1F12A4EF6C54B0D3DC27DA5D6 (Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___obj0, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B *, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *, const RuntimeMethod*))Action_1_Invoke_m587509C88BB83721D7918D89DF07606BB752D744_gshared)(__this, ___obj0, method);
}
// System.Void System.Action::Invoke()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_Invoke_m3FFA5BE3D64F0FF8E1E1CB6F953913FADB5EB89E (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * __this, const RuntimeMethod* method);
// GoogleMobileAds.Ump.Android.ConsentFormClient GoogleMobileAds.Ump.Android.ConsentFormClient::get_Instance()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * ConsentFormClient_get_Instance_mD32B42448DBC28EC81D836787244FEA50C081944_inline (const RuntimeMethod* method);
// GoogleMobileAds.Ump.Android.ConsentInformationClient GoogleMobileAds.Ump.Android.ConsentInformationClient::get_Instance()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * ConsentInformationClient_get_Instance_m2628C7372920D3887990D2F1949C5A3925253907_inline (const RuntimeMethod* method);
// System.Int32 System.Collections.Generic.List`1<System.String>::get_Count()
inline int32_t List_1_get_Count_m199DB87BCE947106FBA38E19FDFE80CB65B61144_inline (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *, const RuntimeMethod*))List_1_get_Count_m5D847939ABB9A78203B062CAFFE975792174D00F_gshared_inline)(__this, method);
}
// System.Collections.Generic.List`1/Enumerator<!0> System.Collections.Generic.List`1<System.String>::GetEnumerator()
inline Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B  List_1_GetEnumerator_m35388695226DE2F7B0B5D0A07016716D6AD9CAEF (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * __this, const RuntimeMethod* method)
{
	return ((  Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B  (*) (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *, const RuntimeMethod*))List_1_GetEnumerator_m1739A5E25DF502A6984F9B98CFCAC2D3FABCF233_gshared)(__this, method);
}
// !0 System.Collections.Generic.List`1/Enumerator<System.String>::get_Current()
inline String_t* Enumerator_get_Current_m9B0E356FA9FCFB9B1BECC6D7C5DF5C03309251AA_inline (Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B * __this, const RuntimeMethod* method)
{
	return ((  String_t* (*) (Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B *, const RuntimeMethod*))Enumerator_get_Current_m9C4EBBD2108B51885E750F927D7936290C8E20EE_gshared_inline)(__this, method);
}
// System.Boolean System.Collections.Generic.List`1/Enumerator<System.String>::MoveNext()
inline bool Enumerator_MoveNext_mCE70417061695048D84E473D50556E46B8630F54 (Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B * __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B *, const RuntimeMethod*))Enumerator_MoveNext_m2E56233762839CE55C67E00AC8DD3D4D3F6C0DF0_gshared)(__this, method);
}
// System.Void System.Collections.Generic.List`1/Enumerator<System.String>::Dispose()
inline void Enumerator_Dispose_m65A91D17CADA79F187F4D68980A9C8640B6C9FC7 (Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B * __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B *, const RuntimeMethod*))Enumerator_Dispose_mCFB225D9E5E597A1CC8F958E53BEA1367D8AC7B8_gshared)(__this, method);
}
// System.Void UnityEngine.AndroidJavaObject::CallStatic(System.String,System.Object[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AndroidJavaObject_CallStatic_m5A97968767E1603C021023809276443ED24577FB (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * __this, String_t* ___methodName0, ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* ___args1, const RuntimeMethod* method);
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient__ctor_m02CF8A6E9764BF469C72F734FD1D1D429EAAE39B (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D613657609485AE586A3379BA0E3FC13C1E1078);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral51F15D564436CDC5F4CA0943FE275B8092368A12);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7FD3121F912C0E6F15526759C7D703A870317033);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBE7CF21EBC79CE3BF042EB192A59E7131D2D576B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFB4AE4F77150C3A8E8E4F8B23E734E0C7277B7D9);
		s_Il2CppMethodInitialized = true;
	}
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_0 = NULL;
	{
		IL2CPP_RUNTIME_CLASS_INIT(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238(__this, _stringLiteralBE7CF21EBC79CE3BF042EB192A59E7131D2D576B, /*hidden argument*/NULL);
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0066;
		}
	}
	{
		AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * L_1 = (AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 *)il2cpp_codegen_object_new(AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var);
		AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541(L_1, _stringLiteral4D613657609485AE586A3379BA0E3FC13C1E1078, /*hidden argument*/NULL);
		V_0 = L_1;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = V_0;
		NullCheck(L_2);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_3;
		L_3 = AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153(L_2, _stringLiteralFB4AE4F77150C3A8E8E4F8B23E734E0C7277B7D9, /*hidden argument*/AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153_RuntimeMethod_var);
		__this->set__activity_8(L_3);
		AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * L_4 = (AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 *)il2cpp_codegen_object_new(AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var);
		AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541(L_4, _stringLiteral51F15D564436CDC5F4CA0943FE275B8092368A12, /*hidden argument*/NULL);
		__this->set__userMessagingPlatformClass_9(L_4);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_5 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)2);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = L_5;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_7 = __this->get__activity_8();
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_7);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_7);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_8 = L_6;
		NullCheck(L_8);
		ArrayElementTypeCheck (L_8, __this);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject *)__this);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_9 = (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *)il2cpp_codegen_object_new(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_il2cpp_TypeInfo_var);
		AndroidJavaObject__ctor_m6146DBD19BCFFDB3D4F42C8D38491F354B58B001(L_9, _stringLiteral7FD3121F912C0E6F15526759C7D703A870317033, L_8, /*hidden argument*/NULL);
		__this->set__unityConsentForm_11(L_9);
	}

IL_0066:
	{
		return;
	}
}
// GoogleMobileAds.Ump.Android.ConsentFormClient GoogleMobileAds.Ump.Android.ConsentFormClient::get_Instance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * ConsentFormClient_get_Instance_mD32B42448DBC28EC81D836787244FEA50C081944 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_0 = ((ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_StaticFields*)il2cpp_codegen_static_fields_for(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var))->get__instance_7();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::Load(System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_Load_mC9B97E18982519C56B00BB6315795C32792E106B (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ___onFormLoaded0, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onError1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1__ctor_m0CC0E4D1D61626822ACA46CAE3121E0E9FE989C9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC644795AFB1E0FCDD4A4D5FE30D4EE0F3EBDA497_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m83019FB280AA8BBBF7202F1110851479163653FE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * L_0 = (U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 *)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834_il2cpp_TypeInfo_var);
		U3CLoadU3Ec__AnonStorey0__ctor_m2481275D945C664C823EBE378B801BD39050CA8D(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * L_1 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_2 = ___onFormLoaded0;
		NullCheck(L_1);
		L_1->set_onFormLoaded_0(L_2);
		U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * L_3 = V_0;
		NullCheck(L_3);
		L_3->set_U24this_1(__this);
		U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * L_4 = V_0;
		Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * L_5 = (Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B *)il2cpp_codegen_object_new(Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B_il2cpp_TypeInfo_var);
		Action_1__ctor_m0CC0E4D1D61626822ACA46CAE3121E0E9FE989C9(L_5, L_4, (intptr_t)((intptr_t)U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC644795AFB1E0FCDD4A4D5FE30D4EE0F3EBDA497_RuntimeMethod_var), /*hidden argument*/Action_1__ctor_m0CC0E4D1D61626822ACA46CAE3121E0E9FE989C9_RuntimeMethod_var);
		OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * L_6 = (OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 *)il2cpp_codegen_object_new(OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42_il2cpp_TypeInfo_var);
		OnConsentFormLoadSuccessListener__ctor_m7D259C6D3148F10E17344FFB032D87D6144F6806(L_6, L_5, /*hidden argument*/NULL);
		__this->set__onSuccess_4(L_6);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_7 = ___onError1;
		OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * L_8 = (OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D *)il2cpp_codegen_object_new(OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D_il2cpp_TypeInfo_var);
		OnConsentFormLoadFailureListener__ctor_mE6F632602335283E28633423C49A21306A6051AB(L_8, L_7, /*hidden argument*/NULL);
		__this->set__onFailure_5(L_8);
		int32_t L_9;
		L_9 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_9) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0068;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_10 = __this->get__activity_8();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_11 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_12 = L_11;
		U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * L_13 = V_0;
		AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 * L_14 = (AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 *)il2cpp_codegen_object_new(AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727(L_14, L_13, (intptr_t)((intptr_t)U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m83019FB280AA8BBBF7202F1110851479163653FE_RuntimeMethod_var), /*hidden argument*/NULL);
		NullCheck(L_12);
		ArrayElementTypeCheck (L_12, L_14);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_14);
		NullCheck(L_10);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_10, _stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D, L_12, /*hidden argument*/NULL);
	}

IL_0068:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_Show_m97B631BFEEF9B147D63B37845DCA575CA74D1BAD (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_U3CShowU3Em__0_m21708BA8AE24D8FA97F7B8BE9A0EF4A2F02D606D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = ___onDismissed0;
		__this->set__onConsentFormDismissed_12(L_0);
		int32_t L_1;
		L_1 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0038;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = __this->get__activity_8();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_4 = L_3;
		AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 * L_5 = (AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 *)il2cpp_codegen_object_new(AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727(L_5, __this, (intptr_t)((intptr_t)ConsentFormClient_U3CShowU3Em__0_m21708BA8AE24D8FA97F7B8BE9A0EF4A2F02D606D_RuntimeMethod_var), /*hidden argument*/NULL);
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, L_5);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_5);
		NullCheck(L_2);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_2, _stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D, L_4, /*hidden argument*/NULL);
	}

IL_0038:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_LoadAndShowConsentFormIfRequired_m4409238478D27134BBD0683FFEAF69483B723A56 (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_mDD267BEE3AD41D6B78540E8E2C2C95C84BA3E367_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = ___onDismissed0;
		__this->set__onConsentFormDismissed_12(L_0);
		int32_t L_1;
		L_1 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0038;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = __this->get__activity_8();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_4 = L_3;
		AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 * L_5 = (AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 *)il2cpp_codegen_object_new(AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727(L_5, __this, (intptr_t)((intptr_t)ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_mDD267BEE3AD41D6B78540E8E2C2C95C84BA3E367_RuntimeMethod_var), /*hidden argument*/NULL);
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, L_5);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_5);
		NullCheck(L_2);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_2, _stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D, L_4, /*hidden argument*/NULL);
	}

IL_0038:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::ShowPrivacyOptionsForm(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_ShowPrivacyOptionsForm_m4B58A41B3EC1B1A210E25624A1D883EB0F979AFF (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m1569F1485B135B4E9B7466B4A95A10A35FE1A703_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = ___onDismissed0;
		__this->set__onConsentFormDismissed_12(L_0);
		int32_t L_1;
		L_1 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0038;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = __this->get__activity_8();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_4 = L_3;
		AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 * L_5 = (AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60 *)il2cpp_codegen_object_new(AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_il2cpp_TypeInfo_var);
		AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727(L_5, __this, (intptr_t)((intptr_t)ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m1569F1485B135B4E9B7466B4A95A10A35FE1A703_RuntimeMethod_var), /*hidden argument*/NULL);
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, L_5);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_5);
		NullCheck(L_2);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_2, _stringLiteral7D5D6BBF8281151C9F5F57DE5D5BABB7140A651D, L_4, /*hidden argument*/NULL);
	}

IL_0038:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::onConsentFormDismissed(UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_onConsentFormDismissed_m135CB7979C20ADBB70E82CC9173DBB3A6C2764DD (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6E4657CA6250CD99B3E629C68F25D97249F44C22);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006);
		s_Il2CppMethodInitialized = true;
	}
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * V_0 = NULL;
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * G_B4_0 = NULL;
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = __this->get__onConsentFormDismissed_12();
		if (!L_0)
		{
			goto IL_0057;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = ___error0;
		if (L_1)
		{
			goto IL_0017;
		}
	}
	{
		G_B4_0 = ((FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)(NULL));
		goto IL_003e;
	}

IL_0017:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_2);
		int32_t L_4;
		L_4 = AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A(L_2, _stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006, L_3, /*hidden argument*/AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_5 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_5);
		String_t* L_7;
		L_7 = AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE(L_5, _stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998, L_6, /*hidden argument*/AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_8 = (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)il2cpp_codegen_object_new(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A(L_8, L_4, L_7, /*hidden argument*/NULL);
		G_B4_0 = L_8;
	}

IL_003e:
	{
		V_0 = G_B4_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_9 = __this->get__onConsentFormDismissed_12();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_10 = V_0;
		NullCheck(L_9);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_9, L_10, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		__this->set__onConsentFormDismissed_12((Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *)NULL);
		goto IL_0061;
	}

IL_0057:
	{
		IL2CPP_RUNTIME_CLASS_INIT(Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		Debug_LogError_m8850D65592770A364D494025FF3A73E8D4D70485(_stringLiteral6E4657CA6250CD99B3E629C68F25D97249F44C22, /*hidden argument*/NULL);
	}

IL_0061:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient__cctor_mA8BDAAFB528B71A411FCABF5A4638BCE51D93C7B (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_0 = (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA *)il2cpp_codegen_object_new(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		ConsentFormClient__ctor_m02CF8A6E9764BF469C72F734FD1D1D429EAAE39B(L_0, /*hidden argument*/NULL);
		((ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_StaticFields*)il2cpp_codegen_static_fields_for(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var))->set__instance_7(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<Show>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_U3CShowU3Em__0_m21708BA8AE24D8FA97F7B8BE9A0EF4A2F02D606D (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral42737D67B57D1AED1E18C475494BB867AD0AA192);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t * V_0 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	il2cpp::utils::ExceptionSupportStack<int32_t, 2> __leave_targets;

IL_0000:
	try
	{ // begin try (depth: 1)
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_0 = __this->get__unityConsentForm_11();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_1 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = L_1;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_3 = __this->get__consentForm_10();
		NullCheck(L_2);
		ArrayElementTypeCheck (L_2, L_3);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_3);
		NullCheck(L_0);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_0, _stringLiteral42737D67B57D1AED1E18C475494BB867AD0AA192, L_2, /*hidden argument*/NULL);
		goto IL_003f;
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_0024;
		}
		throw e;
	}

CATCH_0024:
	{ // begin catch(System.Exception)
		V_0 = ((Exception_t *)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t *));
		Exception_t * L_4 = V_0;
		NullCheck(L_4);
		String_t* L_5;
		L_5 = VirtFuncInvoker0< String_t* >::Invoke(9 /* System.String System.Exception::get_StackTrace() */, L_4);
		String_t* L_6;
		L_6 = String_Concat_m4B4AB72618348C5DFBFBA8DED84B9E2EBDB55E1B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral110E0A2D48A545A1D32B314C250A0161599F2C9B)), L_5, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var)));
		Debug_LogError_m8850D65592770A364D494025FF3A73E8D4D70485(L_6, /*hidden argument*/NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION();
		goto IL_003f;
	} // end catch (depth: 1)

IL_003f:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<LoadAndShowConsentFormIfRequired>m__1()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_mDD267BEE3AD41D6B78540E8E2C2C95C84BA3E367 (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCAB0CB77D616B0B7305968B4EA47AD2DCF10E56D);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t * V_0 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	il2cpp::utils::ExceptionSupportStack<int32_t, 2> __leave_targets;

IL_0000:
	try
	{ // begin try (depth: 1)
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_0 = __this->get__unityConsentForm_11();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_1 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_0);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_0, _stringLiteralCAB0CB77D616B0B7305968B4EA47AD2DCF10E56D, L_1, /*hidden argument*/NULL);
		goto IL_0036;
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_001b;
		}
		throw e;
	}

CATCH_001b:
	{ // begin catch(System.Exception)
		V_0 = ((Exception_t *)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t *));
		Exception_t * L_2 = V_0;
		NullCheck(L_2);
		String_t* L_3;
		L_3 = VirtFuncInvoker0< String_t* >::Invoke(5 /* System.String System.Exception::get_Message() */, L_2);
		String_t* L_4;
		L_4 = String_Concat_m4B4AB72618348C5DFBFBA8DED84B9E2EBDB55E1B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF78C185BAABEDEF7DF7B666CE38F4A6E6A86F1C0)), L_3, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var)));
		Debug_LogError_m8850D65592770A364D494025FF3A73E8D4D70485(L_4, /*hidden argument*/NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION();
		goto IL_0036;
	} // end catch (depth: 1)

IL_0036:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<ShowPrivacyOptionsForm>m__2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m1569F1485B135B4E9B7466B4A95A10A35FE1A703 (ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral16D6B99AEBC4C36DEE7AF021A4F103C19802543C);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t * V_0 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	il2cpp::utils::ExceptionSupportStack<int32_t, 2> __leave_targets;

IL_0000:
	try
	{ // begin try (depth: 1)
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_0 = __this->get__unityConsentForm_11();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_1 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_0);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_0, _stringLiteral16D6B99AEBC4C36DEE7AF021A4F103C19802543C, L_1, /*hidden argument*/NULL);
		goto IL_0036;
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_001b;
		}
		throw e;
	}

CATCH_001b:
	{ // begin catch(System.Exception)
		V_0 = ((Exception_t *)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t *));
		Exception_t * L_2 = V_0;
		NullCheck(L_2);
		String_t* L_3;
		L_3 = VirtFuncInvoker0< String_t* >::Invoke(5 /* System.String System.Exception::get_Message() */, L_2);
		String_t* L_4;
		L_4 = String_Concat_m4B4AB72618348C5DFBFBA8DED84B9E2EBDB55E1B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralED905D79B2EFE56922FF3A3AE7CE4B8680E9789C)), L_3, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var)));
		Debug_LogError_m8850D65592770A364D494025FF3A73E8D4D70485(L_4, /*hidden argument*/NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION();
		goto IL_0036;
	} // end catch (depth: 1)

IL_0036:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentInformationClient__ctor_m77D7E2D66558FBB1A82C44FB7534567AB1930B18 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_CallStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mAD48C38D66AB67D0F0274D195F4A99CB7AB589F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D613657609485AE586A3379BA0E3FC13C1E1078);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral51F15D564436CDC5F4CA0943FE275B8092368A12);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB3588DE9307F0F69133E3EB2F32C1E05E8719923);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFB4AE4F77150C3A8E8E4F8B23E734E0C7277B7D9);
		s_Il2CppMethodInitialized = true;
	}
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_0 = NULL;
	AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * V_1 = NULL;
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0059;
		}
	}
	{
		AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * L_1 = (AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 *)il2cpp_codegen_object_new(AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var);
		AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541(L_1, _stringLiteral4D613657609485AE586A3379BA0E3FC13C1E1078, /*hidden argument*/NULL);
		V_0 = L_1;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = V_0;
		NullCheck(L_2);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_3;
		L_3 = AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153(L_2, _stringLiteralFB4AE4F77150C3A8E8E4F8B23E734E0C7277B7D9, /*hidden argument*/AndroidJavaObject_GetStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC84C97A7EC20ED712D21107C9FA32E0785021153_RuntimeMethod_var);
		__this->set__activity_4(L_3);
		AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * L_4 = (AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 *)il2cpp_codegen_object_new(AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4_il2cpp_TypeInfo_var);
		AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541(L_4, _stringLiteral51F15D564436CDC5F4CA0943FE275B8092368A12, /*hidden argument*/NULL);
		V_1 = L_4;
		AndroidJavaClass_t52E934B16476D72AA6E4B248F989F2F825EB62D4 * L_5 = V_1;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_7 = L_6;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_8 = __this->get__activity_4();
		NullCheck(L_7);
		ArrayElementTypeCheck (L_7, L_8);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_8);
		NullCheck(L_5);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_9;
		L_9 = AndroidJavaObject_CallStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mAD48C38D66AB67D0F0274D195F4A99CB7AB589F2(L_5, _stringLiteralB3588DE9307F0F69133E3EB2F32C1E05E8719923, L_7, /*hidden argument*/AndroidJavaObject_CallStatic_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mAD48C38D66AB67D0F0274D195F4A99CB7AB589F2_RuntimeMethod_var);
		__this->set__consentInformation_3(L_9);
	}

IL_0059:
	{
		return;
	}
}
// GoogleMobileAds.Ump.Android.ConsentInformationClient GoogleMobileAds.Ump.Android.ConsentInformationClient::get_Instance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * ConsentInformationClient_get_Instance_m2628C7372920D3887990D2F1949C5A3925253907 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * L_0 = ((ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_StaticFields*)il2cpp_codegen_static_fields_for(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var))->get__instance_2();
		return L_0;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentInformationClient_Update_m2CC76384A9425E837F75F9FAB641183078A33F27 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * ___request0, Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ___onConsentInfoUpdateSuccessCallback1, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onConsentInfoUpdateFailureCallback2, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral41B244E6AA749FD04A6C56697A4AFF71754B6BC5);
		s_Il2CppMethodInitialized = true;
	}
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_0 = NULL;
	{
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_0 = ___onConsentInfoUpdateSuccessCallback1;
		OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * L_1 = (OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 *)il2cpp_codegen_object_new(OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7_il2cpp_TypeInfo_var);
		OnConsentInfoUpdateSuccessListener__ctor_mB868212ADBE76EA0C1807201067C5801C333A44A(L_1, L_0, /*hidden argument*/NULL);
		__this->set__onSuccess_0(L_1);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_2 = ___onConsentInfoUpdateFailureCallback2;
		OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * L_3 = (OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 *)il2cpp_codegen_object_new(OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1_il2cpp_TypeInfo_var);
		OnConsentInfoUpdateFailureListener__ctor_mAA13AA008BA8709FC4035B6F0ECA7CFEEA1CC370(L_3, L_2, /*hidden argument*/NULL);
		__this->set__onFailure_1(L_3);
		ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * L_4 = ___request0;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_5 = __this->get__activity_4();
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_6;
		L_6 = Utils_GetConsentRequestParametersJavaObject_m9EACC27838E61BF36A6BD835CDB189B3BA0CEA4B(L_4, L_5, /*hidden argument*/NULL);
		V_0 = L_6;
		int32_t L_7;
		L_7 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_7) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0066;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_8 = __this->get__consentInformation_3();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_9 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)4);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_10 = L_9;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_11 = __this->get__activity_4();
		NullCheck(L_10);
		ArrayElementTypeCheck (L_10, L_11);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_11);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_12 = L_10;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_13 = V_0;
		NullCheck(L_12);
		ArrayElementTypeCheck (L_12, L_13);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject *)L_13);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_14 = L_12;
		OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * L_15 = __this->get__onSuccess_0();
		NullCheck(L_14);
		ArrayElementTypeCheck (L_14, L_15);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject *)L_15);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_16 = L_14;
		OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * L_17 = __this->get__onFailure_1();
		NullCheck(L_16);
		ArrayElementTypeCheck (L_16, L_17);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject *)L_17);
		NullCheck(L_8);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_8, _stringLiteral41B244E6AA749FD04A6C56697A4AFF71754B6BC5, L_16, /*hidden argument*/NULL);
	}

IL_0066:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::Reset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentInformationClient_Reset_m40DD537DE51F4E3E67FF90598C8760C730480D61 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral777AE62A4AAF93B33F90DF3E12489F43ABA14E39);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0027;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = __this->get__consentInformation_3();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_1);
		AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D(L_1, _stringLiteral777AE62A4AAF93B33F90DF3E12489F43ABA14E39, L_2, /*hidden argument*/NULL);
		goto IL_0046;
	}

IL_0027:
	{
		MethodBase_t * L_3;
		L_3 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_Reset_m40DD537DE51F4E3E67FF90598C8760C730480D61_RuntimeMethod_var)));
		NullCheck(L_3);
		String_t* L_4;
		L_4 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_3);
		String_t* L_5;
		L_5 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_6 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_6, L_5, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_Reset_m40DD537DE51F4E3E67FF90598C8760C730480D61_RuntimeMethod_var)));
	}

IL_0046:
	{
		return;
	}
}
// System.Int32 GoogleMobileAds.Ump.Android.ConsentInformationClient::GetConsentStatus()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ConsentInformationClient_GetConsentStatus_mD6C92990E65197BFCAE0F13614F2718B242D9446 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral244940E192616C6FDCE2CD494CC5BE9AA60F5CD8);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0023;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = __this->get__consentInformation_3();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_1);
		int32_t L_3;
		L_3 = AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A(L_1, _stringLiteral244940E192616C6FDCE2CD494CC5BE9AA60F5CD8, L_2, /*hidden argument*/AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		return L_3;
	}

IL_0023:
	{
		MethodBase_t * L_4;
		L_4 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_GetConsentStatus_mD6C92990E65197BFCAE0F13614F2718B242D9446_RuntimeMethod_var)));
		NullCheck(L_4);
		String_t* L_5;
		L_5 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_4);
		String_t* L_6;
		L_6 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_7 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_7, L_6, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_GetConsentStatus_mD6C92990E65197BFCAE0F13614F2718B242D9446_RuntimeMethod_var)));
	}
}
// System.Int32 GoogleMobileAds.Ump.Android.ConsentInformationClient::GetPrivacyOptionsRequirementStatus()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m3C8615C525A07D949F95EFD3AF4A0C5D82DCDC33 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0FB8683E34C0E832D7B6B99C752834988560A605);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC9CD9BE5636542BD3CFAC8C3719D519A618BC894);
		s_Il2CppMethodInitialized = true;
	}
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_0 = NULL;
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0035;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = __this->get__consentInformation_3();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_1);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_3;
		L_3 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_1, _stringLiteral0FB8683E34C0E832D7B6B99C752834988560A605, L_2, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		V_0 = L_3;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_4 = V_0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_5 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_4);
		int32_t L_6;
		L_6 = AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A(L_4, _stringLiteralC9CD9BE5636542BD3CFAC8C3719D519A618BC894, L_5, /*hidden argument*/AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		return L_6;
	}

IL_0035:
	{
		MethodBase_t * L_7;
		L_7 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m3C8615C525A07D949F95EFD3AF4A0C5D82DCDC33_RuntimeMethod_var)));
		NullCheck(L_7);
		String_t* L_8;
		L_8 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_7);
		String_t* L_9;
		L_9 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_8, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_10 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_10, L_9, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_10, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m3C8615C525A07D949F95EFD3AF4A0C5D82DCDC33_RuntimeMethod_var)));
	}
}
// System.Boolean GoogleMobileAds.Ump.Android.ConsentInformationClient::CanRequestAds()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ConsentInformationClient_CanRequestAds_mCAABCD8029F40C78E0167549811E25B623056757 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4AAD8F8A579371AB1C26B13B652DA8BA29CC7926);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0023;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = __this->get__consentInformation_3();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_1);
		bool L_3;
		L_3 = AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A(L_1, _stringLiteral4AAD8F8A579371AB1C26B13B652DA8BA29CC7926, L_2, /*hidden argument*/AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_RuntimeMethod_var);
		return L_3;
	}

IL_0023:
	{
		MethodBase_t * L_4;
		L_4 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_CanRequestAds_mCAABCD8029F40C78E0167549811E25B623056757_RuntimeMethod_var)));
		NullCheck(L_4);
		String_t* L_5;
		L_5 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_4);
		String_t* L_6;
		L_6 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_7 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_7, L_6, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_CanRequestAds_mCAABCD8029F40C78E0167549811E25B623056757_RuntimeMethod_var)));
	}
}
// System.Boolean GoogleMobileAds.Ump.Android.ConsentInformationClient::IsConsentFormAvailable()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ConsentInformationClient_IsConsentFormAvailable_m5B6E6AACDF70DC9925C9AF4ED8C2C7F9281C4D56 (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA815C138A9BCEB494E0655DE56D11FA37230A46B);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0023;
		}
	}
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = __this->get__consentInformation_3();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_1);
		bool L_3;
		L_3 = AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A(L_1, _stringLiteralA815C138A9BCEB494E0655DE56D11FA37230A46B, L_2, /*hidden argument*/AndroidJavaObject_Call_TisBoolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_m705BF7B215A83B7851B19591CE37DA93250C7A8A_RuntimeMethod_var);
		return L_3;
	}

IL_0023:
	{
		MethodBase_t * L_4;
		L_4 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_IsConsentFormAvailable_m5B6E6AACDF70DC9925C9AF4ED8C2C7F9281C4D56_RuntimeMethod_var)));
		NullCheck(L_4);
		String_t* L_5;
		L_5 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_4);
		String_t* L_6;
		L_6 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_7 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_7, L_6, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ConsentInformationClient_IsConsentFormAvailable_m5B6E6AACDF70DC9925C9AF4ED8C2C7F9281C4D56_RuntimeMethod_var)));
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentInformationClient__cctor_m66D1DD7B407E76FC4D3107954F8AF569FFE76449 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * L_0 = (ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE *)il2cpp_codegen_object_new(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		ConsentInformationClient__ctor_m77D7E2D66558FBB1A82C44FB7534567AB1930B18(L_0, /*hidden argument*/NULL);
		((ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_StaticFields*)il2cpp_codegen_static_fields_for(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var))->set__instance_2(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormDismissedListener__ctor_m2BF723DA9FB1F5BA2B7A97299DA7541A12241569 (OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84 * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onConsentFormDismissedAction0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral375FAFE344F4C2DEFCE90CB6F7DAC4DA51729E3F);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238(__this, _stringLiteral375FAFE344F4C2DEFCE90CB6F7DAC4DA51729E3F, /*hidden argument*/NULL);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = ___onConsentFormDismissedAction0;
		__this->set__onConsentFormDismissedAction_4(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::onConsentFormDismissed(UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormDismissedListener_onConsentFormDismissed_m5FBDB7737D17192545F42DF4C82DBF227C89C8F5 (OnConsentFormDismissedListener_tE87D77329235C69ADA9B8F1FD398795185B9BF84 * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB54E8601CF18631B8097B2949990B5D8DD33F197);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006);
		s_Il2CppMethodInitialized = true;
	}
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * V_0 = NULL;
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * G_B5_0 = NULL;
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = __this->get__onConsentFormDismissedAction_4();
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		Debug_Log_mC26E5AD0D8D156C7FFD173AA15827F69225E9DB8(_stringLiteralB54E8601CF18631B8097B2949990B5D8DD33F197, /*hidden argument*/NULL);
		return;
	}

IL_0016:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = ___error0;
		if (L_1)
		{
			goto IL_0022;
		}
	}
	{
		G_B5_0 = ((FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)(NULL));
		goto IL_0049;
	}

IL_0022:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_2);
		int32_t L_4;
		L_4 = AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A(L_2, _stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006, L_3, /*hidden argument*/AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_5 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_5);
		String_t* L_7;
		L_7 = AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE(L_5, _stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998, L_6, /*hidden argument*/AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_8 = (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)il2cpp_codegen_object_new(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A(L_8, L_4, L_7, /*hidden argument*/NULL);
		G_B5_0 = L_8;
	}

IL_0049:
	{
		V_0 = G_B5_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_9 = __this->get__onConsentFormDismissedAction_4();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_10 = V_0;
		NullCheck(L_9);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_9, L_10, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormLoadFailureListener__ctor_mE6F632602335283E28633423C49A21306A6051AB (OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onConsentFormLoadFailureAction0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4AC0D82903EC38AEDFDD6B6F68456C96D5DCD877);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238(__this, _stringLiteral4AC0D82903EC38AEDFDD6B6F68456C96D5DCD877, /*hidden argument*/NULL);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = ___onConsentFormLoadFailureAction0;
		__this->set__onConsentFormLoadFailureAction_4(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::onConsentFormLoadFailure(UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormLoadFailureListener_onConsentFormLoadFailure_m998D74F6B7EED655F50D7F850FC35F29985088FE (OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB5DDAB11F0FE2E649C78FA5A86258A2B3653B3CD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006);
		s_Il2CppMethodInitialized = true;
	}
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * V_0 = NULL;
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * G_B5_0 = NULL;
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = __this->get__onConsentFormLoadFailureAction_4();
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		Debug_Log_mC26E5AD0D8D156C7FFD173AA15827F69225E9DB8(_stringLiteralB5DDAB11F0FE2E649C78FA5A86258A2B3653B3CD, /*hidden argument*/NULL);
		return;
	}

IL_0016:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = ___error0;
		if (L_1)
		{
			goto IL_0022;
		}
	}
	{
		G_B5_0 = ((FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)(NULL));
		goto IL_0049;
	}

IL_0022:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_2);
		int32_t L_4;
		L_4 = AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A(L_2, _stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006, L_3, /*hidden argument*/AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_5 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_5);
		String_t* L_7;
		L_7 = AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE(L_5, _stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998, L_6, /*hidden argument*/AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_8 = (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)il2cpp_codegen_object_new(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A(L_8, L_4, L_7, /*hidden argument*/NULL);
		G_B5_0 = L_8;
	}

IL_0049:
	{
		V_0 = G_B5_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_9 = __this->get__onConsentFormLoadFailureAction_4();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_10 = V_0;
		NullCheck(L_9);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_9, L_10, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::.ctor(System.Action`1<UnityEngine.AndroidJavaObject>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormLoadSuccessListener__ctor_m7D259C6D3148F10E17344FFB032D87D6144F6806 (OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * __this, Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * ___onConsentFormLoadSuccessAction0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral45DDA05892176F58A7B35CE2ED91EC900D9D2067);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238(__this, _stringLiteral45DDA05892176F58A7B35CE2ED91EC900D9D2067, /*hidden argument*/NULL);
		Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * L_0 = ___onConsentFormLoadSuccessAction0;
		__this->set__onConsentFormLoadSuccessAction_4(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::onConsentFormLoadSuccess(UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentFormLoadSuccessListener_onConsentFormLoadSuccess_m22A2098FBAF898A33C3490EB6D8CFD3362FF0A38 (OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___consentFormJavaObject0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_mCA8DB3C200C032B1F12A4EF6C54B0D3DC27DA5D6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF4691AD5F8DF3030E11F162E7F2F52C353D6D08C);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * L_0 = __this->get__onConsentFormLoadSuccessAction_4();
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		Debug_Log_mC26E5AD0D8D156C7FFD173AA15827F69225E9DB8(_stringLiteralF4691AD5F8DF3030E11F162E7F2F52C353D6D08C, /*hidden argument*/NULL);
		return;
	}

IL_0016:
	{
		Action_1_t7449340739FEAA6CF9730CC1FBD056945E792E5B * L_1 = __this->get__onConsentFormLoadSuccessAction_4();
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = ___consentFormJavaObject0;
		NullCheck(L_1);
		Action_1_Invoke_mCA8DB3C200C032B1F12A4EF6C54B0D3DC27DA5D6(L_1, L_2, /*hidden argument*/Action_1_Invoke_mCA8DB3C200C032B1F12A4EF6C54B0D3DC27DA5D6_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentInfoUpdateFailureListener__ctor_mAA13AA008BA8709FC4035B6F0ECA7CFEEA1CC370 (OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onConsentInfoUpdateFailureAction0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4B5590C684CEA428C87D4AF085DE91F2CEFF57C2);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238(__this, _stringLiteral4B5590C684CEA428C87D4AF085DE91F2CEFF57C2, /*hidden argument*/NULL);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = ___onConsentInfoUpdateFailureAction0;
		__this->set__onConsentInfoUpdateFailureAction_4(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::onConsentInfoUpdateFailure(UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentInfoUpdateFailureListener_onConsentInfoUpdateFailure_m9D2A831919007892CBDFD2874EE4CDAA8F73C6E7 (OnConsentInfoUpdateFailureListener_t8BCD06581CFECAE62303FAF4E931327C2BF529C1 * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE39CFBC2A5BA78BC0A2504F2FD6185ACF03D38A9);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006);
		s_Il2CppMethodInitialized = true;
	}
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * V_0 = NULL;
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * G_B5_0 = NULL;
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = __this->get__onConsentInfoUpdateFailureAction_4();
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		Debug_Log_mC26E5AD0D8D156C7FFD173AA15827F69225E9DB8(_stringLiteralE39CFBC2A5BA78BC0A2504F2FD6185ACF03D38A9, /*hidden argument*/NULL);
		return;
	}

IL_0016:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = ___error0;
		if (L_1)
		{
			goto IL_0022;
		}
	}
	{
		G_B5_0 = ((FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)(NULL));
		goto IL_0049;
	}

IL_0022:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_2);
		int32_t L_4;
		L_4 = AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A(L_2, _stringLiteralF0474EA2BCC21B0C61E022093E9D8BA1DB878006, L_3, /*hidden argument*/AndroidJavaObject_Call_TisInt32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_m02A4D4C9FD8B15173829454766683BA424408A2A_RuntimeMethod_var);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_5 = ___error0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_5);
		String_t* L_7;
		L_7 = AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE(L_5, _stringLiteral2FCE983D70DEDD25C3565391BB2267C1FEACB998, L_6, /*hidden argument*/AndroidJavaObject_Call_TisString_t_mB2E722C64FC7BD9F98B983053A6D3F9D94D355AE_RuntimeMethod_var);
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_8 = (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)il2cpp_codegen_object_new(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3_il2cpp_TypeInfo_var);
		FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A(L_8, L_4, L_7, /*hidden argument*/NULL);
		G_B5_0 = L_8;
	}

IL_0049:
	{
		V_0 = G_B5_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_9 = __this->get__onConsentInfoUpdateFailureAction_4();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_10 = V_0;
		NullCheck(L_9);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_9, L_10, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::.ctor(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentInfoUpdateSuccessListener__ctor_mB868212ADBE76EA0C1807201067C5801C333A44A (OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * __this, Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ___onConsentInfoUpdateSuccessAction0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral03A8B37AD1D0955D7622AC6CD210F9E6E1AD40D7);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(AndroidJavaProxy_tA8C86826A74CB7CC5511CB353DBA595C9270D9AF_il2cpp_TypeInfo_var);
		AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238(__this, _stringLiteral03A8B37AD1D0955D7622AC6CD210F9E6E1AD40D7, /*hidden argument*/NULL);
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_0 = ___onConsentInfoUpdateSuccessAction0;
		__this->set__onConsentInfoUpdateSuccessAction_4(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::onConsentInfoUpdateSuccess()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnConsentInfoUpdateSuccessListener_onConsentInfoUpdateSuccess_m0A18C7C32C90ED49832203DA16A41A6C6E455280 (OnConsentInfoUpdateSuccessListener_t656CFB193752B96047BE520AA8464178993A96D7 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3F0D13AB774CBD4B95456A54A6A0F9BE25A12554);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_0 = __this->get__onConsentInfoUpdateSuccessAction_4();
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var);
		Debug_Log_mC26E5AD0D8D156C7FFD173AA15827F69225E9DB8(_stringLiteral3F0D13AB774CBD4B95456A54A6A0F9BE25A12554, /*hidden argument*/NULL);
		return;
	}

IL_0016:
	{
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_1 = __this->get__onConsentInfoUpdateSuccessAction_4();
		NullCheck(L_1);
		Action_Invoke_m3FFA5BE3D64F0FF8E1E1CB6F953913FADB5EB89E(L_1, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.UmpClientFactory::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UmpClientFactory__ctor_m2A940E2F9765368A7373758FA7D985882DA3FEC8 (UmpClientFactory_tEB81174E5F666DA098ACA4B4E4C00DEF9BC5081A * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Android.UmpClientFactory::ConsentFormClient()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* UmpClientFactory_ConsentFormClient_m3AEC3CD62E7D9DF830B4ABEB6B5A63F772CA03C7 (UmpClientFactory_tEB81174E5F666DA098ACA4B4E4C00DEF9BC5081A * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0012;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_1;
		L_1 = ConsentFormClient_get_Instance_mD32B42448DBC28EC81D836787244FEA50C081944_inline(/*hidden argument*/NULL);
		return L_1;
	}

IL_0012:
	{
		MethodBase_t * L_2;
		L_2 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UmpClientFactory_ConsentFormClient_m3AEC3CD62E7D9DF830B4ABEB6B5A63F772CA03C7_RuntimeMethod_var)));
		NullCheck(L_2);
		String_t* L_3;
		L_3 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_2);
		String_t* L_4;
		L_4 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_5 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_5, L_4, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UmpClientFactory_ConsentFormClient_m3AEC3CD62E7D9DF830B4ABEB6B5A63F772CA03C7_RuntimeMethod_var)));
	}
}
// GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Android.UmpClientFactory::ConsentInformationClient()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* UmpClientFactory_ConsentInformationClient_mCB353E67E65957F579BF7896716EB47A99AC77F4 (UmpClientFactory_tEB81174E5F666DA098ACA4B4E4C00DEF9BC5081A * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0012;
		}
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * L_1;
		L_1 = ConsentInformationClient_get_Instance_m2628C7372920D3887990D2F1949C5A3925253907_inline(/*hidden argument*/NULL);
		return L_1;
	}

IL_0012:
	{
		MethodBase_t * L_2;
		L_2 = il2cpp_codegen_get_method_object(((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UmpClientFactory_ConsentInformationClient_mCB353E67E65957F579BF7896716EB47A99AC77F4_RuntimeMethod_var)));
		NullCheck(L_2);
		String_t* L_3;
		L_3 = VirtFuncInvoker0< String_t* >::Invoke(7 /* System.String System.Reflection.MemberInfo::get_Name() */, L_2);
		String_t* L_4;
		L_4 = String_Concat_m89EAB4C6A96B0E5C3F87300D6BE78D386B9EFC44(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0A42A54D7FD4D3A3A3F27FE6D9BA4765B168528A)), L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCE61FBE3F397454A333702786D0FA8442C1D66B0)), /*hidden argument*/NULL);
		InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB * L_5 = (InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t10D3EE59AD28EC641ACEE05BCA4271A527E5ECAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mC012CE552988309733C896F3FEA8249171E4402E(L_5, L_4, /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UmpClientFactory_ConsentInformationClient_mCB353E67E65957F579BF7896716EB47A99AC77F4_RuntimeMethod_var)));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.Utils::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Utils__ctor_m0058B11D96582C27E72A591B0D10180754248E0A (Utils_tD5EFE985A2E725E51C06BAF94E0D25A7A31806EB * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.Utils::GetConsentRequestParametersJavaObject(GoogleMobileAds.Ump.Api.ConsentRequestParameters,UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * Utils_GetConsentRequestParametersJavaObject_m9EACC27838E61BF36A6BD835CDB189B3BA0CEA4B (ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * ___consentRequestParameters0, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___activity1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m65A91D17CADA79F187F4D68980A9C8640B6C9FC7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_mCE70417061695048D84E473D50556E46B8630F54_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m9B0E356FA9FCFB9B1BECC6D7C5DF5C03309251AA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m35388695226DE2F7B0B5D0A07016716D6AD9CAEF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m199DB87BCE947106FBA38E19FDFE80CB65B61144_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2D4ED83444630924C3A223A95C183373672AFA3A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral802F23DDB5D2A0B7807EBAADD5E1DCC85F33F3D3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA42C5DDC073620888C073D79151E73091416273D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA733C7FC19A8317471D21AD091D1A9A6F973A728);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC6F2712DA56297B977518F6DEDB64B4367DECAB3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE742A0CC8947EA498A5F70DC274F54915AF90F21);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF9E201A19431397F0E527CE1CBEFB38F0D1AA5EA);
		s_Il2CppMethodInitialized = true;
	}
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_0 = NULL;
	ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * V_1 = NULL;
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_2 = NULL;
	String_t* V_3 = NULL;
	Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B  V_4;
	memset((&V_4), 0, sizeof(V_4));
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_5 = NULL;
	AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * V_6 = NULL;
	Exception_t * __last_unhandled_exception = 0;
	il2cpp::utils::ExceptionSupportStack<int32_t, 1> __leave_targets;
	{
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_0 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *)il2cpp_codegen_object_new(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_il2cpp_TypeInfo_var);
		AndroidJavaObject__ctor_m6146DBD19BCFFDB3D4F42C8D38491F354B58B001(L_1, _stringLiteralC6F2712DA56297B977518F6DEDB64B4367DECAB3, L_0, /*hidden argument*/NULL);
		V_0 = L_1;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_2 = V_0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_4 = L_3;
		ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * L_5 = ___consentRequestParameters0;
		NullCheck(L_5);
		bool L_6 = L_5->get_TagForUnderAgeOfConsent_0();
		bool L_7 = L_6;
		RuntimeObject * L_8 = Box(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_il2cpp_TypeInfo_var, &L_7);
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, L_8);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_8);
		NullCheck(L_2);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_9;
		L_9 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_2, _stringLiteral802F23DDB5D2A0B7807EBAADD5E1DCC85F33F3D3, L_4, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		V_0 = L_9;
		ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * L_10 = ___consentRequestParameters0;
		NullCheck(L_10);
		ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * L_11 = L_10->get_ConsentDebugSettings_1();
		V_1 = L_11;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_12 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_13 = L_12;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_14 = ___activity1;
		NullCheck(L_13);
		ArrayElementTypeCheck (L_13, L_14);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_14);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_15 = (AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E *)il2cpp_codegen_object_new(AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_il2cpp_TypeInfo_var);
		AndroidJavaObject__ctor_m6146DBD19BCFFDB3D4F42C8D38491F354B58B001(L_15, _stringLiteralF9E201A19431397F0E527CE1CBEFB38F0D1AA5EA, L_13, /*hidden argument*/NULL);
		V_2 = L_15;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_16 = V_2;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_17 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_18 = L_17;
		ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * L_19 = V_1;
		NullCheck(L_19);
		int32_t L_20 = L_19->get_DebugGeography_0();
		int32_t L_21 = ((int32_t)L_20);
		RuntimeObject * L_22 = Box(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046_il2cpp_TypeInfo_var, &L_21);
		NullCheck(L_18);
		ArrayElementTypeCheck (L_18, L_22);
		(L_18)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_22);
		NullCheck(L_16);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_23;
		L_23 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_16, _stringLiteralE742A0CC8947EA498A5F70DC274F54915AF90F21, L_18, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		V_2 = L_23;
		ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * L_24 = V_1;
		NullCheck(L_24);
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_25 = L_24->get_TestDeviceHashedIds_1();
		if (!L_25)
		{
			goto IL_00d8;
		}
	}
	{
		ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * L_26 = V_1;
		NullCheck(L_26);
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_27 = L_26->get_TestDeviceHashedIds_1();
		NullCheck(L_27);
		int32_t L_28;
		L_28 = List_1_get_Count_m199DB87BCE947106FBA38E19FDFE80CB65B61144_inline(L_27, /*hidden argument*/List_1_get_Count_m199DB87BCE947106FBA38E19FDFE80CB65B61144_RuntimeMethod_var);
		if ((((int32_t)L_28) <= ((int32_t)0)))
		{
			goto IL_00d8;
		}
	}
	{
		ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * L_29 = V_1;
		NullCheck(L_29);
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_30 = L_29->get_TestDeviceHashedIds_1();
		NullCheck(L_30);
		Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B  L_31;
		L_31 = List_1_GetEnumerator_m35388695226DE2F7B0B5D0A07016716D6AD9CAEF(L_30, /*hidden argument*/List_1_GetEnumerator_m35388695226DE2F7B0B5D0A07016716D6AD9CAEF_RuntimeMethod_var);
		V_4 = L_31;
	}

IL_0096:
	try
	{ // begin try (depth: 1)
		{
			goto IL_00b9;
		}

IL_009b:
		{
			String_t* L_32;
			L_32 = Enumerator_get_Current_m9B0E356FA9FCFB9B1BECC6D7C5DF5C03309251AA_inline((Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B *)(&V_4), /*hidden argument*/Enumerator_get_Current_m9B0E356FA9FCFB9B1BECC6D7C5DF5C03309251AA_RuntimeMethod_var);
			V_3 = L_32;
			AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_33 = V_2;
			ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_34 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
			ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_35 = L_34;
			String_t* L_36 = V_3;
			NullCheck(L_35);
			ArrayElementTypeCheck (L_35, L_36);
			(L_35)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_36);
			NullCheck(L_33);
			AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_37;
			L_37 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_33, _stringLiteral2D4ED83444630924C3A223A95C183373672AFA3A, L_35, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
			V_2 = L_37;
		}

IL_00b9:
		{
			bool L_38;
			L_38 = Enumerator_MoveNext_mCE70417061695048D84E473D50556E46B8630F54((Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B *)(&V_4), /*hidden argument*/Enumerator_MoveNext_mCE70417061695048D84E473D50556E46B8630F54_RuntimeMethod_var);
			if (L_38)
			{
				goto IL_009b;
			}
		}

IL_00c5:
		{
			IL2CPP_LEAVE(0xD8, FINALLY_00ca);
		}
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		__last_unhandled_exception = (Exception_t *)e.ex;
		goto FINALLY_00ca;
	}

FINALLY_00ca:
	{ // begin finally (depth: 1)
		Enumerator_Dispose_m65A91D17CADA79F187F4D68980A9C8640B6C9FC7((Enumerator_tCDCE241581BD00D8EDB03C9DC4133A65ADABF67B *)(&V_4), /*hidden argument*/Enumerator_Dispose_m65A91D17CADA79F187F4D68980A9C8640B6C9FC7_RuntimeMethod_var);
		IL2CPP_END_FINALLY(202)
	} // end finally (depth: 1)
	IL2CPP_CLEANUP(202)
	{
		IL2CPP_RETHROW_IF_UNHANDLED(Exception_t *)
		IL2CPP_JUMP_TBL(0xD8, IL_00d8)
	}

IL_00d8:
	{
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_39 = V_2;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_40 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_39);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_41;
		L_41 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_39, _stringLiteralA733C7FC19A8317471D21AD091D1A9A6F973A728, L_40, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		V_5 = L_41;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_42 = V_0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_43 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)1);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_44 = L_43;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_45 = V_5;
		NullCheck(L_44);
		ArrayElementTypeCheck (L_44, L_45);
		(L_44)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_45);
		NullCheck(L_42);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_46;
		L_46 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_42, _stringLiteralA42C5DDC073620888C073D79151E73091416273D, L_44, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		V_0 = L_46;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_47 = V_0;
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_48 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)0);
		NullCheck(L_47);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_49;
		L_49 = AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412(L_47, _stringLiteralA733C7FC19A8317471D21AD091D1A9A6F973A728, L_48, /*hidden argument*/AndroidJavaObject_Call_TisAndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E_mC5ED59BBD9C24EFFD98D8C3819C7E15DFE08F412_RuntimeMethod_var);
		V_6 = L_49;
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_50 = V_6;
		return L_50;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m2481275D945C664C823EBE378B801BD39050CA8D (U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::<>m__0(UnityEngine.AndroidJavaObject)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC644795AFB1E0FCDD4A4D5FE30D4EE0F3EBDA497 (U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * __this, AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * ___consentFormJavaObject0, const RuntimeMethod* method)
{
	{
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_0 = __this->get_U24this_1();
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = ___consentFormJavaObject0;
		NullCheck(L_0);
		L_0->set__consentForm_10(L_1);
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_2 = __this->get_onFormLoaded_0();
		NullCheck(L_2);
		Action_Invoke_m3FFA5BE3D64F0FF8E1E1CB6F953913FADB5EB89E(L_2, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::<>m__1()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m83019FB280AA8BBBF7202F1110851479163653FE (U3CLoadU3Ec__AnonStorey0_t2EA9DE720A1DB9C35D7425F3519E6D7A4C912834 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralAAA25E8902DA4A760B857FFC74176D9BB9EB25D2);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t * V_0 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	il2cpp::utils::ExceptionSupportStack<int32_t, 2> __leave_targets;

IL_0000:
	try
	{ // begin try (depth: 1)
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_0 = __this->get_U24this_1();
		NullCheck(L_0);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_1 = L_0->get__userMessagingPlatformClass_9();
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_2 = (ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE*)SZArrayNew(ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE_il2cpp_TypeInfo_var, (uint32_t)3);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_3 = L_2;
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_4 = __this->get_U24this_1();
		NullCheck(L_4);
		AndroidJavaObject_t10188D5695DCD09C9F621B44B0A8C93A2281236E * L_5 = L_4->get__activity_8();
		NullCheck(L_3);
		ArrayElementTypeCheck (L_3, L_5);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject *)L_5);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_6 = L_3;
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_7 = __this->get_U24this_1();
		NullCheck(L_7);
		OnConsentFormLoadSuccessListener_t4DAD74248E94A4F256DE24632AFF8A93D071FC42 * L_8 = L_7->get__onSuccess_4();
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_8);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject *)L_8);
		ObjectU5BU5D_tC1F4EE0DB0B7300255F5FD4AF64FE4C585CF5ADE* L_9 = L_6;
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_10 = __this->get_U24this_1();
		NullCheck(L_10);
		OnConsentFormLoadFailureListener_tC0D04285B18C582E17BCBD84F71F0931CE4D640D * L_11 = L_10->get__onFailure_5();
		NullCheck(L_9);
		ArrayElementTypeCheck (L_9, L_11);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject *)L_11);
		NullCheck(L_1);
		AndroidJavaObject_CallStatic_m5A97968767E1603C021023809276443ED24577FB(L_1, _stringLiteralAAA25E8902DA4A760B857FFC74176D9BB9EB25D2, L_9, /*hidden argument*/NULL);
		goto IL_0065;
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_004a;
		}
		throw e;
	}

CATCH_004a:
	{ // begin catch(System.Exception)
		V_0 = ((Exception_t *)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t *));
		Exception_t * L_12 = V_0;
		NullCheck(L_12);
		String_t* L_13;
		L_13 = VirtFuncInvoker0< String_t* >::Invoke(9 /* System.String System.Exception::get_StackTrace() */, L_12);
		String_t* L_14;
		L_14 = String_Concat_m4B4AB72618348C5DFBFBA8DED84B9E2EBDB55E1B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB95E94A0154D0393CEC86372990C5EC19EB9E045)), L_13, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Debug_tEB68BCBEB8EFD60F8043C67146DC05E7F50F374B_il2cpp_TypeInfo_var)));
		Debug_LogError_m8850D65592770A364D494025FF3A73E8D4D70485(L_14, /*hidden argument*/NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION();
		goto IL_0065;
	} // end catch (depth: 1)

IL_0065:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * ConsentFormClient_get_Instance_mD32B42448DBC28EC81D836787244FEA50C081944_inline (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var);
		ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA * L_0 = ((ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_StaticFields*)il2cpp_codegen_static_fields_for(ConsentFormClient_tE2FF4511AAD735164C7CC0D2B459FDEF7FC57EEA_il2cpp_TypeInfo_var))->get__instance_7();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * ConsentInformationClient_get_Instance_m2628C7372920D3887990D2F1949C5A3925253907_inline (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var);
		ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE * L_0 = ((ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_StaticFields*)il2cpp_codegen_static_fields_for(ConsentInformationClient_t2867FE085C711D3EE969FC45635323DA9D3EA8AE_il2cpp_TypeInfo_var))->get__instance_2();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m5D847939ABB9A78203B062CAFFE975792174D00F_gshared_inline (List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = (int32_t)__this->get__size_2();
		return (int32_t)L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject * Enumerator_get_Current_m9C4EBBD2108B51885E750F927D7936290C8E20EE_gshared_inline (Enumerator_tB6009981BD4E3881E3EC83627255A24198F902D6 * __this, const RuntimeMethod* method)
{
	{
		RuntimeObject * L_0 = (RuntimeObject *)__this->get_current_3();
		return (RuntimeObject *)L_0;
	}
}
