%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 337831424, guid: 6a10b2909283487f913b00d94cd3faf5, type: 3}
  m_Name: TIMELINE world animsTimeline
  m_EditorClassIdentifier: 
  m_Id: 0
  m_NextId: 0
  m_Tracks:
  - {fileID: 114511440093341732}
  m_FixedDuration: 0
  m_EditorSettings:
    fps: 60
  m_UpdateMode: 0
  m_ParameterName: 
  m_DurationMode: 0
--- !u!74 &74923508422926796
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Recorded
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: -4.1106005}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 14.933333
        value: {x: 0, y: 0, z: 32.4}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 2
      script: {fileID: 0}
      typeID: 95
      customType: 8
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 95
      customType: 8
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 95
      customType: 8
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 14.933333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 14.933333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 14.933333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: -4.1106005
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 14.933333
        value: 32.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 1
  m_Events: []
--- !u!114 &114511440093341732
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1467732076, guid: 6a10b2909283487f913b00d94cd3faf5, type: 3}
  m_Name: Animation Track1
  m_EditorClassIdentifier: 
  m_Locked: 0
  m_Muted: 0
  m_Soloed: 0
  m_Height: 0
  m_CustomPlayableFullTypename: 
  m_AnimClip: {fileID: 74923508422926796}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_OpenClipPreExtrapolation: 1
  m_OpenClipPostExtrapolation: 1
  m_OpenClipOffsetPosition: {x: -0.66, y: 1, z: -12.5}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_OpenClipTimeOffset: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
