﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>


template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1, typename T2>
struct InterfaceActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3>
struct InterfaceActionInvoker3
{
	typedef void (*Action)(void*, T1, T2, T3, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2, T3 p3)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, invokeData.method);
	}
};

// System.Action`1<GoogleMobileAds.Ump.Api.FormError>
struct Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD;
// System.Action`1<System.Object>
struct Action_1_tD9663D9715FAA4E62035CFCF1AD4D094EE7872DC;
// System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError>
struct Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC;
// System.Action`2<System.Object,System.Object>
struct Action_2_t4FB8E5660AE634E13BF340904C61FEA9DCE9D52D;
// System.Collections.Generic.List`1<System.Object>
struct List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5;
// System.Collections.Generic.List`1<System.String>
struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3;
// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Delegate[]
struct DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8;
// System.String[]
struct StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A;
// System.Type[]
struct TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755;
// System.Action
struct Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6;
// System.AsyncCallback
struct AsyncCallback_tA7921BEF974919C46FF8F9D9867C567B200BB0EA;
// System.Reflection.Binder
struct Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30;
// GoogleMobileAds.Ump.Api.ConsentDebugSettings
struct ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0;
// GoogleMobileAds.Ump.Api.ConsentForm
struct ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E;
// GoogleMobileAds.Ump.Api.ConsentRequestParameters
struct ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17;
// System.DelegateData
struct DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288;
// GoogleMobileAds.Ump.Api.FormError
struct FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3;
// System.IAsyncResult
struct IAsyncResult_tC9F97BF36FCF122D29D3101D80642278297BF370;
// GoogleMobileAds.Ump.Common.IConsentFormClient
struct IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4;
// GoogleMobileAds.Ump.Common.IUmpClientFactory
struct IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0;
// System.Reflection.MemberFilter
struct MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81;
// System.Reflection.MethodInfo
struct MethodInfo_t;
// System.String
struct String_t;
// System.Type
struct Type_t;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;
// GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525;
// GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4
struct U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49;
// GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2
struct U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212;
// GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0
struct U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE;
// GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE;
// GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5
struct U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293;
// GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3
struct U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED;
// GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1
struct U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF;

IL2CPP_EXTERN_C RuntimeClass* Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral87D3D808CD24C33F1425FFDE4A0710FC4BBC9C19;
IL2CPP_EXTERN_C String_t* _stringLiteralE4186B0B875F8115D04109741D50C304CE9A671F;
IL2CPP_EXTERN_C String_t* _stringLiteralE707B37FA85D7F3FB6BF6DA486F7250D62EBC726;
IL2CPP_EXTERN_C const RuntimeMethod* Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Type_GetType_m2D692327E10692E11116805CC604CD47F144A9E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m4D8F5A1FC9EDEAF5EB5B25E1C2D14F6968A5FB87_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_m21DA3654C3294D3962D16163AD770CCBB4743C54_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1CF0A2B6F36249D56CFC63DAB4D5BC4261418975_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m444618FD30337AA9758A791CE803EE060E84C3BB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m11CB34935A9C66D1220FBF3B27563652742DF13C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mCD16116799A31497AD04EA3ADDF4C573655C0EF7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CShowU3Ec__AnonStorey2_U3CU3Em__0_m38174121A671B27509DA35C2F861E9118EF44123_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CShowU3Ec__AnonStorey3_U3CU3Em__0_m5C652CABA32FB41EC6CECC0503ABF7DE2AFB7FB2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mE789E6055115C2D195F9FC002D46C1B957CCB1D8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mA811CC59FFF687B19D9EF5F8EE15759726ECA7FF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m528F4F63FA761B55DD63EC85CB14ECC6E507677D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_mAEEFDD424A26FA8B8C9722924CD4D8749D616627_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_t938505C2AEA0B107C3CB79A7445F4257FA30F228 
{
public:

public:
};


// System.Object


// System.Collections.Generic.List`1<System.String>
struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3  : public RuntimeObject
{
public:
	// T[] System.Collections.Generic.List`1::_items
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject * ____syncRoot_4;

public:
	inline static int32_t get_offset_of__items_1() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____items_1)); }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* get__items_1() const { return ____items_1; }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A** get_address_of__items_1() { return &____items_1; }
	inline void set__items_1(StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* value)
	{
		____items_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____items_1), (void*)value);
	}

	inline static int32_t get_offset_of__size_2() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____size_2)); }
	inline int32_t get__size_2() const { return ____size_2; }
	inline int32_t* get_address_of__size_2() { return &____size_2; }
	inline void set__size_2(int32_t value)
	{
		____size_2 = value;
	}

	inline static int32_t get_offset_of__version_3() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____version_3)); }
	inline int32_t get__version_3() const { return ____version_3; }
	inline int32_t* get_address_of__version_3() { return &____version_3; }
	inline void set__version_3(int32_t value)
	{
		____version_3 = value;
	}

	inline static int32_t get_offset_of__syncRoot_4() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3, ____syncRoot_4)); }
	inline RuntimeObject * get__syncRoot_4() const { return ____syncRoot_4; }
	inline RuntimeObject ** get_address_of__syncRoot_4() { return &____syncRoot_4; }
	inline void set__syncRoot_4(RuntimeObject * value)
	{
		____syncRoot_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____syncRoot_4), (void*)value);
	}
};

struct List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_StaticFields
{
public:
	// T[] System.Collections.Generic.List`1::_emptyArray
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* ____emptyArray_5;

public:
	inline static int32_t get_offset_of__emptyArray_5() { return static_cast<int32_t>(offsetof(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_StaticFields, ____emptyArray_5)); }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* get__emptyArray_5() const { return ____emptyArray_5; }
	inline StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A** get_address_of__emptyArray_5() { return &____emptyArray_5; }
	inline void set__emptyArray_5(StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* value)
	{
		____emptyArray_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____emptyArray_5), (void*)value);
	}
};

struct Il2CppArrayBounds;

// System.Array


// GoogleMobileAds.Ump.Api.ConsentForm
struct ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Api.ConsentForm::_client
	RuntimeObject* ____client_0;

public:
	inline static int32_t get_offset_of__client_0() { return static_cast<int32_t>(offsetof(ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E, ____client_0)); }
	inline RuntimeObject* get__client_0() const { return ____client_0; }
	inline RuntimeObject** get_address_of__client_0() { return &____client_0; }
	inline void set__client_0(RuntimeObject* value)
	{
		____client_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____client_0), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentInformation
struct ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34  : public RuntimeObject
{
public:

public:
};

struct ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_StaticFields
{
public:
	// GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.ConsentInformation::_clientFactory
	RuntimeObject* ____clientFactory_0;

public:
	inline static int32_t get_offset_of__clientFactory_0() { return static_cast<int32_t>(offsetof(ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_StaticFields, ____clientFactory_0)); }
	inline RuntimeObject* get__clientFactory_0() const { return ____clientFactory_0; }
	inline RuntimeObject** get_address_of__clientFactory_0() { return &____clientFactory_0; }
	inline void set__clientFactory_0(RuntimeObject* value)
	{
		____clientFactory_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____clientFactory_0), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentRequestParameters
struct ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17  : public RuntimeObject
{
public:
	// System.Boolean GoogleMobileAds.Ump.Api.ConsentRequestParameters::TagForUnderAgeOfConsent
	bool ___TagForUnderAgeOfConsent_0;
	// GoogleMobileAds.Ump.Api.ConsentDebugSettings GoogleMobileAds.Ump.Api.ConsentRequestParameters::ConsentDebugSettings
	ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * ___ConsentDebugSettings_1;

public:
	inline static int32_t get_offset_of_TagForUnderAgeOfConsent_0() { return static_cast<int32_t>(offsetof(ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17, ___TagForUnderAgeOfConsent_0)); }
	inline bool get_TagForUnderAgeOfConsent_0() const { return ___TagForUnderAgeOfConsent_0; }
	inline bool* get_address_of_TagForUnderAgeOfConsent_0() { return &___TagForUnderAgeOfConsent_0; }
	inline void set_TagForUnderAgeOfConsent_0(bool value)
	{
		___TagForUnderAgeOfConsent_0 = value;
	}

	inline static int32_t get_offset_of_ConsentDebugSettings_1() { return static_cast<int32_t>(offsetof(ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17, ___ConsentDebugSettings_1)); }
	inline ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * get_ConsentDebugSettings_1() const { return ___ConsentDebugSettings_1; }
	inline ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 ** get_address_of_ConsentDebugSettings_1() { return &___ConsentDebugSettings_1; }
	inline void set_ConsentDebugSettings_1(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * value)
	{
		___ConsentDebugSettings_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___ConsentDebugSettings_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.FormError
struct FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3  : public RuntimeObject
{
public:
	// System.Int32 GoogleMobileAds.Ump.Api.FormError::<ErrorCode>k__BackingField
	int32_t ___U3CErrorCodeU3Ek__BackingField_0;
	// System.String GoogleMobileAds.Ump.Api.FormError::<Message>k__BackingField
	String_t* ___U3CMessageU3Ek__BackingField_1;

public:
	inline static int32_t get_offset_of_U3CErrorCodeU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3, ___U3CErrorCodeU3Ek__BackingField_0)); }
	inline int32_t get_U3CErrorCodeU3Ek__BackingField_0() const { return ___U3CErrorCodeU3Ek__BackingField_0; }
	inline int32_t* get_address_of_U3CErrorCodeU3Ek__BackingField_0() { return &___U3CErrorCodeU3Ek__BackingField_0; }
	inline void set_U3CErrorCodeU3Ek__BackingField_0(int32_t value)
	{
		___U3CErrorCodeU3Ek__BackingField_0 = value;
	}

	inline static int32_t get_offset_of_U3CMessageU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3, ___U3CMessageU3Ek__BackingField_1)); }
	inline String_t* get_U3CMessageU3Ek__BackingField_1() const { return ___U3CMessageU3Ek__BackingField_1; }
	inline String_t** get_address_of_U3CMessageU3Ek__BackingField_1() { return &___U3CMessageU3Ek__BackingField_1; }
	inline void set_U3CMessageU3Ek__BackingField_1(String_t* value)
	{
		___U3CMessageU3Ek__BackingField_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CMessageU3Ek__BackingField_1), (void*)value);
	}
};


// System.Reflection.MemberInfo
struct MemberInfo_t  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.Utils
struct Utils_tA2FAAA8FEA1DC17BBE2E07588D867BD569B1D4D8  : public RuntimeObject
{
public:

public:
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525  : public RuntimeObject
{
public:
	// System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::formLoadCallback
	Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * ___formLoadCallback_0;
	// GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::client
	RuntimeObject* ___client_1;

public:
	inline static int32_t get_offset_of_formLoadCallback_0() { return static_cast<int32_t>(offsetof(U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525, ___formLoadCallback_0)); }
	inline Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * get_formLoadCallback_0() const { return ___formLoadCallback_0; }
	inline Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC ** get_address_of_formLoadCallback_0() { return &___formLoadCallback_0; }
	inline void set_formLoadCallback_0(Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * value)
	{
		___formLoadCallback_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___formLoadCallback_0), (void*)value);
	}

	inline static int32_t get_offset_of_client_1() { return static_cast<int32_t>(offsetof(U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525, ___client_1)); }
	inline RuntimeObject* get_client_1() const { return ___client_1; }
	inline RuntimeObject** get_address_of_client_1() { return &___client_1; }
	inline void set_client_1(RuntimeObject* value)
	{
		___client_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___client_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4
struct U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49  : public RuntimeObject
{
public:
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::onDismissed
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed_0;

public:
	inline static int32_t get_offset_of_onDismissed_0() { return static_cast<int32_t>(offsetof(U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49, ___onDismissed_0)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get_onDismissed_0() const { return ___onDismissed_0; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of_onDismissed_0() { return &___onDismissed_0; }
	inline void set_onDismissed_0(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		___onDismissed_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___onDismissed_0), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2
struct U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212  : public RuntimeObject
{
public:
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::onDismissed
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed_0;

public:
	inline static int32_t get_offset_of_onDismissed_0() { return static_cast<int32_t>(offsetof(U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212, ___onDismissed_0)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get_onDismissed_0() const { return ___onDismissed_0; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of_onDismissed_0() { return &___onDismissed_0; }
	inline void set_onDismissed_0(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		___onDismissed_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___onDismissed_0), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0
struct U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE  : public RuntimeObject
{
public:
	// System.Action`1<GoogleMobileAds.Ump.Api.FormError> GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::consentInfoUpdateCallback
	Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___consentInfoUpdateCallback_0;

public:
	inline static int32_t get_offset_of_consentInfoUpdateCallback_0() { return static_cast<int32_t>(offsetof(U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE, ___consentInfoUpdateCallback_0)); }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * get_consentInfoUpdateCallback_0() const { return ___consentInfoUpdateCallback_0; }
	inline Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD ** get_address_of_consentInfoUpdateCallback_0() { return &___consentInfoUpdateCallback_0; }
	inline void set_consentInfoUpdateCallback_0(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * value)
	{
		___consentInfoUpdateCallback_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___consentInfoUpdateCallback_0), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Api.FormError GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::error
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error_0;
	// GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0 GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>f__ref$0
	U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * ___U3CU3Ef__refU240_1;

public:
	inline static int32_t get_offset_of_error_0() { return static_cast<int32_t>(offsetof(U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE, ___error_0)); }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * get_error_0() const { return ___error_0; }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 ** get_address_of_error_0() { return &___error_0; }
	inline void set_error_0(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * value)
	{
		___error_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___error_0), (void*)value);
	}

	inline static int32_t get_offset_of_U3CU3Ef__refU240_1() { return static_cast<int32_t>(offsetof(U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE, ___U3CU3Ef__refU240_1)); }
	inline U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * get_U3CU3Ef__refU240_1() const { return ___U3CU3Ef__refU240_1; }
	inline U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 ** get_address_of_U3CU3Ef__refU240_1() { return &___U3CU3Ef__refU240_1; }
	inline void set_U3CU3Ef__refU240_1(U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * value)
	{
		___U3CU3Ef__refU240_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CU3Ef__refU240_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5
struct U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Api.FormError GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::error
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error_0;
	// GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4 GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::<>f__ref$4
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * ___U3CU3Ef__refU244_1;

public:
	inline static int32_t get_offset_of_error_0() { return static_cast<int32_t>(offsetof(U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293, ___error_0)); }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * get_error_0() const { return ___error_0; }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 ** get_address_of_error_0() { return &___error_0; }
	inline void set_error_0(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * value)
	{
		___error_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___error_0), (void*)value);
	}

	inline static int32_t get_offset_of_U3CU3Ef__refU244_1() { return static_cast<int32_t>(offsetof(U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293, ___U3CU3Ef__refU244_1)); }
	inline U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * get_U3CU3Ef__refU244_1() const { return ___U3CU3Ef__refU244_1; }
	inline U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 ** get_address_of_U3CU3Ef__refU244_1() { return &___U3CU3Ef__refU244_1; }
	inline void set_U3CU3Ef__refU244_1(U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * value)
	{
		___U3CU3Ef__refU244_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CU3Ef__refU244_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3
struct U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Api.FormError GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::error
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error_0;
	// GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2 GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::<>f__ref$2
	U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * ___U3CU3Ef__refU242_1;

public:
	inline static int32_t get_offset_of_error_0() { return static_cast<int32_t>(offsetof(U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED, ___error_0)); }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * get_error_0() const { return ___error_0; }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 ** get_address_of_error_0() { return &___error_0; }
	inline void set_error_0(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * value)
	{
		___error_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___error_0), (void*)value);
	}

	inline static int32_t get_offset_of_U3CU3Ef__refU242_1() { return static_cast<int32_t>(offsetof(U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED, ___U3CU3Ef__refU242_1)); }
	inline U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * get_U3CU3Ef__refU242_1() const { return ___U3CU3Ef__refU242_1; }
	inline U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 ** get_address_of_U3CU3Ef__refU242_1() { return &___U3CU3Ef__refU242_1; }
	inline void set_U3CU3Ef__refU242_1(U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * value)
	{
		___U3CU3Ef__refU242_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CU3Ef__refU242_1), (void*)value);
	}
};


// GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1
struct U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Api.FormError GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::error
	FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error_0;
	// GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0 GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::<>f__ref$0
	U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * ___U3CU3Ef__refU240_1;

public:
	inline static int32_t get_offset_of_error_0() { return static_cast<int32_t>(offsetof(U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF, ___error_0)); }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * get_error_0() const { return ___error_0; }
	inline FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 ** get_address_of_error_0() { return &___error_0; }
	inline void set_error_0(FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * value)
	{
		___error_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___error_0), (void*)value);
	}

	inline static int32_t get_offset_of_U3CU3Ef__refU240_1() { return static_cast<int32_t>(offsetof(U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF, ___U3CU3Ef__refU240_1)); }
	inline U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * get_U3CU3Ef__refU240_1() const { return ___U3CU3Ef__refU240_1; }
	inline U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE ** get_address_of_U3CU3Ef__refU240_1() { return &___U3CU3Ef__refU240_1; }
	inline void set_U3CU3Ef__refU240_1(U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * value)
	{
		___U3CU3Ef__refU240_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CU3Ef__refU240_1), (void*)value);
	}
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// System.Int32
struct Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046 
{
public:
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046, ___m_value_0)); }
	inline int32_t get_m_value_0() const { return ___m_value_0; }
	inline int32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int32_t value)
	{
		___m_value_0 = value;
	}
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// System.Reflection.BindingFlags
struct BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733 
{
public:
	// System.Int32 System.Reflection.BindingFlags::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Ump.Api.ConsentStatus
struct ConsentStatus_t351323CACE43994E9E00D6A7068C0C55682FD1CF 
{
public:
	// System.Int32 GoogleMobileAds.Ump.Api.ConsentStatus::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(ConsentStatus_t351323CACE43994E9E00D6A7068C0C55682FD1CF, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// GoogleMobileAds.Ump.Api.DebugGeography
struct DebugGeography_t17CE8783791AC778B9B84042AFFE7247DE5AF70D 
{
public:
	// System.Int32 GoogleMobileAds.Ump.Api.DebugGeography::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(DebugGeography_t17CE8783791AC778B9B84042AFFE7247DE5AF70D, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Delegate
struct Delegate_t  : public RuntimeObject
{
public:
	// System.IntPtr System.Delegate::method_ptr
	Il2CppMethodPointer ___method_ptr_0;
	// System.IntPtr System.Delegate::invoke_impl
	intptr_t ___invoke_impl_1;
	// System.Object System.Delegate::m_target
	RuntimeObject * ___m_target_2;
	// System.IntPtr System.Delegate::method
	intptr_t ___method_3;
	// System.IntPtr System.Delegate::delegate_trampoline
	intptr_t ___delegate_trampoline_4;
	// System.IntPtr System.Delegate::extra_arg
	intptr_t ___extra_arg_5;
	// System.IntPtr System.Delegate::method_code
	intptr_t ___method_code_6;
	// System.Reflection.MethodInfo System.Delegate::method_info
	MethodInfo_t * ___method_info_7;
	// System.Reflection.MethodInfo System.Delegate::original_method_info
	MethodInfo_t * ___original_method_info_8;
	// System.DelegateData System.Delegate::data
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	// System.Boolean System.Delegate::method_is_virtual
	bool ___method_is_virtual_10;

public:
	inline static int32_t get_offset_of_method_ptr_0() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_ptr_0)); }
	inline Il2CppMethodPointer get_method_ptr_0() const { return ___method_ptr_0; }
	inline Il2CppMethodPointer* get_address_of_method_ptr_0() { return &___method_ptr_0; }
	inline void set_method_ptr_0(Il2CppMethodPointer value)
	{
		___method_ptr_0 = value;
	}

	inline static int32_t get_offset_of_invoke_impl_1() { return static_cast<int32_t>(offsetof(Delegate_t, ___invoke_impl_1)); }
	inline intptr_t get_invoke_impl_1() const { return ___invoke_impl_1; }
	inline intptr_t* get_address_of_invoke_impl_1() { return &___invoke_impl_1; }
	inline void set_invoke_impl_1(intptr_t value)
	{
		___invoke_impl_1 = value;
	}

	inline static int32_t get_offset_of_m_target_2() { return static_cast<int32_t>(offsetof(Delegate_t, ___m_target_2)); }
	inline RuntimeObject * get_m_target_2() const { return ___m_target_2; }
	inline RuntimeObject ** get_address_of_m_target_2() { return &___m_target_2; }
	inline void set_m_target_2(RuntimeObject * value)
	{
		___m_target_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_target_2), (void*)value);
	}

	inline static int32_t get_offset_of_method_3() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_3)); }
	inline intptr_t get_method_3() const { return ___method_3; }
	inline intptr_t* get_address_of_method_3() { return &___method_3; }
	inline void set_method_3(intptr_t value)
	{
		___method_3 = value;
	}

	inline static int32_t get_offset_of_delegate_trampoline_4() { return static_cast<int32_t>(offsetof(Delegate_t, ___delegate_trampoline_4)); }
	inline intptr_t get_delegate_trampoline_4() const { return ___delegate_trampoline_4; }
	inline intptr_t* get_address_of_delegate_trampoline_4() { return &___delegate_trampoline_4; }
	inline void set_delegate_trampoline_4(intptr_t value)
	{
		___delegate_trampoline_4 = value;
	}

	inline static int32_t get_offset_of_extra_arg_5() { return static_cast<int32_t>(offsetof(Delegate_t, ___extra_arg_5)); }
	inline intptr_t get_extra_arg_5() const { return ___extra_arg_5; }
	inline intptr_t* get_address_of_extra_arg_5() { return &___extra_arg_5; }
	inline void set_extra_arg_5(intptr_t value)
	{
		___extra_arg_5 = value;
	}

	inline static int32_t get_offset_of_method_code_6() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_code_6)); }
	inline intptr_t get_method_code_6() const { return ___method_code_6; }
	inline intptr_t* get_address_of_method_code_6() { return &___method_code_6; }
	inline void set_method_code_6(intptr_t value)
	{
		___method_code_6 = value;
	}

	inline static int32_t get_offset_of_method_info_7() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_info_7)); }
	inline MethodInfo_t * get_method_info_7() const { return ___method_info_7; }
	inline MethodInfo_t ** get_address_of_method_info_7() { return &___method_info_7; }
	inline void set_method_info_7(MethodInfo_t * value)
	{
		___method_info_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___method_info_7), (void*)value);
	}

	inline static int32_t get_offset_of_original_method_info_8() { return static_cast<int32_t>(offsetof(Delegate_t, ___original_method_info_8)); }
	inline MethodInfo_t * get_original_method_info_8() const { return ___original_method_info_8; }
	inline MethodInfo_t ** get_address_of_original_method_info_8() { return &___original_method_info_8; }
	inline void set_original_method_info_8(MethodInfo_t * value)
	{
		___original_method_info_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___original_method_info_8), (void*)value);
	}

	inline static int32_t get_offset_of_data_9() { return static_cast<int32_t>(offsetof(Delegate_t, ___data_9)); }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * get_data_9() const { return ___data_9; }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 ** get_address_of_data_9() { return &___data_9; }
	inline void set_data_9(DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * value)
	{
		___data_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___data_9), (void*)value);
	}

	inline static int32_t get_offset_of_method_is_virtual_10() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_is_virtual_10)); }
	inline bool get_method_is_virtual_10() const { return ___method_is_virtual_10; }
	inline bool* get_address_of_method_is_virtual_10() { return &___method_is_virtual_10; }
	inline void set_method_is_virtual_10(bool value)
	{
		___method_is_virtual_10 = value;
	}
};

// Native definition for P/Invoke marshalling of System.Delegate
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};
// Native definition for COM marshalling of System.Delegate
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};

// UnityEngine.RuntimePlatform
struct RuntimePlatform_tB8798C800FD9810C0FE2B7D2F2A0A3979D239065 
{
public:
	// System.Int32 UnityEngine.RuntimePlatform::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(RuntimePlatform_tB8798C800FD9810C0FE2B7D2F2A0A3979D239065, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.RuntimeTypeHandle
struct RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 
{
public:
	// System.IntPtr System.RuntimeTypeHandle::value
	intptr_t ___value_0;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9, ___value_0)); }
	inline intptr_t get_value_0() const { return ___value_0; }
	inline intptr_t* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(intptr_t value)
	{
		___value_0 = value;
	}
};


// GoogleMobileAds.Ump.Api.ConsentDebugSettings
struct ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0  : public RuntimeObject
{
public:
	// GoogleMobileAds.Ump.Api.DebugGeography GoogleMobileAds.Ump.Api.ConsentDebugSettings::DebugGeography
	int32_t ___DebugGeography_0;
	// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Ump.Api.ConsentDebugSettings::TestDeviceHashedIds
	List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * ___TestDeviceHashedIds_1;

public:
	inline static int32_t get_offset_of_DebugGeography_0() { return static_cast<int32_t>(offsetof(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0, ___DebugGeography_0)); }
	inline int32_t get_DebugGeography_0() const { return ___DebugGeography_0; }
	inline int32_t* get_address_of_DebugGeography_0() { return &___DebugGeography_0; }
	inline void set_DebugGeography_0(int32_t value)
	{
		___DebugGeography_0 = value;
	}

	inline static int32_t get_offset_of_TestDeviceHashedIds_1() { return static_cast<int32_t>(offsetof(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0, ___TestDeviceHashedIds_1)); }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * get_TestDeviceHashedIds_1() const { return ___TestDeviceHashedIds_1; }
	inline List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 ** get_address_of_TestDeviceHashedIds_1() { return &___TestDeviceHashedIds_1; }
	inline void set_TestDeviceHashedIds_1(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * value)
	{
		___TestDeviceHashedIds_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TestDeviceHashedIds_1), (void*)value);
	}
};


// System.MulticastDelegate
struct MulticastDelegate_t  : public Delegate_t
{
public:
	// System.Delegate[] System.MulticastDelegate::delegates
	DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* ___delegates_11;

public:
	inline static int32_t get_offset_of_delegates_11() { return static_cast<int32_t>(offsetof(MulticastDelegate_t, ___delegates_11)); }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* get_delegates_11() const { return ___delegates_11; }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8** get_address_of_delegates_11() { return &___delegates_11; }
	inline void set_delegates_11(DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* value)
	{
		___delegates_11 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___delegates_11), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates_11;
};
// Native definition for COM marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates_11;
};

// System.Type
struct Type_t  : public MemberInfo_t
{
public:
	// System.RuntimeTypeHandle System.Type::_impl
	RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  ____impl_9;

public:
	inline static int32_t get_offset_of__impl_9() { return static_cast<int32_t>(offsetof(Type_t, ____impl_9)); }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  get__impl_9() const { return ____impl_9; }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 * get_address_of__impl_9() { return &____impl_9; }
	inline void set__impl_9(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  value)
	{
		____impl_9 = value;
	}
};

struct Type_t_StaticFields
{
public:
	// System.Reflection.MemberFilter System.Type::FilterAttribute
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterAttribute_0;
	// System.Reflection.MemberFilter System.Type::FilterName
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterName_1;
	// System.Reflection.MemberFilter System.Type::FilterNameIgnoreCase
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterNameIgnoreCase_2;
	// System.Object System.Type::Missing
	RuntimeObject * ___Missing_3;
	// System.Char System.Type::Delimiter
	Il2CppChar ___Delimiter_4;
	// System.Type[] System.Type::EmptyTypes
	TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* ___EmptyTypes_5;
	// System.Reflection.Binder System.Type::defaultBinder
	Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * ___defaultBinder_6;

public:
	inline static int32_t get_offset_of_FilterAttribute_0() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterAttribute_0)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterAttribute_0() const { return ___FilterAttribute_0; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterAttribute_0() { return &___FilterAttribute_0; }
	inline void set_FilterAttribute_0(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterAttribute_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterAttribute_0), (void*)value);
	}

	inline static int32_t get_offset_of_FilterName_1() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterName_1)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterName_1() const { return ___FilterName_1; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterName_1() { return &___FilterName_1; }
	inline void set_FilterName_1(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterName_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterName_1), (void*)value);
	}

	inline static int32_t get_offset_of_FilterNameIgnoreCase_2() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterNameIgnoreCase_2)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterNameIgnoreCase_2() const { return ___FilterNameIgnoreCase_2; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterNameIgnoreCase_2() { return &___FilterNameIgnoreCase_2; }
	inline void set_FilterNameIgnoreCase_2(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterNameIgnoreCase_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterNameIgnoreCase_2), (void*)value);
	}

	inline static int32_t get_offset_of_Missing_3() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Missing_3)); }
	inline RuntimeObject * get_Missing_3() const { return ___Missing_3; }
	inline RuntimeObject ** get_address_of_Missing_3() { return &___Missing_3; }
	inline void set_Missing_3(RuntimeObject * value)
	{
		___Missing_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Missing_3), (void*)value);
	}

	inline static int32_t get_offset_of_Delimiter_4() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Delimiter_4)); }
	inline Il2CppChar get_Delimiter_4() const { return ___Delimiter_4; }
	inline Il2CppChar* get_address_of_Delimiter_4() { return &___Delimiter_4; }
	inline void set_Delimiter_4(Il2CppChar value)
	{
		___Delimiter_4 = value;
	}

	inline static int32_t get_offset_of_EmptyTypes_5() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___EmptyTypes_5)); }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* get_EmptyTypes_5() const { return ___EmptyTypes_5; }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755** get_address_of_EmptyTypes_5() { return &___EmptyTypes_5; }
	inline void set_EmptyTypes_5(TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* value)
	{
		___EmptyTypes_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___EmptyTypes_5), (void*)value);
	}

	inline static int32_t get_offset_of_defaultBinder_6() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___defaultBinder_6)); }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * get_defaultBinder_6() const { return ___defaultBinder_6; }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 ** get_address_of_defaultBinder_6() { return &___defaultBinder_6; }
	inline void set_defaultBinder_6(Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * value)
	{
		___defaultBinder_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___defaultBinder_6), (void*)value);
	}
};


// System.Action`1<GoogleMobileAds.Ump.Api.FormError>
struct Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD  : public MulticastDelegate_t
{
public:

public:
};


// System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError>
struct Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC  : public MulticastDelegate_t
{
public:

public:
};


// System.Action
struct Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6  : public MulticastDelegate_t
{
public:

public:
};

#ifdef __clang__
#pragma clang diagnostic pop
#endif


// System.Void System.Collections.Generic.List`1<System.Object>::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m0F0E00088CF56FEACC9E32D8B7D91B93D91DAA3B_gshared (List_1_t3F94120C77410A62EAE48421CF166B83AB95A2F5 * __this, const RuntimeMethod* method);
// System.Void System.Action`1<System.Object>::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mA671E933C9D3DAE4E3F71D34FDDA971739618158_gshared (Action_1_tD9663D9715FAA4E62035CFCF1AD4D094EE7872DC * __this, RuntimeObject * ___object0, intptr_t ___method1, const RuntimeMethod* method);
// System.Void System.Action`2<System.Object,System.Object>::Invoke(!0,!1)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_2_Invoke_m54EE979C4D83695ED736A3177A68C2968C8C4382_gshared (Action_2_t4FB8E5660AE634E13BF340904C61FEA9DCE9D52D * __this, RuntimeObject * ___arg10, RuntimeObject * ___arg21, const RuntimeMethod* method);
// System.Void System.Action`1<System.Object>::Invoke(!0)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1_Invoke_m587509C88BB83721D7918D89DF07606BB752D744_gshared (Action_1_tD9663D9715FAA4E62035CFCF1AD4D094EE7872DC * __this, RuntimeObject * ___obj0, const RuntimeMethod* method);

// System.Void System.Collections.Generic.List`1<System.String>::.ctor()
inline void List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9 (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *, const RuntimeMethod*))List_1__ctor_m0F0E00088CF56FEACC9E32D8B7D91B93D91DAA3B_gshared)(__this, method);
}
// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405 (RuntimeObject * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m8E661B2BCDB91D83C55F8340F9752BBFFCDA1A6F (U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * __this, const RuntimeMethod* method);
// GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.ConsentInformation::get_ClientFactory()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920 (const RuntimeMethod* method);
// System.Void System.Action::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * __this, RuntimeObject * ___object0, intptr_t ___method1, const RuntimeMethod* method);
// System.Void System.Action`1<GoogleMobileAds.Ump.Api.FormError>::.ctor(System.Object,System.IntPtr)
inline void Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443 (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * __this, RuntimeObject * ___object0, intptr_t ___method1, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *, RuntimeObject *, intptr_t, const RuntimeMethod*))Action_1__ctor_mA671E933C9D3DAE4E3F71D34FDDA971739618158_gshared)(__this, ___object0, ___method1, method);
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CShowU3Ec__AnonStorey2__ctor_m34D3325E5D9192F8253C4E21500582C7660A0575 (U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_m8AFD2FE260D4B698ED3579C9622B6FACD51FF590 (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * __this, const RuntimeMethod* method);
// GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.Utils::GetClientFactory()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7 (const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey0__ctor_mA550557A6E4952249FFC171CBF181BF8835915DD (U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentDebugSettings::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentDebugSettings__ctor_m7953C457CCE387F1AB1165EEB792209003F2F8EC (ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.FormError::set_ErrorCode(System.Int32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FormError_set_ErrorCode_mAAA6A7B5ECDFE56E892C9D2DFF1ACC6E7D6DF039_inline (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, int32_t ___value0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.FormError::set_Message(System.String)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FormError_set_Message_mBF54D641DB7C616DD97257F3CEE2E1C4FCD92029_inline (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, String_t* ___value0, const RuntimeMethod* method);
// UnityEngine.RuntimePlatform UnityEngine.Application::get_platform()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4 (const RuntimeMethod* method);
// System.Object System.Activator::CreateInstance(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject * Activator_CreateInstance_m1BACAB5F4FBF138CCCB537DDCB0683A2AC064295 (Type_t * ___type0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Api.MobileAds::RaiseAction(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381 (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * ___action0, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m63B0FCA7692DE144B9260C50C5E9E63B7FAE6FD2 (U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentForm::.ctor(GoogleMobileAds.Ump.Common.IConsentFormClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentForm__ctor_m8F9A01A5586D4314EFC0BC6659A5C8CBEB90D862 (ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E * __this, RuntimeObject* ___client0, const RuntimeMethod* method);
// System.Void System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError>::Invoke(!0,!1)
inline void Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C (Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * __this, ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E * ___arg10, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___arg21, const RuntimeMethod* method)
{
	((  void (*) (Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC *, ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E *, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *, const RuntimeMethod*))Action_2_Invoke_m54EE979C4D83695ED736A3177A68C2968C8C4382_gshared)(__this, ___arg10, ___arg21, method);
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m93560390999C3D79C039637F816CC46AEC4ABC73 (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CShowU3Ec__AnonStorey3__ctor_m2300B413E984C95454C93C9F1B2F180B8A984095 (U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * __this, const RuntimeMethod* method);
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey1__ctor_m4D16F1DB530771F57AF4C88CFB5FCB51669044CE (U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * __this, const RuntimeMethod* method);
// System.Void System.Action`1<GoogleMobileAds.Ump.Api.FormError>::Invoke(!0)
inline void Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827 (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * __this, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___obj0, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *, const RuntimeMethod*))Action_1_Invoke_m587509C88BB83721D7918D89DF07606BB752D744_gshared)(__this, ___obj0, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentDebugSettings::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentDebugSettings__ctor_m7953C457CCE387F1AB1165EEB792209003F2F8EC (ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 * L_0 = (List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3 *)il2cpp_codegen_object_new(List_1_t6C9F81EDBF0F4A31A9B0DA372D2EF34BDA3A1AF3_il2cpp_TypeInfo_var);
		List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9(L_0, /*hidden argument*/List_1__ctor_m30C52A4F2828D86CA3FAB0B1B583948F4DA9F1F9_RuntimeMethod_var);
		__this->set_TestDeviceHashedIds_1(L_0);
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm::.ctor(GoogleMobileAds.Ump.Common.IConsentFormClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentForm__ctor_m8F9A01A5586D4314EFC0BC6659A5C8CBEB90D862 (ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E * __this, RuntimeObject* ___client0, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		RuntimeObject* L_0 = ___client0;
		__this->set__client_0(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm::Load(System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentForm_Load_mF6D739366833EE3109C1F264B4E5BF5F98016F8F (Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * ___formLoadCallback0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1CF0A2B6F36249D56CFC63DAB4D5BC4261418975_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m444618FD30337AA9758A791CE803EE060E84C3BB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_0 = (U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 *)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525_il2cpp_TypeInfo_var);
		U3CLoadU3Ec__AnonStorey0__ctor_m8E661B2BCDB91D83C55F8340F9752BBFFCDA1A6F(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_1 = V_0;
		Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * L_2 = ___formLoadCallback0;
		NullCheck(L_1);
		L_1->set_formLoadCallback_0(L_2);
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_3 = V_0;
		RuntimeObject* L_4;
		L_4 = ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920(/*hidden argument*/NULL);
		NullCheck(L_4);
		RuntimeObject* L_5;
		L_5 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(0 /* GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentFormClient() */, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var, L_4);
		NullCheck(L_3);
		L_3->set_client_1(L_5);
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_6 = V_0;
		NullCheck(L_6);
		RuntimeObject* L_7 = L_6->get_client_1();
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_8 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_9 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_9, L_8, (intptr_t)((intptr_t)U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1CF0A2B6F36249D56CFC63DAB4D5BC4261418975_RuntimeMethod_var), /*hidden argument*/NULL);
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_10 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_11 = (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *)il2cpp_codegen_object_new(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443(L_11, L_10, (intptr_t)((intptr_t)U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m444618FD30337AA9758A791CE803EE060E84C3BB_RuntimeMethod_var), /*hidden argument*/Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		NullCheck(L_7);
		InterfaceActionInvoker2< Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * >::Invoke(0 /* System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::Load(System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>) */, IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var, L_7, L_9, L_11);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentForm_Show_m8351A9290BBA523E2A89AF47E172A06FF31D69D8 (ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E * __this, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CShowU3Ec__AnonStorey2_U3CU3Em__0_m38174121A671B27509DA35C2F861E9118EF44123_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * V_0 = NULL;
	{
		U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * L_0 = (U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 *)il2cpp_codegen_object_new(U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212_il2cpp_TypeInfo_var);
		U3CShowU3Ec__AnonStorey2__ctor_m34D3325E5D9192F8253C4E21500582C7660A0575(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * L_1 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_2 = ___onDismissed0;
		NullCheck(L_1);
		L_1->set_onDismissed_0(L_2);
		RuntimeObject* L_3 = __this->get__client_0();
		U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * L_4 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_5 = (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *)il2cpp_codegen_object_new(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443(L_5, L_4, (intptr_t)((intptr_t)U3CShowU3Ec__AnonStorey2_U3CU3Em__0_m38174121A671B27509DA35C2F861E9118EF44123_RuntimeMethod_var), /*hidden argument*/Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		NullCheck(L_3);
		InterfaceActionInvoker1< Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * >::Invoke(1 /* System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>) */, IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var, L_3, L_5);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentForm_LoadAndShowConsentFormIfRequired_mC795C84B7D7B008CDA73A16C75A5BEB29AE1A143 (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___onDismissed0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m4D8F5A1FC9EDEAF5EB5B25E1C2D14F6968A5FB87_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * V_0 = NULL;
	RuntimeObject* V_1 = NULL;
	{
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * L_0 = (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 *)il2cpp_codegen_object_new(U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49_il2cpp_TypeInfo_var);
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_m8AFD2FE260D4B698ED3579C9622B6FACD51FF590(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * L_1 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_2 = ___onDismissed0;
		NullCheck(L_1);
		L_1->set_onDismissed_0(L_2);
		RuntimeObject* L_3;
		L_3 = ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920(/*hidden argument*/NULL);
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(0 /* GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentFormClient() */, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var, L_3);
		V_1 = L_4;
		RuntimeObject* L_5 = V_1;
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * L_6 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_7 = (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *)il2cpp_codegen_object_new(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443(L_7, L_6, (intptr_t)((intptr_t)U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m4D8F5A1FC9EDEAF5EB5B25E1C2D14F6968A5FB87_RuntimeMethod_var), /*hidden argument*/Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		NullCheck(L_5);
		InterfaceActionInvoker1< Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * >::Invoke(2 /* System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>) */, IConsentFormClient_t66C0C8249DD3D4897E5D8DD3CDB9380CDF1565C4_il2cpp_TypeInfo_var, L_5, L_7);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.ConsentInformation::get_ClientFactory()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ((ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_StaticFields*)il2cpp_codegen_static_fields_for(ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_il2cpp_TypeInfo_var))->get__clientFactory_0();
		if (L_0)
		{
			goto IL_0014;
		}
	}
	{
		RuntimeObject* L_1;
		L_1 = Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7(/*hidden argument*/NULL);
		((ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_StaticFields*)il2cpp_codegen_static_fields_for(ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_il2cpp_TypeInfo_var))->set__clientFactory_0(L_1);
	}

IL_0014:
	{
		RuntimeObject* L_2 = ((ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_StaticFields*)il2cpp_codegen_static_fields_for(ConsentInformation_t791CA3EA7E622BCAE9CC8168D274C71E0F675C34_il2cpp_TypeInfo_var))->get__clientFactory_0();
		return L_2;
	}
}
// GoogleMobileAds.Ump.Api.ConsentStatus GoogleMobileAds.Ump.Api.ConsentInformation::get_ConsentStatus()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ConsentInformation_get_ConsentStatus_mB55EE4B3C368D0CCB5675FBCF717EC96EEC9F55B (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	{
		RuntimeObject* L_0;
		L_0 = ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920(/*hidden argument*/NULL);
		NullCheck(L_0);
		RuntimeObject* L_1;
		L_1 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(1 /* GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentInformationClient() */, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var, L_0);
		V_0 = L_1;
		RuntimeObject* L_2 = V_0;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = InterfaceFuncInvoker0< int32_t >::Invoke(1 /* System.Int32 GoogleMobileAds.Ump.Common.IConsentInformationClient::GetConsentStatus() */, IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var, L_2);
		return (int32_t)(L_3);
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentInformation_Update_m934E9851532297C0A2732A53C14DFDB87D1C38FD (ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * ___request0, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * ___consentInfoUpdateCallback1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mE789E6055115C2D195F9FC002D46C1B957CCB1D8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mA811CC59FFF687B19D9EF5F8EE15759726ECA7FF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * V_0 = NULL;
	RuntimeObject* V_1 = NULL;
	{
		U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * L_0 = (U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE *)il2cpp_codegen_object_new(U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE_il2cpp_TypeInfo_var);
		U3CUpdateU3Ec__AnonStorey0__ctor_mA550557A6E4952249FFC171CBF181BF8835915DD(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * L_1 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_2 = ___consentInfoUpdateCallback1;
		NullCheck(L_1);
		L_1->set_consentInfoUpdateCallback_0(L_2);
		RuntimeObject* L_3;
		L_3 = ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920(/*hidden argument*/NULL);
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(1 /* GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentInformationClient() */, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var, L_3);
		V_1 = L_4;
		RuntimeObject* L_5 = V_1;
		ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * L_6 = ___request0;
		U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * L_7 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_8 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_8, L_7, (intptr_t)((intptr_t)U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mE789E6055115C2D195F9FC002D46C1B957CCB1D8_RuntimeMethod_var), /*hidden argument*/NULL);
		U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * L_9 = V_0;
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_10 = (Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD *)il2cpp_codegen_object_new(Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD_il2cpp_TypeInfo_var);
		Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443(L_10, L_9, (intptr_t)((intptr_t)U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mA811CC59FFF687B19D9EF5F8EE15759726ECA7FF_RuntimeMethod_var), /*hidden argument*/Action_1__ctor_m3FB1FB5C0A8BE830A32B45C45C5BBC382191B443_RuntimeMethod_var);
		NullCheck(L_5);
		InterfaceActionInvoker3< ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 *, Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *, Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * >::Invoke(0 /* System.Void GoogleMobileAds.Ump.Common.IConsentInformationClient::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>) */, IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var, L_5, L_6, L_8, L_10);
		return;
	}
}
// System.Boolean GoogleMobileAds.Ump.Api.ConsentInformation::CanRequestAds()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ConsentInformation_CanRequestAds_mE4EBA2C5AE2C6A7AF24922540D4A481C28DFE330 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	{
		RuntimeObject* L_0;
		L_0 = ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920(/*hidden argument*/NULL);
		NullCheck(L_0);
		RuntimeObject* L_1;
		L_1 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(1 /* GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentInformationClient() */, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var, L_0);
		V_0 = L_1;
		RuntimeObject* L_2 = V_0;
		NullCheck(L_2);
		bool L_3;
		L_3 = InterfaceFuncInvoker0< bool >::Invoke(2 /* System.Boolean GoogleMobileAds.Ump.Common.IConsentInformationClient::CanRequestAds() */, IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var, L_2);
		return L_3;
	}
}
// System.Boolean GoogleMobileAds.Ump.Api.ConsentInformation::IsConsentFormAvailable()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ConsentInformation_IsConsentFormAvailable_m4B479DA906396E823F8244247900192C7B53C8BA (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	{
		RuntimeObject* L_0;
		L_0 = ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920(/*hidden argument*/NULL);
		NullCheck(L_0);
		RuntimeObject* L_1;
		L_1 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(1 /* GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentInformationClient() */, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var, L_0);
		V_0 = L_1;
		RuntimeObject* L_2 = V_0;
		NullCheck(L_2);
		bool L_3;
		L_3 = InterfaceFuncInvoker0< bool >::Invoke(3 /* System.Boolean GoogleMobileAds.Ump.Common.IConsentInformationClient::IsConsentFormAvailable() */, IConsentInformationClient_tA7B5CE9646678154880F1168D258FD7436957951_il2cpp_TypeInfo_var, L_2);
		return L_3;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentRequestParameters::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConsentRequestParameters__ctor_mCE2AA9DE4F2C121A54076D50D1FF2699E9113E27 (ConsentRequestParameters_t35399BA753CA2526CE6DA6ECD534088438025F17 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 * L_0 = (ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0 *)il2cpp_codegen_object_new(ConsentDebugSettings_tD2CD7F371377FF27D763107CB2221DC829047FF0_il2cpp_TypeInfo_var);
		ConsentDebugSettings__ctor_m7953C457CCE387F1AB1165EEB792209003F2F8EC(L_0, /*hidden argument*/NULL);
		__this->set_ConsentDebugSettings_1(L_0);
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.FormError::.ctor(System.Int32,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, int32_t ___errorCode0, String_t* ___message1, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		int32_t L_0 = ___errorCode0;
		FormError_set_ErrorCode_mAAA6A7B5ECDFE56E892C9D2DFF1ACC6E7D6DF039_inline(__this, L_0, /*hidden argument*/NULL);
		String_t* L_1 = ___message1;
		FormError_set_Message_mBF54D641DB7C616DD97257F3CEE2E1C4FCD92029_inline(__this, L_1, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.FormError::set_ErrorCode(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FormError_set_ErrorCode_mAAA6A7B5ECDFE56E892C9D2DFF1ACC6E7D6DF039 (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CErrorCodeU3Ek__BackingField_0(L_0);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.FormError::set_Message(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FormError_set_Message_mBF54D641DB7C616DD97257F3CEE2E1C4FCD92029 (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CMessageU3Ek__BackingField_1(L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.Utils::GetClientFactory()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_GetType_m2D692327E10692E11116805CC604CD47F144A9E4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral87D3D808CD24C33F1425FFDE4A0710FC4BBC9C19);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE4186B0B875F8115D04109741D50C304CE9A671F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE707B37FA85D7F3FB6BF6DA486F7250D62EBC726);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	Type_t * V_1 = NULL;
	{
		V_0 = (String_t*)NULL;
		int32_t L_0;
		L_0 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)8))))
		{
			goto IL_0018;
		}
	}
	{
		V_0 = _stringLiteral87D3D808CD24C33F1425FFDE4A0710FC4BBC9C19;
		goto IL_0035;
	}

IL_0018:
	{
		int32_t L_1;
		L_1 = Application_get_platform_mB22F7F39CDD46667C3EF64507E55BB7DA18F66C4(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_002f;
		}
	}
	{
		V_0 = _stringLiteralE4186B0B875F8115D04109741D50C304CE9A671F;
		goto IL_0035;
	}

IL_002f:
	{
		V_0 = _stringLiteralE707B37FA85D7F3FB6BF6DA486F7250D62EBC726;
	}

IL_0035:
	{
		String_t* L_2 = V_0;
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_3;
		L_3 = il2cpp_codegen_get_type(Type_GetType_m2D692327E10692E11116805CC604CD47F144A9E4_RuntimeMethod_var, L_2, Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7_RuntimeMethod_var);
		V_1 = L_3;
		Type_t * L_4 = V_1;
		RuntimeObject * L_5;
		L_5 = Activator_CreateInstance_m1BACAB5F4FBF138CCCB537DDCB0683A2AC064295(L_4, /*hidden argument*/NULL);
		return ((RuntimeObject*)Castclass((RuntimeObject*)L_5, IUmpClientFactory_t1B6B82F1114B7203E5E4E2C298C460B542A56DA0_il2cpp_TypeInfo_var));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m8E661B2BCDB91D83C55F8340F9752BBFFCDA1A6F (U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1CF0A2B6F36249D56CFC63DAB4D5BC4261418975 (U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m11CB34935A9C66D1220FBF3B27563652742DF13C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * L_0 = __this->get_formLoadCallback_0();
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_1 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_1, __this, (intptr_t)((intptr_t)U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m11CB34935A9C66D1220FBF3B27563652742DF13C_RuntimeMethod_var), /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381(L_1, /*hidden argument*/NULL);
	}

IL_001c:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__1(GoogleMobileAds.Ump.Api.FormError)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m444618FD30337AA9758A791CE803EE060E84C3BB (U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * __this, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mCD16116799A31497AD04EA3ADDF4C573655C0EF7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * L_0 = (U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE *)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE_il2cpp_TypeInfo_var);
		U3CLoadU3Ec__AnonStorey1__ctor_m63B0FCA7692DE144B9260C50C5E9E63B7FAE6FD2(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * L_1 = V_0;
		NullCheck(L_1);
		L_1->set_U3CU3Ef__refU240_1(__this);
		U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * L_2 = V_0;
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_3 = ___error0;
		NullCheck(L_2);
		L_2->set_error_0(L_3);
		Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * L_4 = __this->get_formLoadCallback_0();
		if (!L_4)
		{
			goto IL_0030;
		}
	}
	{
		U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * L_5 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_6 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_6, L_5, (intptr_t)((intptr_t)U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mCD16116799A31497AD04EA3ADDF4C573655C0EF7_RuntimeMethod_var), /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381(L_6, /*hidden argument*/NULL);
	}

IL_0030:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m11CB34935A9C66D1220FBF3B27563652742DF13C (U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * L_0 = __this->get_formLoadCallback_0();
		RuntimeObject* L_1 = __this->get_client_1();
		ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E * L_2 = (ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E *)il2cpp_codegen_object_new(ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E_il2cpp_TypeInfo_var);
		ConsentForm__ctor_m8F9A01A5586D4314EFC0BC6659A5C8CBEB90D862(L_2, L_1, /*hidden argument*/NULL);
		NullCheck(L_0);
		Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C(L_0, L_2, (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)NULL, /*hidden argument*/Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_m8AFD2FE260D4B698ED3579C9622B6FACD51FF590 (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::<>m__0(GoogleMobileAds.Ump.Api.FormError)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m4D8F5A1FC9EDEAF5EB5B25E1C2D14F6968A5FB87 (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * __this, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_m21DA3654C3294D3962D16163AD770CCBB4743C54_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * V_0 = NULL;
	{
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * L_0 = (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 *)il2cpp_codegen_object_new(U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293_il2cpp_TypeInfo_var);
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m93560390999C3D79C039637F816CC46AEC4ABC73(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * L_1 = V_0;
		NullCheck(L_1);
		L_1->set_U3CU3Ef__refU244_1(__this);
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * L_2 = V_0;
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_3 = ___error0;
		NullCheck(L_2);
		L_2->set_error_0(L_3);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_4 = __this->get_onDismissed_0();
		if (!L_4)
		{
			goto IL_0030;
		}
	}
	{
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * L_5 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_6 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_6, L_5, (intptr_t)((intptr_t)U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_m21DA3654C3294D3962D16163AD770CCBB4743C54_RuntimeMethod_var), /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381(L_6, /*hidden argument*/NULL);
	}

IL_0030:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CShowU3Ec__AnonStorey2__ctor_m34D3325E5D9192F8253C4E21500582C7660A0575 (U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::<>m__0(GoogleMobileAds.Ump.Api.FormError)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CShowU3Ec__AnonStorey2_U3CU3Em__0_m38174121A671B27509DA35C2F861E9118EF44123 (U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * __this, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CShowU3Ec__AnonStorey3_U3CU3Em__0_m5C652CABA32FB41EC6CECC0503ABF7DE2AFB7FB2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * V_0 = NULL;
	{
		U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * L_0 = (U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED *)il2cpp_codegen_object_new(U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED_il2cpp_TypeInfo_var);
		U3CShowU3Ec__AnonStorey3__ctor_m2300B413E984C95454C93C9F1B2F180B8A984095(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * L_1 = V_0;
		NullCheck(L_1);
		L_1->set_U3CU3Ef__refU242_1(__this);
		U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * L_2 = V_0;
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_3 = ___error0;
		NullCheck(L_2);
		L_2->set_error_0(L_3);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_4 = __this->get_onDismissed_0();
		if (!L_4)
		{
			goto IL_0030;
		}
	}
	{
		U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * L_5 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_6 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_6, L_5, (intptr_t)((intptr_t)U3CShowU3Ec__AnonStorey3_U3CU3Em__0_m5C652CABA32FB41EC6CECC0503ABF7DE2AFB7FB2_RuntimeMethod_var), /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381(L_6, /*hidden argument*/NULL);
	}

IL_0030:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey0__ctor_mA550557A6E4952249FFC171CBF181BF8835915DD (U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mE789E6055115C2D195F9FC002D46C1B957CCB1D8 (U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m528F4F63FA761B55DD63EC85CB14ECC6E507677D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = __this->get_consentInfoUpdateCallback_0();
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_1 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_1, __this, (intptr_t)((intptr_t)U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m528F4F63FA761B55DD63EC85CB14ECC6E507677D_RuntimeMethod_var), /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381(L_1, /*hidden argument*/NULL);
	}

IL_001c:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__1(GoogleMobileAds.Ump.Api.FormError)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mA811CC59FFF687B19D9EF5F8EE15759726ECA7FF (U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * __this, FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * ___error0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_mAEEFDD424A26FA8B8C9722924CD4D8749D616627_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * V_0 = NULL;
	{
		U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * L_0 = (U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF *)il2cpp_codegen_object_new(U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF_il2cpp_TypeInfo_var);
		U3CUpdateU3Ec__AnonStorey1__ctor_m4D16F1DB530771F57AF4C88CFB5FCB51669044CE(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * L_1 = V_0;
		NullCheck(L_1);
		L_1->set_U3CU3Ef__refU240_1(__this);
		U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * L_2 = V_0;
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_3 = ___error0;
		NullCheck(L_2);
		L_2->set_error_0(L_3);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_4 = __this->get_consentInfoUpdateCallback_0();
		if (!L_4)
		{
			goto IL_0030;
		}
	}
	{
		U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * L_5 = V_0;
		Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 * L_6 = (Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6 *)il2cpp_codegen_object_new(Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_il2cpp_TypeInfo_var);
		Action__ctor_m07BE5EE8A629FBBA52AE6356D57A0D371BE2574B(L_6, L_5, (intptr_t)((intptr_t)U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_mAEEFDD424A26FA8B8C9722924CD4D8749D616627_RuntimeMethod_var), /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381(L_6, /*hidden argument*/NULL);
	}

IL_0030:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m528F4F63FA761B55DD63EC85CB14ECC6E507677D (U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_0 = __this->get_consentInfoUpdateCallback_0();
		NullCheck(L_0);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_0, (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 *)NULL, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m63B0FCA7692DE144B9260C50C5E9E63B7FAE6FD2 (U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mCD16116799A31497AD04EA3ADDF4C573655C0EF7 (U3CLoadU3Ec__AnonStorey1_t8D3383919C909023830C34AF16D21D1C710D1EEE * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CLoadU3Ec__AnonStorey0_t9EA86384888EEC1E00DA311203A51C2499FC8525 * L_0 = __this->get_U3CU3Ef__refU240_1();
		NullCheck(L_0);
		Action_2_tC9B70C22037BEFDDBDB930B81E15D388FC735DCC * L_1 = L_0->get_formLoadCallback_0();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_2 = __this->get_error_0();
		NullCheck(L_1);
		Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C(L_1, (ConsentForm_t9A9AA2D103115F38D944636B7B2F15E8C05A118E *)NULL, L_2, /*hidden argument*/Action_2_Invoke_m6702D53200B95E289368C53435E140750373B23C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m93560390999C3D79C039637F816CC46AEC4ABC73 (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_m21DA3654C3294D3962D16163AD770CCBB4743C54 (U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_tD0F3BF98E172FF5E9DAE8D48B8761644BF025293 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_t1885E0C38B4D2190C827EF7A8E462DA418F58B49 * L_0 = __this->get_U3CU3Ef__refU244_1();
		NullCheck(L_0);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_1 = L_0->get_onDismissed_0();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_2 = __this->get_error_0();
		NullCheck(L_1);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_1, L_2, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CShowU3Ec__AnonStorey3__ctor_m2300B413E984C95454C93C9F1B2F180B8A984095 (U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CShowU3Ec__AnonStorey3_U3CU3Em__0_m5C652CABA32FB41EC6CECC0503ABF7DE2AFB7FB2 (U3CShowU3Ec__AnonStorey3_t8848D9F3A98B540AF80CE0FC4184DE2AAC5748ED * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CShowU3Ec__AnonStorey2_t473633911707C90E311435A282983998925D0212 * L_0 = __this->get_U3CU3Ef__refU242_1();
		NullCheck(L_0);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_1 = L_0->get_onDismissed_0();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_2 = __this->get_error_0();
		NullCheck(L_1);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_1, L_2, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey1__ctor_m4D16F1DB530771F57AF4C88CFB5FCB51669044CE (U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_mAEEFDD424A26FA8B8C9722924CD4D8749D616627 (U3CUpdateU3Ec__AnonStorey1_t41026D542AB552884B1B8A04DAC774CA437B49BF * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CUpdateU3Ec__AnonStorey0_t09EE3237FFEAB4E307FE872AC3DDE461E5FBAEAE * L_0 = __this->get_U3CU3Ef__refU240_1();
		NullCheck(L_0);
		Action_1_t84F61A36AAAB8499049436B9413C5A318CF7A7CD * L_1 = L_0->get_consentInfoUpdateCallback_0();
		FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * L_2 = __this->get_error_0();
		NullCheck(L_1);
		Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827(L_1, L_2, /*hidden argument*/Action_1_Invoke_m77F5A40355EDC668B632154181BE92E5F9120827_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FormError_set_ErrorCode_mAAA6A7B5ECDFE56E892C9D2DFF1ACC6E7D6DF039_inline (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_U3CErrorCodeU3Ek__BackingField_0(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FormError_set_Message_mBF54D641DB7C616DD97257F3CEE2E1C4FCD92029_inline (FormError_t29EA4DC8BAE8D4F7391E42BA351A7B0FC1BB3AA3 * __this, String_t* ___value0, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___value0;
		__this->set_U3CMessageU3Ek__BackingField_1(L_0);
		return;
	}
}
