﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif








IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END




IL2CPP_EXTERN_C void DelegatePInvokeWrapper_Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6();
IL2CPP_EXTERN_C_CONST RuntimeType Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60();
IL2CPP_EXTERN_C_CONST RuntimeType AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_0_0_0;
IL2CPP_EXTERN_C void AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_0_0_0;
IL2CPP_EXTERN_C void AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_0_0_0;
IL2CPP_EXTERN_C void AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_0_0_0;
IL2CPP_EXTERN_C void AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_0_0_0;
IL2CPP_EXTERN_C void AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_0_0_0;
IL2CPP_EXTERN_C void Assembly_t_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Assembly_t_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Assembly_t_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Assembly_t_0_0_0;
IL2CPP_EXTERN_C void AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_0_0_0;
IL2CPP_EXTERN_C void AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_0_0_0;
IL2CPP_EXTERN_C void AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_0_0_0;
IL2CPP_EXTERN_C void AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_0_0_0;
IL2CPP_EXTERN_C void AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_0_0_0;
IL2CPP_EXTERN_C void AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_0_0_0;
IL2CPP_EXTERN_C void AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_0_0_0;
IL2CPP_EXTERN_C void BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_0_0_0;
IL2CPP_EXTERN_C void CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_0_0_0;
IL2CPP_EXTERN_C void CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_0_0_0;
IL2CPP_EXTERN_C void CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_0_0_0;
IL2CPP_EXTERN_C void CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_0_0_0;
IL2CPP_EXTERN_C void Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_0_0_0;
IL2CPP_EXTERN_C void ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_0_0_0;
IL2CPP_EXTERN_C void ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_0_0_0;
IL2CPP_EXTERN_C void Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_0_0_0;
IL2CPP_EXTERN_C void ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_0_0_0;
IL2CPP_EXTERN_C void Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_CrossContextDelegate_t12C7A08ED124090185A3E209E6CA9E28148A7682();
IL2CPP_EXTERN_C_CONST RuntimeType CrossContextDelegate_t12C7A08ED124090185A3E209E6CA9E28148A7682_0_0_0;
IL2CPP_EXTERN_C void CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_0_0_0;
IL2CPP_EXTERN_C void CultureData_t53CDF1C5F789A28897415891667799420D3C5529_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CultureData_t53CDF1C5F789A28897415891667799420D3C5529_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CultureData_t53CDF1C5F789A28897415891667799420D3C5529_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CultureData_t53CDF1C5F789A28897415891667799420D3C5529_0_0_0;
IL2CPP_EXTERN_C void CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_0_0_0;
IL2CPP_EXTERN_C void CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_0_0_0;
IL2CPP_EXTERN_C void CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_0_0_0;
IL2CPP_EXTERN_C void DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_0_0_0;
IL2CPP_EXTERN_C void DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_0_0_0;
IL2CPP_EXTERN_C void DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_0_0_0;
IL2CPP_EXTERN_C void Delegate_t_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Delegate_t_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Delegate_t_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Delegate_t_0_0_0;
IL2CPP_EXTERN_C void DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_0_0_0;
IL2CPP_EXTERN_C void DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_0_0_0;
IL2CPP_EXTERN_C void DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_0_0_0;
IL2CPP_EXTERN_C void Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Enum_t23B90B40F60E677A8025267341651C94AE079CDA_0_0_0;
IL2CPP_EXTERN_C void Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_0_0_0;
IL2CPP_EXTERN_C void Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_0_0_0;
IL2CPP_EXTERN_C void Exception_t_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Exception_t_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Exception_t_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Exception_t_0_0_0;
IL2CPP_EXTERN_C void ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_0_0_0;
IL2CPP_EXTERN_C void ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_0_0_0;
IL2CPP_EXTERN_C void FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_0_0_0;
IL2CPP_EXTERN_C void FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_0_0_0;
IL2CPP_EXTERN_C void FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_0_0_0;
IL2CPP_EXTERN_C void GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_0_0_0;
IL2CPP_EXTERN_C void GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_0_0_0;
IL2CPP_EXTERN_C void GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_0_0_0;
IL2CPP_EXTERN_C void Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_0_0_0;
IL2CPP_EXTERN_C void HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_0_0_0;
IL2CPP_EXTERN_C void IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_0_0_0;
IL2CPP_EXTERN_C void IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_0_0_0;
IL2CPP_EXTERN_C void IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_0_0_0;
IL2CPP_EXTERN_C void InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_0_0_0;
IL2CPP_EXTERN_C void InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_0_0_0;
IL2CPP_EXTERN_C void InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_0_0_0;
IL2CPP_EXTERN_C void InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_0_0_0;
IL2CPP_EXTERN_C void IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_0_0_0;
IL2CPP_EXTERN_C void IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_0_0_0;
IL2CPP_EXTERN_C void InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_0_0_0;
IL2CPP_EXTERN_C void InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_0_0_0;
IL2CPP_EXTERN_C void LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_0_0_0;
IL2CPP_EXTERN_C void LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_0_0_0;
IL2CPP_EXTERN_C void LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_0_0_0;
IL2CPP_EXTERN_C void LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_0_0_0;
IL2CPP_EXTERN_C void MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_0_0_0;
IL2CPP_EXTERN_C void MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_0_0_0;
IL2CPP_EXTERN_C void MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_0_0_0;
IL2CPP_EXTERN_C void Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_0_0_0;
IL2CPP_EXTERN_C void MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_0_0_0;
IL2CPP_EXTERN_C void MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_0_0_0;
IL2CPP_EXTERN_C void MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_0_0_0;
IL2CPP_EXTERN_C void MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_0_0_0;
IL2CPP_EXTERN_C void MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_0_0_0;
IL2CPP_EXTERN_C void MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_0_0_0;
IL2CPP_EXTERN_C void MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_0_0_0;
IL2CPP_EXTERN_C void MulticastDelegate_t_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MulticastDelegate_t_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MulticastDelegate_t_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MulticastDelegate_t_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_NativeUpdateCallback_t617743B3361FE4B086E28DDB8EDB4A7AC2490FC6();
IL2CPP_EXTERN_C_CONST RuntimeType NativeUpdateCallback_t617743B3361FE4B086E28DDB8EDB4A7AC2490FC6_0_0_0;
IL2CPP_EXTERN_C void Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_0_0_0;
IL2CPP_EXTERN_C void Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0;
IL2CPP_EXTERN_C void ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_0_0_0;
IL2CPP_EXTERN_C void ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_0_0_0;
IL2CPP_EXTERN_C void ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_0_0_0;
IL2CPP_EXTERN_C void PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_0_0_0;
IL2CPP_EXTERN_C void PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_0_0_0;
IL2CPP_EXTERN_C void PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_0_0_0;
IL2CPP_EXTERN_C void PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_0_0_0;
IL2CPP_EXTERN_C void PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_0_0_0;
IL2CPP_EXTERN_C void RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_0_0_0;
IL2CPP_EXTERN_C void RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_0_0_0;
IL2CPP_EXTERN_C void RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_0_0_0;
IL2CPP_EXTERN_C void RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_0_0_0;
IL2CPP_EXTERN_C void RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_0_0_0;
IL2CPP_EXTERN_C void ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_0_0_0;
IL2CPP_EXTERN_C void ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_0_0_0;
IL2CPP_EXTERN_C void SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_0_0_0;
IL2CPP_EXTERN_C void ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_0_0_0;
IL2CPP_EXTERN_C void SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_SerializationEventHandler_t3033BE1E86AE40A7533AD615FF9122FC8ED0B7C1();
IL2CPP_EXTERN_C_CONST RuntimeType SerializationEventHandler_t3033BE1E86AE40A7533AD615FF9122FC8ED0B7C1_0_0_0;
IL2CPP_EXTERN_C void SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_0_0_0;
IL2CPP_EXTERN_C void SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_0_0_0;
IL2CPP_EXTERN_C void SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_0_0_0;
IL2CPP_EXTERN_C void SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_0_0_0;
IL2CPP_EXTERN_C void SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_0_0_0;
IL2CPP_EXTERN_C void SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_0_0_0;
IL2CPP_EXTERN_C void StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_0_0_0;
IL2CPP_EXTERN_C void StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_0_0_0;
IL2CPP_EXTERN_C void TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_0_0_0;
IL2CPP_EXTERN_C void TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_0_0_0;
IL2CPP_EXTERN_C void TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_0_0_0;
IL2CPP_EXTERN_C void TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_ThreadStart_tA13019555BA3CB2B0128F0880760196BF790E687();
IL2CPP_EXTERN_C_CONST RuntimeType ThreadStart_tA13019555BA3CB2B0128F0880760196BF790E687_0_0_0;
IL2CPP_EXTERN_C void TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_0_0_0;
IL2CPP_EXTERN_C void TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_0_0_0;
IL2CPP_EXTERN_C void UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_UnityAction_t22E545F8BE0A62EE051C6A83E209587A0DB1C099();
IL2CPP_EXTERN_C_CONST RuntimeType UnityAction_t22E545F8BE0A62EE051C6A83E209587A0DB1C099_0_0_0;
IL2CPP_EXTERN_C void ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_0_0_0;
IL2CPP_EXTERN_C void VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_0_0_0;
IL2CPP_EXTERN_C void WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_0_0_0;
IL2CPP_EXTERN_C void WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_0_0_0;
IL2CPP_EXTERN_C void WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_0_0_0;
IL2CPP_EXTERN_C void YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_0_0_0;
IL2CPP_EXTERN_C void __DTString_t594255B76730E715A2A5655F8238B0029484B27A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void __DTString_t594255B76730E715A2A5655F8238B0029484B27A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void __DTString_t594255B76730E715A2A5655F8238B0029484B27A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType __DTString_t594255B76730E715A2A5655F8238B0029484B27A_0_0_0;
IL2CPP_EXTERN_C void jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_OnOverrideControllerDirtyCallback_t9E38572D7CF06EEFF943EA68082DAC68AB40476C();
IL2CPP_EXTERN_C_CONST RuntimeType OnOverrideControllerDirtyCallback_t9E38572D7CF06EEFF943EA68082DAC68AB40476C_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_LogCallback_t8C3C9B1E0F185E2A25D09DE10DD8414898698BBD();
IL2CPP_EXTERN_C_CONST RuntimeType LogCallback_t8C3C9B1E0F185E2A25D09DE10DD8414898698BBD_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_LowMemoryCallback_tF94AC614EDACA9AD4CEA3DE77FF8EFF5DA1E5240();
IL2CPP_EXTERN_C_CONST RuntimeType LowMemoryCallback_tF94AC614EDACA9AD4CEA3DE77FF8EFF5DA1E5240_0_0_0;
IL2CPP_EXTERN_C void SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_0_0_0;
IL2CPP_EXTERN_C void SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_PCMReaderCallback_t9CA1437D36509A9FAC5EDD8FF2BC3259C24D0E0B();
IL2CPP_EXTERN_C_CONST RuntimeType PCMReaderCallback_t9CA1437D36509A9FAC5EDD8FF2BC3259C24D0E0B_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_PCMSetPositionCallback_tBDD99E7C0697687F1E7B06CDD5DE444A3709CF4C();
IL2CPP_EXTERN_C_CONST RuntimeType PCMSetPositionCallback_tBDD99E7C0697687F1E7B06CDD5DE444A3709CF4C_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_AudioConfigurationChangeHandler_t1A997C51DF7B553A94DAD358F8D968308994774A();
IL2CPP_EXTERN_C_CONST RuntimeType AudioConfigurationChangeHandler_t1A997C51DF7B553A94DAD358F8D968308994774A_0_0_0;
IL2CPP_EXTERN_C void OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_0_0_0;
IL2CPP_EXTERN_C void RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_WillRenderCanvases_t459621B4F3FA2571DE0ED6B4DEF0752F2E9EE958();
IL2CPP_EXTERN_C_CONST RuntimeType WillRenderCanvases_t459621B4F3FA2571DE0ED6B4DEF0752F2E9EE958_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_InternalCancelHandler_t7F0E9BBFE542C3B0E62620118961AC10E0DFB000();
IL2CPP_EXTERN_C_CONST RuntimeType InternalCancelHandler_t7F0E9BBFE542C3B0E62620118961AC10E0DFB000_0_0_0;
IL2CPP_EXTERN_C void ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_StateChanged_tAE96F0A8860BFCD704179F6C1F376A6FAE3E25E0();
IL2CPP_EXTERN_C_CONST RuntimeType StateChanged_tAE96F0A8860BFCD704179F6C1F376A6FAE3E25E0_0_0_0;
IL2CPP_EXTERN_C void Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_0_0_0;
IL2CPP_EXTERN_C void Resources_tA64317917B3D01310E84588407113D059D802DEB_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Resources_tA64317917B3D01310E84588407113D059D802DEB_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Resources_tA64317917B3D01310E84588407113D059D802DEB_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Resources_tA64317917B3D01310E84588407113D059D802DEB_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_DisplaysUpdatedDelegate_tC6A6AD44FAD98C9E28479FFF4BD3D9932458A6A1();
IL2CPP_EXTERN_C_CONST RuntimeType DisplaysUpdatedDelegate_tC6A6AD44FAD98C9E28479FFF4BD3D9932458A6A1_0_0_0;
IL2CPP_EXTERN_C void Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_ReadDelegate_tB245FDB608C11A53AC71F333C1A6BE3D7CDB21BB();
IL2CPP_EXTERN_C_CONST RuntimeType ReadDelegate_tB245FDB608C11A53AC71F333C1A6BE3D7CDB21BB_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_WriteDelegate_tF68E6D874C089E69933FA2B9A0C1C6639929C4F6();
IL2CPP_EXTERN_C_CONST RuntimeType WriteDelegate_tF68E6D874C089E69933FA2B9A0C1C6639929C4F6_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_FontTextureRebuildCallback_tBF11A511EBD8D237A1C5885D460B42A45DDBB2DB();
IL2CPP_EXTERN_C_CONST RuntimeType FontTextureRebuildCallback_tBF11A511EBD8D237A1C5885D460B42A45DDBB2DB_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_Action_tA166CCE0E00A94BB300974487FE20CE9F118E0FC();
IL2CPP_EXTERN_C_CONST RuntimeType Action_tA166CCE0E00A94BB300974487FE20CE9F118E0FC_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_SWIG_CompletionDelegate_tB2D099DCC04B71228A5C8BF73BC0D5114A6ACF72();
IL2CPP_EXTERN_C_CONST RuntimeType SWIG_CompletionDelegate_tB2D099DCC04B71228A5C8BF73BC0D5114A6ACF72_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_WindowFunction_tFA5DBAB811627D7B0946C4AAD398D4CC154C174D();
IL2CPP_EXTERN_C_CONST RuntimeType WindowFunction_tFA5DBAB811627D7B0946C4AAD398D4CC154C174D_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_SkinChangedDelegate_t8BECC691E2A259B07F4A51D8F1A639B83F055E1E();
IL2CPP_EXTERN_C_CONST RuntimeType SkinChangedDelegate_t8BECC691E2A259B07F4A51D8F1A639B83F055E1E_0_0_0;
IL2CPP_EXTERN_C void GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_0_0_0;
IL2CPP_EXTERN_C void bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_OnValidateInput_t721D2C2A7710D113E4909B36D9893CC6B1C69B9F();
IL2CPP_EXTERN_C_CONST RuntimeType OnValidateInput_t721D2C2A7710D113E4909B36D9893CC6B1C69B9F_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_PanicFunction__tFB4D850E5B848FFEF210623F5669284D723544EB();
IL2CPP_EXTERN_C_CONST RuntimeType PanicFunction__tFB4D850E5B848FFEF210623F5669284D723544EB_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_LogMessageDelegate_tA2E3C07710334F0F6F81098ECB5A95F7747C3E84();
IL2CPP_EXTERN_C_CONST RuntimeType LogMessageDelegate_tA2E3C07710334F0F6F81098ECB5A95F7747C3E84_0_0_0;
IL2CPP_EXTERN_C void Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_OnNavMeshPreUpdate_t5E34F761F39A1F6B898F0E729B36C0782B92D572();
IL2CPP_EXTERN_C_CONST RuntimeType OnNavMeshPreUpdate_t5E34F761F39A1F6B898F0E729B36C0782B92D572_0_0_0;
IL2CPP_EXTERN_C void NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_InvocationEntryDelegate_t751DEAE9B64F61CCD4029B67E7916F00C823E61A();
IL2CPP_EXTERN_C_CONST RuntimeType InvocationEntryDelegate_t751DEAE9B64F61CCD4029B67E7916F00C823E61A_0_0_0;
IL2CPP_EXTERN_C void FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_0_0_0;
IL2CPP_EXTERN_C void EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_0_0_0;
IL2CPP_EXTERN_C void EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_0_0_0;
IL2CPP_EXTERN_C void MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_CreateOutputMethod_t7A129D00E8823B50AEDD0C9B082C9CB3DF863876();
IL2CPP_EXTERN_C_CONST RuntimeType CreateOutputMethod_t7A129D00E8823B50AEDD0C9B082C9CB3DF863876_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_UpdateFunction_tEDC2A88F61F179480CAA9443E6ADDA3F126B8AEA();
IL2CPP_EXTERN_C_CONST RuntimeType UpdateFunction_tEDC2A88F61F179480CAA9443E6ADDA3F126B8AEA_0_0_0;
IL2CPP_EXTERN_C void originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_onBehaviorChanged_t8EDFEFBF7EE7EF8942B2D5D2D9F242B976CB87FA();
IL2CPP_EXTERN_C_CONST RuntimeType onBehaviorChanged_t8EDFEFBF7EE7EF8942B2D5D2D9F242B976CB87FA_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_onMainControllerChanged_tF09F3C466021134D0B896E2F9FC36AF8D4DCABF7();
IL2CPP_EXTERN_C_CONST RuntimeType onMainControllerChanged_tF09F3C466021134D0B896E2F9FC36AF8D4DCABF7_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_onVehicleChanged_t96D5C0BA2E24DD7B0645AE01D6F4AD32540430A9();
IL2CPP_EXTERN_C_CONST RuntimeType onVehicleChanged_t96D5C0BA2E24DD7B0645AE01D6F4AD32540430A9_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_GetRayIntersectionAllCallback_t9D6C059892DE030746D2873EB8871415BAC79311();
IL2CPP_EXTERN_C_CONST RuntimeType GetRayIntersectionAllCallback_t9D6C059892DE030746D2873EB8871415BAC79311_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_GetRayIntersectionAllNonAllocCallback_t6DAE64211C37E996B257BF2C54707DAD3474D69C();
IL2CPP_EXTERN_C_CONST RuntimeType GetRayIntersectionAllNonAllocCallback_t6DAE64211C37E996B257BF2C54707DAD3474D69C_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_GetRaycastNonAllocCallback_tA4A6A2336A9B9FEE31F8F5344576B3BB0A7B3F34();
IL2CPP_EXTERN_C_CONST RuntimeType GetRaycastNonAllocCallback_tA4A6A2336A9B9FEE31F8F5344576B3BB0A7B3F34_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_Raycast2DCallback_t125C1CA6D0148380915E597AC8ADBB93EFB0EE29();
IL2CPP_EXTERN_C_CONST RuntimeType Raycast2DCallback_t125C1CA6D0148380915E597AC8ADBB93EFB0EE29_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_Raycast3DCallback_t27A8B301052E9C6A4A7D38F95293CA129C39373F();
IL2CPP_EXTERN_C_CONST RuntimeType Raycast3DCallback_t27A8B301052E9C6A4A7D38F95293CA129C39373F_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_RaycastAllCallback_t48E12CFDCFDEA0CD7D83F9DDE1E341DBCC855005();
IL2CPP_EXTERN_C_CONST RuntimeType RaycastAllCallback_t48E12CFDCFDEA0CD7D83F9DDE1E341DBCC855005_0_0_0;
IL2CPP_EXTERN_C void LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_0_0_0;
IL2CPP_EXTERN_C void HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_0_0_0;
IL2CPP_EXTERN_C void Escape_t0479DB63473055AD46754E698B2114579D5D944E_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void Escape_t0479DB63473055AD46754E698B2114579D5D944E_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void Escape_t0479DB63473055AD46754E698B2114579D5D944E_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType Escape_t0479DB63473055AD46754E698B2114579D5D944E_0_0_0;
IL2CPP_EXTERN_C void FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_0_0_0;
IL2CPP_EXTERN_C void DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_0_0_0;
IL2CPP_EXTERN_C void TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_0_0_0;
IL2CPP_EXTERN_C void TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_0_0_0;
IL2CPP_EXTERN_C void DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_0_0_0;
IL2CPP_EXTERN_C void WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_0_0_0;
IL2CPP_EXTERN_C void UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_0_0_0;
IL2CPP_EXTERN_C void XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_0_0_0;
IL2CPP_EXTERN_C void XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_marshal_pinvoke(void* managedStructure, void* marshaledStructure);
IL2CPP_EXTERN_C void XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_marshal_pinvoke_back(void* marshaledStructure, void* managedStructure);
IL2CPP_EXTERN_C void XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_marshal_pinvoke_cleanup(void* marshaledStructure);
IL2CPP_EXTERN_C_CONST RuntimeType XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_ExceptionArgumentDelegate_t1AA80F883D60128F2D0AC8B6585E7E5E24046CA1();
IL2CPP_EXTERN_C_CONST RuntimeType ExceptionArgumentDelegate_t1AA80F883D60128F2D0AC8B6585E7E5E24046CA1_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_ExceptionDelegate_tDD1A55BD95B6728CB90312C6FD53BF55FD0AEF17();
IL2CPP_EXTERN_C_CONST RuntimeType ExceptionDelegate_tDD1A55BD95B6728CB90312C6FD53BF55FD0AEF17_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_SWIGStringDelegate_tB27940DE2B521137937A0B37C46010B0A28E7394();
IL2CPP_EXTERN_C_CONST RuntimeType SWIGStringDelegate_tB27940DE2B521137937A0B37C46010B0A28E7394_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_WindowsCancelHandler_tFD0F0B721F93ACA04D9CD9340DA39075A8FF2ACF();
IL2CPP_EXTERN_C_CONST RuntimeType WindowsCancelHandler_tFD0F0B721F93ACA04D9CD9340DA39075A8FF2ACF_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_ExceptionArgumentDelegate_t0B4E75C6FEE2D674D1B6C177ED0DEEF03B9B9E7E();
IL2CPP_EXTERN_C_CONST RuntimeType ExceptionArgumentDelegate_t0B4E75C6FEE2D674D1B6C177ED0DEEF03B9B9E7E_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_ExceptionDelegate_t73D9DC3453B171BDC76275017D99E401B4F2B6CC();
IL2CPP_EXTERN_C_CONST RuntimeType ExceptionDelegate_t73D9DC3453B171BDC76275017D99E401B4F2B6CC_0_0_0;
IL2CPP_EXTERN_C void DelegatePInvokeWrapper_SWIGStringDelegate_t2B9F99922AC4679D8AE49E4D8BDD2C52391467B1();
IL2CPP_EXTERN_C_CONST RuntimeType SWIGStringDelegate_t2B9F99922AC4679D8AE49E4D8BDD2C52391467B1_0_0_0;
IL2CPP_EXTERN_C Il2CppInteropData g_Il2CppInteropData[];
Il2CppInteropData g_Il2CppInteropData[197] = 
{
	{ DelegatePInvokeWrapper_Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6, NULL, NULL, NULL, NULL, NULL, &Action_tAF41423D285AE0862865348CF6CE51CD085ABBA6_0_0_0 } /* System.Action */,
	{ DelegatePInvokeWrapper_AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60, NULL, NULL, NULL, NULL, NULL, &AndroidJavaRunnable_tFA31E7D68EAAEB756F1B8F2EF8344C24042EDD60_0_0_0 } /* UnityEngine.AndroidJavaRunnable */,
	{ NULL, AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_marshal_pinvoke, AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_marshal_pinvoke_back, AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_marshal_pinvoke_cleanup, NULL, NULL, &AnimationCurve_t2D452A14820CEDB83BFF2C911682A4E59001AD03_0_0_0 } /* UnityEngine.AnimationCurve */,
	{ NULL, AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_marshal_pinvoke, AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_marshal_pinvoke_back, AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_marshal_pinvoke_cleanup, NULL, NULL, &AnimationEvent_tC15CA47BE450896AF876FFA75D7A8E22C2D286AF_0_0_0 } /* UnityEngine.AnimationEvent */,
	{ NULL, AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_marshal_pinvoke, AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_marshal_pinvoke_back, AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_marshal_pinvoke_cleanup, NULL, NULL, &AnimatorTransitionInfo_t7D0BAD3D274C055F1FC7ACE0F3A195CA3C9026A0_0_0_0 } /* UnityEngine.AnimatorTransitionInfo */,
	{ NULL, AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_marshal_pinvoke, AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_marshal_pinvoke_back, AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_marshal_pinvoke_cleanup, NULL, NULL, &AppDomain_tBEB6322D51DCB12C09A56A49886C2D09BA1C1A8A_0_0_0 } /* System.AppDomain */,
	{ NULL, AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_marshal_pinvoke, AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_marshal_pinvoke_back, AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_marshal_pinvoke_cleanup, NULL, NULL, &AppDomainSetup_tF2C6AD0D3A09543EAC7388BD3F6500E8527F63A8_0_0_0 } /* System.AppDomainSetup */,
	{ NULL, Assembly_t_marshal_pinvoke, Assembly_t_marshal_pinvoke_back, Assembly_t_marshal_pinvoke_cleanup, NULL, NULL, &Assembly_t_0_0_0 } /* System.Reflection.Assembly */,
	{ NULL, AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshal_pinvoke, AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshal_pinvoke_back, AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_marshal_pinvoke_cleanup, NULL, NULL, &AssemblyName_t066E458E26373ECD644F79643E9D4483212C9824_0_0_0 } /* System.Reflection.AssemblyName */,
	{ NULL, AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_marshal_pinvoke, AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_marshal_pinvoke_back, AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_marshal_pinvoke_cleanup, NULL, NULL, &AsyncMethodBuilderCore_t2C85055E04767C52B9F66144476FCBF500DBFA34_0_0_0 } /* System.Runtime.CompilerServices.AsyncMethodBuilderCore */,
	{ NULL, AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshal_pinvoke, AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshal_pinvoke_back, AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshal_pinvoke_cleanup, NULL, NULL, &AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_0_0_0 } /* UnityEngine.AsyncOperation */,
	{ NULL, AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_marshal_pinvoke, AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_marshal_pinvoke_back, AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_marshal_pinvoke_cleanup, NULL, NULL, &AsyncReadManagerMetricsFilters_t8C1F78DA967FD9457A11E672AB0FF865D6BD3787_0_0_0 } /* Unity.IO.LowLevel.Unsafe.AsyncReadManagerMetricsFilters */,
	{ NULL, AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_marshal_pinvoke, AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_marshal_pinvoke_back, AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_marshal_pinvoke_cleanup, NULL, NULL, &AsyncReadManagerRequestMetric_t3F1145613E99A2410D1AFBCE8BEFF59D07FE26E0_0_0_0 } /* Unity.IO.LowLevel.Unsafe.AsyncReadManagerRequestMetric */,
	{ NULL, AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_marshal_pinvoke, AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_marshal_pinvoke_back, AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_marshal_pinvoke_cleanup, NULL, NULL, &AsyncResult_t7AD876FCD0341D8317ADB430701F4E391E6BB75B_0_0_0 } /* System.Runtime.Remoting.Messaging.AsyncResult */,
	{ NULL, AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_marshal_pinvoke, AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_marshal_pinvoke_back, AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_marshal_pinvoke_cleanup, NULL, NULL, &AutoUnwrapSettings_tB2126FBA7D5FDDB8947FE6599B5071FE6800ED59_0_0_0 } /* UnityEngine.ProBuilder.AutoUnwrapSettings */,
	{ NULL, BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_marshal_pinvoke, BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_marshal_pinvoke_back, BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_marshal_pinvoke_cleanup, NULL, NULL, &BatchRendererGroup_t68C1EAC6F7158DC1C02C16D4E343397D5EC4574A_0_0_0 } /* UnityEngine.Rendering.BatchRendererGroup */,
	{ NULL, CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_marshal_pinvoke, CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_marshal_pinvoke_back, CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_marshal_pinvoke_cleanup, NULL, NULL, &CalendarData_t76EF6EAAED8C2BC4089643722CE589E213F7B4A4_0_0_0 } /* System.Globalization.CalendarData */,
	{ NULL, CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_marshal_pinvoke, CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_marshal_pinvoke_back, CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_marshal_pinvoke_cleanup, NULL, NULL, &CancellationCallbackCoreWorkArguments_t9ECCD883EF9DF3283696D1CE1F7A81C0F075923E_0_0_0 } /* System.Threading.CancellationCallbackCoreWorkArguments */,
	{ NULL, CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_marshal_pinvoke, CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_marshal_pinvoke_back, CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_marshal_pinvoke_cleanup, NULL, NULL, &CancellationToken_tC9D68381C9164A4BA10397257E87ADC832AF5FFD_0_0_0 } /* System.Threading.CancellationToken */,
	{ NULL, CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_marshal_pinvoke, CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_marshal_pinvoke_back, CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_marshal_pinvoke_cleanup, NULL, NULL, &CancellationTokenRegistration_t407059AA0E00ABE74F43C533E7D035C4BA451F6A_0_0_0 } /* System.Threading.CancellationTokenRegistration */,
	{ NULL, Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_marshal_pinvoke, Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_marshal_pinvoke_back, Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_marshal_pinvoke_cleanup, NULL, NULL, &Collision_tDC11F9B3834FD25DEB8C7DD1C51B635D240BBBF0_0_0_0 } /* UnityEngine.Collision */,
	{ NULL, ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_marshal_pinvoke, ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_marshal_pinvoke_back, ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_marshal_pinvoke_cleanup, NULL, NULL, &ColorTween_tB608DC1CF7A7F226B0D4DD8B269798F27CECE339_0_0_0 } /* UnityEngine.UI.CoroutineTween.ColorTween */,
	{ NULL, ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_marshal_pinvoke, ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_marshal_pinvoke_back, ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_marshal_pinvoke_cleanup, NULL, NULL, &ConsoleKeyInfo_tDA8AC07839288484FCB167A81B4FBA92ECCEAF88_0_0_0 } /* System.ConsoleKeyInfo */,
	{ NULL, Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_marshal_pinvoke, Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_marshal_pinvoke_back, Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_marshal_pinvoke_cleanup, NULL, NULL, &Context_t8A5B564FD0F970E10A97ACB8A7579FFF3EE4C678_0_0_0 } /* System.Runtime.Remoting.Contexts.Context */,
	{ NULL, ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_marshal_pinvoke, ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_marshal_pinvoke_back, ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_marshal_pinvoke_cleanup, NULL, NULL, &ControllerColliderHit_t483E787AA2D92263EC1F899BCF1FFC3F2B96D550_0_0_0 } /* UnityEngine.ControllerColliderHit */,
	{ NULL, Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_marshal_pinvoke, Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_marshal_pinvoke_back, Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_marshal_pinvoke_cleanup, NULL, NULL, &Coroutine_t899D5232EF542CB8BA70AF9ECEECA494FAA9CCB7_0_0_0 } /* UnityEngine.Coroutine */,
	{ DelegatePInvokeWrapper_CrossContextDelegate_t12C7A08ED124090185A3E209E6CA9E28148A7682, NULL, NULL, NULL, NULL, NULL, &CrossContextDelegate_t12C7A08ED124090185A3E209E6CA9E28148A7682_0_0_0 } /* System.Runtime.Remoting.Contexts.CrossContextDelegate */,
	{ NULL, CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_marshal_pinvoke, CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_marshal_pinvoke_back, CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_marshal_pinvoke_cleanup, NULL, NULL, &CullingGroup_t63379D76B9825516F762DDEDD594814B981DB307_0_0_0 } /* UnityEngine.CullingGroup */,
	{ NULL, CultureData_t53CDF1C5F789A28897415891667799420D3C5529_marshal_pinvoke, CultureData_t53CDF1C5F789A28897415891667799420D3C5529_marshal_pinvoke_back, CultureData_t53CDF1C5F789A28897415891667799420D3C5529_marshal_pinvoke_cleanup, NULL, NULL, &CultureData_t53CDF1C5F789A28897415891667799420D3C5529_0_0_0 } /* System.Globalization.CultureData */,
	{ NULL, CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshal_pinvoke, CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshal_pinvoke_back, CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_marshal_pinvoke_cleanup, NULL, NULL, &CultureInfo_t1B787142231DB79ABDCE0659823F908A040E9A98_0_0_0 } /* System.Globalization.CultureInfo */,
	{ NULL, CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_marshal_pinvoke, CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_marshal_pinvoke_back, CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_marshal_pinvoke_cleanup, NULL, NULL, &CustomAttributeNamedArgument_t618778691CF7F5B44F7177210A817A29D3DAEDDA_0_0_0 } /* System.Reflection.CustomAttributeNamedArgument */,
	{ NULL, CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_marshal_pinvoke, CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_marshal_pinvoke_back, CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_marshal_pinvoke_cleanup, NULL, NULL, &CustomAttributeTypedArgument_tE7152E8FACDD29A8E0040E151C86F436FA8E6910_0_0_0 } /* System.Reflection.CustomAttributeTypedArgument */,
	{ NULL, DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_marshal_pinvoke, DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_marshal_pinvoke_back, DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_marshal_pinvoke_cleanup, NULL, NULL, &DTSubString_t17C1E5092BC79CB2A5DA8B2B4AB2047B2BE51F74_0_0_0 } /* System.DTSubString */,
	{ NULL, DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_marshal_pinvoke, DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_marshal_pinvoke_back, DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_marshal_pinvoke_cleanup, NULL, NULL, &DateTimeRawInfo_t0054428F65AC1EA6EADE6C93D4075B3D96A47ECE_0_0_0 } /* System.DateTimeRawInfo */,
	{ NULL, DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_marshal_pinvoke, DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_marshal_pinvoke_back, DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_marshal_pinvoke_cleanup, NULL, NULL, &DateTimeResult_t44941ADE58F716AB71DABBFE9BE490F0331F3EF0_0_0_0 } /* System.DateTimeResult */,
	{ NULL, Delegate_t_marshal_pinvoke, Delegate_t_marshal_pinvoke_back, Delegate_t_marshal_pinvoke_cleanup, NULL, NULL, &Delegate_t_0_0_0 } /* System.Delegate */,
	{ NULL, DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_marshal_pinvoke, DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_marshal_pinvoke_back, DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_marshal_pinvoke_cleanup, NULL, NULL, &DictionaryEntry_tF60471FAB430320A9C7D4382BF966EAAC06D7A90_0_0_0 } /* System.Collections.DictionaryEntry */,
	{ NULL, DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_marshal_pinvoke, DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_marshal_pinvoke_back, DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_marshal_pinvoke_cleanup, NULL, NULL, &DirectionalLight_t64077C15074628F61CE703ED3A168AA8AB7F0AB7_0_0_0 } /* UnityEngine.Experimental.GlobalIllumination.DirectionalLight */,
	{ NULL, DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_marshal_pinvoke, DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_marshal_pinvoke_back, DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_marshal_pinvoke_cleanup, NULL, NULL, &DiscLight_t2F3E542C8536D7FE93D943F5336DCCE844D6CB8D_0_0_0 } /* UnityEngine.Experimental.GlobalIllumination.DiscLight */,
	{ NULL, Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshal_pinvoke, Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshal_pinvoke_back, Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshal_pinvoke_cleanup, NULL, NULL, &Enum_t23B90B40F60E677A8025267341651C94AE079CDA_0_0_0 } /* System.Enum */,
	{ NULL, Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_marshal_pinvoke, Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_marshal_pinvoke_back, Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_marshal_pinvoke_cleanup, NULL, NULL, &Ephemeron_t76EEAA1BDD5BE64FEAF9E3CD185451837EAA6208_0_0_0 } /* System.Runtime.CompilerServices.Ephemeron */,
	{ NULL, Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_marshal_pinvoke, Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_marshal_pinvoke_back, Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_marshal_pinvoke_cleanup, NULL, NULL, &Event_tED49F8EC5A2514F6E877E301B1AB7ABE4647253E_0_0_0 } /* UnityEngine.Event */,
	{ NULL, Exception_t_marshal_pinvoke, Exception_t_marshal_pinvoke_back, Exception_t_marshal_pinvoke_cleanup, NULL, NULL, &Exception_t_0_0_0 } /* System.Exception */,
	{ NULL, ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_marshal_pinvoke, ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_marshal_pinvoke_back, ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_marshal_pinvoke_cleanup, NULL, NULL, &ExceptionHandlingClause_t5ECB535787E9B1D0DF95061E051CAEDDBB363104_0_0_0 } /* System.Reflection.ExceptionHandlingClause */,
	{ NULL, ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_marshal_pinvoke, ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_marshal_pinvoke_back, ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_marshal_pinvoke_cleanup, NULL, NULL, &ExecutionContextSwitcher_t11B7DEE83408478EE3D5E29C988E5385AA9D7277_0_0_0 } /* System.Threading.ExecutionContextSwitcher */,
	{ NULL, FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_marshal_pinvoke, FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_marshal_pinvoke_back, FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_marshal_pinvoke_cleanup, NULL, NULL, &FailedToLoadScriptObject_tDD47793ADC980A7A6E4369C9E9381609453869B4_0_0_0 } /* UnityEngine.FailedToLoadScriptObject */,
	{ NULL, FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_marshal_pinvoke, FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_marshal_pinvoke_back, FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_marshal_pinvoke_cleanup, NULL, NULL, &FixedBitArray3_tEED315B1247810B1D858361D82C9A250237AF9C4_0_0_0 } /* UnityEngine.ProBuilder.Poly2Tri.FixedBitArray3 */,
	{ NULL, FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_marshal_pinvoke, FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_marshal_pinvoke_back, FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_marshal_pinvoke_cleanup, NULL, NULL, &FloatTween_tFC6A79CB4DD9D51D99523093925F926E12D2F228_0_0_0 } /* UnityEngine.UI.CoroutineTween.FloatTween */,
	{ NULL, GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_marshal_pinvoke, GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_marshal_pinvoke_back, GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_marshal_pinvoke_cleanup, NULL, NULL, &GUIContent_t39256993BF4A33F76E073488D6A2F13D678DF60E_0_0_0 } /* UnityEngine.GUIContent */,
	{ NULL, GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_marshal_pinvoke, GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_marshal_pinvoke_back, GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_marshal_pinvoke_cleanup, NULL, NULL, &GUIStyle_t29C59470ACD0A35C81EB0615653FD38C455A4726_0_0_0 } /* UnityEngine.GUIStyle */,
	{ NULL, GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_marshal_pinvoke, GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_marshal_pinvoke_back, GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_marshal_pinvoke_cleanup, NULL, NULL, &GUIStyleState_tC89202668617B1D7884980314F293AD382B9AAD9_0_0_0 } /* UnityEngine.GUIStyleState */,
	{ NULL, Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_marshal_pinvoke, Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_marshal_pinvoke_back, Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_marshal_pinvoke_cleanup, NULL, NULL, &Gradient_t297BAC6722F67728862AE2FBE760A400DA8902F2_0_0_0 } /* UnityEngine.Gradient */,
	{ NULL, HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_marshal_pinvoke, HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_marshal_pinvoke_back, HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_marshal_pinvoke_cleanup, NULL, NULL, &HumanBone_tFEE7CD9B6E62BBB95CC4A6F1AA7FC7A26541D62D_0_0_0 } /* UnityEngine.HumanBone */,
	{ NULL, IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_marshal_pinvoke, IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_marshal_pinvoke_back, IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_marshal_pinvoke_cleanup, NULL, NULL, &IOAsyncResult_t099E328DEE4054063493B8A96C1FE9AFB0EDAAF9_0_0_0 } /* System.IOAsyncResult */,
	{ NULL, IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_marshal_pinvoke, IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_marshal_pinvoke_back, IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_marshal_pinvoke_cleanup, NULL, NULL, &IOSelectorJob_t684DF541EAF1AB720C017E9DE172EA8168FDBDA9_0_0_0 } /* System.IOSelectorJob */,
	{ NULL, IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_marshal_pinvoke, IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_marshal_pinvoke_back, IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_marshal_pinvoke_cleanup, NULL, NULL, &IPv6AddressFormatter_tB4B75557A1014D1E6E250A35E5F94411EF2979BA_0_0_0 } /* System.Net.IPv6AddressFormatter */,
	{ NULL, InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_marshal_pinvoke, InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_marshal_pinvoke_back, InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_marshal_pinvoke_cleanup, NULL, NULL, &InputDevice_t69B790C68145C769BA3819DE33AA94155C77207E_0_0_0 } /* UnityEngine.XR.InputDevice */,
	{ NULL, InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_marshal_pinvoke, InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_marshal_pinvoke_back, InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_marshal_pinvoke_cleanup, NULL, NULL, &InputDevices_t50F530D78AE16C2F160416FBAE9BC04024C448CC_0_0_0 } /* UnityEngine.XR.InputDevices */,
	{ NULL, InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_marshal_pinvoke, InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_marshal_pinvoke_back, InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_marshal_pinvoke_cleanup, NULL, NULL, &InputFeatureUsage_tB971D811B38B1DA549F529BB15E60672940FB0EE_0_0_0 } /* UnityEngine.XR.InputFeatureUsage */,
	{ NULL, InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_marshal_pinvoke, InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_marshal_pinvoke_back, InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_marshal_pinvoke_cleanup, NULL, NULL, &InputRecord_t041607D11686DA35B10AE9E9F71E2448ACDCB1A8_0_0_0 } /* System.InputRecord */,
	{ NULL, IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_marshal_pinvoke, IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_marshal_pinvoke_back, IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_marshal_pinvoke_cleanup, NULL, NULL, &IntegratedSubsystem_t8FB3A371F812CF9521903AC016C64E95C7412002_0_0_0 } /* UnityEngine.IntegratedSubsystem */,
	{ NULL, IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_marshal_pinvoke, IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_marshal_pinvoke_back, IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_marshal_pinvoke_cleanup, NULL, NULL, &IntegratedSubsystemDescriptor_tDC8AF8E5B67B983E4492D784A419F01693926D7A_0_0_0 } /* UnityEngine.IntegratedSubsystemDescriptor */,
	{ NULL, InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_marshal_pinvoke, InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_marshal_pinvoke_back, InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_marshal_pinvoke_cleanup, NULL, NULL, &InternalCodePageDataItem_t885932F372A8EEC39396B0D57CC93AC72E2A3DA7_0_0_0 } /* System.Globalization.InternalCodePageDataItem */,
	{ NULL, InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_marshal_pinvoke, InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_marshal_pinvoke_back, InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_marshal_pinvoke_cleanup, NULL, NULL, &InternalEncodingDataItem_t2854F84125B1F420ABB3AA251C75E288EC87568C_0_0_0 } /* System.Globalization.InternalEncodingDataItem */,
	{ NULL, LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_marshal_pinvoke, LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_marshal_pinvoke_back, LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_marshal_pinvoke_cleanup, NULL, NULL, &LightBakingOutput_t4F4130B900C21B6DADEF7D2AEAB2F120DCC84553_0_0_0 } /* UnityEngine.LightBakingOutput */,
	{ NULL, LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_marshal_pinvoke, LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_marshal_pinvoke_back, LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_marshal_pinvoke_cleanup, NULL, NULL, &LightProbes_t32F17E0994042933C3CECAAD32AC3A5D3BB50284_0_0_0 } /* UnityEngine.LightProbes */,
	{ NULL, LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_marshal_pinvoke, LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_marshal_pinvoke_back, LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_marshal_pinvoke_cleanup, NULL, NULL, &LocalBuilder_t7D66C7BAA00271B00F8FDBE1F3D85A6223E99E16_0_0_0 } /* System.Reflection.Emit.LocalBuilder */,
	{ NULL, LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_marshal_pinvoke, LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_marshal_pinvoke_back, LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_marshal_pinvoke_cleanup, NULL, NULL, &LocalVariableInfo_t886B53D36BA0B4BA37FEEB6DB4834A6933FDAF61_0_0_0 } /* System.Reflection.LocalVariableInfo */,
	{ NULL, MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshal_pinvoke, MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshal_pinvoke_back, MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshal_pinvoke_cleanup, NULL, NULL, &MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_0_0_0 } /* System.MarshalByRefObject */,
	{ NULL, MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_marshal_pinvoke, MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_marshal_pinvoke_back, MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_marshal_pinvoke_cleanup, NULL, NULL, &MeshGenerationResult_t081845588E8932BB4BA2D6F087D2F2F0EE3373CF_0_0_0 } /* UnityEngine.XR.MeshGenerationResult */,
	{ NULL, MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_marshal_pinvoke, MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_marshal_pinvoke_back, MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_marshal_pinvoke_cleanup, NULL, NULL, &MethodBody_t994D7AC5F4F2C64BBDFA87CF62D9520EDBC44975_0_0_0 } /* System.Reflection.MethodBody */,
	{ NULL, Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_marshal_pinvoke, Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_marshal_pinvoke_back, Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_marshal_pinvoke_cleanup, NULL, NULL, &Module_tAAF0DBC4FB20AB46035441C66C41A8DB813C8CD7_0_0_0 } /* System.Reflection.Module */,
	{ NULL, MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_marshal_pinvoke, MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_marshal_pinvoke_back, MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_marshal_pinvoke_cleanup, NULL, NULL, &MonoAsyncCall_t4BAF695CDD88BF675F1E67C0CF12E3115D3F158E_0_0_0 } /* System.MonoAsyncCall */,
	{ NULL, MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_marshal_pinvoke, MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_marshal_pinvoke_back, MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_marshal_pinvoke_cleanup, NULL, NULL, &MonoEventInfo_t0748824AF7D8732CE1A1D0F67436972A448CB59F_0_0_0 } /* System.Reflection.MonoEventInfo */,
	{ NULL, MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_marshal_pinvoke, MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_marshal_pinvoke_back, MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_marshal_pinvoke_cleanup, NULL, NULL, &MonoMethodInfo_tE93FDE712D5034241FFC36C41D315D9EDD2C2D38_0_0_0 } /* System.Reflection.MonoMethodInfo */,
	{ NULL, MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_marshal_pinvoke, MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_marshal_pinvoke_back, MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_marshal_pinvoke_cleanup, NULL, NULL, &MonoMethodMessage_t0B5F9B92AC439517E0DD283EFEBAFBDBE8B12FAC_0_0_0 } /* System.Runtime.Remoting.Messaging.MonoMethodMessage */,
	{ NULL, MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_marshal_pinvoke, MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_marshal_pinvoke_back, MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_marshal_pinvoke_cleanup, NULL, NULL, &MonoPropertyInfo_tA5A058F3C4CD862912818E54A4B6152F21433B82_0_0_0 } /* System.Reflection.MonoPropertyInfo */,
	{ NULL, MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_marshal_pinvoke, MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_marshal_pinvoke_back, MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_marshal_pinvoke_cleanup, NULL, NULL, &MonoTypeInfo_tD048FE6E8A79174435DD9BA986294B02C68DFC79_0_0_0 } /* System.MonoTypeInfo */,
	{ NULL, MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_marshal_pinvoke, MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_marshal_pinvoke_back, MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_marshal_pinvoke_cleanup, NULL, NULL, &MovedFromAttributeData_tD215FAE7C2C99058DABB245C5A5EC95AEF05533C_0_0_0 } /* UnityEngine.Scripting.APIUpdating.MovedFromAttributeData */,
	{ NULL, MulticastDelegate_t_marshal_pinvoke, MulticastDelegate_t_marshal_pinvoke_back, MulticastDelegate_t_marshal_pinvoke_cleanup, NULL, NULL, &MulticastDelegate_t_0_0_0 } /* System.MulticastDelegate */,
	{ DelegatePInvokeWrapper_NativeUpdateCallback_t617743B3361FE4B086E28DDB8EDB4A7AC2490FC6, NULL, NULL, NULL, NULL, NULL, &NativeUpdateCallback_t617743B3361FE4B086E28DDB8EDB4A7AC2490FC6_0_0_0 } /* UnityEngineInternal.Input.NativeUpdateCallback */,
	{ NULL, Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_marshal_pinvoke, Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_marshal_pinvoke_back, Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_marshal_pinvoke_cleanup, NULL, NULL, &Navigation_t1CF0FFB22C0357CD64714FB7A40A275F899D363A_0_0_0 } /* UnityEngine.UI.Navigation */,
	{ NULL, Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshal_pinvoke, Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshal_pinvoke_back, Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshal_pinvoke_cleanup, NULL, NULL, &Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0 } /* UnityEngine.Object */,
	{ NULL, ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_marshal_pinvoke, ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_marshal_pinvoke_back, ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_marshal_pinvoke_cleanup, NULL, NULL, &ParameterInfo_t9D9DBDD93E685815E35F4F6D6F58E90EBC8852B7_0_0_0 } /* System.Reflection.ParameterInfo */,
	{ NULL, ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_marshal_pinvoke, ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_marshal_pinvoke_back, ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_marshal_pinvoke_cleanup, NULL, NULL, &ParameterModifier_tC1C793BD8B003B24010657487AFD17A4BA3DF6EA_0_0_0 } /* System.Reflection.ParameterModifier */,
	{ NULL, ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_marshal_pinvoke, ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_marshal_pinvoke_back, ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_marshal_pinvoke_cleanup, NULL, NULL, &ParamsArray_t23479E79CB44DA9007429A97C23DAB83F26857CB_0_0_0 } /* System.ParamsArray */,
	{ NULL, PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_marshal_pinvoke, PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_marshal_pinvoke_back, PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_marshal_pinvoke_cleanup, NULL, NULL, &PickerOptions_t2D793E35EE9DF54EDED9FC7766CC76ACAE36ED87_0_0_0 } /* UnityEngine.ProBuilder.PickerOptions */,
	{ NULL, PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_marshal_pinvoke, PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_marshal_pinvoke_back, PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_marshal_pinvoke_cleanup, NULL, NULL, &PlayableBinding_t265202500C703254AD9777368C05D1986C8AC7A2_0_0_0 } /* UnityEngine.Playables.PlayableBinding */,
	{ NULL, PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_marshal_pinvoke, PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_marshal_pinvoke_back, PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_marshal_pinvoke_cleanup, NULL, NULL, &PlayerLoopSystem_t3C4FAE5D2149A8DBB8BED0C2AE9B957B7830E54C_0_0_0 } /* UnityEngine.LowLevel.PlayerLoopSystem */,
	{ NULL, PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_marshal_pinvoke, PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_marshal_pinvoke_back, PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_marshal_pinvoke_cleanup, NULL, NULL, &PlayerLoopSystemInternal_t47326D2B668596299A94B36D0A20A874FBED781B_0_0_0 } /* UnityEngine.LowLevel.PlayerLoopSystemInternal */,
	{ NULL, PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_marshal_pinvoke, PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_marshal_pinvoke_back, PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_marshal_pinvoke_cleanup, NULL, NULL, &PointLight_t543DD0461FFC4EA9F3B08CF9F4BF5BB2164D167E_0_0_0 } /* UnityEngine.Experimental.GlobalIllumination.PointLight */,
	{ NULL, RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_marshal_pinvoke, RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_marshal_pinvoke_back, RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_marshal_pinvoke_cleanup, NULL, NULL, &RaycastResult_t9EFDE24B29650BD6DC8A49D954A3769E17146BCE_0_0_0 } /* UnityEngine.EventSystems.RaycastResult */,
	{ NULL, RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_marshal_pinvoke, RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_marshal_pinvoke_back, RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_marshal_pinvoke_cleanup, NULL, NULL, &RealProxy_t323149046389A393F3F96DBAD6066A96B21CB744_0_0_0 } /* System.Runtime.Remoting.Proxies.RealProxy */,
	{ NULL, RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_marshal_pinvoke, RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_marshal_pinvoke_back, RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_marshal_pinvoke_cleanup, NULL, NULL, &RectOffset_tE3A58467CD0749AD9D3E1271F9E315B38F39AE70_0_0_0 } /* UnityEngine.RectOffset */,
	{ NULL, RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_marshal_pinvoke, RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_marshal_pinvoke_back, RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_marshal_pinvoke_cleanup, NULL, NULL, &RectangleLight_t9F02AC7041621773D7676A5E2707898F24892985_0_0_0 } /* UnityEngine.Experimental.GlobalIllumination.RectangleLight */,
	{ NULL, RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_marshal_pinvoke, RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_marshal_pinvoke_back, RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_marshal_pinvoke_cleanup, NULL, NULL, &RegionInfo_t3F61C7100AA2F796A6BC57D31F1EFA76F6DCE59A_0_0_0 } /* System.Globalization.RegionInfo */,
	{ NULL, ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_marshal_pinvoke, ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_marshal_pinvoke_back, ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_marshal_pinvoke_cleanup, NULL, NULL, &ResourceLocator_t3D496606F94367D5D6B24DA9DC0A3B46E6B53B11_0_0_0 } /* System.Resources.ResourceLocator */,
	{ NULL, ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshal_pinvoke, ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshal_pinvoke_back, ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshal_pinvoke_cleanup, NULL, NULL, &ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_0_0_0 } /* UnityEngine.ResourceRequest */,
	{ NULL, SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_marshal_pinvoke, SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_marshal_pinvoke_back, SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_marshal_pinvoke_cleanup, NULL, NULL, &SafeStringMarshal_t3F5BD5E96CFBAF124814DED946144CF39A82F11E_0_0_0 } /* Mono.SafeStringMarshal */,
	{ NULL, ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshal_pinvoke, ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshal_pinvoke_back, ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshal_pinvoke_cleanup, NULL, NULL, &ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_0_0_0 } /* UnityEngine.ScriptableObject */,
	{ NULL, SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_marshal_pinvoke, SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_marshal_pinvoke_back, SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_marshal_pinvoke_cleanup, NULL, NULL, &SerializationEntry_t33A292618975AD7AC936CB98B2F28256817A467E_0_0_0 } /* System.Runtime.Serialization.SerializationEntry */,
	{ DelegatePInvokeWrapper_SerializationEventHandler_t3033BE1E86AE40A7533AD615FF9122FC8ED0B7C1, NULL, NULL, NULL, NULL, NULL, &SerializationEventHandler_t3033BE1E86AE40A7533AD615FF9122FC8ED0B7C1_0_0_0 } /* System.Runtime.Serialization.SerializationEventHandler */,
	{ NULL, SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_marshal_pinvoke, SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_marshal_pinvoke_back, SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_marshal_pinvoke_cleanup, NULL, NULL, &SkeletonBone_t0AD95EAD0BE7D2EC13B2C7505225D340CB456A9E_0_0_0 } /* UnityEngine.SkeletonBone */,
	{ NULL, SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_marshal_pinvoke, SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_marshal_pinvoke_back, SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_marshal_pinvoke_cleanup, NULL, NULL, &SortKey_tBBD5A739AC7187C1514CBA47698C1D5E36877F52_0_0_0 } /* System.Globalization.SortKey */,
	{ NULL, SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_marshal_pinvoke, SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_marshal_pinvoke_back, SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_marshal_pinvoke_cleanup, NULL, NULL, &SpotLight_tAE1210A6FAE3F41CA62CB63E9012C9BED625AC9D_0_0_0 } /* UnityEngine.Experimental.GlobalIllumination.SpotLight */,
	{ NULL, SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_marshal_pinvoke, SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_marshal_pinvoke_back, SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_marshal_pinvoke_cleanup, NULL, NULL, &SpriteBone_t7BF68B13FD8E65DC10C7C48D4B6C1D14030AFF2D_0_0_0 } /* UnityEngine.U2D.SpriteBone */,
	{ NULL, SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_marshal_pinvoke, SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_marshal_pinvoke_back, SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_marshal_pinvoke_cleanup, NULL, NULL, &SpriteRendererGroup_tC158DDBE7C79A8EE915F52F3D3D0412B05F8522E_0_0_0 } /* UnityEngine.Experimental.U2D.SpriteRendererGroup */,
	{ NULL, SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_marshal_pinvoke, SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_marshal_pinvoke_back, SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_marshal_pinvoke_cleanup, NULL, NULL, &SpriteState_t9024961148433175CE2F3D9E8E9239A8B1CAB15E_0_0_0 } /* UnityEngine.UI.SpriteState */,
	{ NULL, StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_marshal_pinvoke, StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_marshal_pinvoke_back, StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_marshal_pinvoke_cleanup, NULL, NULL, &StackFrame_t6018A5362C2E8F6F80F153F3D40623D213094E0F_0_0_0 } /* System.Diagnostics.StackFrame */,
	{ NULL, StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_marshal_pinvoke, StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_marshal_pinvoke_back, StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_marshal_pinvoke_cleanup, NULL, NULL, &StreamingContext_t5888E7E8C81AB6EF3B14FDDA6674F458076A8505_0_0_0 } /* System.Runtime.Serialization.StreamingContext */,
	{ NULL, TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_marshal_pinvoke, TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_marshal_pinvoke_back, TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_marshal_pinvoke_cleanup, NULL, NULL, &TaskAwaiter_t3780D365E9D10C2D6C4E76C78AA0CDF92B8F181C_0_0_0 } /* System.Runtime.CompilerServices.TaskAwaiter */,
	{ NULL, TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_marshal_pinvoke, TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_marshal_pinvoke_back, TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_marshal_pinvoke_cleanup, NULL, NULL, &TerrainLayer_t8DDE2984BBC3E36CF0EBC3F0DD1862106248D38E_0_0_0 } /* UnityEngine.TerrainLayer */,
	{ NULL, TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_marshal_pinvoke, TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_marshal_pinvoke_back, TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_marshal_pinvoke_cleanup, NULL, NULL, &TextGenerationSettings_tAD927E4DCB8644B1B2BB810B5FB13C90B753898A_0_0_0 } /* UnityEngine.TextGenerationSettings */,
	{ NULL, TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_marshal_pinvoke, TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_marshal_pinvoke_back, TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_marshal_pinvoke_cleanup, NULL, NULL, &TextGenerator_t893F256D3587633108E00E5731CDC5A77AFF1B70_0_0_0 } /* UnityEngine.TextGenerator */,
	{ DelegatePInvokeWrapper_ThreadStart_tA13019555BA3CB2B0128F0880760196BF790E687, NULL, NULL, NULL, NULL, NULL, &ThreadStart_tA13019555BA3CB2B0128F0880760196BF790E687_0_0_0 } /* System.Threading.ThreadStart */,
	{ NULL, TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_marshal_pinvoke, TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_marshal_pinvoke_back, TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_marshal_pinvoke_cleanup, NULL, NULL, &TrackedReference_t17AA313389C655DCF279F96A2D85332B29596514_0_0_0 } /* UnityEngine.TrackedReference */,
	{ NULL, TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_marshal_pinvoke, TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_marshal_pinvoke_back, TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_marshal_pinvoke_cleanup, NULL, NULL, &TransparentProxy_t0A3E7468290B2C8EEEC64C242D586F3EE7B3F968_0_0_0 } /* System.Runtime.Remoting.Proxies.TransparentProxy */,
	{ NULL, UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_marshal_pinvoke, UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_marshal_pinvoke_back, UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_marshal_pinvoke_cleanup, NULL, NULL, &UnSafeCharBuffer_tC2F1C142D69686631C1660F318C983106FF36F23_0_0_0 } /* System.UnSafeCharBuffer */,
	{ DelegatePInvokeWrapper_UnityAction_t22E545F8BE0A62EE051C6A83E209587A0DB1C099, NULL, NULL, NULL, NULL, NULL, &UnityAction_t22E545F8BE0A62EE051C6A83E209587A0DB1C099_0_0_0 } /* UnityEngine.Events.UnityAction */,
	{ NULL, ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshal_pinvoke, ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshal_pinvoke_back, ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshal_pinvoke_cleanup, NULL, NULL, &ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_0_0_0 } /* System.ValueType */,
	{ NULL, VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_marshal_pinvoke, VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_marshal_pinvoke_back, VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_marshal_pinvoke_cleanup, NULL, NULL, &VertexPickerEntry_tDB1FD25E37D117402777482B98D3654524880775_0_0_0 } /* UnityEngine.ProBuilder.VertexPickerEntry */,
	{ NULL, WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_marshal_pinvoke, WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_marshal_pinvoke_back, WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_marshal_pinvoke_cleanup, NULL, NULL, &WaitForSeconds_t8F9189BE6E467C98C99177038881F8982E0E4013_0_0_0 } /* UnityEngine.WaitForSeconds */,
	{ NULL, WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_marshal_pinvoke, WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_marshal_pinvoke_back, WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_marshal_pinvoke_cleanup, NULL, NULL, &WaitHandle_t1D7DD8480FD5DA4E3AF92F569890FB972D9B1842_0_0_0 } /* System.Threading.WaitHandle */,
	{ NULL, WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_marshal_pinvoke, WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_marshal_pinvoke_back, WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_marshal_pinvoke_cleanup, NULL, NULL, &WheelHit_t29625F675B7C6CD83AE028E64F770C15811A36BB_0_0_0 } /* UnityEngine.WheelHit */,
	{ NULL, YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshal_pinvoke, YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshal_pinvoke_back, YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshal_pinvoke_cleanup, NULL, NULL, &YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_0_0_0 } /* UnityEngine.YieldInstruction */,
	{ NULL, __DTString_t594255B76730E715A2A5655F8238B0029484B27A_marshal_pinvoke, __DTString_t594255B76730E715A2A5655F8238B0029484B27A_marshal_pinvoke_back, __DTString_t594255B76730E715A2A5655F8238B0029484B27A_marshal_pinvoke_cleanup, NULL, NULL, &__DTString_t594255B76730E715A2A5655F8238B0029484B27A_0_0_0 } /* System.__DTString */,
	{ NULL, jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_marshal_pinvoke, jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_marshal_pinvoke_back, jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_marshal_pinvoke_cleanup, NULL, NULL, &jvalue_t220BECEE73180D6A4DE0F66CB6BA852EC6A5B587_0_0_0 } /* UnityEngine.jvalue */,
	{ DelegatePInvokeWrapper_OnOverrideControllerDirtyCallback_t9E38572D7CF06EEFF943EA68082DAC68AB40476C, NULL, NULL, NULL, NULL, NULL, &OnOverrideControllerDirtyCallback_t9E38572D7CF06EEFF943EA68082DAC68AB40476C_0_0_0 } /* UnityEngine.AnimatorOverrideController/OnOverrideControllerDirtyCallback */,
	{ DelegatePInvokeWrapper_LogCallback_t8C3C9B1E0F185E2A25D09DE10DD8414898698BBD, NULL, NULL, NULL, NULL, NULL, &LogCallback_t8C3C9B1E0F185E2A25D09DE10DD8414898698BBD_0_0_0 } /* UnityEngine.Application/LogCallback */,
	{ DelegatePInvokeWrapper_LowMemoryCallback_tF94AC614EDACA9AD4CEA3DE77FF8EFF5DA1E5240, NULL, NULL, NULL, NULL, NULL, &LowMemoryCallback_tF94AC614EDACA9AD4CEA3DE77FF8EFF5DA1E5240_0_0_0 } /* UnityEngine.Application/LowMemoryCallback */,
	{ NULL, SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_marshal_pinvoke, SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_marshal_pinvoke_back, SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_marshal_pinvoke_cleanup, NULL, NULL, &SorterGenericArray_t2369B44171030E280B31E4036E95D06C4810BBB9_0_0_0 } /* System.Array/SorterGenericArray */,
	{ NULL, SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_marshal_pinvoke, SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_marshal_pinvoke_back, SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_marshal_pinvoke_cleanup, NULL, NULL, &SorterObjectArray_t60785845A840F9562AA723FF11ECA3597C5A9FD1_0_0_0 } /* System.Array/SorterObjectArray */,
	{ DelegatePInvokeWrapper_PCMReaderCallback_t9CA1437D36509A9FAC5EDD8FF2BC3259C24D0E0B, NULL, NULL, NULL, NULL, NULL, &PCMReaderCallback_t9CA1437D36509A9FAC5EDD8FF2BC3259C24D0E0B_0_0_0 } /* UnityEngine.AudioClip/PCMReaderCallback */,
	{ DelegatePInvokeWrapper_PCMSetPositionCallback_tBDD99E7C0697687F1E7B06CDD5DE444A3709CF4C, NULL, NULL, NULL, NULL, NULL, &PCMSetPositionCallback_tBDD99E7C0697687F1E7B06CDD5DE444A3709CF4C_0_0_0 } /* UnityEngine.AudioClip/PCMSetPositionCallback */,
	{ DelegatePInvokeWrapper_AudioConfigurationChangeHandler_t1A997C51DF7B553A94DAD358F8D968308994774A, NULL, NULL, NULL, NULL, NULL, &AudioConfigurationChangeHandler_t1A997C51DF7B553A94DAD358F8D968308994774A_0_0_0 } /* UnityEngine.AudioSettings/AudioConfigurationChangeHandler */,
	{ NULL, OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_marshal_pinvoke, OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_marshal_pinvoke_back, OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_marshal_pinvoke_cleanup, NULL, NULL, &OrderBlock_t0B106828F588BC2F0B9895425E6FD39EDA45C1E2_0_0_0 } /* UnityEngine.BeforeRenderHelper/OrderBlock */,
	{ NULL, RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_marshal_pinvoke, RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_marshal_pinvoke_back, RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_marshal_pinvoke_cleanup, NULL, NULL, &RenderRequest_t7DEDFA6AAA1C8D381280183054C328F26BBCCE94_0_0_0 } /* UnityEngine.Camera/RenderRequest */,
	{ DelegatePInvokeWrapper_WillRenderCanvases_t459621B4F3FA2571DE0ED6B4DEF0752F2E9EE958, NULL, NULL, NULL, NULL, NULL, &WillRenderCanvases_t459621B4F3FA2571DE0ED6B4DEF0752F2E9EE958_0_0_0 } /* UnityEngine.Canvas/WillRenderCanvases */,
	{ DelegatePInvokeWrapper_InternalCancelHandler_t7F0E9BBFE542C3B0E62620118961AC10E0DFB000, NULL, NULL, NULL, NULL, NULL, &InternalCancelHandler_t7F0E9BBFE542C3B0E62620118961AC10E0DFB000_0_0_0 } /* System.Console/InternalCancelHandler */,
	{ NULL, ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_marshal_pinvoke, ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_marshal_pinvoke_back, ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_marshal_pinvoke_cleanup, NULL, NULL, &ProcessMessageRes_tEB8A216399166053C37BA6F520ADEA92455104E9_0_0_0 } /* System.Runtime.Remoting.Channels.CrossAppDomainSink/ProcessMessageRes */,
	{ DelegatePInvokeWrapper_StateChanged_tAE96F0A8860BFCD704179F6C1F376A6FAE3E25E0, NULL, NULL, NULL, NULL, NULL, &StateChanged_tAE96F0A8860BFCD704179F6C1F376A6FAE3E25E0_0_0_0 } /* UnityEngine.CullingGroup/StateChanged */,
	{ NULL, Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_marshal_pinvoke, Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_marshal_pinvoke_back, Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_marshal_pinvoke_cleanup, NULL, NULL, &Data_tD2910A75571233E80DF4714C1F6CBB1852B3BF68_0_0_0 } /* System.Globalization.CultureInfo/Data */,
	{ NULL, Resources_tA64317917B3D01310E84588407113D059D802DEB_marshal_pinvoke, Resources_tA64317917B3D01310E84588407113D059D802DEB_marshal_pinvoke_back, Resources_tA64317917B3D01310E84588407113D059D802DEB_marshal_pinvoke_cleanup, NULL, NULL, &Resources_tA64317917B3D01310E84588407113D059D802DEB_0_0_0 } /* UnityEngine.UI.DefaultControls/Resources */,
	{ DelegatePInvokeWrapper_DisplaysUpdatedDelegate_tC6A6AD44FAD98C9E28479FFF4BD3D9932458A6A1, NULL, NULL, NULL, NULL, NULL, &DisplaysUpdatedDelegate_tC6A6AD44FAD98C9E28479FFF4BD3D9932458A6A1_0_0_0 } /* UnityEngine.Display/DisplaysUpdatedDelegate */,
	{ NULL, Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_marshal_pinvoke, Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_marshal_pinvoke_back, Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_marshal_pinvoke_cleanup, NULL, NULL, &Reader_t6C70587C0F5A8CE8367A0407E3109E196764848C_0_0_0 } /* System.Threading.ExecutionContext/Reader */,
	{ DelegatePInvokeWrapper_ReadDelegate_tB245FDB608C11A53AC71F333C1A6BE3D7CDB21BB, NULL, NULL, NULL, NULL, NULL, &ReadDelegate_tB245FDB608C11A53AC71F333C1A6BE3D7CDB21BB_0_0_0 } /* System.IO.FileStream/ReadDelegate */,
	{ DelegatePInvokeWrapper_WriteDelegate_tF68E6D874C089E69933FA2B9A0C1C6639929C4F6, NULL, NULL, NULL, NULL, NULL, &WriteDelegate_tF68E6D874C089E69933FA2B9A0C1C6639929C4F6_0_0_0 } /* System.IO.FileStream/WriteDelegate */,
	{ DelegatePInvokeWrapper_FontTextureRebuildCallback_tBF11A511EBD8D237A1C5885D460B42A45DDBB2DB, NULL, NULL, NULL, NULL, NULL, &FontTextureRebuildCallback_tBF11A511EBD8D237A1C5885D460B42A45DDBB2DB_0_0_0 } /* UnityEngine.Font/FontTextureRebuildCallback */,
	{ DelegatePInvokeWrapper_Action_tA166CCE0E00A94BB300974487FE20CE9F118E0FC, NULL, NULL, NULL, NULL, NULL, &Action_tA166CCE0E00A94BB300974487FE20CE9F118E0FC_0_0_0 } /* Firebase.FutureVoid/Action */,
	{ DelegatePInvokeWrapper_SWIG_CompletionDelegate_tB2D099DCC04B71228A5C8BF73BC0D5114A6ACF72, NULL, NULL, NULL, NULL, NULL, &SWIG_CompletionDelegate_tB2D099DCC04B71228A5C8BF73BC0D5114A6ACF72_0_0_0 } /* Firebase.FutureVoid/SWIG_CompletionDelegate */,
	{ DelegatePInvokeWrapper_WindowFunction_tFA5DBAB811627D7B0946C4AAD398D4CC154C174D, NULL, NULL, NULL, NULL, NULL, &WindowFunction_tFA5DBAB811627D7B0946C4AAD398D4CC154C174D_0_0_0 } /* UnityEngine.GUI/WindowFunction */,
	{ DelegatePInvokeWrapper_SkinChangedDelegate_t8BECC691E2A259B07F4A51D8F1A639B83F055E1E, NULL, NULL, NULL, NULL, NULL, &SkinChangedDelegate_t8BECC691E2A259B07F4A51D8F1A639B83F055E1E_0_0_0 } /* UnityEngine.GUISkin/SkinChangedDelegate */,
	{ NULL, GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_marshal_pinvoke, GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_marshal_pinvoke_back, GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_marshal_pinvoke_cleanup, NULL, NULL, &GuidResult_t0DA162EF4F1F1C93059A6A44E1C5CCE6F2924A6E_0_0_0 } /* System.Guid/GuidResult */,
	{ NULL, bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_marshal_pinvoke, bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_marshal_pinvoke_back, bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_marshal_pinvoke_cleanup, NULL, NULL, &bucket_t56D642DDC4ABBCED9DB7F620CC35AEEC0778869D_0_0_0 } /* System.Collections.Hashtable/bucket */,
	{ DelegatePInvokeWrapper_OnValidateInput_t721D2C2A7710D113E4909B36D9893CC6B1C69B9F, NULL, NULL, NULL, NULL, NULL, &OnValidateInput_t721D2C2A7710D113E4909B36D9893CC6B1C69B9F_0_0_0 } /* UnityEngine.UI.InputField/OnValidateInput */,
	{ DelegatePInvokeWrapper_PanicFunction__tFB4D850E5B848FFEF210623F5669284D723544EB, NULL, NULL, NULL, NULL, NULL, &PanicFunction__tFB4D850E5B848FFEF210623F5669284D723544EB_0_0_0 } /* Unity.Jobs.LowLevel.Unsafe.JobsUtility/PanicFunction_ */,
	{ DelegatePInvokeWrapper_LogMessageDelegate_tA2E3C07710334F0F6F81098ECB5A95F7747C3E84, NULL, NULL, NULL, NULL, NULL, &LogMessageDelegate_tA2E3C07710334F0F6F81098ECB5A95F7747C3E84_0_0_0 } /* Firebase.LogUtil/LogMessageDelegate */,
	{ NULL, Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_marshal_pinvoke, Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_marshal_pinvoke_back, Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_marshal_pinvoke_cleanup, NULL, NULL, &Reader_tCFB139CA143817B24496D4F1B0DD8F51A256AB13_0_0_0 } /* System.Runtime.Remoting.Messaging.LogicalCallContext/Reader */,
	{ DelegatePInvokeWrapper_OnNavMeshPreUpdate_t5E34F761F39A1F6B898F0E729B36C0782B92D572, NULL, NULL, NULL, NULL, NULL, &OnNavMeshPreUpdate_t5E34F761F39A1F6B898F0E729B36C0782B92D572_0_0_0 } /* UnityEngine.AI.NavMesh/OnNavMeshPreUpdate */,
	{ NULL, NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_marshal_pinvoke, NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_marshal_pinvoke_back, NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_marshal_pinvoke_cleanup, NULL, NULL, &NumberBuffer_t5EC5B27BA4105EA147F2DE7CE7B96D7E9EAC9271_0_0_0 } /* System.Number/NumberBuffer */,
	{ DelegatePInvokeWrapper_InvocationEntryDelegate_t751DEAE9B64F61CCD4029B67E7916F00C823E61A, NULL, NULL, NULL, NULL, NULL, &InvocationEntryDelegate_t751DEAE9B64F61CCD4029B67E7916F00C823E61A_0_0_0 } /* System.Threading.OSSpecificSynchronizationContext/InvocationEntryDelegate */,
	{ NULL, FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_marshal_pinvoke, FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_marshal_pinvoke_back, FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_marshal_pinvoke_cleanup, NULL, NULL, &FormatParam_tA765680E7894569CC4BDEB5DF722F646311E23EE_0_0_0 } /* System.ParameterizedStrings/FormatParam */,
	{ NULL, EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_marshal_pinvoke, EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_marshal_pinvoke_back, EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_marshal_pinvoke_cleanup, NULL, NULL, &EmissionModule_tE778D94F4003A96ECE3D8B670DDEDD2D557DE52D_0_0_0 } /* UnityEngine.ParticleSystem/EmissionModule */,
	{ NULL, EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_marshal_pinvoke, EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_marshal_pinvoke_back, EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_marshal_pinvoke_cleanup, NULL, NULL, &EmitParams_t4F6429654653488A5D430701CD0743D011807CCC_0_0_0 } /* UnityEngine.ParticleSystem/EmitParams */,
	{ NULL, MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_marshal_pinvoke, MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_marshal_pinvoke_back, MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_marshal_pinvoke_cleanup, NULL, NULL, &MainModule_t671F49558CB1A3CFAAD637A7927C076EC2E61F0B_0_0_0 } /* UnityEngine.ParticleSystem/MainModule */,
	{ DelegatePInvokeWrapper_CreateOutputMethod_t7A129D00E8823B50AEDD0C9B082C9CB3DF863876, NULL, NULL, NULL, NULL, NULL, &CreateOutputMethod_t7A129D00E8823B50AEDD0C9B082C9CB3DF863876_0_0_0 } /* UnityEngine.Playables.PlayableBinding/CreateOutputMethod */,
	{ DelegatePInvokeWrapper_UpdateFunction_tEDC2A88F61F179480CAA9443E6ADDA3F126B8AEA, NULL, NULL, NULL, NULL, NULL, &UpdateFunction_tEDC2A88F61F179480CAA9443E6ADDA3F126B8AEA_0_0_0 } /* UnityEngine.LowLevel.PlayerLoopSystem/UpdateFunction */,
	{ NULL, originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_marshal_pinvoke, originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_marshal_pinvoke_back, originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_marshal_pinvoke_cleanup, NULL, NULL, &originalMeshVerts_tB1FBCE18C26030F8521E6C5E26E7A727283D866B_0_0_0 } /* RCC_CarControllerV3/originalMeshVerts */,
	{ DelegatePInvokeWrapper_onBehaviorChanged_t8EDFEFBF7EE7EF8942B2D5D2D9F242B976CB87FA, NULL, NULL, NULL, NULL, NULL, &onBehaviorChanged_t8EDFEFBF7EE7EF8942B2D5D2D9F242B976CB87FA_0_0_0 } /* RCC_SceneManager/onBehaviorChanged */,
	{ DelegatePInvokeWrapper_onMainControllerChanged_tF09F3C466021134D0B896E2F9FC36AF8D4DCABF7, NULL, NULL, NULL, NULL, NULL, &onMainControllerChanged_tF09F3C466021134D0B896E2F9FC36AF8D4DCABF7_0_0_0 } /* RCC_SceneManager/onMainControllerChanged */,
	{ DelegatePInvokeWrapper_onVehicleChanged_t96D5C0BA2E24DD7B0645AE01D6F4AD32540430A9, NULL, NULL, NULL, NULL, NULL, &onVehicleChanged_t96D5C0BA2E24DD7B0645AE01D6F4AD32540430A9_0_0_0 } /* RCC_SceneManager/onVehicleChanged */,
	{ DelegatePInvokeWrapper_GetRayIntersectionAllCallback_t9D6C059892DE030746D2873EB8871415BAC79311, NULL, NULL, NULL, NULL, NULL, &GetRayIntersectionAllCallback_t9D6C059892DE030746D2873EB8871415BAC79311_0_0_0 } /* UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllCallback */,
	{ DelegatePInvokeWrapper_GetRayIntersectionAllNonAllocCallback_t6DAE64211C37E996B257BF2C54707DAD3474D69C, NULL, NULL, NULL, NULL, NULL, &GetRayIntersectionAllNonAllocCallback_t6DAE64211C37E996B257BF2C54707DAD3474D69C_0_0_0 } /* UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllNonAllocCallback */,
	{ DelegatePInvokeWrapper_GetRaycastNonAllocCallback_tA4A6A2336A9B9FEE31F8F5344576B3BB0A7B3F34, NULL, NULL, NULL, NULL, NULL, &GetRaycastNonAllocCallback_tA4A6A2336A9B9FEE31F8F5344576B3BB0A7B3F34_0_0_0 } /* UnityEngine.UI.ReflectionMethodsCache/GetRaycastNonAllocCallback */,
	{ DelegatePInvokeWrapper_Raycast2DCallback_t125C1CA6D0148380915E597AC8ADBB93EFB0EE29, NULL, NULL, NULL, NULL, NULL, &Raycast2DCallback_t125C1CA6D0148380915E597AC8ADBB93EFB0EE29_0_0_0 } /* UnityEngine.UI.ReflectionMethodsCache/Raycast2DCallback */,
	{ DelegatePInvokeWrapper_Raycast3DCallback_t27A8B301052E9C6A4A7D38F95293CA129C39373F, NULL, NULL, NULL, NULL, NULL, &Raycast3DCallback_t27A8B301052E9C6A4A7D38F95293CA129C39373F_0_0_0 } /* UnityEngine.UI.ReflectionMethodsCache/Raycast3DCallback */,
	{ DelegatePInvokeWrapper_RaycastAllCallback_t48E12CFDCFDEA0CD7D83F9DDE1E341DBCC855005, NULL, NULL, NULL, NULL, NULL, &RaycastAllCallback_t48E12CFDCFDEA0CD7D83F9DDE1E341DBCC855005_0_0_0 } /* UnityEngine.UI.ReflectionMethodsCache/RaycastAllCallback */,
	{ NULL, LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_marshal_pinvoke, LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_marshal_pinvoke_back, LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_marshal_pinvoke_cleanup, NULL, NULL, &LowerCaseMapping_t54FB537AEA4CA2EBAB5BDCC79881428C202241DE_0_0_0 } /* System.Text.RegularExpressions.RegexCharClass/LowerCaseMapping */,
	{ NULL, HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_marshal_pinvoke, HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_marshal_pinvoke_back, HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_marshal_pinvoke_cleanup, NULL, NULL, &HitInfo_t74B96DDC302EB605CCC557B737A5C88EB67B57D6_0_0_0 } /* UnityEngine.SendMouseEvents/HitInfo */,
	{ NULL, Escape_t0479DB63473055AD46754E698B2114579D5D944E_marshal_pinvoke, Escape_t0479DB63473055AD46754E698B2114579D5D944E_marshal_pinvoke_back, Escape_t0479DB63473055AD46754E698B2114579D5D944E_marshal_pinvoke_cleanup, NULL, NULL, &Escape_t0479DB63473055AD46754E698B2114579D5D944E_0_0_0 } /* Mono.Globalization.Unicode.SimpleCollator/Escape */,
	{ NULL, FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_marshal_pinvoke, FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_marshal_pinvoke_back, FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_marshal_pinvoke_cleanup, NULL, NULL, &FormatLiterals_t8EC4E080425C3E3AE6627A6BB7F5B487680E3C94_0_0_0 } /* System.Globalization.TimeSpanFormat/FormatLiterals */,
	{ NULL, DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_marshal_pinvoke, DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_marshal_pinvoke_back, DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_marshal_pinvoke_cleanup, NULL, NULL, &DYNAMIC_TIME_ZONE_INFORMATION_t2A935E4357B99965B322E468058134B139805895_0_0_0 } /* System.TimeZoneInfo/DYNAMIC_TIME_ZONE_INFORMATION */,
	{ NULL, TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_marshal_pinvoke, TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_marshal_pinvoke_back, TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_marshal_pinvoke_cleanup, NULL, NULL, &TIME_ZONE_INFORMATION_t895CF3EE73EA839A7D135CD7187F514DA758F578_0_0_0 } /* System.TimeZoneInfo/TIME_ZONE_INFORMATION */,
	{ NULL, TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_marshal_pinvoke, TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_marshal_pinvoke_back, TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_marshal_pinvoke_cleanup, NULL, NULL, &TransitionTime_tD3B9CE442418566444BB123BA7297AE071D0D47A_0_0_0 } /* System.TimeZoneInfo/TransitionTime */,
	{ NULL, DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_marshal_pinvoke, DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_marshal_pinvoke_back, DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_marshal_pinvoke_cleanup, NULL, NULL, &DateMapping_tF281DC47BDB7C1EDCB7C15F22ABB05B892A2AB60_0_0_0 } /* System.Globalization.UmAlQuraCalendar/DateMapping */,
	{ NULL, WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_marshal_pinvoke, WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_marshal_pinvoke_back, WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_marshal_pinvoke_cleanup, NULL, NULL, &WorkRequest_tA19FD4D1269D8EE2EA886AAF036C4F7F09154393_0_0_0 } /* UnityEngine.UnitySynchronizationContext/WorkRequest */,
	{ NULL, UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_marshal_pinvoke, UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_marshal_pinvoke_back, UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_marshal_pinvoke_cleanup, NULL, NULL, &UsedAxis_t80EC79F49412B7E583C8E420D7B66C3E3F9DCE07_0_0_0 } /* WaypointMover/UsedAxis */,
	{ NULL, XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_marshal_pinvoke, XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_marshal_pinvoke_back, XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_marshal_pinvoke_cleanup, NULL, NULL, &XRMirrorViewBlitDesc_t3BD136F0BF088017ABB0EF1856191541211848A5_0_0_0 } /* UnityEngine.XR.XRDisplaySubsystem/XRMirrorViewBlitDesc */,
	{ NULL, XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_marshal_pinvoke, XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_marshal_pinvoke_back, XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_marshal_pinvoke_cleanup, NULL, NULL, &XRRenderPass_tCB4A9F3B07C2C59889BD3EE40F44E9347A2BC9BB_0_0_0 } /* UnityEngine.XR.XRDisplaySubsystem/XRRenderPass */,
	{ DelegatePInvokeWrapper_ExceptionArgumentDelegate_t1AA80F883D60128F2D0AC8B6585E7E5E24046CA1, NULL, NULL, NULL, NULL, NULL, &ExceptionArgumentDelegate_t1AA80F883D60128F2D0AC8B6585E7E5E24046CA1_0_0_0 } /* Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate */,
	{ DelegatePInvokeWrapper_ExceptionDelegate_tDD1A55BD95B6728CB90312C6FD53BF55FD0AEF17, NULL, NULL, NULL, NULL, NULL, &ExceptionDelegate_tDD1A55BD95B6728CB90312C6FD53BF55FD0AEF17_0_0_0 } /* Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate */,
	{ DelegatePInvokeWrapper_SWIGStringDelegate_tB27940DE2B521137937A0B37C46010B0A28E7394, NULL, NULL, NULL, NULL, NULL, &SWIGStringDelegate_tB27940DE2B521137937A0B37C46010B0A28E7394_0_0_0 } /* Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate */,
	{ DelegatePInvokeWrapper_WindowsCancelHandler_tFD0F0B721F93ACA04D9CD9340DA39075A8FF2ACF, NULL, NULL, NULL, NULL, NULL, &WindowsCancelHandler_tFD0F0B721F93ACA04D9CD9340DA39075A8FF2ACF_0_0_0 } /* System.Console/WindowsConsole/WindowsCancelHandler */,
	{ DelegatePInvokeWrapper_ExceptionArgumentDelegate_t0B4E75C6FEE2D674D1B6C177ED0DEEF03B9B9E7E, NULL, NULL, NULL, NULL, NULL, &ExceptionArgumentDelegate_t0B4E75C6FEE2D674D1B6C177ED0DEEF03B9B9E7E_0_0_0 } /* Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate */,
	{ DelegatePInvokeWrapper_ExceptionDelegate_t73D9DC3453B171BDC76275017D99E401B4F2B6CC, NULL, NULL, NULL, NULL, NULL, &ExceptionDelegate_t73D9DC3453B171BDC76275017D99E401B4F2B6CC_0_0_0 } /* Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate */,
	{ DelegatePInvokeWrapper_SWIGStringDelegate_t2B9F99922AC4679D8AE49E4D8BDD2C52391467B1, NULL, NULL, NULL, NULL, NULL, &SWIGStringDelegate_t2B9F99922AC4679D8AE49E4D8BDD2C52391467B1_0_0_0 } /* Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper/SWIGStringDelegate */,
	NULL,
};
