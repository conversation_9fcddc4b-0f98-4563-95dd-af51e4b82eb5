﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Api.AdError::.ctor(GoogleMobileAds.Common.IAdErrorClient)
extern void AdError__ctor_m75162359A2D2A3EC74C4D93C4F1FCF39F8D4FAB4 (void);
// 0x00000002 System.String GoogleMobileAds.Api.AdError::ToString()
extern void AdError_ToString_m6B9BA7043A52DB62AF8646F8697E1A32743FCE88 (void);
// 0x00000003 System.Void GoogleMobileAds.Api.AppOpenAd::.ctor(GoogleMobileAds.Common.IAppOpenAdClient)
extern void AppOpenAd__ctor_m23069D08AA0576F8C3A1E8ED8BDB4AA4F340FDAD (void);
// 0x00000004 System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdImpressionRecorded(System.Action)
extern void AppOpenAd_add_OnAdImpressionRecorded_m024C47A8AB06DBC76BA4571EDED873743E3F30E5 (void);
// 0x00000005 System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdImpressionRecorded(System.Action)
extern void AppOpenAd_remove_OnAdImpressionRecorded_m5E377061E2BE7E7AC4ABBAD16773816A6A67A27C (void);
// 0x00000006 System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentClosed(System.Action)
extern void AppOpenAd_add_OnAdFullScreenContentClosed_mCAB05E8942F7EF4874BCEBBD8A8A43BF435AED8E (void);
// 0x00000007 System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void AppOpenAd_remove_OnAdFullScreenContentClosed_mD7C2D1772D3D114E18C214EBD5E56AC470AF2EEB (void);
// 0x00000008 System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void AppOpenAd_add_OnAdFullScreenContentFailed_m254123049CED4B9C9C213F83D2D64ACE9E733491 (void);
// 0x00000009 System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void AppOpenAd_remove_OnAdFullScreenContentFailed_m9FA7EB731F72C932BD0525072A71158E47FEDB0B (void);
// 0x0000000A System.Void GoogleMobileAds.Api.AppOpenAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>)
extern void AppOpenAd_Load_mFA1409AC8809A6F8A835C3F14BE5FFF0BA306C9A (void);
// 0x0000000B System.Boolean GoogleMobileAds.Api.AppOpenAd::CanShowAd()
extern void AppOpenAd_CanShowAd_mC7D41DB510A25134156ABC621FE11165C84CEF33 (void);
// 0x0000000C System.Void GoogleMobileAds.Api.AppOpenAd::Show()
extern void AppOpenAd_Show_m0BDB3E193F7E4DD88EC46C330B5F661889956D0A (void);
// 0x0000000D System.Void GoogleMobileAds.Api.AppOpenAd::Destroy()
extern void AppOpenAd_Destroy_m62E4E94A9579369744315568A15133F5792CBC2C (void);
// 0x0000000E System.Void GoogleMobileAds.Api.AppOpenAd::RegisterAdEvents()
extern void AppOpenAd_RegisterAdEvents_mF1F438E2657A544DC5E00DB502A19FBEAD222E93 (void);
// 0x0000000F System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__0()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__0_m6EB81ECE17183A949FE0D9A1E1161982BF11103C (void);
// 0x00000010 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__1_mDD77C10B8C79F36145F039C5E46E2A5F4BF98CD8 (void);
// 0x00000011 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__2_m0745E7DCB5926AEC7A942C72CFA54A04533BE2B3 (void);
// 0x00000012 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__3_m9E010CC5392B712457E641BF5548AAD5F900DB66 (void);
// 0x00000013 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__4_m9E97077F81575A81B9AB836AEB1AB284F72C7DA4 (void);
// 0x00000014 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__5(System.Object,GoogleMobileAds.Api.AdValueEventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__5_m1A2128B5A04CDD0DD741FF0FA7E614914662D349 (void);
// 0x00000015 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__6()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__6_mDA2F15595A3B1DE9A59987F766BDBDE62DA02B3B (void);
// 0x00000016 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__7()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__7_mC2C2932C98115FB731FEBCEB7D14BCECDBF18E04 (void);
// 0x00000017 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__8()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__8_mEBBF84209D60D055AA2108C55F9C5B256A111786 (void);
// 0x00000018 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__9()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__9_mFAF62BAD52DBBA208A350C8287396F93ADE86D40 (void);
// 0x00000019 System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_mB114A18F7C06F791F1FE20B5523A7A7049E283F6 (void);
// 0x0000001A System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m109390BB4960431AB6CBCB98B4B5AF4B0AD3F771 (void);
// 0x0000001B System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mA71446EEE7179C6FB24F3E440B2102FD4AEAA3E5 (void);
// 0x0000001C System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m4EA34BD79081682018B340E7E3F9E103ED29F86A (void);
// 0x0000001D System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_mF4992A1F8C3FFA2ED6D381C87300283F582196F0 (void);
// 0x0000001E System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m4146799EF7435C625784973820731937E001947F (void);
// 0x0000001F System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m1CEFDF3AD1B70504D6807A2C7BBEEE42F84FBEB6 (void);
// 0x00000020 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mD5C8E0D375E878A173A696113A7D2FECCF5A795E (void);
// 0x00000021 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey5::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey5__ctor_m8C8921EB99AC5CCB0E91ADE820BF5F8EB2FABC1E (void);
// 0x00000022 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey5::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey5_U3CU3Em__0_mF673909B2D31A52D8960E421B24CB4FDB80FC08B (void);
// 0x00000023 System.Void GoogleMobileAds.Api.BannerView::.ctor(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void BannerView__ctor_mCFE456D97EB8D004D950ECABA22A44185984303B (void);
// 0x00000024 System.Void GoogleMobileAds.Api.BannerView::add_OnBannerAdLoaded(System.Action)
extern void BannerView_add_OnBannerAdLoaded_m1C22C7B5353261BD1F40A65BDE455296D44E116A (void);
// 0x00000025 System.Void GoogleMobileAds.Api.BannerView::remove_OnBannerAdLoaded(System.Action)
extern void BannerView_remove_OnBannerAdLoaded_m15A059AA97882FB008AD47216A19166C25B2CF39 (void);
// 0x00000026 System.Void GoogleMobileAds.Api.BannerView::add_OnBannerAdLoadFailed(System.Action`1<GoogleMobileAds.Api.LoadAdError>)
extern void BannerView_add_OnBannerAdLoadFailed_m78AB9BF93A57D78C6CA5523A808286136886FBE0 (void);
// 0x00000027 System.Void GoogleMobileAds.Api.BannerView::remove_OnBannerAdLoadFailed(System.Action`1<GoogleMobileAds.Api.LoadAdError>)
extern void BannerView_remove_OnBannerAdLoadFailed_m1EEB499798A7FD860561891F1C5010A12FC9C3C6 (void);
// 0x00000028 System.Void GoogleMobileAds.Api.BannerView::Destroy()
extern void BannerView_Destroy_mBE40E1F64AE5B793C011002988540402EDC0345D (void);
// 0x00000029 System.Void GoogleMobileAds.Api.BannerView::LoadAd(GoogleMobileAds.Api.AdRequest)
extern void BannerView_LoadAd_mCB8DE994927BF8D2F4E93D659FCBE630B6B7761C (void);
// 0x0000002A System.Void GoogleMobileAds.Api.BannerView::Show()
extern void BannerView_Show_m5C1C0FF737CA2B4B06394885BC94EC7335ABB707 (void);
// 0x0000002B System.Void GoogleMobileAds.Api.BannerView::Hide()
extern void BannerView_Hide_m14B1CF6644711560E11092DBEBA76DC4159E54E0 (void);
// 0x0000002C System.Void GoogleMobileAds.Api.BannerView::ConfigureBannerEvents()
extern void BannerView_ConfigureBannerEvents_mDF2FF80E541A6D5C9A19507AFA92618698B7D694 (void);
// 0x0000002D System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__0(System.Object,System.EventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__0_mAB59CDA05FF9FAC2CBE5CB5815C46895F641CB54 (void);
// 0x0000002E System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__1_mCC40AAD739C5097E87710765BD6E616290EB9A8F (void);
// 0x0000002F System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__2(System.Object,System.EventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__2_m45BDA7B13065BB9326A56CB29357CC5BE655AEFB (void);
// 0x00000030 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__3(System.Object,System.EventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__3_mC978D558480BB77CB457D63268531A4B42F05DB1 (void);
// 0x00000031 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__4(System.Object,GoogleMobileAds.Api.AdValueEventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__4_mF3FFB125AB04A05BCAD1AD9895549454FCDC22CC (void);
// 0x00000032 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__5()
extern void BannerView_U3CConfigureBannerEventsU3Em__5_mC876D2C02AE75C3F88460A7B1117A6A8E83BBFDA (void);
// 0x00000033 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__6()
extern void BannerView_U3CConfigureBannerEventsU3Em__6_m5CF638B97328668C57B6ADD8F894F8A896E5CE55 (void);
// 0x00000034 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__7()
extern void BannerView_U3CConfigureBannerEventsU3Em__7_m2EF5ACBA7BC2ED7757DE3168313B510254D40360 (void);
// 0x00000035 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__8()
extern void BannerView_U3CConfigureBannerEventsU3Em__8_m45637795C8771179168190F2416C12E00F7F5FD4 (void);
// 0x00000036 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__9()
extern void BannerView_U3CConfigureBannerEventsU3Em__9_mBAFF6B7A8D1EBEC7EAA84045F33688A758D5E350 (void);
// 0x00000037 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__A()
extern void BannerView_U3CConfigureBannerEventsU3Em__A_m7E6F8FC51B0D9116AD0FCDD8BA911ACDEEF81B6B (void);
// 0x00000038 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__B()
extern void BannerView_U3CConfigureBannerEventsU3Em__B_m3F0E58BBAF524995BE9191214C7A00866D239EE7 (void);
// 0x00000039 System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::.ctor()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m060EBAF5682FAC0AFC373373F04DCB3E79E3AA08 (void);
// 0x0000003A System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::<>m__0()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mDB959B52C4E56D81A153F3A5377657D018724B8E (void);
// 0x0000003B System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::.ctor()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_mEFC54BEB5990142F62419DC4F7E5FFBC6E6BD564 (void);
// 0x0000003C System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::<>m__0()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m230F5823E8BA52015F1F517CD46BFAF062F8606A (void);
// 0x0000003D System.Void GoogleMobileAds.Api.InitializationStatus::.ctor(GoogleMobileAds.Common.IInitializationStatusClient)
extern void InitializationStatus__ctor_m81E5D6C244479E99C7FBC22CBA022A78940CF9AA (void);
// 0x0000003E System.Void GoogleMobileAds.Api.InterstitialAd::.ctor(GoogleMobileAds.Common.IInterstitialClient)
extern void InterstitialAd__ctor_mC20FB4E66457695B6B5E5B2F83F7F4EBC6409E52 (void);
// 0x0000003F System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdImpressionRecorded(System.Action)
extern void InterstitialAd_add_OnAdImpressionRecorded_mEE76C3EE5B40AD5CDE9E599DB85416C2D1BBEC97 (void);
// 0x00000040 System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdImpressionRecorded(System.Action)
extern void InterstitialAd_remove_OnAdImpressionRecorded_mE8711AA242D2D15C796EF98D6737BD5C0805BA4C (void);
// 0x00000041 System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentClosed(System.Action)
extern void InterstitialAd_add_OnAdFullScreenContentClosed_m885899220C4F4FD0B0500B7B2A7C2B5990C67189 (void);
// 0x00000042 System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void InterstitialAd_remove_OnAdFullScreenContentClosed_m4309E45A485A3BA8DA5D6C6FBAF6FA51E988B56F (void);
// 0x00000043 System.Void GoogleMobileAds.Api.InterstitialAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>)
extern void InterstitialAd_Load_m65BE712C6BB288D905F04928E564375F5519A23E (void);
// 0x00000044 System.Boolean GoogleMobileAds.Api.InterstitialAd::CanShowAd()
extern void InterstitialAd_CanShowAd_mB791950EBEFBBF843AF43EA8D9726B427A8B0075 (void);
// 0x00000045 System.Void GoogleMobileAds.Api.InterstitialAd::Show()
extern void InterstitialAd_Show_mA8A9BC2F5AA2D8EC12343136AACEAD8A22547906 (void);
// 0x00000046 System.Void GoogleMobileAds.Api.InterstitialAd::Destroy()
extern void InterstitialAd_Destroy_m0A4025154E957CA31D3B6A73734AD5D654C0E899 (void);
// 0x00000047 System.Void GoogleMobileAds.Api.InterstitialAd::RegisterAdEvents()
extern void InterstitialAd_RegisterAdEvents_m5676A141658FC9AD1DACE7DCDBFE4B864AC88FB8 (void);
// 0x00000048 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__0()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__0_m5C25BCCFC3BF2A06F96EC0835D47171031D421BB (void);
// 0x00000049 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__1_mC7A3521C70DAAE2ABA2EE6D9C991B2F67D8F8958 (void);
// 0x0000004A System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__2_m750551D2E4E46E2F6B45F8034DC66D11021D21CC (void);
// 0x0000004B System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__3_mA5A87376CBE52F0B1B619BC1D93E80583FD9FCB3 (void);
// 0x0000004C System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__4_m88E8DE4728A66B7F5D639F15DAD0CDD62DD45710 (void);
// 0x0000004D System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__5(System.Object,GoogleMobileAds.Api.AdValueEventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__5_mBF6B8452E7AD80EE2856817AFA2D559125B00A1D (void);
// 0x0000004E System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__6()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__6_m5704D40E2891F4DA8D22775889CC7ACF18477910 (void);
// 0x0000004F System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__7()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__7_mBFF8EE45A4089CD23352D93FA5343038A6568AC0 (void);
// 0x00000050 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__8()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__8_m562F35643C919BED83DDA088AB0744F9436B8365 (void);
// 0x00000051 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__9()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__9_m49563EB6919CD19798232B4B5B7B067111F4DD5F (void);
// 0x00000052 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_mE612BFD783FEF8ECF97F11F2AFD7B10D404A3725 (void);
// 0x00000053 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1312C08C350C77C79136982F93B03FE3509483BC (void);
// 0x00000054 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m8706A3E0A1D4EF22D739DD82F9A2971141CF24AE (void);
// 0x00000055 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m1D2A527036CA367DBBE7767A55DFC25EDA465580 (void);
// 0x00000056 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mC4E95AD1C9D793A40E3FD29F3F00460C6C600077 (void);
// 0x00000057 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::.ctor()
extern void U3CLoadU3Ec__AnonStorey2__ctor_m684E4231ADBFA4D0268DAF28417F5531C4CD2EAF (void);
// 0x00000058 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::<>m__0()
extern void U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_mA4C09658060763D9631DA58CD78FB52682D766F8 (void);
// 0x00000059 System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m23EF2C66ED730E3AF9B0439AF3077498C41E5A20 (void);
// 0x0000005A System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m744E51FCBABD7300BF0B0F1FBB2E08D626CE05C7 (void);
// 0x0000005B System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m413619CAC5BA55114DFDF00AD9E0E75EEAB4DC3A (void);
// 0x0000005C System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m065AB1CA8539A3184AD62DCBC027F9E0C99B8F78 (void);
// 0x0000005D System.Void GoogleMobileAds.Api.LoadAdError::.ctor(GoogleMobileAds.Common.ILoadAdErrorClient)
extern void LoadAdError__ctor_mB9A53C4D050AA092D795A4A6F777A11C58A1BA38 (void);
// 0x0000005E System.String GoogleMobileAds.Api.LoadAdError::ToString()
extern void LoadAdError_ToString_mC4331750780379F9A2BD15E3D9FC58263527F3C3 (void);
// 0x0000005F System.Void GoogleMobileAds.Api.MobileAds::.cctor()
extern void MobileAds__cctor_m325501C5A17B90DE872C1ACF89C85B5782804073 (void);
// 0x00000060 System.Void GoogleMobileAds.Api.MobileAds::.ctor()
extern void MobileAds__ctor_m20AC393BB1FF9462DC408B8C0C3C9BEB78BA107C (void);
// 0x00000061 GoogleMobileAds.Api.MobileAds GoogleMobileAds.Api.MobileAds::get_Instance()
extern void MobileAds_get_Instance_m28C1BB1B478806A18C69C684201793F82642CB46 (void);
// 0x00000062 System.Boolean GoogleMobileAds.Api.MobileAds::get_RaiseAdEventsOnUnityMainThread()
extern void MobileAds_get_RaiseAdEventsOnUnityMainThread_m666A82842C0649D1082F7093EEA4339F5040F86D (void);
// 0x00000063 System.Void GoogleMobileAds.Api.MobileAds::Initialize(System.Action`1<GoogleMobileAds.Api.InitializationStatus>)
extern void MobileAds_Initialize_m72020D9E6210DB3A70DAA5D2A2E8AA4650A91682 (void);
// 0x00000064 GoogleMobileAds.IClientFactory GoogleMobileAds.Api.MobileAds::GetClientFactory()
extern void MobileAds_GetClientFactory_mBB9C0F59D916B464F0355F5E4D77C736EE0E4797 (void);
// 0x00000065 System.Void GoogleMobileAds.Api.MobileAds::RaiseAction(System.Action)
extern void MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381 (void);
// 0x00000066 System.Void GoogleMobileAds.Api.MobileAds::SetUnityMainThreadSynchronizationContext()
extern void MobileAds_SetUnityMainThreadSynchronizationContext_m57B0D93EE70DF4361A738A70BD539AF4C02856EE (void);
// 0x00000067 GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.Api.MobileAds::GetMobileAdsClient()
extern void MobileAds_GetMobileAdsClient_m9C2C09FEB0C57C7B95507858250F69C614545A22 (void);
// 0x00000068 System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::.ctor()
extern void U3CInitializeU3Ec__AnonStorey0__ctor_m6BCC6755920E584CDF9D85E05CFC2D5BCEF5DDD8 (void);
// 0x00000069 System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::<>m__0(GoogleMobileAds.Common.IInitializationStatusClient)
extern void U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8F8F6710234EB73B47E080EAD12705240A67BCDC (void);
// 0x0000006A System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::.ctor()
extern void U3CInitializeU3Ec__AnonStorey1__ctor_m741F3A211AE607EA0E9B16CFBD679151BBB9FC5F (void);
// 0x0000006B System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::<>m__0()
extern void U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m1DB1BED306B691F0574FDF2C8085843959F8E9B8 (void);
// 0x0000006C System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey4::.ctor()
extern void U3CRaiseActionU3Ec__AnonStorey4__ctor_mD40D1E8675829115CBBB41B625B16808C2B442B8 (void);
// 0x0000006D System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey4::<>m__0(System.Object)
extern void U3CRaiseActionU3Ec__AnonStorey4_U3CU3Em__0_mBD5D966DAE68B53DF0169B4643CAD0F1B3032275 (void);
// 0x0000006E System.Void GoogleMobileAds.Api.RewardedAd::.ctor(GoogleMobileAds.Common.IRewardedAdClient)
extern void RewardedAd__ctor_mBF4FD1B0A8126720B59CCA56740DBE631C5AC3E2 (void);
// 0x0000006F System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdImpressionRecorded(System.Action)
extern void RewardedAd_add_OnAdImpressionRecorded_m77FD4DF4E4B80853872FF3E8FCF2EC89F9778285 (void);
// 0x00000070 System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdImpressionRecorded(System.Action)
extern void RewardedAd_remove_OnAdImpressionRecorded_mB2345AE9EE80EA7D45CE0AD5B007EB74FF0A017A (void);
// 0x00000071 System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentClosed(System.Action)
extern void RewardedAd_add_OnAdFullScreenContentClosed_m93A2301AC1BA0C64D82253C4CBCD2EF605D51D08 (void);
// 0x00000072 System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void RewardedAd_remove_OnAdFullScreenContentClosed_mF679F86473CC5D525DFDC77C8DAC0564CFA56EC2 (void);
// 0x00000073 System.Void GoogleMobileAds.Api.RewardedAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>)
extern void RewardedAd_Load_m1D4AB054F4D1471F6093EC64489802C8978ED23F (void);
// 0x00000074 System.Boolean GoogleMobileAds.Api.RewardedAd::CanShowAd()
extern void RewardedAd_CanShowAd_m7D7482F53F2599086917F6FD0A6567F18E6AD95D (void);
// 0x00000075 System.Void GoogleMobileAds.Api.RewardedAd::Show(System.Action`1<GoogleMobileAds.Api.Reward>)
extern void RewardedAd_Show_mDF055BF09643F042DC3A45EA8A3F3E540169AD6B (void);
// 0x00000076 System.Void GoogleMobileAds.Api.RewardedAd::Destroy()
extern void RewardedAd_Destroy_m69BD0C0C20AB57597D92F2784C10D0BB34FF713A (void);
// 0x00000077 System.Void GoogleMobileAds.Api.RewardedAd::RegisterAdEvents()
extern void RewardedAd_RegisterAdEvents_mD9C07CFE82774348721C15D2F75BB196550F59D1 (void);
// 0x00000078 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__0()
extern void RewardedAd_U3CRegisterAdEventsU3Em__0_mAEB8B50B32D272E4C7619E7DCC33B523F94BE5EC (void);
// 0x00000079 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__1_m141DE54DA1D602B3EB716134CE0FBADCD2144744 (void);
// 0x0000007A System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__2_m5EEEB3616932EC86B7C5F2F182C77890C1AFD5BA (void);
// 0x0000007B System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__3_mF6E35C54409557EB2C616968A93848D0C76274E0 (void);
// 0x0000007C System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__4_m2A8445A99D60F80091ACF715A44698A34FF64C2F (void);
// 0x0000007D System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__5(System.Object,GoogleMobileAds.Api.AdValueEventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__5_m1964F8BF3D5D2B871688F810CCC93C0B1988CC3C (void);
// 0x0000007E System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__6(System.Object,GoogleMobileAds.Api.Reward)
extern void RewardedAd_U3CRegisterAdEventsU3Em__6_m02BAE62EBD229AAE3E055A27950B6BDA2348DDEF (void);
// 0x0000007F System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__7()
extern void RewardedAd_U3CRegisterAdEventsU3Em__7_m3363873E5ED93C748889CBBA82F729836EEEF341 (void);
// 0x00000080 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__8()
extern void RewardedAd_U3CRegisterAdEventsU3Em__8_m07B5B43AAD0578F38B75424B547CCF128B4993BF (void);
// 0x00000081 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__9()
extern void RewardedAd_U3CRegisterAdEventsU3Em__9_m31BBA6C3DE03F61AA0AD2F9BAD77099FE7299902 (void);
// 0x00000082 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__A()
extern void RewardedAd_U3CRegisterAdEventsU3Em__A_mA16151D456016AA5B967CA38D0E6C26D3138F9CC (void);
// 0x00000083 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m9F04B6A33CC486E2F33C68674100252B0C6350B1 (void);
// 0x00000084 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m743183A467410BD696AB884EFBD625BC2534FAAD (void);
// 0x00000085 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m4A5DA47A71334D0CBF420977C3D35A43447BEBA1 (void);
// 0x00000086 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mD91B2B52A30D48F377A674E4721A16EA2B8D3F7A (void);
// 0x00000087 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m1598D3DE4C82B0879CEDEDBA19FBAD1E5DDCC694 (void);
// 0x00000088 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m2835D1C5F7BEA5B05901A30106F40347E5B8A42A (void);
// 0x00000089 System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m80A53A73AB7B6C88A7716EF97DB7213DB13953B7 (void);
// 0x0000008A System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_mA8BE0B9BAF1204516DBA55A83E82BA4ED277C3B4 (void);
// 0x0000008B System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m7AE56F8868840992DE6BAE9EEC4F5910F5C1E059 (void);
// 0x0000008C System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m5AF6FD0C0E1EEE656F43621BCADE752A803FF5F1 (void);
// 0x0000008D System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m9C5946CD95F68D8E1CE133F84406DB44F5AC25CE (void);
// 0x0000008E System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m92D3C38FF40FD078475098EC1C40431383E37BEE (void);
// 0x0000008F System.Void GoogleMobileAds.Api.RewardedInterstitialAd::.ctor(GoogleMobileAds.Common.IRewardedInterstitialAdClient)
extern void RewardedInterstitialAd__ctor_m6C8409D21D1CCE49EB6900ACC410D3D715BD1676 (void);
// 0x00000090 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdImpressionRecorded(System.Action)
extern void RewardedInterstitialAd_add_OnAdImpressionRecorded_m62213B2AF13FCB977B73D66ECAB9493604293C43 (void);
// 0x00000091 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdImpressionRecorded(System.Action)
extern void RewardedInterstitialAd_remove_OnAdImpressionRecorded_m1960B52BBD3359F7C45EB6DF6C7E4E5E229FA056 (void);
// 0x00000092 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentClosed(System.Action)
extern void RewardedInterstitialAd_add_OnAdFullScreenContentClosed_m89962D06C0D085477408E2F300AC37E0AA450808 (void);
// 0x00000093 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void RewardedInterstitialAd_remove_OnAdFullScreenContentClosed_m5584C6EF14321CC6962929E1748C7073902142DA (void);
// 0x00000094 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>)
extern void RewardedInterstitialAd_Load_mF0704352E6E9AAF33981C98DAA765233445FF217 (void);
// 0x00000095 System.Boolean GoogleMobileAds.Api.RewardedInterstitialAd::CanShowAd()
extern void RewardedInterstitialAd_CanShowAd_m4AC3E282C25EA78C3E1117EA7C4DB2A1148A01C6 (void);
// 0x00000096 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Show(System.Action`1<GoogleMobileAds.Api.Reward>)
extern void RewardedInterstitialAd_Show_mB86E92AB833142ECA09FC4BE910E5447A5313564 (void);
// 0x00000097 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Destroy()
extern void RewardedInterstitialAd_Destroy_m9ED33C42D9C9EC80C861C1EB45FF6B3B3E131CCA (void);
// 0x00000098 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::RegisterAdEvents()
extern void RewardedInterstitialAd_RegisterAdEvents_mC83B4F7540F306953DB6871E29B15A5481597099 (void);
// 0x00000099 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__0()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m4280C1BED635E8CE98831B28294A9449DAB06A25 (void);
// 0x0000009A System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m94CF1788E4D4B09025A96C2E0B0B2F8E787BAD61 (void);
// 0x0000009B System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_mCAF40DE07FAF857BB7C1D2C912D787AA7AB5BA49 (void);
// 0x0000009C System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m49F90F797705E782795E7CE3549809389CBF9556 (void);
// 0x0000009D System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m70A2ADA6428F0F6A5F28126640D1991D29EEA750 (void);
// 0x0000009E System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__5(System.Object,GoogleMobileAds.Api.AdValueEventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m9C805E78125CE3E185F7F62351B8EAC3391C77F3 (void);
// 0x0000009F System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__6(System.Object,GoogleMobileAds.Api.Reward)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_m932A05721D7C43A3AEF86AB3694B98DE14F7C824 (void);
// 0x000000A0 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__7()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_m634C748D4119E9D2AC7F0887C838B6A2B125DB03 (void);
// 0x000000A1 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__8()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mC29EE6EF37D9872E422D28750BABD3465BD2EF74 (void);
// 0x000000A2 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__9()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m33BB80707918E085C311394254E35D827FEBEA70 (void);
// 0x000000A3 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__A()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m27A124481E82CF9ECF20BD94A3C9396C94CCD948 (void);
// 0x000000A4 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_mD052E160F7B62998DC5CC2598A1427C9F53A5B9F (void);
// 0x000000A5 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC21E632D1E5E289F12F45DAE33CEB9988C94ED19 (void);
// 0x000000A6 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m97EE0BE3A38F7A5C2EE76A1E583FB64B0776A5C0 (void);
// 0x000000A7 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m41602AC9F16FBCED2C8BEC595E63928139CCDC40 (void);
// 0x000000A8 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_mAECA63012477E66A42906AB2932ED8D2CEA92C87 (void);
// 0x000000A9 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mA253F2F5D70A6F3710EBDE7A335721FEC303DFE9 (void);
// 0x000000AA System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_mE73F6AE49A6AAE73693152E1C7739C2FBE23C7C5 (void);
// 0x000000AB System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m866C96355349CBB87597D773BBAE4DEF09AF19FA (void);
// 0x000000AC System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m4A5FE98784AF8477D71B2FB633101DEE5894D7F4 (void);
// 0x000000AD System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m5F3995A957271AEAC42467408971897F705E3869 (void);
// 0x000000AE System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m7080B982B125E09371C32F60DE5036B9F819634E (void);
// 0x000000AF System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m9437C3E2705F18B4E367F8BD684D67D257328A5E (void);
static Il2CppMethodPointer s_methodPointers[175] = 
{
	AdError__ctor_m75162359A2D2A3EC74C4D93C4F1FCF39F8D4FAB4,
	AdError_ToString_m6B9BA7043A52DB62AF8646F8697E1A32743FCE88,
	AppOpenAd__ctor_m23069D08AA0576F8C3A1E8ED8BDB4AA4F340FDAD,
	AppOpenAd_add_OnAdImpressionRecorded_m024C47A8AB06DBC76BA4571EDED873743E3F30E5,
	AppOpenAd_remove_OnAdImpressionRecorded_m5E377061E2BE7E7AC4ABBAD16773816A6A67A27C,
	AppOpenAd_add_OnAdFullScreenContentClosed_mCAB05E8942F7EF4874BCEBBD8A8A43BF435AED8E,
	AppOpenAd_remove_OnAdFullScreenContentClosed_mD7C2D1772D3D114E18C214EBD5E56AC470AF2EEB,
	AppOpenAd_add_OnAdFullScreenContentFailed_m254123049CED4B9C9C213F83D2D64ACE9E733491,
	AppOpenAd_remove_OnAdFullScreenContentFailed_m9FA7EB731F72C932BD0525072A71158E47FEDB0B,
	AppOpenAd_Load_mFA1409AC8809A6F8A835C3F14BE5FFF0BA306C9A,
	AppOpenAd_CanShowAd_mC7D41DB510A25134156ABC621FE11165C84CEF33,
	AppOpenAd_Show_m0BDB3E193F7E4DD88EC46C330B5F661889956D0A,
	AppOpenAd_Destroy_m62E4E94A9579369744315568A15133F5792CBC2C,
	AppOpenAd_RegisterAdEvents_mF1F438E2657A544DC5E00DB502A19FBEAD222E93,
	AppOpenAd_U3CRegisterAdEventsU3Em__0_m6EB81ECE17183A949FE0D9A1E1161982BF11103C,
	AppOpenAd_U3CRegisterAdEventsU3Em__1_mDD77C10B8C79F36145F039C5E46E2A5F4BF98CD8,
	AppOpenAd_U3CRegisterAdEventsU3Em__2_m0745E7DCB5926AEC7A942C72CFA54A04533BE2B3,
	AppOpenAd_U3CRegisterAdEventsU3Em__3_m9E010CC5392B712457E641BF5548AAD5F900DB66,
	AppOpenAd_U3CRegisterAdEventsU3Em__4_m9E97077F81575A81B9AB836AEB1AB284F72C7DA4,
	AppOpenAd_U3CRegisterAdEventsU3Em__5_m1A2128B5A04CDD0DD741FF0FA7E614914662D349,
	AppOpenAd_U3CRegisterAdEventsU3Em__6_mDA2F15595A3B1DE9A59987F766BDBDE62DA02B3B,
	AppOpenAd_U3CRegisterAdEventsU3Em__7_mC2C2932C98115FB731FEBCEB7D14BCECDBF18E04,
	AppOpenAd_U3CRegisterAdEventsU3Em__8_mEBBF84209D60D055AA2108C55F9C5B256A111786,
	AppOpenAd_U3CRegisterAdEventsU3Em__9_mFAF62BAD52DBBA208A350C8287396F93ADE86D40,
	U3CLoadU3Ec__AnonStorey0__ctor_mB114A18F7C06F791F1FE20B5523A7A7049E283F6,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m109390BB4960431AB6CBCB98B4B5AF4B0AD3F771,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mA71446EEE7179C6FB24F3E440B2102FD4AEAA3E5,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m4EA34BD79081682018B340E7E3F9E103ED29F86A,
	U3CLoadU3Ec__AnonStorey1__ctor_mF4992A1F8C3FFA2ED6D381C87300283F582196F0,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m4146799EF7435C625784973820731937E001947F,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m1CEFDF3AD1B70504D6807A2C7BBEEE42F84FBEB6,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mD5C8E0D375E878A173A696113A7D2FECCF5A795E,
	U3CRegisterAdEventsU3Ec__AnonStorey5__ctor_m8C8921EB99AC5CCB0E91ADE820BF5F8EB2FABC1E,
	U3CRegisterAdEventsU3Ec__AnonStorey5_U3CU3Em__0_mF673909B2D31A52D8960E421B24CB4FDB80FC08B,
	BannerView__ctor_mCFE456D97EB8D004D950ECABA22A44185984303B,
	BannerView_add_OnBannerAdLoaded_m1C22C7B5353261BD1F40A65BDE455296D44E116A,
	BannerView_remove_OnBannerAdLoaded_m15A059AA97882FB008AD47216A19166C25B2CF39,
	BannerView_add_OnBannerAdLoadFailed_m78AB9BF93A57D78C6CA5523A808286136886FBE0,
	BannerView_remove_OnBannerAdLoadFailed_m1EEB499798A7FD860561891F1C5010A12FC9C3C6,
	BannerView_Destroy_mBE40E1F64AE5B793C011002988540402EDC0345D,
	BannerView_LoadAd_mCB8DE994927BF8D2F4E93D659FCBE630B6B7761C,
	BannerView_Show_m5C1C0FF737CA2B4B06394885BC94EC7335ABB707,
	BannerView_Hide_m14B1CF6644711560E11092DBEBA76DC4159E54E0,
	BannerView_ConfigureBannerEvents_mDF2FF80E541A6D5C9A19507AFA92618698B7D694,
	BannerView_U3CConfigureBannerEventsU3Em__0_mAB59CDA05FF9FAC2CBE5CB5815C46895F641CB54,
	BannerView_U3CConfigureBannerEventsU3Em__1_mCC40AAD739C5097E87710765BD6E616290EB9A8F,
	BannerView_U3CConfigureBannerEventsU3Em__2_m45BDA7B13065BB9326A56CB29357CC5BE655AEFB,
	BannerView_U3CConfigureBannerEventsU3Em__3_mC978D558480BB77CB457D63268531A4B42F05DB1,
	BannerView_U3CConfigureBannerEventsU3Em__4_mF3FFB125AB04A05BCAD1AD9895549454FCDC22CC,
	BannerView_U3CConfigureBannerEventsU3Em__5_mC876D2C02AE75C3F88460A7B1117A6A8E83BBFDA,
	BannerView_U3CConfigureBannerEventsU3Em__6_m5CF638B97328668C57B6ADD8F894F8A896E5CE55,
	BannerView_U3CConfigureBannerEventsU3Em__7_m2EF5ACBA7BC2ED7757DE3168313B510254D40360,
	BannerView_U3CConfigureBannerEventsU3Em__8_m45637795C8771179168190F2416C12E00F7F5FD4,
	BannerView_U3CConfigureBannerEventsU3Em__9_mBAFF6B7A8D1EBEC7EAA84045F33688A758D5E350,
	BannerView_U3CConfigureBannerEventsU3Em__A_m7E6F8FC51B0D9116AD0FCDD8BA911ACDEEF81B6B,
	BannerView_U3CConfigureBannerEventsU3Em__B_m3F0E58BBAF524995BE9191214C7A00866D239EE7,
	U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m060EBAF5682FAC0AFC373373F04DCB3E79E3AA08,
	U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mDB959B52C4E56D81A153F3A5377657D018724B8E,
	U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_mEFC54BEB5990142F62419DC4F7E5FFBC6E6BD564,
	U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m230F5823E8BA52015F1F517CD46BFAF062F8606A,
	InitializationStatus__ctor_m81E5D6C244479E99C7FBC22CBA022A78940CF9AA,
	InterstitialAd__ctor_mC20FB4E66457695B6B5E5B2F83F7F4EBC6409E52,
	InterstitialAd_add_OnAdImpressionRecorded_mEE76C3EE5B40AD5CDE9E599DB85416C2D1BBEC97,
	InterstitialAd_remove_OnAdImpressionRecorded_mE8711AA242D2D15C796EF98D6737BD5C0805BA4C,
	InterstitialAd_add_OnAdFullScreenContentClosed_m885899220C4F4FD0B0500B7B2A7C2B5990C67189,
	InterstitialAd_remove_OnAdFullScreenContentClosed_m4309E45A485A3BA8DA5D6C6FBAF6FA51E988B56F,
	InterstitialAd_Load_m65BE712C6BB288D905F04928E564375F5519A23E,
	InterstitialAd_CanShowAd_mB791950EBEFBBF843AF43EA8D9726B427A8B0075,
	InterstitialAd_Show_mA8A9BC2F5AA2D8EC12343136AACEAD8A22547906,
	InterstitialAd_Destroy_m0A4025154E957CA31D3B6A73734AD5D654C0E899,
	InterstitialAd_RegisterAdEvents_m5676A141658FC9AD1DACE7DCDBFE4B864AC88FB8,
	InterstitialAd_U3CRegisterAdEventsU3Em__0_m5C25BCCFC3BF2A06F96EC0835D47171031D421BB,
	InterstitialAd_U3CRegisterAdEventsU3Em__1_mC7A3521C70DAAE2ABA2EE6D9C991B2F67D8F8958,
	InterstitialAd_U3CRegisterAdEventsU3Em__2_m750551D2E4E46E2F6B45F8034DC66D11021D21CC,
	InterstitialAd_U3CRegisterAdEventsU3Em__3_mA5A87376CBE52F0B1B619BC1D93E80583FD9FCB3,
	InterstitialAd_U3CRegisterAdEventsU3Em__4_m88E8DE4728A66B7F5D639F15DAD0CDD62DD45710,
	InterstitialAd_U3CRegisterAdEventsU3Em__5_mBF6B8452E7AD80EE2856817AFA2D559125B00A1D,
	InterstitialAd_U3CRegisterAdEventsU3Em__6_m5704D40E2891F4DA8D22775889CC7ACF18477910,
	InterstitialAd_U3CRegisterAdEventsU3Em__7_mBFF8EE45A4089CD23352D93FA5343038A6568AC0,
	InterstitialAd_U3CRegisterAdEventsU3Em__8_m562F35643C919BED83DDA088AB0744F9436B8365,
	InterstitialAd_U3CRegisterAdEventsU3Em__9_m49563EB6919CD19798232B4B5B7B067111F4DD5F,
	U3CLoadU3Ec__AnonStorey0__ctor_mE612BFD783FEF8ECF97F11F2AFD7B10D404A3725,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1312C08C350C77C79136982F93B03FE3509483BC,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m8706A3E0A1D4EF22D739DD82F9A2971141CF24AE,
	U3CLoadU3Ec__AnonStorey1__ctor_m1D2A527036CA367DBBE7767A55DFC25EDA465580,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mC4E95AD1C9D793A40E3FD29F3F00460C6C600077,
	U3CLoadU3Ec__AnonStorey2__ctor_m684E4231ADBFA4D0268DAF28417F5531C4CD2EAF,
	U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_mA4C09658060763D9631DA58CD78FB52682D766F8,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m23EF2C66ED730E3AF9B0439AF3077498C41E5A20,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m744E51FCBABD7300BF0B0F1FBB2E08D626CE05C7,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m413619CAC5BA55114DFDF00AD9E0E75EEAB4DC3A,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m065AB1CA8539A3184AD62DCBC027F9E0C99B8F78,
	LoadAdError__ctor_mB9A53C4D050AA092D795A4A6F777A11C58A1BA38,
	LoadAdError_ToString_mC4331750780379F9A2BD15E3D9FC58263527F3C3,
	MobileAds__cctor_m325501C5A17B90DE872C1ACF89C85B5782804073,
	MobileAds__ctor_m20AC393BB1FF9462DC408B8C0C3C9BEB78BA107C,
	MobileAds_get_Instance_m28C1BB1B478806A18C69C684201793F82642CB46,
	MobileAds_get_RaiseAdEventsOnUnityMainThread_m666A82842C0649D1082F7093EEA4339F5040F86D,
	MobileAds_Initialize_m72020D9E6210DB3A70DAA5D2A2E8AA4650A91682,
	MobileAds_GetClientFactory_mBB9C0F59D916B464F0355F5E4D77C736EE0E4797,
	MobileAds_RaiseAction_mF41F39A8B795CD04336A9A9A64808070FAFA4381,
	MobileAds_SetUnityMainThreadSynchronizationContext_m57B0D93EE70DF4361A738A70BD539AF4C02856EE,
	MobileAds_GetMobileAdsClient_m9C2C09FEB0C57C7B95507858250F69C614545A22,
	U3CInitializeU3Ec__AnonStorey0__ctor_m6BCC6755920E584CDF9D85E05CFC2D5BCEF5DDD8,
	U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8F8F6710234EB73B47E080EAD12705240A67BCDC,
	U3CInitializeU3Ec__AnonStorey1__ctor_m741F3A211AE607EA0E9B16CFBD679151BBB9FC5F,
	U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m1DB1BED306B691F0574FDF2C8085843959F8E9B8,
	U3CRaiseActionU3Ec__AnonStorey4__ctor_mD40D1E8675829115CBBB41B625B16808C2B442B8,
	U3CRaiseActionU3Ec__AnonStorey4_U3CU3Em__0_mBD5D966DAE68B53DF0169B4643CAD0F1B3032275,
	RewardedAd__ctor_mBF4FD1B0A8126720B59CCA56740DBE631C5AC3E2,
	RewardedAd_add_OnAdImpressionRecorded_m77FD4DF4E4B80853872FF3E8FCF2EC89F9778285,
	RewardedAd_remove_OnAdImpressionRecorded_mB2345AE9EE80EA7D45CE0AD5B007EB74FF0A017A,
	RewardedAd_add_OnAdFullScreenContentClosed_m93A2301AC1BA0C64D82253C4CBCD2EF605D51D08,
	RewardedAd_remove_OnAdFullScreenContentClosed_mF679F86473CC5D525DFDC77C8DAC0564CFA56EC2,
	RewardedAd_Load_m1D4AB054F4D1471F6093EC64489802C8978ED23F,
	RewardedAd_CanShowAd_m7D7482F53F2599086917F6FD0A6567F18E6AD95D,
	RewardedAd_Show_mDF055BF09643F042DC3A45EA8A3F3E540169AD6B,
	RewardedAd_Destroy_m69BD0C0C20AB57597D92F2784C10D0BB34FF713A,
	RewardedAd_RegisterAdEvents_mD9C07CFE82774348721C15D2F75BB196550F59D1,
	RewardedAd_U3CRegisterAdEventsU3Em__0_mAEB8B50B32D272E4C7619E7DCC33B523F94BE5EC,
	RewardedAd_U3CRegisterAdEventsU3Em__1_m141DE54DA1D602B3EB716134CE0FBADCD2144744,
	RewardedAd_U3CRegisterAdEventsU3Em__2_m5EEEB3616932EC86B7C5F2F182C77890C1AFD5BA,
	RewardedAd_U3CRegisterAdEventsU3Em__3_mF6E35C54409557EB2C616968A93848D0C76274E0,
	RewardedAd_U3CRegisterAdEventsU3Em__4_m2A8445A99D60F80091ACF715A44698A34FF64C2F,
	RewardedAd_U3CRegisterAdEventsU3Em__5_m1964F8BF3D5D2B871688F810CCC93C0B1988CC3C,
	RewardedAd_U3CRegisterAdEventsU3Em__6_m02BAE62EBD229AAE3E055A27950B6BDA2348DDEF,
	RewardedAd_U3CRegisterAdEventsU3Em__7_m3363873E5ED93C748889CBBA82F729836EEEF341,
	RewardedAd_U3CRegisterAdEventsU3Em__8_m07B5B43AAD0578F38B75424B547CCF128B4993BF,
	RewardedAd_U3CRegisterAdEventsU3Em__9_m31BBA6C3DE03F61AA0AD2F9BAD77099FE7299902,
	RewardedAd_U3CRegisterAdEventsU3Em__A_mA16151D456016AA5B967CA38D0E6C26D3138F9CC,
	U3CLoadU3Ec__AnonStorey0__ctor_m9F04B6A33CC486E2F33C68674100252B0C6350B1,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m743183A467410BD696AB884EFBD625BC2534FAAD,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m4A5DA47A71334D0CBF420977C3D35A43447BEBA1,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mD91B2B52A30D48F377A674E4721A16EA2B8D3F7A,
	U3CLoadU3Ec__AnonStorey1__ctor_m1598D3DE4C82B0879CEDEDBA19FBAD1E5DDCC694,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m2835D1C5F7BEA5B05901A30106F40347E5B8A42A,
	U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m80A53A73AB7B6C88A7716EF97DB7213DB13953B7,
	U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_mA8BE0B9BAF1204516DBA55A83E82BA4ED277C3B4,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m7AE56F8868840992DE6BAE9EEC4F5910F5C1E059,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m5AF6FD0C0E1EEE656F43621BCADE752A803FF5F1,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m9C5946CD95F68D8E1CE133F84406DB44F5AC25CE,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m92D3C38FF40FD078475098EC1C40431383E37BEE,
	RewardedInterstitialAd__ctor_m6C8409D21D1CCE49EB6900ACC410D3D715BD1676,
	RewardedInterstitialAd_add_OnAdImpressionRecorded_m62213B2AF13FCB977B73D66ECAB9493604293C43,
	RewardedInterstitialAd_remove_OnAdImpressionRecorded_m1960B52BBD3359F7C45EB6DF6C7E4E5E229FA056,
	RewardedInterstitialAd_add_OnAdFullScreenContentClosed_m89962D06C0D085477408E2F300AC37E0AA450808,
	RewardedInterstitialAd_remove_OnAdFullScreenContentClosed_m5584C6EF14321CC6962929E1748C7073902142DA,
	RewardedInterstitialAd_Load_mF0704352E6E9AAF33981C98DAA765233445FF217,
	RewardedInterstitialAd_CanShowAd_m4AC3E282C25EA78C3E1117EA7C4DB2A1148A01C6,
	RewardedInterstitialAd_Show_mB86E92AB833142ECA09FC4BE910E5447A5313564,
	RewardedInterstitialAd_Destroy_m9ED33C42D9C9EC80C861C1EB45FF6B3B3E131CCA,
	RewardedInterstitialAd_RegisterAdEvents_mC83B4F7540F306953DB6871E29B15A5481597099,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m4280C1BED635E8CE98831B28294A9449DAB06A25,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m94CF1788E4D4B09025A96C2E0B0B2F8E787BAD61,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_mCAF40DE07FAF857BB7C1D2C912D787AA7AB5BA49,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m49F90F797705E782795E7CE3549809389CBF9556,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m70A2ADA6428F0F6A5F28126640D1991D29EEA750,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m9C805E78125CE3E185F7F62351B8EAC3391C77F3,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_m932A05721D7C43A3AEF86AB3694B98DE14F7C824,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_m634C748D4119E9D2AC7F0887C838B6A2B125DB03,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mC29EE6EF37D9872E422D28750BABD3465BD2EF74,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m33BB80707918E085C311394254E35D827FEBEA70,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m27A124481E82CF9ECF20BD94A3C9396C94CCD948,
	U3CLoadU3Ec__AnonStorey0__ctor_mD052E160F7B62998DC5CC2598A1427C9F53A5B9F,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC21E632D1E5E289F12F45DAE33CEB9988C94ED19,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m97EE0BE3A38F7A5C2EE76A1E583FB64B0776A5C0,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m41602AC9F16FBCED2C8BEC595E63928139CCDC40,
	U3CLoadU3Ec__AnonStorey1__ctor_mAECA63012477E66A42906AB2932ED8D2CEA92C87,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mA253F2F5D70A6F3710EBDE7A335721FEC303DFE9,
	U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_mE73F6AE49A6AAE73693152E1C7739C2FBE23C7C5,
	U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m866C96355349CBB87597D773BBAE4DEF09AF19FA,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m4A5FE98784AF8477D71B2FB633101DEE5894D7F4,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m5F3995A957271AEAC42467408971897F705E3869,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_m7080B982B125E09371C32F60DE5036B9F819634E,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m9437C3E2705F18B4E367F8BD684D67D257328A5E,
};
static const int32_t s_InvokerIndices[175] = 
{
	1610,
	1900,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	2606,
	1866,
	1935,
	1935,
	1935,
	1935,
	934,
	934,
	934,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	572,
	1610,
	1610,
	1610,
	1610,
	1935,
	1610,
	1935,
	1935,
	1935,
	934,
	934,
	934,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	2606,
	1866,
	1935,
	1935,
	1935,
	1935,
	934,
	934,
	934,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1900,
	3231,
	1935,
	3215,
	3203,
	3189,
	3215,
	3189,
	3231,
	3215,
	1935,
	1610,
	1935,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	2606,
	1866,
	1610,
	1935,
	1935,
	1935,
	934,
	934,
	934,
	934,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	2606,
	1866,
	1610,
	1935,
	1935,
	1935,
	934,
	934,
	934,
	934,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
};
extern const CustomAttributesCacheGenerator g_GoogleMobileAds_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_CodeGenModule = 
{
	"GoogleMobileAds.dll",
	175,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_GoogleMobileAds_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
