{"root": [{"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}]}