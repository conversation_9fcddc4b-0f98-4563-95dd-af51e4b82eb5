fileFormatVersion: 2
guid: 9f2edbf81053418f879076c05f816dc2
labels:
- gvh
- gvh_targets-editor
- gvh_version-11.6.0
- gvhp_exportpath-Firebase/Editor/Firebase.Editor.dll
PluginImporter:
  externalObjects: {}
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  defineConstraints: []
  isPreloaded: 0
  isOverridable: 0
  isExplicitlyReferenced: 0
  validateReferences: 0
  platformData:
  - first:
      : Any
    second:
      enabled: 0
      settings:
        Exclude Android: 1
        Exclude Editor: 0
        Exclude Linux64: 1
        Exclude OSXUniversal: 1
        Exclude Win: 1
        Exclude Win64: 1
  - first:
      : Linux
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : LinuxUniversal
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : OSXIntel
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : OSXIntel64
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : Web
    second:
      enabled: 0
      settings: {}
  - first:
      : WebStreamed
    second:
      enabled: 0
      settings: {}
  - first:
      Android: Android
    second:
      enabled: 0
      settings:
        CPU: ARMv7
  - first:
      Any: 
    second:
      enabled: 0
      settings: {}
  - first:
      Editor: Editor
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
  - first:
      Standalone: Linux64
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: OSXUniversal
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: Win
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: Win64
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Windows Store Apps: WindowsStoreApps
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  - first:
      iPhone: iOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  - first:
      tvOS: tvOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
