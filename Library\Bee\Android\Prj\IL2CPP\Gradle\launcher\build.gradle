apply plugin: 'com.android.application'

dependencies {
    implementation project(':unityLibrary')
    }

android {
    namespace "com.smg.tractor.trolly.games.farming.game"
    ndkPath "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK"
    compileSdkVersion 36
    buildToolsVersion '34.0.0'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 36
        applicationId 'com.smg.tractor.trolly.games.farming.game'
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
        versionCode 24
        versionName '2.4'
    }

    aaptOptions {
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
            jniDebuggable true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
        }
    }

    packagingOptions {
        doNotStrip '*/armeabi-v7a/*.so'
        doNotStrip '*/arm64-v8a/*.so'
        jniLibs {
            useLegacyPackaging true
        }
    }

    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = false
        }
        abi {
            enableSplit = true
        }
    }
}






apply from: '..\\unityLibrary\\GoogleMobileAdsPlugin.androidlib\\packaging_options.gradle'