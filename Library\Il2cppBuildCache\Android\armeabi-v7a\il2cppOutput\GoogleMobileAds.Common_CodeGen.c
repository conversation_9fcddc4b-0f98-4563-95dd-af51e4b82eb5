﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Common.AdErrorClientEventArgs::.ctor()
extern void AdErrorClientEventArgs__ctor_mF9539459BB3A7B964E940681D628BB03EA609353 (void);
// 0x00000002 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.AdErrorClientEventArgs::get_AdErrorClient()
extern void AdErrorClientEventArgs_get_AdErrorClient_m5A802BFC28476C80B37BFCFC9613D33430BCC87A (void);
// 0x00000003 System.Void GoogleMobileAds.Common.AdErrorClientEventArgs::set_AdErrorClient(GoogleMobileAds.Common.IAdErrorClient)
extern void AdErrorClientEventArgs_set_AdErrorClient_m8BE7E20C2CBCE3E110160A3315C67B0CB5B34752 (void);
// 0x00000004 System.Void GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::.ctor()
extern void AdInspectorErrorClientEventArgs__ctor_m18E7E5BACFE6BFF40F99775511C94D0E247EA188 (void);
// 0x00000005 System.Void GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::set_AdErrorClient(GoogleMobileAds.Common.IAdInspectorErrorClient)
extern void AdInspectorErrorClientEventArgs_set_AdErrorClient_m1F4C50D1C3548A1844E63D273BBD765091CA9A9E (void);
// 0x00000006 System.Void GoogleMobileAds.Common.AppStateEventClient::.ctor()
extern void AppStateEventClient__ctor_m363112C60B6460D70B815F28976BEFDC02F116CC (void);
// 0x00000007 GoogleMobileAds.Common.AppStateEventClient GoogleMobileAds.Common.AppStateEventClient::get_Instance()
extern void AppStateEventClient_get_Instance_m8DA2FA97849BC1318E2F0B86A4A4959FB903AE0A (void);
// 0x00000008 System.Void GoogleMobileAds.Common.AppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_AppStateChanged_m80083B55A920651778D9F90CC88667E8664FD468 (void);
// 0x00000009 System.Void GoogleMobileAds.Common.AppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_AppStateChanged_mE5371835404054ADAFF0B502BE61FBA28F8C06EC (void);
// 0x0000000A System.Void GoogleMobileAds.Common.AppStateEventClient::OnApplicationPause(System.Boolean)
extern void AppStateEventClient_OnApplicationPause_m3C5F0067EEEA87ABCAAF2AE8943025CA51AE5FCF (void);
// 0x0000000B System.Void GoogleMobileAds.Common.AppStateEventClient::<AppStateChanged>m__0(GoogleMobileAds.Common.AppState)
extern void AppStateEventClient_U3CAppStateChangedU3Em__0_m448ED5CEE240E283ECABCF1BF57BBE4D5AB8FF1E (void);
// 0x0000000C System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000000D System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000000E System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000000F System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000010 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x00000011 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x00000012 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000013 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000014 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000015 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000016 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000017 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000018 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000019 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000001A System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdClicked(System.Action)
// 0x0000001B System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdClicked(System.Action)
// 0x0000001C System.Void GoogleMobileAds.Common.IAppOpenAdClient::CreateAppOpenAd()
// 0x0000001D System.Void GoogleMobileAds.Common.IAppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x0000001E System.Void GoogleMobileAds.Common.IAppOpenAdClient::Show()
// 0x0000001F System.Void GoogleMobileAds.Common.IAppOpenAdClient::DestroyAppOpenAd()
// 0x00000020 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000021 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000022 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000023 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000024 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdOpening(System.EventHandler`1<System.EventArgs>)
// 0x00000025 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdOpening(System.EventHandler`1<System.EventArgs>)
// 0x00000026 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClosed(System.EventHandler`1<System.EventArgs>)
// 0x00000027 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdClosed(System.EventHandler`1<System.EventArgs>)
// 0x00000028 System.Void GoogleMobileAds.Common.IBannerClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x00000029 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x0000002A System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClicked(System.Action)
// 0x0000002B System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdClicked(System.Action)
// 0x0000002C System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdImpressionRecorded(System.Action)
// 0x0000002D System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdImpressionRecorded(System.Action)
// 0x0000002E System.Void GoogleMobileAds.Common.IBannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
// 0x0000002F System.Void GoogleMobileAds.Common.IBannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
// 0x00000030 System.Void GoogleMobileAds.Common.IBannerClient::ShowBannerView()
// 0x00000031 System.Void GoogleMobileAds.Common.IBannerClient::HideBannerView()
// 0x00000032 System.Void GoogleMobileAds.Common.IBannerClient::DestroyBannerView()
// 0x00000033 GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.IClientFactory::BuildAppOpenAdClient()
// 0x00000034 GoogleMobileAds.Common.IBannerClient GoogleMobileAds.IClientFactory::BuildBannerClient()
// 0x00000035 GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.IClientFactory::BuildInterstitialClient()
// 0x00000036 GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.IClientFactory::BuildRewardedAdClient()
// 0x00000037 GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.IClientFactory::BuildRewardedInterstitialAdClient()
// 0x00000038 GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.IClientFactory::MobileAdsInstance()
// 0x00000039 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000003A System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000003B System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000003C System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000003D System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x0000003E System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x0000003F System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000040 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000041 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000042 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000043 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000044 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000045 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000046 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000047 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdClicked(System.Action)
// 0x00000048 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdClicked(System.Action)
// 0x00000049 System.Void GoogleMobileAds.Common.IInterstitialClient::CreateInterstitialAd()
// 0x0000004A System.Void GoogleMobileAds.Common.IInterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x0000004B System.Void GoogleMobileAds.Common.IInterstitialClient::Show()
// 0x0000004C System.Void GoogleMobileAds.Common.IInterstitialClient::DestroyInterstitial()
// 0x0000004D System.Void GoogleMobileAds.Common.IMobileAdsClient::Initialize(System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>)
// 0x0000004E System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000004F System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000050 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000051 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000052 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x00000053 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x00000054 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x00000055 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x00000056 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000057 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000058 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000059 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000005A System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000005B System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000005C System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000005D System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000005E System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdClicked(System.Action)
// 0x0000005F System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdClicked(System.Action)
// 0x00000060 System.Void GoogleMobileAds.Common.IRewardedAdClient::CreateRewardedAd()
// 0x00000061 System.Void GoogleMobileAds.Common.IRewardedAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x00000062 System.Void GoogleMobileAds.Common.IRewardedAdClient::Show()
// 0x00000063 System.Void GoogleMobileAds.Common.IRewardedAdClient::DestroyRewardedAd()
// 0x00000064 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000065 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000066 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000067 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000068 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x00000069 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnPaidEvent(System.EventHandler`1<GoogleMobileAds.Api.AdValueEventArgs>)
// 0x0000006A System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x0000006B System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x0000006C System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x0000006D System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x0000006E System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000006F System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000070 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000071 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000072 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000073 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000074 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdClicked(System.Action)
// 0x00000075 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdClicked(System.Action)
// 0x00000076 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::CreateRewardedInterstitialAd()
// 0x00000077 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x00000078 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::Show()
// 0x00000079 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::DestroyRewardedInterstitialAd()
// 0x0000007A System.Void GoogleMobileAds.Common.LoadAdErrorClientEventArgs::.ctor()
extern void LoadAdErrorClientEventArgs__ctor_m9BCDD48E982A07F06D4B8FBCC6808D0625832BC1 (void);
// 0x0000007B GoogleMobileAds.Common.ILoadAdErrorClient GoogleMobileAds.Common.LoadAdErrorClientEventArgs::get_LoadAdErrorClient()
extern void LoadAdErrorClientEventArgs_get_LoadAdErrorClient_mABB3784FB4183AFDED61523A41539741345DC175 (void);
// 0x0000007C System.Void GoogleMobileAds.Common.LoadAdErrorClientEventArgs::set_LoadAdErrorClient(GoogleMobileAds.Common.ILoadAdErrorClient)
extern void LoadAdErrorClientEventArgs_set_LoadAdErrorClient_mEAF93733A05D1C6933761095E84EE86FD59FB13A (void);
// 0x0000007D System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::.ctor()
extern void MobileAdsEventExecutor__ctor_m4EAD1D50E75751067EEA49CF7F2E5D19746F620E (void);
// 0x0000007E System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Initialize()
extern void MobileAdsEventExecutor_Initialize_m64F3E072B9A45288A91DB5FDF080451D4ECAB771 (void);
// 0x0000007F System.Boolean GoogleMobileAds.Common.MobileAdsEventExecutor::IsActive()
extern void MobileAdsEventExecutor_IsActive_mD5DADA864C851318D7D891E44D2E72249EA01F30 (void);
// 0x00000080 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Awake()
extern void MobileAdsEventExecutor_Awake_mD1851C1ECFADC29165BDE7F0C342AF51C8706A3F (void);
// 0x00000081 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::ExecuteInUpdate(System.Action)
extern void MobileAdsEventExecutor_ExecuteInUpdate_mA2C686B95516CA97E84928B85599A3F45E6B5D03 (void);
// 0x00000082 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::InvokeInUpdate(UnityEngine.Events.UnityEvent)
extern void MobileAdsEventExecutor_InvokeInUpdate_mB8C2FC27B1D01C769BBDBE43F3D76F93CE52FB8F (void);
// 0x00000083 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Update()
extern void MobileAdsEventExecutor_Update_m795B12C70DB63AD41B86DA44280B1E9DC7F2A3D1 (void);
// 0x00000084 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::OnDisable()
extern void MobileAdsEventExecutor_OnDisable_mF579AF1538B669796A4DDCEA0EB3E9CFF56E2B2A (void);
// 0x00000085 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::.cctor()
extern void MobileAdsEventExecutor__cctor_m70FCF764EDFAED3A1800F0C032025508E552AF75 (void);
// 0x00000086 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor/<InvokeInUpdate>c__AnonStorey0::.ctor()
extern void U3CInvokeInUpdateU3Ec__AnonStorey0__ctor_m0397D0BED1610691EC23408DEA201EFE53C08870 (void);
// 0x00000087 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor/<InvokeInUpdate>c__AnonStorey0::<>m__0()
extern void U3CInvokeInUpdateU3Ec__AnonStorey0_U3CU3Em__0_mC2851336211B0BAD34ED01EADAB185B82B4F0306 (void);
static Il2CppMethodPointer s_methodPointers[135] = 
{
	AdErrorClientEventArgs__ctor_mF9539459BB3A7B964E940681D628BB03EA609353,
	AdErrorClientEventArgs_get_AdErrorClient_m5A802BFC28476C80B37BFCFC9613D33430BCC87A,
	AdErrorClientEventArgs_set_AdErrorClient_m8BE7E20C2CBCE3E110160A3315C67B0CB5B34752,
	AdInspectorErrorClientEventArgs__ctor_m18E7E5BACFE6BFF40F99775511C94D0E247EA188,
	AdInspectorErrorClientEventArgs_set_AdErrorClient_m1F4C50D1C3548A1844E63D273BBD765091CA9A9E,
	AppStateEventClient__ctor_m363112C60B6460D70B815F28976BEFDC02F116CC,
	AppStateEventClient_get_Instance_m8DA2FA97849BC1318E2F0B86A4A4959FB903AE0A,
	AppStateEventClient_add_AppStateChanged_m80083B55A920651778D9F90CC88667E8664FD468,
	AppStateEventClient_remove_AppStateChanged_mE5371835404054ADAFF0B502BE61FBA28F8C06EC,
	AppStateEventClient_OnApplicationPause_m3C5F0067EEEA87ABCAAF2AE8943025CA51AE5FCF,
	AppStateEventClient_U3CAppStateChangedU3Em__0_m448ED5CEE240E283ECABCF1BF57BBE4D5AB8FF1E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LoadAdErrorClientEventArgs__ctor_m9BCDD48E982A07F06D4B8FBCC6808D0625832BC1,
	LoadAdErrorClientEventArgs_get_LoadAdErrorClient_mABB3784FB4183AFDED61523A41539741345DC175,
	LoadAdErrorClientEventArgs_set_LoadAdErrorClient_mEAF93733A05D1C6933761095E84EE86FD59FB13A,
	MobileAdsEventExecutor__ctor_m4EAD1D50E75751067EEA49CF7F2E5D19746F620E,
	MobileAdsEventExecutor_Initialize_m64F3E072B9A45288A91DB5FDF080451D4ECAB771,
	MobileAdsEventExecutor_IsActive_mD5DADA864C851318D7D891E44D2E72249EA01F30,
	MobileAdsEventExecutor_Awake_mD1851C1ECFADC29165BDE7F0C342AF51C8706A3F,
	MobileAdsEventExecutor_ExecuteInUpdate_mA2C686B95516CA97E84928B85599A3F45E6B5D03,
	MobileAdsEventExecutor_InvokeInUpdate_mB8C2FC27B1D01C769BBDBE43F3D76F93CE52FB8F,
	MobileAdsEventExecutor_Update_m795B12C70DB63AD41B86DA44280B1E9DC7F2A3D1,
	MobileAdsEventExecutor_OnDisable_mF579AF1538B669796A4DDCEA0EB3E9CFF56E2B2A,
	MobileAdsEventExecutor__cctor_m70FCF764EDFAED3A1800F0C032025508E552AF75,
	U3CInvokeInUpdateU3Ec__AnonStorey0__ctor_m0397D0BED1610691EC23408DEA201EFE53C08870,
	U3CInvokeInUpdateU3Ec__AnonStorey0_U3CU3Em__0_mC2851336211B0BAD34ED01EADAB185B82B4F0306,
};
static const int32_t s_InvokerIndices[135] = 
{
	1935,
	1900,
	1610,
	1935,
	1610,
	1935,
	3215,
	1610,
	1610,
	1571,
	3185,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	572,
	1610,
	1935,
	1935,
	1935,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	934,
	1935,
	1935,
	1935,
	1900,
	1610,
	1935,
	3231,
	3203,
	1935,
	3189,
	3189,
	1935,
	1935,
	3231,
	1935,
	1935,
};
extern const CustomAttributesCacheGenerator g_GoogleMobileAds_Common_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Common_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Common_CodeGenModule = 
{
	"GoogleMobileAds.Common.dll",
	135,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_GoogleMobileAds_Common_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
