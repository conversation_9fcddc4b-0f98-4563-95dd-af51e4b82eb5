﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void System.Configuration.ConfigurationSection::DeserializeSection(System.Xml.XmlReader)
extern void ConfigurationSection_DeserializeSection_m1C615C8DFE790CC354FBBA7E24DC47D34FF35892 (void);
// 0x00000002 System.Boolean System.Configuration.ConfigurationSection::IsModified()
extern void ConfigurationSection_IsModified_mE1B598DDAA4CB726AA146CA26FF50B08264C99CA (void);
// 0x00000003 System.Void System.Configuration.ConfigurationSection::ResetModified()
extern void ConfigurationSection_ResetModified_m2E13CE65C1ACB85FAE901015FC7DF5ED93FC8BD9 (void);
// 0x00000004 System.String System.Configuration.ConfigurationSection::SerializeSection(System.Configuration.ConfigurationElement,System.String,System.Configuration.ConfigurationSaveMode)
extern void ConfigurationSection_SerializeSection_m02D8387BAC999E6DF264C895775FD5A2E20A78F2 (void);
// 0x00000005 System.Configuration.ConfigurationPropertyCollection System.Configuration.ConfigurationElement::get_Properties()
extern void ConfigurationElement_get_Properties_m9BEF5154370B1E154727190595DF0B7E68F4AA35 (void);
// 0x00000006 System.Boolean System.Configuration.ConfigurationElement::IsModified()
extern void ConfigurationElement_IsModified_mEBE1C1B7CBDE483BBD4D10CE356D5DAD5E91A760 (void);
// 0x00000007 System.Void System.Configuration.ConfigurationElement::Reset(System.Configuration.ConfigurationElement)
extern void ConfigurationElement_Reset_mBB5E07E68F8F5B8980BAD6FD48444FE0CB6CD852 (void);
// 0x00000008 System.Void System.Configuration.ConfigurationElement::ResetModified()
extern void ConfigurationElement_ResetModified_m5C8C779356B852E71503BC907E0A870988B13792 (void);
// 0x00000009 System.Void System.Configuration.ConfigurationCollectionAttribute::.ctor(System.Type)
extern void ConfigurationCollectionAttribute__ctor_m89928B3545B1827E694566EC696326B4A3F85206 (void);
// 0x0000000A System.Void System.Configuration.IgnoreSection::.ctor()
extern void IgnoreSection__ctor_m5D6F875ED441BB5BDBDC88367453018FD4ADAA21 (void);
// 0x0000000B System.Configuration.ConfigurationPropertyCollection System.Configuration.IgnoreSection::get_Properties()
extern void IgnoreSection_get_Properties_m9DEFC7F18A9DD901D65941DF65150798D4A4D613 (void);
// 0x0000000C System.Void System.Configuration.IgnoreSection::DeserializeSection(System.Xml.XmlReader)
extern void IgnoreSection_DeserializeSection_mD8F2A20AC1A2B9E594D17A81D19414AB36F74A35 (void);
// 0x0000000D System.Boolean System.Configuration.IgnoreSection::IsModified()
extern void IgnoreSection_IsModified_mECA1267F687512A088A94149235591453E6E37FC (void);
// 0x0000000E System.Void System.Configuration.IgnoreSection::Reset(System.Configuration.ConfigurationElement)
extern void IgnoreSection_Reset_m41C043D527066CDB0A8CE5659AE44979885F00B2 (void);
// 0x0000000F System.Void System.Configuration.IgnoreSection::ResetModified()
extern void IgnoreSection_ResetModified_m194F47B7E5C45CC2341BF2729411FA9E6B814C36 (void);
// 0x00000010 System.String System.Configuration.IgnoreSection::SerializeSection(System.Configuration.ConfigurationElement,System.String,System.Configuration.ConfigurationSaveMode)
extern void IgnoreSection_SerializeSection_mE2219D4373E65ADEFA760E951D87E8E151306E9E (void);
// 0x00000011 System.Void Unity.ThrowStub::ThrowNotSupportedException()
extern void ThrowStub_ThrowNotSupportedException_m7D3AEF39540E1D8CFA631552E9D0116434E7A9ED (void);
static Il2CppMethodPointer s_methodPointers[17] = 
{
	ConfigurationSection_DeserializeSection_m1C615C8DFE790CC354FBBA7E24DC47D34FF35892,
	ConfigurationSection_IsModified_mE1B598DDAA4CB726AA146CA26FF50B08264C99CA,
	ConfigurationSection_ResetModified_m2E13CE65C1ACB85FAE901015FC7DF5ED93FC8BD9,
	ConfigurationSection_SerializeSection_m02D8387BAC999E6DF264C895775FD5A2E20A78F2,
	ConfigurationElement_get_Properties_m9BEF5154370B1E154727190595DF0B7E68F4AA35,
	ConfigurationElement_IsModified_mEBE1C1B7CBDE483BBD4D10CE356D5DAD5E91A760,
	ConfigurationElement_Reset_mBB5E07E68F8F5B8980BAD6FD48444FE0CB6CD852,
	ConfigurationElement_ResetModified_m5C8C779356B852E71503BC907E0A870988B13792,
	ConfigurationCollectionAttribute__ctor_m89928B3545B1827E694566EC696326B4A3F85206,
	IgnoreSection__ctor_m5D6F875ED441BB5BDBDC88367453018FD4ADAA21,
	IgnoreSection_get_Properties_m9DEFC7F18A9DD901D65941DF65150798D4A4D613,
	IgnoreSection_DeserializeSection_mD8F2A20AC1A2B9E594D17A81D19414AB36F74A35,
	IgnoreSection_IsModified_mECA1267F687512A088A94149235591453E6E37FC,
	IgnoreSection_Reset_m41C043D527066CDB0A8CE5659AE44979885F00B2,
	IgnoreSection_ResetModified_m194F47B7E5C45CC2341BF2729411FA9E6B814C36,
	IgnoreSection_SerializeSection_mE2219D4373E65ADEFA760E951D87E8E151306E9E,
	ThrowStub_ThrowNotSupportedException_m7D3AEF39540E1D8CFA631552E9D0116434E7A9ED,
};
static const int32_t s_InvokerIndices[17] = 
{
	1610,
	1866,
	1935,
	489,
	1900,
	1866,
	1610,
	1935,
	1610,
	1935,
	1900,
	1610,
	1866,
	1610,
	1935,
	489,
	3231,
};
extern const CustomAttributesCacheGenerator g_System_Configuration_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Configuration_CodeGenModule;
const Il2CppCodeGenModule g_System_Configuration_CodeGenModule = 
{
	"System.Configuration.dll",
	17,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_System_Configuration_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
