using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class grasscutter : MonoBeh<PERSON>our
{
    public GameObject Spownprefab;
    
    [Header("Progress UI")]
    public Slider progressSlider;
    public Text progressText;
    public GameObject completionPanel;
    
    private float currentProgress = 0f;
    private float progressPerTrigger = 2f; // 2% per trigger

   void OnTriggerEnter(Collider other)
    {
        if(other.gameObject.tag == "Grass")
        {
          
            Vector3 spawnPosition = other.transform.position;
            
        
            other.gameObject.SetActive(false);
            
          
            if(Spownprefab != null)
            {
                Instantiate(Spownprefab, spawnPosition, Quaternion.identity);
            }
            
            // Update progress
            UpdateProgress();
        }
    }
    
    void UpdateProgress()
    {
        currentProgress += progressPerTrigger;
        
        // Clamp progress to 100%
        currentProgress = Mathf.Clamp(currentProgress, 0f, 100f);
        
        // Update slider (slider value is 0-1, so divide by 100)
        if(progressSlider != null)
        {
            progressSlider.value = currentProgress / 100f;
        }
        
        // Update text
        if(progressText != null)
        {
            progressText.text = currentProgress.ToString("F0") + "%";
        }
        
        // Check if completed
        if(currentProgress >= 100f)
        {
            ShowCompletionPanel();
        }
    }
    
    void ShowCompletionPanel()
    {
        if(completionPanel != null)
        {
            completionPanel.SetActive(true);
        }
    }
}
