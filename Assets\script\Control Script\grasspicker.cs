using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class grasspicker : MonoBehaviour
{
    public GameObject SpawnGrassBundle;
    public Slider progressSlider;
    public Text progressText;
    public GameObject completePanel;
    
    private int triggerCount = 0;
    private int triggersNeeded = 5;
    private static int bundleCount = 0;
    private int bundlesNeeded = 12;
    
    void Start()
    {
        UpdateUI();
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "CutGrass")
        {
            other.gameObject.SetActive(false);
            triggerCount++;
            
            // Check if we've reached 5 triggers
            if (triggerCount >= triggersNeeded)
            {
                // Spawn the grass bundle at this position
                if (SpawnGrassBundle != null)
                {
                    Instantiate(SpawnGrassBundle, transform.position, transform.rotation);
                    bundleCount++;
                    UpdateUI();
                    
                    // Check if we've completed 12 bundles
                    if (bundleCount >= bundlesNeeded)
                    {
                        CompleteTask();
                    }
                }
                
                // Reset the counter to repeat the process
                triggerCount = 0;
            }
        }
    }
    
    void UpdateUI()
    {
        if (progressSlider != null)
        {
            float progress = (float)bundleCount / bundlesNeeded;
            progressSlider.value = progress;
        }
        
        if (progressText != null)
        {
            float percentage = ((float)bundleCount / bundlesNeeded) * 100f;
            progressText.text = percentage.ToString("F0") + "%";
        }
    }
    
    void CompleteTask()
    {
        // Set slider to 100%
        if (progressSlider != null)
        {
            progressSlider.value = 1f;
        }
        
        // Set text to 100%
        if (progressText != null)
        {
            progressText.text = "100%";
        }
        
        // Activate complete panel
        if (completePanel != null)
        {
            completePanel.SetActive(true);
        }
        
        Debug.Log("Task Completed! 12 bundles collected.");
    }
}
