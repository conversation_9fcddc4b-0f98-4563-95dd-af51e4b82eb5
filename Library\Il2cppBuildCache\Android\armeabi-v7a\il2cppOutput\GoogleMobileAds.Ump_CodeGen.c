﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Ump.Api.ConsentDebugSettings::.ctor()
extern void ConsentDebugSettings__ctor_m7953C457CCE387F1AB1165EEB792209003F2F8EC (void);
// 0x00000002 System.Void GoogleMobileAds.Ump.Api.ConsentForm::.ctor(GoogleMobileAds.Ump.Common.IConsentFormClient)
extern void ConsentForm__ctor_m8F9A01A5586D4314EFC0BC6659A5C8CBEB90D862 (void);
// 0x00000003 System.Void GoogleMobileAds.Ump.Api.ConsentForm::Load(System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentForm_Load_mF6D739366833EE3109C1F264B4E5BF5F98016F8F (void);
// 0x00000004 System.Void GoogleMobileAds.Ump.Api.ConsentForm::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentForm_Show_m8351A9290BBA523E2A89AF47E172A06FF31D69D8 (void);
// 0x00000005 System.Void GoogleMobileAds.Ump.Api.ConsentForm::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentForm_LoadAndShowConsentFormIfRequired_mC795C84B7D7B008CDA73A16C75A5BEB29AE1A143 (void);
// 0x00000006 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m8E661B2BCDB91D83C55F8340F9752BBFFCDA1A6F (void);
// 0x00000007 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__0()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1CF0A2B6F36249D56CFC63DAB4D5BC4261418975 (void);
// 0x00000008 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__1(GoogleMobileAds.Ump.Api.FormError)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m444618FD30337AA9758A791CE803EE060E84C3BB (void);
// 0x00000009 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m11CB34935A9C66D1220FBF3B27563652742DF13C (void);
// 0x0000000A System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m63B0FCA7692DE144B9260C50C5E9E63B7FAE6FD2 (void);
// 0x0000000B System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mCD16116799A31497AD04EA3ADDF4C573655C0EF7 (void);
// 0x0000000C System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::.ctor()
extern void U3CShowU3Ec__AnonStorey2__ctor_m34D3325E5D9192F8253C4E21500582C7660A0575 (void);
// 0x0000000D System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::<>m__0(GoogleMobileAds.Ump.Api.FormError)
extern void U3CShowU3Ec__AnonStorey2_U3CU3Em__0_m38174121A671B27509DA35C2F861E9118EF44123 (void);
// 0x0000000E System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::.ctor()
extern void U3CShowU3Ec__AnonStorey3__ctor_m2300B413E984C95454C93C9F1B2F180B8A984095 (void);
// 0x0000000F System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::<>m__0()
extern void U3CShowU3Ec__AnonStorey3_U3CU3Em__0_m5C652CABA32FB41EC6CECC0503ABF7DE2AFB7FB2 (void);
// 0x00000010 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::.ctor()
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_m8AFD2FE260D4B698ED3579C9622B6FACD51FF590 (void);
// 0x00000011 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::<>m__0(GoogleMobileAds.Ump.Api.FormError)
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m4D8F5A1FC9EDEAF5EB5B25E1C2D14F6968A5FB87 (void);
// 0x00000012 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::.ctor()
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m93560390999C3D79C039637F816CC46AEC4ABC73 (void);
// 0x00000013 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::<>m__0()
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_m21DA3654C3294D3962D16163AD770CCBB4743C54 (void);
// 0x00000014 GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.ConsentInformation::get_ClientFactory()
extern void ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920 (void);
// 0x00000015 GoogleMobileAds.Ump.Api.ConsentStatus GoogleMobileAds.Ump.Api.ConsentInformation::get_ConsentStatus()
extern void ConsentInformation_get_ConsentStatus_mB55EE4B3C368D0CCB5675FBCF717EC96EEC9F55B (void);
// 0x00000016 System.Void GoogleMobileAds.Ump.Api.ConsentInformation::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentInformation_Update_m934E9851532297C0A2732A53C14DFDB87D1C38FD (void);
// 0x00000017 System.Boolean GoogleMobileAds.Ump.Api.ConsentInformation::CanRequestAds()
extern void ConsentInformation_CanRequestAds_mE4EBA2C5AE2C6A7AF24922540D4A481C28DFE330 (void);
// 0x00000018 System.Boolean GoogleMobileAds.Ump.Api.ConsentInformation::IsConsentFormAvailable()
extern void ConsentInformation_IsConsentFormAvailable_m4B479DA906396E823F8244247900192C7B53C8BA (void);
// 0x00000019 System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::.ctor()
extern void U3CUpdateU3Ec__AnonStorey0__ctor_mA550557A6E4952249FFC171CBF181BF8835915DD (void);
// 0x0000001A System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__0()
extern void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mE789E6055115C2D195F9FC002D46C1B957CCB1D8 (void);
// 0x0000001B System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__1(GoogleMobileAds.Ump.Api.FormError)
extern void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mA811CC59FFF687B19D9EF5F8EE15759726ECA7FF (void);
// 0x0000001C System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__2()
extern void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m528F4F63FA761B55DD63EC85CB14ECC6E507677D (void);
// 0x0000001D System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::.ctor()
extern void U3CUpdateU3Ec__AnonStorey1__ctor_m4D16F1DB530771F57AF4C88CFB5FCB51669044CE (void);
// 0x0000001E System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::<>m__0()
extern void U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_mAEEFDD424A26FA8B8C9722924CD4D8749D616627 (void);
// 0x0000001F System.Void GoogleMobileAds.Ump.Api.ConsentRequestParameters::.ctor()
extern void ConsentRequestParameters__ctor_mCE2AA9DE4F2C121A54076D50D1FF2699E9113E27 (void);
// 0x00000020 System.Void GoogleMobileAds.Ump.Api.FormError::.ctor(System.Int32,System.String)
extern void FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A (void);
// 0x00000021 System.Void GoogleMobileAds.Ump.Api.FormError::set_ErrorCode(System.Int32)
extern void FormError_set_ErrorCode_mAAA6A7B5ECDFE56E892C9D2DFF1ACC6E7D6DF039 (void);
// 0x00000022 System.Void GoogleMobileAds.Ump.Api.FormError::set_Message(System.String)
extern void FormError_set_Message_mBF54D641DB7C616DD97257F3CEE2E1C4FCD92029 (void);
// 0x00000023 GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.Utils::GetClientFactory()
extern void Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7 (void);
// 0x00000024 System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::Load(System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000025 System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000026 System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000027 System.Void GoogleMobileAds.Ump.Common.IConsentInformationClient::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000028 System.Int32 GoogleMobileAds.Ump.Common.IConsentInformationClient::GetConsentStatus()
// 0x00000029 System.Boolean GoogleMobileAds.Ump.Common.IConsentInformationClient::CanRequestAds()
// 0x0000002A System.Boolean GoogleMobileAds.Ump.Common.IConsentInformationClient::IsConsentFormAvailable()
// 0x0000002B GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentFormClient()
// 0x0000002C GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentInformationClient()
static Il2CppMethodPointer s_methodPointers[44] = 
{
	ConsentDebugSettings__ctor_m7953C457CCE387F1AB1165EEB792209003F2F8EC,
	ConsentForm__ctor_m8F9A01A5586D4314EFC0BC6659A5C8CBEB90D862,
	ConsentForm_Load_mF6D739366833EE3109C1F264B4E5BF5F98016F8F,
	ConsentForm_Show_m8351A9290BBA523E2A89AF47E172A06FF31D69D8,
	ConsentForm_LoadAndShowConsentFormIfRequired_mC795C84B7D7B008CDA73A16C75A5BEB29AE1A143,
	U3CLoadU3Ec__AnonStorey0__ctor_m8E661B2BCDB91D83C55F8340F9752BBFFCDA1A6F,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m1CF0A2B6F36249D56CFC63DAB4D5BC4261418975,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m444618FD30337AA9758A791CE803EE060E84C3BB,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m11CB34935A9C66D1220FBF3B27563652742DF13C,
	U3CLoadU3Ec__AnonStorey1__ctor_m63B0FCA7692DE144B9260C50C5E9E63B7FAE6FD2,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_mCD16116799A31497AD04EA3ADDF4C573655C0EF7,
	U3CShowU3Ec__AnonStorey2__ctor_m34D3325E5D9192F8253C4E21500582C7660A0575,
	U3CShowU3Ec__AnonStorey2_U3CU3Em__0_m38174121A671B27509DA35C2F861E9118EF44123,
	U3CShowU3Ec__AnonStorey3__ctor_m2300B413E984C95454C93C9F1B2F180B8A984095,
	U3CShowU3Ec__AnonStorey3_U3CU3Em__0_m5C652CABA32FB41EC6CECC0503ABF7DE2AFB7FB2,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_m8AFD2FE260D4B698ED3579C9622B6FACD51FF590,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m4D8F5A1FC9EDEAF5EB5B25E1C2D14F6968A5FB87,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m93560390999C3D79C039637F816CC46AEC4ABC73,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_m21DA3654C3294D3962D16163AD770CCBB4743C54,
	ConsentInformation_get_ClientFactory_m4E275915AA1D1D14F231DF3DC3BAFD0B5281B920,
	ConsentInformation_get_ConsentStatus_mB55EE4B3C368D0CCB5675FBCF717EC96EEC9F55B,
	ConsentInformation_Update_m934E9851532297C0A2732A53C14DFDB87D1C38FD,
	ConsentInformation_CanRequestAds_mE4EBA2C5AE2C6A7AF24922540D4A481C28DFE330,
	ConsentInformation_IsConsentFormAvailable_m4B479DA906396E823F8244247900192C7B53C8BA,
	U3CUpdateU3Ec__AnonStorey0__ctor_mA550557A6E4952249FFC171CBF181BF8835915DD,
	U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mE789E6055115C2D195F9FC002D46C1B957CCB1D8,
	U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mA811CC59FFF687B19D9EF5F8EE15759726ECA7FF,
	U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m528F4F63FA761B55DD63EC85CB14ECC6E507677D,
	U3CUpdateU3Ec__AnonStorey1__ctor_m4D16F1DB530771F57AF4C88CFB5FCB51669044CE,
	U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_mAEEFDD424A26FA8B8C9722924CD4D8749D616627,
	ConsentRequestParameters__ctor_mCE2AA9DE4F2C121A54076D50D1FF2699E9113E27,
	FormError__ctor_mD420241D469D1F34C35A13C37C9387918F545A9A,
	FormError_set_ErrorCode_mAAA6A7B5ECDFE56E892C9D2DFF1ACC6E7D6DF039,
	FormError_set_Message_mBF54D641DB7C616DD97257F3CEE2E1C4FCD92029,
	Utils_GetClientFactory_mAA17FE642295BE069DF98D13641659CC26C8A7D7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
static const int32_t s_InvokerIndices[44] = 
{
	1935,
	1610,
	3189,
	1610,
	3189,
	1935,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1935,
	1935,
	1610,
	1935,
	1935,
	3215,
	3209,
	2915,
	3203,
	3203,
	1935,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	880,
	1597,
	1610,
	3215,
	934,
	1610,
	1610,
	573,
	1886,
	1866,
	1866,
	1900,
	1900,
};
extern const CustomAttributesCacheGenerator g_GoogleMobileAds_Ump_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Ump_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Ump_CodeGenModule = 
{
	"GoogleMobileAds.Ump.dll",
	44,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_GoogleMobileAds_Ump_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
