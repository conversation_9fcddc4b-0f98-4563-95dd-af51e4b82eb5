﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.ctor()
extern void ConsentFormClient__ctor_m02CF8A6E9764BF469C72F734FD1D1D429EAAE39B (void);
// 0x00000002 GoogleMobileAds.Ump.Android.ConsentFormClient GoogleMobileAds.Ump.Android.ConsentFormClient::get_Instance()
extern void ConsentFormClient_get_Instance_mD32B42448DBC28EC81D836787244FEA50C081944 (void);
// 0x00000003 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::Load(System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_Load_mC9B97E18982519C56B00BB6315795C32792E106B (void);
// 0x00000004 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_Show_m97B631BFEEF9B147D63B37845DCA575CA74D1BAD (void);
// 0x00000005 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_LoadAndShowConsentFormIfRequired_m4409238478D27134BBD0683FFEAF69483B723A56 (void);
// 0x00000006 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::ShowPrivacyOptionsForm(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_ShowPrivacyOptionsForm_m4B58A41B3EC1B1A210E25624A1D883EB0F979AFF (void);
// 0x00000007 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::onConsentFormDismissed(UnityEngine.AndroidJavaObject)
extern void ConsentFormClient_onConsentFormDismissed_m135CB7979C20ADBB70E82CC9173DBB3A6C2764DD (void);
// 0x00000008 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.cctor()
extern void ConsentFormClient__cctor_mA8BDAAFB528B71A411FCABF5A4638BCE51D93C7B (void);
// 0x00000009 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<Show>m__0()
extern void ConsentFormClient_U3CShowU3Em__0_m21708BA8AE24D8FA97F7B8BE9A0EF4A2F02D606D (void);
// 0x0000000A System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<LoadAndShowConsentFormIfRequired>m__1()
extern void ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_mDD267BEE3AD41D6B78540E8E2C2C95C84BA3E367 (void);
// 0x0000000B System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<ShowPrivacyOptionsForm>m__2()
extern void ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m1569F1485B135B4E9B7466B4A95A10A35FE1A703 (void);
// 0x0000000C System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m2481275D945C664C823EBE378B801BD39050CA8D (void);
// 0x0000000D System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::<>m__0(UnityEngine.AndroidJavaObject)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC644795AFB1E0FCDD4A4D5FE30D4EE0F3EBDA497 (void);
// 0x0000000E System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::<>m__1()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m83019FB280AA8BBBF7202F1110851479163653FE (void);
// 0x0000000F System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.ctor()
extern void ConsentInformationClient__ctor_m77D7E2D66558FBB1A82C44FB7534567AB1930B18 (void);
// 0x00000010 GoogleMobileAds.Ump.Android.ConsentInformationClient GoogleMobileAds.Ump.Android.ConsentInformationClient::get_Instance()
extern void ConsentInformationClient_get_Instance_m2628C7372920D3887990D2F1949C5A3925253907 (void);
// 0x00000011 System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentInformationClient_Update_m2CC76384A9425E837F75F9FAB641183078A33F27 (void);
// 0x00000012 System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::Reset()
extern void ConsentInformationClient_Reset_m40DD537DE51F4E3E67FF90598C8760C730480D61 (void);
// 0x00000013 System.Int32 GoogleMobileAds.Ump.Android.ConsentInformationClient::GetConsentStatus()
extern void ConsentInformationClient_GetConsentStatus_mD6C92990E65197BFCAE0F13614F2718B242D9446 (void);
// 0x00000014 System.Int32 GoogleMobileAds.Ump.Android.ConsentInformationClient::GetPrivacyOptionsRequirementStatus()
extern void ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m3C8615C525A07D949F95EFD3AF4A0C5D82DCDC33 (void);
// 0x00000015 System.Boolean GoogleMobileAds.Ump.Android.ConsentInformationClient::CanRequestAds()
extern void ConsentInformationClient_CanRequestAds_mCAABCD8029F40C78E0167549811E25B623056757 (void);
// 0x00000016 System.Boolean GoogleMobileAds.Ump.Android.ConsentInformationClient::IsConsentFormAvailable()
extern void ConsentInformationClient_IsConsentFormAvailable_m5B6E6AACDF70DC9925C9AF4ED8C2C7F9281C4D56 (void);
// 0x00000017 System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.cctor()
extern void ConsentInformationClient__cctor_m66D1DD7B407E76FC4D3107954F8AF569FFE76449 (void);
// 0x00000018 System.Void GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void OnConsentFormDismissedListener__ctor_m2BF723DA9FB1F5BA2B7A97299DA7541A12241569 (void);
// 0x00000019 System.Void GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::onConsentFormDismissed(UnityEngine.AndroidJavaObject)
extern void OnConsentFormDismissedListener_onConsentFormDismissed_m5FBDB7737D17192545F42DF4C82DBF227C89C8F5 (void);
// 0x0000001A System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void OnConsentFormLoadFailureListener__ctor_mE6F632602335283E28633423C49A21306A6051AB (void);
// 0x0000001B System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::onConsentFormLoadFailure(UnityEngine.AndroidJavaObject)
extern void OnConsentFormLoadFailureListener_onConsentFormLoadFailure_m998D74F6B7EED655F50D7F850FC35F29985088FE (void);
// 0x0000001C System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::.ctor(System.Action`1<UnityEngine.AndroidJavaObject>)
extern void OnConsentFormLoadSuccessListener__ctor_m7D259C6D3148F10E17344FFB032D87D6144F6806 (void);
// 0x0000001D System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::onConsentFormLoadSuccess(UnityEngine.AndroidJavaObject)
extern void OnConsentFormLoadSuccessListener_onConsentFormLoadSuccess_m22A2098FBAF898A33C3490EB6D8CFD3362FF0A38 (void);
// 0x0000001E System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void OnConsentInfoUpdateFailureListener__ctor_mAA13AA008BA8709FC4035B6F0ECA7CFEEA1CC370 (void);
// 0x0000001F System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::onConsentInfoUpdateFailure(UnityEngine.AndroidJavaObject)
extern void OnConsentInfoUpdateFailureListener_onConsentInfoUpdateFailure_m9D2A831919007892CBDFD2874EE4CDAA8F73C6E7 (void);
// 0x00000020 System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::.ctor(System.Action)
extern void OnConsentInfoUpdateSuccessListener__ctor_mB868212ADBE76EA0C1807201067C5801C333A44A (void);
// 0x00000021 System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::onConsentInfoUpdateSuccess()
extern void OnConsentInfoUpdateSuccessListener_onConsentInfoUpdateSuccess_m0A18C7C32C90ED49832203DA16A41A6C6E455280 (void);
// 0x00000022 System.Void GoogleMobileAds.Ump.Android.UmpClientFactory::.ctor()
extern void UmpClientFactory__ctor_m2A940E2F9765368A7373758FA7D985882DA3FEC8 (void);
// 0x00000023 GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Android.UmpClientFactory::ConsentFormClient()
extern void UmpClientFactory_ConsentFormClient_m3AEC3CD62E7D9DF830B4ABEB6B5A63F772CA03C7 (void);
// 0x00000024 GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Android.UmpClientFactory::ConsentInformationClient()
extern void UmpClientFactory_ConsentInformationClient_mCB353E67E65957F579BF7896716EB47A99AC77F4 (void);
// 0x00000025 System.Void GoogleMobileAds.Ump.Android.Utils::.ctor()
extern void Utils__ctor_m0058B11D96582C27E72A591B0D10180754248E0A (void);
// 0x00000026 UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.Utils::GetConsentRequestParametersJavaObject(GoogleMobileAds.Ump.Api.ConsentRequestParameters,UnityEngine.AndroidJavaObject)
extern void Utils_GetConsentRequestParametersJavaObject_m9EACC27838E61BF36A6BD835CDB189B3BA0CEA4B (void);
static Il2CppMethodPointer s_methodPointers[38] = 
{
	ConsentFormClient__ctor_m02CF8A6E9764BF469C72F734FD1D1D429EAAE39B,
	ConsentFormClient_get_Instance_mD32B42448DBC28EC81D836787244FEA50C081944,
	ConsentFormClient_Load_mC9B97E18982519C56B00BB6315795C32792E106B,
	ConsentFormClient_Show_m97B631BFEEF9B147D63B37845DCA575CA74D1BAD,
	ConsentFormClient_LoadAndShowConsentFormIfRequired_m4409238478D27134BBD0683FFEAF69483B723A56,
	ConsentFormClient_ShowPrivacyOptionsForm_m4B58A41B3EC1B1A210E25624A1D883EB0F979AFF,
	ConsentFormClient_onConsentFormDismissed_m135CB7979C20ADBB70E82CC9173DBB3A6C2764DD,
	ConsentFormClient__cctor_mA8BDAAFB528B71A411FCABF5A4638BCE51D93C7B,
	ConsentFormClient_U3CShowU3Em__0_m21708BA8AE24D8FA97F7B8BE9A0EF4A2F02D606D,
	ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_mDD267BEE3AD41D6B78540E8E2C2C95C84BA3E367,
	ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m1569F1485B135B4E9B7466B4A95A10A35FE1A703,
	U3CLoadU3Ec__AnonStorey0__ctor_m2481275D945C664C823EBE378B801BD39050CA8D,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mC644795AFB1E0FCDD4A4D5FE30D4EE0F3EBDA497,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m83019FB280AA8BBBF7202F1110851479163653FE,
	ConsentInformationClient__ctor_m77D7E2D66558FBB1A82C44FB7534567AB1930B18,
	ConsentInformationClient_get_Instance_m2628C7372920D3887990D2F1949C5A3925253907,
	ConsentInformationClient_Update_m2CC76384A9425E837F75F9FAB641183078A33F27,
	ConsentInformationClient_Reset_m40DD537DE51F4E3E67FF90598C8760C730480D61,
	ConsentInformationClient_GetConsentStatus_mD6C92990E65197BFCAE0F13614F2718B242D9446,
	ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m3C8615C525A07D949F95EFD3AF4A0C5D82DCDC33,
	ConsentInformationClient_CanRequestAds_mCAABCD8029F40C78E0167549811E25B623056757,
	ConsentInformationClient_IsConsentFormAvailable_m5B6E6AACDF70DC9925C9AF4ED8C2C7F9281C4D56,
	ConsentInformationClient__cctor_m66D1DD7B407E76FC4D3107954F8AF569FFE76449,
	OnConsentFormDismissedListener__ctor_m2BF723DA9FB1F5BA2B7A97299DA7541A12241569,
	OnConsentFormDismissedListener_onConsentFormDismissed_m5FBDB7737D17192545F42DF4C82DBF227C89C8F5,
	OnConsentFormLoadFailureListener__ctor_mE6F632602335283E28633423C49A21306A6051AB,
	OnConsentFormLoadFailureListener_onConsentFormLoadFailure_m998D74F6B7EED655F50D7F850FC35F29985088FE,
	OnConsentFormLoadSuccessListener__ctor_m7D259C6D3148F10E17344FFB032D87D6144F6806,
	OnConsentFormLoadSuccessListener_onConsentFormLoadSuccess_m22A2098FBAF898A33C3490EB6D8CFD3362FF0A38,
	OnConsentInfoUpdateFailureListener__ctor_mAA13AA008BA8709FC4035B6F0ECA7CFEEA1CC370,
	OnConsentInfoUpdateFailureListener_onConsentInfoUpdateFailure_m9D2A831919007892CBDFD2874EE4CDAA8F73C6E7,
	OnConsentInfoUpdateSuccessListener__ctor_mB868212ADBE76EA0C1807201067C5801C333A44A,
	OnConsentInfoUpdateSuccessListener_onConsentInfoUpdateSuccess_m0A18C7C32C90ED49832203DA16A41A6C6E455280,
	UmpClientFactory__ctor_m2A940E2F9765368A7373758FA7D985882DA3FEC8,
	UmpClientFactory_ConsentFormClient_m3AEC3CD62E7D9DF830B4ABEB6B5A63F772CA03C7,
	UmpClientFactory_ConsentInformationClient_mCB353E67E65957F579BF7896716EB47A99AC77F4,
	Utils__ctor_m0058B11D96582C27E72A591B0D10180754248E0A,
	Utils_GetConsentRequestParametersJavaObject_m9EACC27838E61BF36A6BD835CDB189B3BA0CEA4B,
};
static const int32_t s_InvokerIndices[38] = 
{
	1935,
	3215,
	934,
	1610,
	1610,
	1610,
	1610,
	3231,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1935,
	3215,
	573,
	1935,
	1886,
	1886,
	1866,
	1866,
	3231,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	1935,
	1900,
	1900,
	1935,
	2795,
};
extern const CustomAttributesCacheGenerator g_GoogleMobileAds_Ump_Android_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Ump_Android_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Ump_Android_CodeGenModule = 
{
	"GoogleMobileAds.Ump.Android.dll",
	38,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_GoogleMobileAds_Ump_Android_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
