﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void SciFiBeamScript::Start()
extern void SciFiBeamScript_Start_m670D10209F881EA1FA95473F38599CA57E5F8B42 (void);
// 0x00000002 System.Void SciFiBeamScript::Update()
extern void SciFiBeamScript_Update_m0639C5CBDCA0D5ED211C73B4765BE79A358DCF66 (void);
// 0x00000003 System.Void SciFiBeamScript::nextBeam()
extern void SciFiBeamScript_nextBeam_m886ACE8FF1FA35D1E81EFDCE6394BD5E63739F70 (void);
// 0x00000004 System.Void SciFiBeamScript::previousBeam()
extern void SciFiBeamScript_previousBeam_mE13F29D12E1ACC902B236F937B315F2C5368E85A (void);
// 0x00000005 System.Void SciFiBeamScript::UpdateEndOffset()
extern void SciFiBeamScript_UpdateEndOffset_m56BEADDCA773730BC3C1465D6C7627F75BCC6255 (void);
// 0x00000006 System.Void SciFiBeamScript::UpdateScrollSpeed()
extern void SciFiBeamScript_UpdateScrollSpeed_m5553A758C651F353B0EC04F28CDF86CEAE674AC7 (void);
// 0x00000007 System.Void SciFiBeamScript::ShootBeamInDir(UnityEngine.Vector3,UnityEngine.Vector3)
extern void SciFiBeamScript_ShootBeamInDir_m493F228B41452BC0C98AD08DED9960DEF96E7D32 (void);
// 0x00000008 System.Void SciFiBeamScript::.ctor()
extern void SciFiBeamScript__ctor_mCDA142C63DC33AD973FA8254D083404DAC90D917 (void);
// 0x00000009 System.Void SciFiLightFlicker::Start()
extern void SciFiLightFlicker_Start_mFA08B8D196F758A37ADC7B270F9FFBDD0A6CE18C (void);
// 0x0000000A System.Void SciFiLightFlicker::Update()
extern void SciFiLightFlicker_Update_m3E217B9164B415B2181B36B78B1277B825D807F9 (void);
// 0x0000000B System.Single SciFiLightFlicker::EvalWave()
extern void SciFiLightFlicker_EvalWave_m6904FDCF56316DF952FB4A332AABA6A9945068AA (void);
// 0x0000000C System.Void SciFiLightFlicker::.ctor()
extern void SciFiLightFlicker__ctor_m96F1D9B988BF135C27544FB279DB48C9AA7A9705 (void);
// 0x0000000D System.Void AdsManager::Awake()
extern void AdsManager_Awake_m643441EC8FDE1A0243445E78E206F17CCA9C2A8D (void);
// 0x0000000E System.Void AdsManager::InitializeAdmob()
extern void AdsManager_InitializeAdmob_mEB4E7AE9BD6AE2A340A399028B52C7D4909AFFC3 (void);
// 0x0000000F System.Void AdsManager::OnInitializationComp()
extern void AdsManager_OnInitializationComp_mB6838C29E6A0E6D66DBF0719D8478BAA0FD06F1B (void);
// 0x00000010 System.Void AdsManager::OnApplicationPause(System.Boolean)
extern void AdsManager_OnApplicationPause_mF959291217B4D051FFD7B85E5AE48AD9C1EF61E7 (void);
// 0x00000011 System.Void AdsManager::DelayInAppOpen()
extern void AdsManager_DelayInAppOpen_mB50D270993AA9ADE9727CAFB4413970C01E6AA54 (void);
// 0x00000012 System.Void AdsManager::checkForAdIds()
extern void AdsManager_checkForAdIds_mBF6A2BACC70A056A611F87F43E56AA5ED4BF2C75 (void);
// 0x00000013 GoogleMobileAds.Api.AdRequest AdsManager::CreateAdRequest()
extern void AdsManager_CreateAdRequest_m29A2B3664ED7BD0FCF2039E56393928721707486 (void);
// 0x00000014 GoogleMobileAds.Api.BannerView AdsManager::ShowAdmobBannerCollapsible(GoogleMobileAds.Api.BannerView,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void AdsManager_ShowAdmobBannerCollapsible_m07F29E5ADB0BF2A41F3EC330B1489B4B8686ECC0 (void);
// 0x00000015 GoogleMobileAds.Api.BannerView AdsManager::showAdmobBanner(GoogleMobileAds.Api.BannerView,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void AdsManager_showAdmobBanner_mAC8AFC4F68852F1C99CEC723AD6118C58DCE0880 (void);
// 0x00000016 System.Void AdsManager::changeBannerBools(GoogleMobileAds.Api.BannerView,System.Boolean)
extern void AdsManager_changeBannerBools_mC1914C3CF9D7EB0E821A7CE3D9F285A95F62D7DB (void);
// 0x00000017 System.Void AdsManager::ListenToAdEvents(GoogleMobileAds.Api.BannerView)
extern void AdsManager_ListenToAdEvents_mD16614710782E68296FCCC2A8E230872CA1061BD (void);
// 0x00000018 System.Void AdsManager::hideAdMobBanner(GoogleMobileAds.Api.BannerView)
extern void AdsManager_hideAdMobBanner_m1ABD2CF9B363C03CF13074CE2A09E71DDD46F098 (void);
// 0x00000019 System.Void AdsManager::showAdMobBannerTop()
extern void AdsManager_showAdMobBannerTop_m0083564C06FDC248192D4698DAFC2BC9500A7251 (void);
// 0x0000001A System.Void AdsManager::showAdMobBannerBottom()
extern void AdsManager_showAdMobBannerBottom_mE843E7CA754A0ADB907FEF1B260235824FCBA1E5 (void);
// 0x0000001B System.Void AdsManager::showAdMobBannerTopLeft()
extern void AdsManager_showAdMobBannerTopLeft_mAE2691DA490B6DCFD99552689FB03C1CA4B959B1 (void);
// 0x0000001C System.Void AdsManager::showAdMobBannerTopRight()
extern void AdsManager_showAdMobBannerTopRight_m7B91DDB4C55762536FFCC60C3F61649480E5D623 (void);
// 0x0000001D System.Void AdsManager::showAdMobBannerBottomLeft()
extern void AdsManager_showAdMobBannerBottomLeft_mC7E18E771D9BE6CE036555D9930B5A4770714AB3 (void);
// 0x0000001E System.Void AdsManager::showAdMobBannerBottomRight()
extern void AdsManager_showAdMobBannerBottomRight_mB9AA02C345C35D2127B4B9C21D4A378830744FCF (void);
// 0x0000001F System.Void AdsManager::showAdMobBannerCenter()
extern void AdsManager_showAdMobBannerCenter_mD8383F99BB0781FF5201619CB54986CD41DF7204 (void);
// 0x00000020 System.Void AdsManager::showAdMobRectangleBannerTop()
extern void AdsManager_showAdMobRectangleBannerTop_m5DEAC503A1B25AD4C8596B22E3AE17D7AC056CA2 (void);
// 0x00000021 System.Void AdsManager::showAdMobRectangleBannerBottom()
extern void AdsManager_showAdMobRectangleBannerBottom_mFC104D12F36458B8C0AD5ED9D555B3C1B5399141 (void);
// 0x00000022 System.Void AdsManager::showAdMobRectangleBannerTopLeft()
extern void AdsManager_showAdMobRectangleBannerTopLeft_mD1690EC887B9A9B8025EC4280154C06909C0ABB1 (void);
// 0x00000023 System.Void AdsManager::showAdMobRectangleBannerTopRight()
extern void AdsManager_showAdMobRectangleBannerTopRight_m61395B91B75BB4CADE95E0181569FB8C04B8E5FE (void);
// 0x00000024 System.Void AdsManager::showAdMobRectangleBannerBottomLeft()
extern void AdsManager_showAdMobRectangleBannerBottomLeft_m936CCC705691EC742E3335AFF51021AECE918C80 (void);
// 0x00000025 System.Void AdsManager::showAdMobRectangleBannerBottomRight()
extern void AdsManager_showAdMobRectangleBannerBottomRight_mBB51A3719CFBF6C11C327DBFD70961E85BE01764 (void);
// 0x00000026 System.Void AdsManager::showAdMobRectangleBannerCenter()
extern void AdsManager_showAdMobRectangleBannerCenter_m045D6E6E3AF9A947753E22D9A15A7CA7642EF751 (void);
// 0x00000027 System.Void AdsManager::showAdmobAdpativeBannerTop()
extern void AdsManager_showAdmobAdpativeBannerTop_m30D5218CF89802F24AC89EC830ABACC2892DCF88 (void);
// 0x00000028 System.Void AdsManager::showAdmobAdpativeBannerBottom()
extern void AdsManager_showAdmobAdpativeBannerBottom_m366D8F9839C8830AD9AA0082B801DA324493622B (void);
// 0x00000029 System.Void AdsManager::showAdmobAdpativeBannerCenter()
extern void AdsManager_showAdmobAdpativeBannerCenter_mF06B3F4EFB1E90895A27154592E7D1C50ACE0156 (void);
// 0x0000002A System.Void AdsManager::showAdmobAdpativeBannerTopCustomWidth()
extern void AdsManager_showAdmobAdpativeBannerTopCustomWidth_mA717C9203328D306FF40BF578D620F163D392AC0 (void);
// 0x0000002B System.Void AdsManager::showAdmobAdpativeBannerTopLeftCustomWidth()
extern void AdsManager_showAdmobAdpativeBannerTopLeftCustomWidth_mEDE6DC227916BEE345C524BFB7256365DFE14D1C (void);
// 0x0000002C System.Void AdsManager::showAdmobAdpativeBannerTopRightCustomWidth()
extern void AdsManager_showAdmobAdpativeBannerTopRightCustomWidth_m3CDD65661FEB728602EE9B1E29B658ED52546800 (void);
// 0x0000002D System.Void AdsManager::showAdmobAdpativeBannerBottomLeftCustomWidth()
extern void AdsManager_showAdmobAdpativeBannerBottomLeftCustomWidth_m64395DC89D88AFB2616A65D879FAA2BAEF41CBDE (void);
// 0x0000002E System.Void AdsManager::showAdmobAdpativeBannerBottomRightCustomWidth()
extern void AdsManager_showAdmobAdpativeBannerBottomRightCustomWidth_m9F90ACEBB6A79561B81C8CD4AC38C0B2BB7417A4 (void);
// 0x0000002F System.Void AdsManager::showAdmobAdpativeBannerBottomCustomWidth()
extern void AdsManager_showAdmobAdpativeBannerBottomCustomWidth_m5B4CFFE08558AC832C4657471909710260B99BE7 (void);
// 0x00000030 System.Void AdsManager::showAdmobSmartBannerTop()
extern void AdsManager_showAdmobSmartBannerTop_m5EA7944A1B08C7B05B807D1E37390D0440F4A857 (void);
// 0x00000031 System.Void AdsManager::showAdmobSmartBannerBottom()
extern void AdsManager_showAdmobSmartBannerBottom_m07F55B144A5712AC8CFBCB767EC8B0155486C5CC (void);
// 0x00000032 System.Void AdsManager::hideAllAdmobBanners()
extern void AdsManager_hideAllAdmobBanners_mAC5079836EE333CFFB4EEA6BD45A1D352AFD62B2 (void);
// 0x00000033 System.Void AdsManager::hideAdmobTopBanner()
extern void AdsManager_hideAdmobTopBanner_m0837854BAE6595104B2A46197658964797373719 (void);
// 0x00000034 System.Void AdsManager::hideAdmobBottomBanner()
extern void AdsManager_hideAdmobBottomBanner_m5E2382AD6B8C3B456D5BDA2A8EB6D9D9379B312B (void);
// 0x00000035 System.Void AdsManager::hideAdmobTopLeftBanner()
extern void AdsManager_hideAdmobTopLeftBanner_mF8D2B8C63D7F82E17CB94E1FF3C7FC8EF6E35100 (void);
// 0x00000036 System.Void AdsManager::hideAdmobTopRightBanner()
extern void AdsManager_hideAdmobTopRightBanner_mF41143348E0662EB63527FE7ECFA42A7CDBA075C (void);
// 0x00000037 System.Void AdsManager::hideAdmobBottomLeftBanner()
extern void AdsManager_hideAdmobBottomLeftBanner_mF46371587842FE2FA7DC505CF031415D7ED49EC5 (void);
// 0x00000038 System.Void AdsManager::hideAdmobBottomRightBanner()
extern void AdsManager_hideAdmobBottomRightBanner_mAE508D1C870338166FFB51EEAA0CE95019061F1F (void);
// 0x00000039 System.Void AdsManager::hideAdmobCenterBanner()
extern void AdsManager_hideAdmobCenterBanner_mEE5CB0E98F7C6A74AC20AB56436D089A26F2D8A0 (void);
// 0x0000003A System.Void AdsManager::tryToLoadAllInterstitials()
extern void AdsManager_tryToLoadAllInterstitials_mFB94E2DEADEC3C1FA3FA6F6998D12769E79AE306 (void);
// 0x0000003B System.Void AdsManager::loadInterstitialAdCustom0(System.String)
extern void AdsManager_loadInterstitialAdCustom0_mE991818F64F3E6476D499F569331A5C73539C020 (void);
// 0x0000003C System.Void AdsManager::loadInterstitialAdCustom1(System.String)
extern void AdsManager_loadInterstitialAdCustom1_mEF2158FF6369947070B95216C098A34B63EC09D0 (void);
// 0x0000003D System.Void AdsManager::loadInterstitialAdCustom2(System.String)
extern void AdsManager_loadInterstitialAdCustom2_m7CAAA431F71C48697A1AF5DD7FD995B7CF770D56 (void);
// 0x0000003E System.Void AdsManager::loadInterstitialAdCustom3(System.String)
extern void AdsManager_loadInterstitialAdCustom3_m5865C98F12B3397E39672B6AFB2797AFF6827AFA (void);
// 0x0000003F System.Void AdsManager::loadInterstitialAdCustom4(System.String)
extern void AdsManager_loadInterstitialAdCustom4_m75EDFA9E35F54A639279A1AE2FD477230F347548 (void);
// 0x00000040 System.Void AdsManager::loadAdMobInterstital0()
extern void AdsManager_loadAdMobInterstital0_m70EFF50BC10509477774B4798CFCD1CB30A80332 (void);
// 0x00000041 System.Void AdsManager::loadAdMobInterstital1()
extern void AdsManager_loadAdMobInterstital1_m026239F8C45EB46A94E087B338A33F3979D522B5 (void);
// 0x00000042 System.Void AdsManager::loadAdMobInterstital2()
extern void AdsManager_loadAdMobInterstital2_m53481517DF44CAE9841886AF321C8202F773417B (void);
// 0x00000043 System.Void AdsManager::loadAdMobInterstital3()
extern void AdsManager_loadAdMobInterstital3_mF848275FB0871A56378691A2E942E58777A9F065 (void);
// 0x00000044 System.Void AdsManager::loadAdMobInterstital4()
extern void AdsManager_loadAdMobInterstital4_m5F57210B7EE953E074EA51E47C9FF81051407F98 (void);
// 0x00000045 System.Void AdsManager::RegisterInterEvents0(GoogleMobileAds.Api.InterstitialAd)
extern void AdsManager_RegisterInterEvents0_m1FBD4E0DAAFB4E270228007443A075A66F67F34A (void);
// 0x00000046 System.Void AdsManager::RegisterInterEvents1(GoogleMobileAds.Api.InterstitialAd)
extern void AdsManager_RegisterInterEvents1_m36998DE8C0FF0800E14C8E9989D3FB692F594C29 (void);
// 0x00000047 System.Void AdsManager::RegisterInterEvents2(GoogleMobileAds.Api.InterstitialAd)
extern void AdsManager_RegisterInterEvents2_m4CE063AF4F1D1076FA5B23371F7E2793A004387C (void);
// 0x00000048 System.Void AdsManager::RegisterInterEvents3(GoogleMobileAds.Api.InterstitialAd)
extern void AdsManager_RegisterInterEvents3_m4738D13BBE6CA48C260D610130F9EE7DE4B28DE0 (void);
// 0x00000049 System.Void AdsManager::RegisterInterEvents4(GoogleMobileAds.Api.InterstitialAd)
extern void AdsManager_RegisterInterEvents4_m64E0E0502F815AF227069F62A675CE89EE6304EA (void);
// 0x0000004A System.Void AdsManager::showAdmobInterstitial()
extern void AdsManager_showAdmobInterstitial_m2780E9C84639C368C943D76BCAB46AE998E6DDF2 (void);
// 0x0000004B System.Boolean AdsManager::showAdmobInter0()
extern void AdsManager_showAdmobInter0_m8AB83B7D31E883D2F20D797C6D92D06C0D7C4561 (void);
// 0x0000004C System.Boolean AdsManager::showAdmobInter1()
extern void AdsManager_showAdmobInter1_m408F8655BFB864722F73CC283C0D352189C36ACE (void);
// 0x0000004D System.Boolean AdsManager::showAdmobInter2()
extern void AdsManager_showAdmobInter2_m87D802F9F8E68FDDD3B6DBB067367CACD4FD9E5B (void);
// 0x0000004E System.Boolean AdsManager::showAdmobInter3()
extern void AdsManager_showAdmobInter3_mF8C8C9FAA4B782975D85454CA1DAC6F011C180F4 (void);
// 0x0000004F System.Boolean AdsManager::showAdmobInter4()
extern void AdsManager_showAdmobInter4_mC6BAAD8BEDF09545603C5E90367BDCF3D13C9C60 (void);
// 0x00000050 System.Void AdsManager::loadAdmobRewardedInterstitial()
extern void AdsManager_loadAdmobRewardedInterstitial_m88A1BFF464E795E027501E1A032213F59E2BC4EA (void);
// 0x00000051 System.Void AdsManager::RegisterRewardedInterEventHandlers(GoogleMobileAds.Api.RewardedInterstitialAd)
extern void AdsManager_RegisterRewardedInterEventHandlers_m36C0D9C583F79C3FD77238D1F796CC999108AE06 (void);
// 0x00000052 System.Boolean AdsManager::checkIfAdmobRewardedInterstitialIsLoaded()
extern void AdsManager_checkIfAdmobRewardedInterstitialIsLoaded_m99A8D5233912C18A1B0874B360E97545A09851E6 (void);
// 0x00000053 System.Void AdsManager::ShowAdmobRewardedInterstitial()
extern void AdsManager_ShowAdmobRewardedInterstitial_m20FD5DB10520931BE57A7F07C8F9448527C6AB90 (void);
// 0x00000054 System.Boolean AdsManager::get_IsAdAvailable()
extern void AdsManager_get_IsAdAvailable_m1E84A19C513E64DBADBC1D84F2DDECABC68FDBAC (void);
// 0x00000055 System.Void AdsManager::LoadAppOpenAd()
extern void AdsManager_LoadAppOpenAd_mAC8258D9523A204DD5C4A2488CBD7F2E0E636D52 (void);
// 0x00000056 System.Void AdsManager::RegisterAppOpenEventHandlers(GoogleMobileAds.Api.AppOpenAd)
extern void AdsManager_RegisterAppOpenEventHandlers_mCA117C699C39FEC371DF9FF3240A5196CE297716 (void);
// 0x00000057 System.Void AdsManager::ShowAdmobAppOpenAd()
extern void AdsManager_ShowAdmobAppOpenAd_m249F6D941DF151ED993C831C1E38E270C315ADA7 (void);
// 0x00000058 System.Void AdsManager::HideBannersForAppOpenAd()
extern void AdsManager_HideBannersForAppOpenAd_mB6207CF9FD0AA2C8305DD390243B42524C4A45A6 (void);
// 0x00000059 System.Void AdsManager::ShowBannersForAppOpenAd()
extern void AdsManager_ShowBannersForAppOpenAd_mEE99F7574E93895E8E21E549EF8031BF95C21AF0 (void);
// 0x0000005A System.Void AdsManager::LoadVideoAD()
extern void AdsManager_LoadVideoAD_m93C3BBE2975448AC7DF253671A69D34AD7C3AF70 (void);
// 0x0000005B System.Void AdsManager::RegisterRewardedVideoEventHandlers(GoogleMobileAds.Api.RewardedAd)
extern void AdsManager_RegisterRewardedVideoEventHandlers_m8AA196DADE837F29CCF052D57F4E7B97FB3FE619 (void);
// 0x0000005C System.Boolean AdsManager::CheckIfAdmobRewardVideoRewardInterIsLoaded()
extern void AdsManager_CheckIfAdmobRewardVideoRewardInterIsLoaded_mFAE99E35A3B4143F3B5CBAA84C2E9A63243E07A0 (void);
// 0x0000005D System.Void AdsManager::ShowAdmobRewardVideoRewardInter()
extern void AdsManager_ShowAdmobRewardVideoRewardInter_m86C5FB69CB70EAAE1661907C98D6C1CD002E11DD (void);
// 0x0000005E System.Void AdsManager::ShowAdmobRewardedVideoAd()
extern void AdsManager_ShowAdmobRewardedVideoAd_m71032B0A83CDE5FE62C2CFB82FA515E29CF452C0 (void);
// 0x0000005F System.Boolean AdsManager::CheckIfAdmobRewardedVideoIsReady()
extern void AdsManager_CheckIfAdmobRewardedVideoIsReady_m98EA7849FC6BFC51F5048BC8E6AE3DE43CB68AD0 (void);
// 0x00000060 System.Void AdsManager::.ctor()
extern void AdsManager__ctor_m1324EE3D9841045940C44D5E0379750DBE0C0A53 (void);
// 0x00000061 System.Void AdsManager::<InitializeAdmob>b__100_0(GoogleMobileAds.Api.InitializationStatus)
extern void AdsManager_U3CInitializeAdmobU3Eb__100_0_mF577590EF5F11DF96E37B33EEA5C71DA2D8AEC40 (void);
// 0x00000062 System.Void AdsManager::<loadInterstitialAdCustom0>b__147_0(GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CloadInterstitialAdCustom0U3Eb__147_0_mBDEDAF6696EFBC73EF156014A62FDA2F4AD947B7 (void);
// 0x00000063 System.Void AdsManager::<loadInterstitialAdCustom1>b__148_0(GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CloadInterstitialAdCustom1U3Eb__148_0_m8EEBB36A49F7E395FCE0A0AA027C77C8871B29D9 (void);
// 0x00000064 System.Void AdsManager::<loadInterstitialAdCustom2>b__149_0(GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CloadInterstitialAdCustom2U3Eb__149_0_m2CCA33817CCD0488526FEE4AD3F2455634A28555 (void);
// 0x00000065 System.Void AdsManager::<loadInterstitialAdCustom3>b__150_0(GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CloadInterstitialAdCustom3U3Eb__150_0_m0EF8B952E0E063BF6AE3B0B2C0EA691ABFF194EC (void);
// 0x00000066 System.Void AdsManager::<loadInterstitialAdCustom4>b__151_0(GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CloadInterstitialAdCustom4U3Eb__151_0_m2B74D4185FF4066E99619AC6865D404EC90AF29B (void);
// 0x00000067 System.Void AdsManager::<RegisterInterEvents0>b__157_0()
extern void AdsManager_U3CRegisterInterEvents0U3Eb__157_0_m714D425E028864A4322427107A7B3665C695FB95 (void);
// 0x00000068 System.Void AdsManager::<RegisterInterEvents0>b__157_1()
extern void AdsManager_U3CRegisterInterEvents0U3Eb__157_1_m27F080C096CD99A962D07567E25302E752537804 (void);
// 0x00000069 System.Void AdsManager::<RegisterInterEvents1>b__158_0()
extern void AdsManager_U3CRegisterInterEvents1U3Eb__158_0_m08D3A39AC12EED321E5528B61E0FEE4A603FC43F (void);
// 0x0000006A System.Void AdsManager::<RegisterInterEvents1>b__158_1()
extern void AdsManager_U3CRegisterInterEvents1U3Eb__158_1_m41B4A5503A31428083DB809226A94D26751B4FE4 (void);
// 0x0000006B System.Void AdsManager::<RegisterInterEvents2>b__159_0()
extern void AdsManager_U3CRegisterInterEvents2U3Eb__159_0_mBFCF289E0DCDC4004398B12F160BF4BC21AE4049 (void);
// 0x0000006C System.Void AdsManager::<RegisterInterEvents2>b__159_1()
extern void AdsManager_U3CRegisterInterEvents2U3Eb__159_1_m8441521F95ABB7A0ABEBE38658C71A58C02E5200 (void);
// 0x0000006D System.Void AdsManager::<RegisterInterEvents3>b__160_0()
extern void AdsManager_U3CRegisterInterEvents3U3Eb__160_0_m0798E776B41548D1E08C1F73CC605F60D57605EC (void);
// 0x0000006E System.Void AdsManager::<RegisterInterEvents3>b__160_1()
extern void AdsManager_U3CRegisterInterEvents3U3Eb__160_1_m1A3526D78454752D5A8AA0029BA1472D6511EDC5 (void);
// 0x0000006F System.Void AdsManager::<RegisterInterEvents4>b__161_0()
extern void AdsManager_U3CRegisterInterEvents4U3Eb__161_0_m244035B19BFCD05D5CB7C5FDAABF6D427C7D11E5 (void);
// 0x00000070 System.Void AdsManager::<RegisterInterEvents4>b__161_1()
extern void AdsManager_U3CRegisterInterEvents4U3Eb__161_1_m0059419AFA9A891B2C28977DCF1CDC1EBB46DCA1 (void);
// 0x00000071 System.Void AdsManager::<loadAdmobRewardedInterstitial>b__168_0(GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CloadAdmobRewardedInterstitialU3Eb__168_0_m66C00DEC641BC221A256D8DCBCBDD7AA8F11D05B (void);
// 0x00000072 System.Void AdsManager::<RegisterRewardedInterEventHandlers>b__169_0()
extern void AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_0_m097621CA9ECA00FE09EBD8AE84210F0DE215DE3D (void);
// 0x00000073 System.Void AdsManager::<RegisterRewardedInterEventHandlers>b__169_1()
extern void AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_1_mCBBEEB536D155E2A27845760525C2FC7DB885D93 (void);
// 0x00000074 System.Void AdsManager::<LoadAppOpenAd>b__175_0(GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CLoadAppOpenAdU3Eb__175_0_m9A72DAD7C216B659D391C69C6615FBDE5EDB5924 (void);
// 0x00000075 System.Void AdsManager::<RegisterAppOpenEventHandlers>b__176_0()
extern void AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_0_m606895315A0B8258891E783CAEC43339AD0D0C8E (void);
// 0x00000076 System.Void AdsManager::<RegisterAppOpenEventHandlers>b__176_1()
extern void AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_1_m2009D1B41B4DD4EBFBE781876AA80D153E8529E9 (void);
// 0x00000077 System.Void AdsManager::<RegisterAppOpenEventHandlers>b__176_2(GoogleMobileAds.Api.AdError)
extern void AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_2_m9174B75FA8AC1D6E6F4C39A082F4EC1209677853 (void);
// 0x00000078 System.Void AdsManager::<LoadVideoAD>b__180_0(GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError)
extern void AdsManager_U3CLoadVideoADU3Eb__180_0_m42A6234A90D26054953322931FDA7918B423852D (void);
// 0x00000079 System.Void AdsManager::<RegisterRewardedVideoEventHandlers>b__181_0()
extern void AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_0_m6ED4779AD56F7554C6E523BC2EB21274138EE9A9 (void);
// 0x0000007A System.Void AdsManager::<RegisterRewardedVideoEventHandlers>b__181_1()
extern void AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_1_mDB2D1DE644BA6DE800E6E24443218E3E9685965B (void);
// 0x0000007B System.Void AdsManager/<>c__DisplayClass111_0::.ctor()
extern void U3CU3Ec__DisplayClass111_0__ctor_m47D935565DC706503A6E1E4DE6AF60CEEC8D1B67 (void);
// 0x0000007C System.Void AdsManager/<>c__DisplayClass111_0::<ListenToAdEvents>b__0()
extern void U3CU3Ec__DisplayClass111_0_U3CListenToAdEventsU3Eb__0_mC96F5C66D92487735E496062A9744FD3A5FB4955 (void);
// 0x0000007D System.Void AdsManager/<>c__DisplayClass111_0::<ListenToAdEvents>b__1(GoogleMobileAds.Api.LoadAdError)
extern void U3CU3Ec__DisplayClass111_0_U3CListenToAdEventsU3Eb__1_m366214F722CDF91143392D35172686CA38106711 (void);
// 0x0000007E System.Void AdsManager/<>c::.cctor()
extern void U3CU3Ec__cctor_m508766EBBE3CEBE4A90C808491E7FD4A9A019C44 (void);
// 0x0000007F System.Void AdsManager/<>c::.ctor()
extern void U3CU3Ec__ctor_m7190908711A853BE0728E534D2747D9FCE792CAC (void);
// 0x00000080 System.Void AdsManager/<>c::<ShowAdmobRewardedInterstitial>b__171_0(GoogleMobileAds.Api.Reward)
extern void U3CU3Ec_U3CShowAdmobRewardedInterstitialU3Eb__171_0_mD764E9EC5A2CDB473A66422A59050ECB097FEA61 (void);
// 0x00000081 System.Void AdsManager/<>c::<ShowAdmobRewardedVideoAd>b__184_0(GoogleMobileAds.Api.Reward)
extern void U3CU3Ec_U3CShowAdmobRewardedVideoAdU3Eb__184_0_m5BCDA4FBAA974BFD12A010F2A9FA9E732CAFBAB3 (void);
// 0x00000082 System.Void UmpManager::Start()
extern void UmpManager_Start_mD8B450E588913A9C8AAD46BD6884F5A51E4F0E93 (void);
// 0x00000083 System.Void UmpManager::FuncStart()
extern void UmpManager_FuncStart_m6EA89D1E261B4291689998BFA4075A7B108330B0 (void);
// 0x00000084 System.Void UmpManager::OnConsentInfoUpdated(GoogleMobileAds.Ump.Api.FormError)
extern void UmpManager_OnConsentInfoUpdated_m103F1B1A61AA12BA3719A78E33CDB15113CE516D (void);
// 0x00000085 System.Void UmpManager::.ctor()
extern void UmpManager__ctor_m18B3E5B472346E89A3A2CF050335F51700465812 (void);
// 0x00000086 System.Void UmpManager/<>c__DisplayClass3_0::.ctor()
extern void U3CU3Ec__DisplayClass3_0__ctor_mF22DD9384541AF453CF11E06F9CC0B512D6520CA (void);
// 0x00000087 System.Void UmpManager/<>c__DisplayClass3_0::<OnConsentInfoUpdated>b__0(GoogleMobileAds.Ump.Api.FormError)
extern void U3CU3Ec__DisplayClass3_0_U3COnConsentInfoUpdatedU3Eb__0_m204141F1F8F44F2F0E559F346C9480783BC35F66 (void);
// 0x00000088 System.Void starFxController::Awake()
extern void starFxController_Awake_m56F5D4BFED423AB72CAB9C7C571B629E254B22BA (void);
// 0x00000089 System.Void starFxController::Start()
extern void starFxController_Start_m1F138E6BC6C9B1D780B1126AA562E8F7D1E0D4C4 (void);
// 0x0000008A System.Void starFxController::Update()
extern void starFxController_Update_mBDC7A5CD199D4B1D52E0C26DB00478F3EB87F39A (void);
// 0x0000008B System.Void starFxController::Reset()
extern void starFxController_Reset_mCEE6E1689A3C3EF0A4E55A4DBB20AF39D206E4F7 (void);
// 0x0000008C System.Void starFxController::.ctor()
extern void starFxController__ctor_m77701D9BF1F5CCD5E55E1402F1B709DF4DF242F1 (void);
// 0x0000008D System.Void vfxController::Start()
extern void vfxController_Start_m31CFE24DDAB49C81B733A80FE841CF3339080793 (void);
// 0x0000008E System.Void vfxController::ChangedStarImage(System.Int32)
extern void vfxController_ChangedStarImage_m2EDFEE3F7992E2540F3B5E27A8033D7515323D24 (void);
// 0x0000008F System.Void vfxController::ChangedStarFX(System.Int32)
extern void vfxController_ChangedStarFX_m4EEA9050B0348B07CFECE4380CDA0E98F8DD5031 (void);
// 0x00000090 System.Void vfxController::ChangedLevel(System.Int32)
extern void vfxController_ChangedLevel_m2B5FB6F4D0D745E807ADB957676C6F9F66B223E0 (void);
// 0x00000091 System.Void vfxController::ChangedBgFx(System.Int32)
extern void vfxController_ChangedBgFx_m460821C0F2A0E34050C680EB2EB288CA98892CCD (void);
// 0x00000092 System.Void vfxController::PlayStarFX()
extern void vfxController_PlayStarFX_mC1F8AE36362C10F443DC83D282E4676B00E41F10 (void);
// 0x00000093 System.Void vfxController::.ctor()
extern void vfxController__ctor_m5AEC8D58EB383F07E8AAC27066553C5E16690F80 (void);
// 0x00000094 System.Void starFxControllerMy::Awake()
extern void starFxControllerMy_Awake_m954F7565F73D46A873E0F9A053E714AA708311FD (void);
// 0x00000095 System.Void starFxControllerMy::Start()
extern void starFxControllerMy_Start_m66FAE607A68967EA0179B4DBD773B07BBF97EE77 (void);
// 0x00000096 System.Void starFxControllerMy::Update()
extern void starFxControllerMy_Update_m59FD9BC0418C85641EF8890277B4EA399D53964A (void);
// 0x00000097 System.Void starFxControllerMy::Reset()
extern void starFxControllerMy_Reset_mEFA34C40E480AF18FCE8C38971C584121FE47419 (void);
// 0x00000098 System.Void starFxControllerMy::.ctor()
extern void starFxControllerMy__ctor_m8D0D009F81E96793A7F09FD447FE8089DC284CEA (void);
// 0x00000099 System.Void ScrollUV::.ctor()
extern void ScrollUV__ctor_m215B33742BB31E7AE93401FCFD7D98AF05012B09 (void);
// 0x0000009A System.Void ScrollUV::Update()
extern void ScrollUV_Update_m05BEA8033AAC9BBF331E2F9CB7C83F8A2AD4ED41 (void);
// 0x0000009B System.Void ScrollUV::Main()
extern void ScrollUV_Main_mF2C07392C3037F88B9296070E8EB999CFD83117C (void);
// 0x0000009C System.Void LightAnimation::Start()
extern void LightAnimation_Start_m5C6E7A45FEA1611A1781A9810637B3E85B3409E0 (void);
// 0x0000009D System.Void LightAnimation::Update()
extern void LightAnimation_Update_m86DA0A3814A9ACEF41C8139D13319F7EC7343258 (void);
// 0x0000009E System.Void LightAnimation::.ctor()
extern void LightAnimation__ctor_m867EA18EAF4F1DAAD42AF1C4EB46C5CECC2470B1 (void);
// 0x0000009F System.Void Rotator::Update()
extern void Rotator_Update_m676DE57B82807D8024D38A4AFE6BD8B0918F4290 (void);
// 0x000000A0 System.Void Rotator::.ctor()
extern void Rotator__ctor_mBB7C78CE11B1B78DD832ECEA175A9F8FF08355F7 (void);
// 0x000000A1 System.Void DemoController::ChangeParticle(System.Int32)
extern void DemoController_ChangeParticle_mB12ABE451BEBD34EF056C0037679D99FE9299FAC (void);
// 0x000000A2 System.Void DemoController::.ctor()
extern void DemoController__ctor_mDCA85316804A352961C2DA6570E01FF23E7C9853 (void);
// 0x000000A3 System.Boolean MeshCombiner::get_CreateMultiMaterialMesh()
extern void MeshCombiner_get_CreateMultiMaterialMesh_mEDB45502D254A42398F37EBC47819BAB014FED36 (void);
// 0x000000A4 System.Void MeshCombiner::set_CreateMultiMaterialMesh(System.Boolean)
extern void MeshCombiner_set_CreateMultiMaterialMesh_mE76A3CEC1E7B10346FDF630163FB206BDC5B0DD7 (void);
// 0x000000A5 System.Boolean MeshCombiner::get_CombineInactiveChildren()
extern void MeshCombiner_get_CombineInactiveChildren_m17EFE50D9EAA3647640A0A435705CD9A5B5885EE (void);
// 0x000000A6 System.Void MeshCombiner::set_CombineInactiveChildren(System.Boolean)
extern void MeshCombiner_set_CombineInactiveChildren_m70EF4464B62076B045B39A397DCEF400E25D314D (void);
// 0x000000A7 System.Boolean MeshCombiner::get_DeactivateCombinedChildren()
extern void MeshCombiner_get_DeactivateCombinedChildren_mD696FAE201247221959180FF3832649F493D23FB (void);
// 0x000000A8 System.Void MeshCombiner::set_DeactivateCombinedChildren(System.Boolean)
extern void MeshCombiner_set_DeactivateCombinedChildren_m18348793B426B6292F94C1E55E14C3DE43EC66F6 (void);
// 0x000000A9 System.Boolean MeshCombiner::get_DeactivateCombinedChildrenMeshRenderers()
extern void MeshCombiner_get_DeactivateCombinedChildrenMeshRenderers_mFA56E16EA0EA45A272E0625E201A6CDED344361D (void);
// 0x000000AA System.Void MeshCombiner::set_DeactivateCombinedChildrenMeshRenderers(System.Boolean)
extern void MeshCombiner_set_DeactivateCombinedChildrenMeshRenderers_m36996592333EB91A0561538D735062ADAEAA5C5C (void);
// 0x000000AB System.Boolean MeshCombiner::get_GenerateUVMap()
extern void MeshCombiner_get_GenerateUVMap_m980DF912B63C57989480AE61EC107F2957B6988E (void);
// 0x000000AC System.Void MeshCombiner::set_GenerateUVMap(System.Boolean)
extern void MeshCombiner_set_GenerateUVMap_m9865F21650EB815DC6DAD98A91598C5A2951CEFD (void);
// 0x000000AD System.Boolean MeshCombiner::get_DestroyCombinedChildren()
extern void MeshCombiner_get_DestroyCombinedChildren_m8C24F88E8D69D8042FE918CF63D82E64E2483F23 (void);
// 0x000000AE System.Void MeshCombiner::set_DestroyCombinedChildren(System.Boolean)
extern void MeshCombiner_set_DestroyCombinedChildren_mDC0F4586EBB8F4DFB1F2692FE180B6BD8ED9839E (void);
// 0x000000AF System.String MeshCombiner::get_FolderPath()
extern void MeshCombiner_get_FolderPath_mA2285CF62DCD93966B153C053B963443BF0AED1C (void);
// 0x000000B0 System.Void MeshCombiner::set_FolderPath(System.String)
extern void MeshCombiner_set_FolderPath_mB92D54973076F15F39E34A7A2F4D2447B2FB2B3E (void);
// 0x000000B1 System.Void MeshCombiner::CheckDeactivateCombinedChildren()
extern void MeshCombiner_CheckDeactivateCombinedChildren_m56D06FE355DBD6F3B8F6E1AE52F6EC27AB2D1F2A (void);
// 0x000000B2 System.Void MeshCombiner::CheckDestroyCombinedChildren()
extern void MeshCombiner_CheckDestroyCombinedChildren_m46493720DE2C2A63BA7D99EA23F549A4B42749AE (void);
// 0x000000B3 System.Void MeshCombiner::CombineMeshes(System.Boolean)
extern void MeshCombiner_CombineMeshes_mABB61494B5B5A9C007919B88413C4C9132E62989 (void);
// 0x000000B4 UnityEngine.MeshFilter[] MeshCombiner::GetMeshFiltersToCombine()
extern void MeshCombiner_GetMeshFiltersToCombine_mD28F858198DA4E57619450465DA794177BB7DE72 (void);
// 0x000000B5 System.Void MeshCombiner::CombineMeshesWithSingleMaterial(System.Boolean)
extern void MeshCombiner_CombineMeshesWithSingleMaterial_mD7927B466258A14116C575B9DC084DB968C6782F (void);
// 0x000000B6 System.Void MeshCombiner::CombineMeshesWithMutliMaterial(System.Boolean)
extern void MeshCombiner_CombineMeshesWithMutliMaterial_m550862E2A444C10861A9290BEC2FC8BEF39B9E4B (void);
// 0x000000B7 System.Void MeshCombiner::DeactivateCombinedGameObjects(UnityEngine.MeshFilter[])
extern void MeshCombiner_DeactivateCombinedGameObjects_mC700A980273A4FE6FEA14999E1D0599956DC09BC (void);
// 0x000000B8 System.Void MeshCombiner::GenerateUV(UnityEngine.Mesh)
extern void MeshCombiner_GenerateUV_m651D43E54555179FAFE11E9D9EDEE0BCFF9CF374 (void);
// 0x000000B9 System.Void MeshCombiner::.ctor()
extern void MeshCombiner__ctor_mB13129257E7B635A0B644F987594B21ACE6F4BB1 (void);
// 0x000000BA System.Void MeshCombiner/<>c__DisplayClass33_0::.ctor()
extern void U3CU3Ec__DisplayClass33_0__ctor_m4FF5AAE6916D1387510E64676EAB39A53DC18014 (void);
// 0x000000BB System.Boolean MeshCombiner/<>c__DisplayClass33_0::<GetMeshFiltersToCombine>b__0(UnityEngine.MeshFilter)
extern void U3CU3Ec__DisplayClass33_0_U3CGetMeshFiltersToCombineU3Eb__0_m257ED5A742B494F6E5C12C847B723D84583EF17D (void);
// 0x000000BC System.Void MeshCombiner/<>c__DisplayClass33_1::.ctor()
extern void U3CU3Ec__DisplayClass33_1__ctor_mF255176B1E0ED28C4A103DB0A66071354CA13A93 (void);
// 0x000000BD System.Boolean MeshCombiner/<>c__DisplayClass33_1::<GetMeshFiltersToCombine>b__2(UnityEngine.MeshFilter)
extern void U3CU3Ec__DisplayClass33_1_U3CGetMeshFiltersToCombineU3Eb__2_m499A70231221E766848D5EB60BB711481249796A (void);
// 0x000000BE System.Void MeshCombiner/<>c::.cctor()
extern void U3CU3Ec__cctor_mA064A116AE9D5AFF5D3180F3DDA35EE843E6D06F (void);
// 0x000000BF System.Void MeshCombiner/<>c::.ctor()
extern void U3CU3Ec__ctor_m92943B6FDBCF5BB35117AF45865D1E4BAFFBDA9F (void);
// 0x000000C0 System.Boolean MeshCombiner/<>c::<GetMeshFiltersToCombine>b__33_1(UnityEngine.MeshFilter)
extern void U3CU3Ec_U3CGetMeshFiltersToCombineU3Eb__33_1_m9330A19419806D557A51C004767734F45BB225F7 (void);
// 0x000000C1 RCC_CarControllerV3 RCC::SpawnRCC(RCC_CarControllerV3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Boolean,System.Boolean,System.Boolean)
extern void RCC_SpawnRCC_m3F6E78E00CB08CED849F05AE9DF0FE006B1991E5 (void);
// 0x000000C2 System.Void RCC::RegisterPlayerVehicle(RCC_CarControllerV3)
extern void RCC_RegisterPlayerVehicle_m8FD23761FBFBA2AC20C377366C5DC12449FFB1AC (void);
// 0x000000C3 System.Void RCC::RegisterPlayerVehicle(RCC_CarControllerV3,System.Boolean)
extern void RCC_RegisterPlayerVehicle_m63F6692C9E25D1640477ABB4D05E193735EB66DA (void);
// 0x000000C4 System.Void RCC::RegisterPlayerVehicle(RCC_CarControllerV3,System.Boolean,System.Boolean)
extern void RCC_RegisterPlayerVehicle_mC516450CCAD9BBE344676A14BDD25294EBBF7CBC (void);
// 0x000000C5 System.Void RCC::DeRegisterPlayerVehicle()
extern void RCC_DeRegisterPlayerVehicle_mAFEC9AB4DC222CA2681272137C7A34B43091A642 (void);
// 0x000000C6 System.Void RCC::SetControl(RCC_CarControllerV3,System.Boolean)
extern void RCC_SetControl_m948786C3886D96548734260E0D9DFE0AFA77DCD7 (void);
// 0x000000C7 System.Void RCC::SetEngine(RCC_CarControllerV3,System.Boolean)
extern void RCC_SetEngine_mED854F74ABA31DED2A38CEC424E447F8D0BDE0B5 (void);
// 0x000000C8 System.Void RCC::SetMobileController(RCC_Settings/MobileController)
extern void RCC_SetMobileController_mF13A14901B46E42363F8CE9CDEB82590D39E4753 (void);
// 0x000000C9 System.Void RCC::SetUnits()
extern void RCC_SetUnits_m1FB9458EA1CC38496A6AA44805667B12A2BEC0F4 (void);
// 0x000000CA System.Void RCC::SetAutomaticGear()
extern void RCC_SetAutomaticGear_mC72492CDD5DA75F8789ABCE9397A7117BF6343ED (void);
// 0x000000CB System.Void RCC::StartStopRecord()
extern void RCC_StartStopRecord_m2E18F53E563D1C8B2FC6F567DCECA8F5028F4756 (void);
// 0x000000CC System.Void RCC::StartStopReplay()
extern void RCC_StartStopReplay_m10F0C8EEE8C98D0E6A1B9E0B7D24D5218EF7219F (void);
// 0x000000CD System.Void RCC::StartStopReplay(RCC_Recorder/Recorded)
extern void RCC_StartStopReplay_m618408A98EE054F92D2011219538901576C94B71 (void);
// 0x000000CE System.Void RCC::StartStopReplay(System.Int32)
extern void RCC_StartStopReplay_m258786FF07B12EBDC015034F11A22D923F49FCED (void);
// 0x000000CF System.Void RCC::StopRecordReplay()
extern void RCC_StopRecordReplay_m90B291EF49427376C3EBEE21B501E66688FF752C (void);
// 0x000000D0 System.Void RCC::SetBehavior(System.Int32)
extern void RCC_SetBehavior_mFCDD3209C6F8DA50833F061D4074EF4AC29740A9 (void);
// 0x000000D1 System.Void RCC::SetController(System.Int32)
extern void RCC_SetController_m8F6F80D4A2393FF11C9CB3BEECB2930A59F7CCFA (void);
// 0x000000D2 System.Void RCC::ChangeCamera()
extern void RCC_ChangeCamera_mDB162E02DD99BA9C0BAC425B46B5D92CF2A1C030 (void);
// 0x000000D3 System.Void RCC::.ctor()
extern void RCC__ctor_mC028620308C25010A68FB84148BA3625D690C739 (void);
// 0x000000D4 System.Void RCC_AIBrakeZone::.ctor()
extern void RCC_AIBrakeZone__ctor_mCB04F16CA317A16F285DB89711BA883957958320 (void);
// 0x000000D5 System.Void RCC_AIBrakeZonesContainer::OnDrawGizmos()
extern void RCC_AIBrakeZonesContainer_OnDrawGizmos_mC7A89421501241A70EB5CE92DBF9E70FAE8269D2 (void);
// 0x000000D6 System.Void RCC_AIBrakeZonesContainer::.ctor()
extern void RCC_AIBrakeZonesContainer__ctor_mF8FCE4365B87320CAAA1DCADC08BCFC027C128D3 (void);
// 0x000000D7 System.Void RCC_AICarController::add_OnRCCAISpawned(RCC_AICarController/onRCCAISpawned)
extern void RCC_AICarController_add_OnRCCAISpawned_m3BBD0EA7218909E9EC80375B0554ED8B038802B4 (void);
// 0x000000D8 System.Void RCC_AICarController::remove_OnRCCAISpawned(RCC_AICarController/onRCCAISpawned)
extern void RCC_AICarController_remove_OnRCCAISpawned_m12038CFCBA20EF03A2964B286F80A3B7E6482E36 (void);
// 0x000000D9 System.Void RCC_AICarController::add_OnRCCAIDestroyed(RCC_AICarController/onRCCAIDestroyed)
extern void RCC_AICarController_add_OnRCCAIDestroyed_mB643B14ED1DF88403006531517DB79B66D111908 (void);
// 0x000000DA System.Void RCC_AICarController::remove_OnRCCAIDestroyed(RCC_AICarController/onRCCAIDestroyed)
extern void RCC_AICarController_remove_OnRCCAIDestroyed_m560E810569BF799FDE187E14A5191DC49EADA4C2 (void);
// 0x000000DB System.Void RCC_AICarController::Start()
extern void RCC_AICarController_Start_mB205F42087DD4684FEB0C464B96A05A019CDA226 (void);
// 0x000000DC System.Void RCC_AICarController::OnEnable()
extern void RCC_AICarController_OnEnable_mD69466CFA4EEF5B438A3F3993DD262DC0C0ED917 (void);
// 0x000000DD System.Void RCC_AICarController::Update()
extern void RCC_AICarController_Update_mF9765F8B081C5B6F2450D382B57CCDC753A6B448 (void);
// 0x000000DE System.Void RCC_AICarController::FixedUpdate()
extern void RCC_AICarController_FixedUpdate_m6E1A5570F03D551166962ADB53E423EB6A76AE25 (void);
// 0x000000DF System.Void RCC_AICarController::Navigation()
extern void RCC_AICarController_Navigation_m141A3C7BBC94B1F7898470BA0F9A4BCA2F29967A (void);
// 0x000000E0 System.Void RCC_AICarController::Resetting()
extern void RCC_AICarController_Resetting_mFF888B4699720C43323D1784163DFBEF23900550 (void);
// 0x000000E1 System.Void RCC_AICarController::FixedRaycasts()
extern void RCC_AICarController_FixedRaycasts_m1357A6F5095E06216D48552A1D4E5201EF69B88D (void);
// 0x000000E2 System.Void RCC_AICarController::FeedRCC()
extern void RCC_AICarController_FeedRCC_m4A33FE2DFC150BC7F614D1711478AD7AD9104601 (void);
// 0x000000E3 System.Void RCC_AICarController::Stop()
extern void RCC_AICarController_Stop_mABD22C7E74025E5EC7963C16DF39DF24B7794F62 (void);
// 0x000000E4 System.Void RCC_AICarController::OnTriggerEnter(UnityEngine.Collider)
extern void RCC_AICarController_OnTriggerEnter_mDEE606ADFCAB7AA005DD8DFBECBC955F474E330B (void);
// 0x000000E5 System.Void RCC_AICarController::OnTriggerExit(UnityEngine.Collider)
extern void RCC_AICarController_OnTriggerExit_m4B5F0FDA6B93BA2EB72B956D4874C43946F82627 (void);
// 0x000000E6 UnityEngine.Transform RCC_AICarController::GetClosestEnemy(RCC_CarControllerV3[])
extern void RCC_AICarController_GetClosestEnemy_m06AAC3DB7D92B23B95000474EAD038AB02F05C76 (void);
// 0x000000E7 System.Void RCC_AICarController::OnDestroy()
extern void RCC_AICarController_OnDestroy_m590BB777572FBBCB67999FB664B415F882E7B013 (void);
// 0x000000E8 System.Void RCC_AICarController::.ctor()
extern void RCC_AICarController__ctor_m929E7EB6C6D6E2D34DD6E276EE03BB57FFDC0563 (void);
// 0x000000E9 System.Void RCC_AICarController/onRCCAISpawned::.ctor(System.Object,System.IntPtr)
extern void onRCCAISpawned__ctor_mB156F9D87D23FDE7CCBEC871B65F75438CD122B5 (void);
// 0x000000EA System.Void RCC_AICarController/onRCCAISpawned::Invoke(RCC_AICarController)
extern void onRCCAISpawned_Invoke_mDF0E988DBAF7DB54C3568F2C1354C5BAEA24BA5A (void);
// 0x000000EB System.IAsyncResult RCC_AICarController/onRCCAISpawned::BeginInvoke(RCC_AICarController,System.AsyncCallback,System.Object)
extern void onRCCAISpawned_BeginInvoke_mC5B3067AAA42C360518AC23F8592B93817C55C99 (void);
// 0x000000EC System.Void RCC_AICarController/onRCCAISpawned::EndInvoke(System.IAsyncResult)
extern void onRCCAISpawned_EndInvoke_m32BAF57C398832C53A0979D1D88D883A96F9EAA4 (void);
// 0x000000ED System.Void RCC_AICarController/onRCCAIDestroyed::.ctor(System.Object,System.IntPtr)
extern void onRCCAIDestroyed__ctor_m12143C7195B27907C06004D6898BF380A8B03ABA (void);
// 0x000000EE System.Void RCC_AICarController/onRCCAIDestroyed::Invoke(RCC_AICarController)
extern void onRCCAIDestroyed_Invoke_m69B8D4FEA0320CC739D42BD7E29E401F08947A44 (void);
// 0x000000EF System.IAsyncResult RCC_AICarController/onRCCAIDestroyed::BeginInvoke(RCC_AICarController,System.AsyncCallback,System.Object)
extern void onRCCAIDestroyed_BeginInvoke_mC923F69511DE6EB2B3B60C89414EF0BFD258D89F (void);
// 0x000000F0 System.Void RCC_AICarController/onRCCAIDestroyed::EndInvoke(System.IAsyncResult)
extern void onRCCAIDestroyed_EndInvoke_mF612A9B4AAA65BBD00F2FA3926FF1F78E0B73EB2 (void);
// 0x000000F1 System.Void RCC_AIO::Start()
extern void RCC_AIO_Start_m21F15EBDB44F0F72457727F5126FA4FDFECFD554 (void);
// 0x000000F2 System.Void RCC_AIO::Update()
extern void RCC_AIO_Update_mB0974A0CDCC08EB880531A4E9EE845D6E0B6F1FB (void);
// 0x000000F3 System.Void RCC_AIO::LoadLevel(System.String)
extern void RCC_AIO_LoadLevel_m3283C640B3D375648E5D0A1232501AB7F5396A05 (void);
// 0x000000F4 System.Void RCC_AIO::ToggleMenu(UnityEngine.GameObject)
extern void RCC_AIO_ToggleMenu_m207A9F88A9562E0427ED28BBDA0683FDC161C0CC (void);
// 0x000000F5 System.Void RCC_AIO::Quit()
extern void RCC_AIO_Quit_m21290FA8AE635763F45C8E702D1906ADB3AA6ADA (void);
// 0x000000F6 System.Void RCC_AIO::.ctor()
extern void RCC_AIO__ctor_mDDDF1636B838283B91292C56D8A6D8339D4E1375 (void);
// 0x000000F7 System.Void RCC_AIWaypointsContainer::OnDrawGizmos()
extern void RCC_AIWaypointsContainer_OnDrawGizmos_mDE6AF20CD59B9CD5AE8CDF47C79F50CDA20DC214 (void);
// 0x000000F8 System.Void RCC_AIWaypointsContainer::.ctor()
extern void RCC_AIWaypointsContainer__ctor_mB00BFDF853275D68088953E32FC64D7763DA60F4 (void);
// 0x000000F9 System.Void RCC_APIExample::Spawn()
extern void RCC_APIExample_Spawn_mCA5082677EA89733D7CE710AADB160002F95B7F2 (void);
// 0x000000FA System.Void RCC_APIExample::SetPlayer()
extern void RCC_APIExample_SetPlayer_m0C12D4E6E5AFFB3F2D409FA59ADAEC419EF3CB17 (void);
// 0x000000FB System.Void RCC_APIExample::SetControl(System.Boolean)
extern void RCC_APIExample_SetControl_mEDE59DC592A5E571845693C05E5C7D204A43A0C9 (void);
// 0x000000FC System.Void RCC_APIExample::SetEngine(System.Boolean)
extern void RCC_APIExample_SetEngine_mB267FE9299B4A240D60AE1B5969029A93F08E99B (void);
// 0x000000FD System.Void RCC_APIExample::DeRegisterPlayer()
extern void RCC_APIExample_DeRegisterPlayer_mDDB542B8D27EC3B2441D1959E299B7A3A2BB3604 (void);
// 0x000000FE System.Void RCC_APIExample::.ctor()
extern void RCC_APIExample__ctor_m200CA303F9E9EA4B5724157900E26411C02A1B57 (void);
// 0x000000FF System.Void RCC_Caliper::Start()
extern void RCC_Caliper_Start_mF1B541F6E2436F9C250B548E5C03E6F7EA6DF0EB (void);
// 0x00000100 System.Void RCC_Caliper::Update()
extern void RCC_Caliper_Update_m1CCB2380CE087314FADF51193D7C3C9423868C12 (void);
// 0x00000101 System.Void RCC_Caliper::.ctor()
extern void RCC_Caliper__ctor_m1DB569DB2F740801597680ED575D5F2380F04625 (void);
// 0x00000102 RCC_Settings RCC_Camera::get_RCCSettings()
extern void RCC_Camera_get_RCCSettings_mC878F5E635C505C7EB912901D884BF4E46F9B205 (void);
// 0x00000103 System.Void RCC_Camera::add_OnBCGCameraSpawned(RCC_Camera/onBCGCameraSpawned)
extern void RCC_Camera_add_OnBCGCameraSpawned_m549CD035B0CD04B8480C8180120D4DB55F875854 (void);
// 0x00000104 System.Void RCC_Camera::remove_OnBCGCameraSpawned(RCC_Camera/onBCGCameraSpawned)
extern void RCC_Camera_remove_OnBCGCameraSpawned_m48CF21668CA531B403CBA86D6255D1B0E61A14B9 (void);
// 0x00000105 System.Void RCC_Camera::Awake()
extern void RCC_Camera_Awake_mFADC75C4D8C046ED1E7CB108358F7ED3237C9F1B (void);
// 0x00000106 System.Void RCC_Camera::OnEnable()
extern void RCC_Camera_OnEnable_m16FB7271A2A73A9622339B93D07B41158C99E20B (void);
// 0x00000107 System.Void RCC_Camera::RCC_CarControllerV3_OnRCCPlayerCollision(RCC_CarControllerV3,UnityEngine.Collision)
extern void RCC_Camera_RCC_CarControllerV3_OnRCCPlayerCollision_m2A68C01DEAD0C4BBE4FEA7DAD479DFBDD26A9A50 (void);
// 0x00000108 System.Void RCC_Camera::GetTarget()
extern void RCC_Camera_GetTarget_mE7E40A0F1273E9A2F8FA10F5D63FE1DAA5800506 (void);
// 0x00000109 System.Void RCC_Camera::SetTarget(UnityEngine.GameObject)
extern void RCC_Camera_SetTarget_mB08AF595795FCB977AE7F175F24B3013A049CD3D (void);
// 0x0000010A System.Void RCC_Camera::RemoveTarget()
extern void RCC_Camera_RemoveTarget_m22F4C0A75C42B07A9D92C9C94A056A4D9CAA8FC9 (void);
// 0x0000010B System.Void RCC_Camera::Update()
extern void RCC_Camera_Update_mCD39559F9AE53F0BFBEC68844046315633E452CD (void);
// 0x0000010C System.Void RCC_Camera::LateUpdate()
extern void RCC_Camera_LateUpdate_m1E3DCCD6D04F60ADBAB947B3C5D54875B6B68945 (void);
// 0x0000010D System.Void RCC_Camera::Inputs()
extern void RCC_Camera_Inputs_m7549CDF596148BA7DF6230FCAD14605D4635A959 (void);
// 0x0000010E System.Void RCC_Camera::ChangeCamera()
extern void RCC_Camera_ChangeCamera_mE8EC96C08DB85C763CAD94FCBBF641410A0700C7 (void);
// 0x0000010F System.Void RCC_Camera::ChangeCamera(RCC_Camera/CameraMode)
extern void RCC_Camera_ChangeCamera_m55B866A76A0598F3C02936D4CD162F3BA9508313 (void);
// 0x00000110 System.Void RCC_Camera::FPS()
extern void RCC_Camera_FPS_m1C0E5821B979424F9D8A4DF9E3CBAAEEF09472C4 (void);
// 0x00000111 System.Void RCC_Camera::WHEEL()
extern void RCC_Camera_WHEEL_m7D33528BA2D233331568420B039EF0ACDBEA3A1E (void);
// 0x00000112 System.Void RCC_Camera::TPS()
extern void RCC_Camera_TPS_mE0FF84544115292ABF3DDB973D25C0EEF2AF6113 (void);
// 0x00000113 System.Void RCC_Camera::FIXED()
extern void RCC_Camera_FIXED_mB5317D8747BF1668FE47796EC9A7D2C13BA63354 (void);
// 0x00000114 System.Void RCC_Camera::TOP()
extern void RCC_Camera_TOP_mD0976668EAFA911DE541CE145B00DE7E5E696E30 (void);
// 0x00000115 System.Void RCC_Camera::ORBIT()
extern void RCC_Camera_ORBIT_mCA99A660E676743DC3A014D334ABF340780457BB (void);
// 0x00000116 System.Void RCC_Camera::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void RCC_Camera_OnDrag_mA25DF21C05FE37010621343AA8E13099A61EFBEE (void);
// 0x00000117 System.Void RCC_Camera::CINEMATIC()
extern void RCC_Camera_CINEMATIC_mD5AFBCFE33D4893B990A508E8BAA11EAA8703398 (void);
// 0x00000118 System.Void RCC_Camera::Collision(UnityEngine.Collision)
extern void RCC_Camera_Collision_m9E6A630F7FD0A50B94426205B713E9C2B68B7890 (void);
// 0x00000119 System.Void RCC_Camera::ResetCamera()
extern void RCC_Camera_ResetCamera_mECBE136E8C8B657A9189C040B9E437EA72CF3E84 (void);
// 0x0000011A System.Void RCC_Camera::ToggleCamera(System.Boolean)
extern void RCC_Camera_ToggleCamera_mA41D43F5502C489A7A04CB1438FEF108C07ED021 (void);
// 0x0000011B System.Void RCC_Camera::OccludeRay(UnityEngine.Vector3)
extern void RCC_Camera_OccludeRay_m82A4010B258BA1278DEC18E508E3D68F27E11379 (void);
// 0x0000011C System.Boolean RCC_Camera::Occluding(UnityEngine.Vector3)
extern void RCC_Camera_Occluding_m80EFF6F963BA1666AB18D0B47057DC37EE852E6D (void);
// 0x0000011D System.Collections.IEnumerator RCC_Camera::AutoFocus()
extern void RCC_Camera_AutoFocus_m81900DC1EFC048D1FEB5DDC99C0EE145AEB2C2FD (void);
// 0x0000011E System.Collections.IEnumerator RCC_Camera::AutoFocus(UnityEngine.Transform)
extern void RCC_Camera_AutoFocus_mF615890DD4E3C817537453620BA1C4541FACA563 (void);
// 0x0000011F System.Collections.IEnumerator RCC_Camera::AutoFocus(UnityEngine.Transform,UnityEngine.Transform)
extern void RCC_Camera_AutoFocus_m6605134E41B5EA121A4E35112EA369D026476F3C (void);
// 0x00000120 System.Collections.IEnumerator RCC_Camera::AutoFocus(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Transform)
extern void RCC_Camera_AutoFocus_mC500C30540E4A110FFA05675A12DA784F4BB0B44 (void);
// 0x00000121 System.Void RCC_Camera::OnDisable()
extern void RCC_Camera_OnDisable_m6595E38A1D7653299AC88F493614B08B1E688EC2 (void);
// 0x00000122 System.Void RCC_Camera::.ctor()
extern void RCC_Camera__ctor_mF09251ED677A819C9B0EF26C9CA1C1BB3989A50F (void);
// 0x00000123 System.Void RCC_Camera/onBCGCameraSpawned::.ctor(System.Object,System.IntPtr)
extern void onBCGCameraSpawned__ctor_mCD9CC693BB70A14AB854D7F10AD748B6A55E0553 (void);
// 0x00000124 System.Void RCC_Camera/onBCGCameraSpawned::Invoke(UnityEngine.GameObject)
extern void onBCGCameraSpawned_Invoke_mE1829B979CE867EF6DDD538D9CA005C276E5DA75 (void);
// 0x00000125 System.IAsyncResult RCC_Camera/onBCGCameraSpawned::BeginInvoke(UnityEngine.GameObject,System.AsyncCallback,System.Object)
extern void onBCGCameraSpawned_BeginInvoke_mA3BDE1DF5564D69AAF557F100884DC0201DCD438 (void);
// 0x00000126 System.Void RCC_Camera/onBCGCameraSpawned::EndInvoke(System.IAsyncResult)
extern void onBCGCameraSpawned_EndInvoke_m035983D13FD26ABC90AF00418D468FC90B9B0325 (void);
// 0x00000127 System.Void RCC_Camera/<AutoFocus>d__103::.ctor(System.Int32)
extern void U3CAutoFocusU3Ed__103__ctor_mB86DAE1538A1817A4DE2965F70A7554F43963218 (void);
// 0x00000128 System.Void RCC_Camera/<AutoFocus>d__103::System.IDisposable.Dispose()
extern void U3CAutoFocusU3Ed__103_System_IDisposable_Dispose_m5BB60050071E0246912AB3BDEA18A1C6D632BC82 (void);
// 0x00000129 System.Boolean RCC_Camera/<AutoFocus>d__103::MoveNext()
extern void U3CAutoFocusU3Ed__103_MoveNext_m2BB8147C3BE0E20ECC7A088B987F3AD6AC48D64D (void);
// 0x0000012A System.Object RCC_Camera/<AutoFocus>d__103::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoFocusU3Ed__103_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m994C4DFCB355539E7E93873A49246D0A88AFE3B0 (void);
// 0x0000012B System.Void RCC_Camera/<AutoFocus>d__103::System.Collections.IEnumerator.Reset()
extern void U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_Reset_m9829DB058D07F760EBBFC42C7178D3C8A7CCB4F0 (void);
// 0x0000012C System.Object RCC_Camera/<AutoFocus>d__103::System.Collections.IEnumerator.get_Current()
extern void U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_get_Current_m94AE930C9BC639C3E657621CA82377820CDAB375 (void);
// 0x0000012D System.Void RCC_Camera/<AutoFocus>d__104::.ctor(System.Int32)
extern void U3CAutoFocusU3Ed__104__ctor_mFEFEB8210262FB261D9927ACB4922D120C7BE39F (void);
// 0x0000012E System.Void RCC_Camera/<AutoFocus>d__104::System.IDisposable.Dispose()
extern void U3CAutoFocusU3Ed__104_System_IDisposable_Dispose_m1E79DE15E4F22EAA324B8B6907B6DAFFFD02CBD7 (void);
// 0x0000012F System.Boolean RCC_Camera/<AutoFocus>d__104::MoveNext()
extern void U3CAutoFocusU3Ed__104_MoveNext_m7F97F41BA14A3CA3783CC56BDDAB0D5DCD0D5D28 (void);
// 0x00000130 System.Object RCC_Camera/<AutoFocus>d__104::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoFocusU3Ed__104_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3B2A71C4778AFB90E1632278C7A4BDF8AA1E33BA (void);
// 0x00000131 System.Void RCC_Camera/<AutoFocus>d__104::System.Collections.IEnumerator.Reset()
extern void U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_Reset_m019D201542FE2E5E4FF8C0ECD1E4CF7F113208C5 (void);
// 0x00000132 System.Object RCC_Camera/<AutoFocus>d__104::System.Collections.IEnumerator.get_Current()
extern void U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_get_Current_mA3C32942E6C8F8367E80DFA28F7F43687BBEC68C (void);
// 0x00000133 System.Void RCC_Camera/<AutoFocus>d__105::.ctor(System.Int32)
extern void U3CAutoFocusU3Ed__105__ctor_m857814C291E57FD0CE48F0CC72F0A75B8677C5A7 (void);
// 0x00000134 System.Void RCC_Camera/<AutoFocus>d__105::System.IDisposable.Dispose()
extern void U3CAutoFocusU3Ed__105_System_IDisposable_Dispose_m5F30716C2F6EA674FE1D07F890F36ECEC8835154 (void);
// 0x00000135 System.Boolean RCC_Camera/<AutoFocus>d__105::MoveNext()
extern void U3CAutoFocusU3Ed__105_MoveNext_m9FA030EF912674D75448CFF467A66E9042F31ED2 (void);
// 0x00000136 System.Object RCC_Camera/<AutoFocus>d__105::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoFocusU3Ed__105_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43C55302F19223A3F9E6A753C1E33495E15D5334 (void);
// 0x00000137 System.Void RCC_Camera/<AutoFocus>d__105::System.Collections.IEnumerator.Reset()
extern void U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_Reset_m37567EB21D0C21FBAF45612A28B6D990F8473682 (void);
// 0x00000138 System.Object RCC_Camera/<AutoFocus>d__105::System.Collections.IEnumerator.get_Current()
extern void U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_get_Current_m1303A4AFE8A3BEA65829C1A2ECB0A431CBC3B22D (void);
// 0x00000139 System.Void RCC_Camera/<AutoFocus>d__106::.ctor(System.Int32)
extern void U3CAutoFocusU3Ed__106__ctor_mD1116B9E48DA8B41D2403A93A8D7AA21FCD0D0AD (void);
// 0x0000013A System.Void RCC_Camera/<AutoFocus>d__106::System.IDisposable.Dispose()
extern void U3CAutoFocusU3Ed__106_System_IDisposable_Dispose_mC71F8B2F7180442670680DD6C0FA2638D844B9D6 (void);
// 0x0000013B System.Boolean RCC_Camera/<AutoFocus>d__106::MoveNext()
extern void U3CAutoFocusU3Ed__106_MoveNext_m6FE04CC110AE02F7DF0181CDBBC6DC58EDA8E74B (void);
// 0x0000013C System.Object RCC_Camera/<AutoFocus>d__106::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoFocusU3Ed__106_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB71E03BE2D95E1613CDB315DC1D0F856FD2B0CDE (void);
// 0x0000013D System.Void RCC_Camera/<AutoFocus>d__106::System.Collections.IEnumerator.Reset()
extern void U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_Reset_m72A38018ED33FC3DAB06663FA585036AB746828E (void);
// 0x0000013E System.Object RCC_Camera/<AutoFocus>d__106::System.Collections.IEnumerator.get_Current()
extern void U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_get_Current_m356DEFF51672783BA4A8008CCF6B9B8F7FB829FC (void);
// 0x0000013F System.Void RCC_CameraCarSelection::Start()
extern void RCC_CameraCarSelection_Start_mAF10156BCC6A87631917C23EB891362BBA6A4076 (void);
// 0x00000140 System.Void RCC_CameraCarSelection::LateUpdate()
extern void RCC_CameraCarSelection_LateUpdate_mBE0C17585648B0C6E83A94DA2817633F4D5FD418 (void);
// 0x00000141 System.Single RCC_CameraCarSelection::ClampAngle(System.Single,System.Single,System.Single)
extern void RCC_CameraCarSelection_ClampAngle_m4A45B0D401BBD7957850C634CBC06E8890CDE829 (void);
// 0x00000142 System.Void RCC_CameraCarSelection::OnDrag(UnityEngine.EventSystems.BaseEventData)
extern void RCC_CameraCarSelection_OnDrag_m2CFEC7930103E565B3F41DC9723B8FD0C4142260 (void);
// 0x00000143 System.Void RCC_CameraCarSelection::.ctor()
extern void RCC_CameraCarSelection__ctor_m4FDDCED0483AD16D697E78978589F4FA84A3D287 (void);
// 0x00000144 RCC_Settings RCC_CarControllerV3::get_RCCSettings()
extern void RCC_CarControllerV3_get_RCCSettings_mCDBFA9A172D3E7C2C9D1A68C827ECE9F7707F310 (void);
// 0x00000145 System.Boolean RCC_CarControllerV3::get_AIController()
extern void RCC_CarControllerV3_get_AIController_mEA0D5918A4C4F76654B014FB2B03A923F20FD525 (void);
// 0x00000146 System.Void RCC_CarControllerV3::set_AIController(System.Boolean)
extern void RCC_CarControllerV3_set_AIController_m2A8722D37ADCC364D4A5CBC398F2433B9DD01CE1 (void);
// 0x00000147 System.Boolean RCC_CarControllerV3::get_runEngineAtAwake()
extern void RCC_CarControllerV3_get_runEngineAtAwake_mDC83B91681A0DC2E801F0000C123C636188003DB (void);
// 0x00000148 System.Boolean RCC_CarControllerV3::get_autoReverse()
extern void RCC_CarControllerV3_get_autoReverse_m100E37A5915AD86940C32F70354595CFAD10CB46 (void);
// 0x00000149 System.Boolean RCC_CarControllerV3::get_automaticGear()
extern void RCC_CarControllerV3_get_automaticGear_m82A0642CC393EF678A4E19CF5A786A0D35E851BB (void);
// 0x0000014A UnityEngine.AudioClip[] RCC_CarControllerV3::get_gearShiftingClips()
extern void RCC_CarControllerV3_get_gearShiftingClips_m5DCE2052AB637E0F5DA91D390717FA9FF57BFA56 (void);
// 0x0000014B UnityEngine.AudioClip[] RCC_CarControllerV3::get_crashClips()
extern void RCC_CarControllerV3_get_crashClips_mC656B50A556AB736D9251383A99E8D5157D2FDD0 (void);
// 0x0000014C UnityEngine.AudioClip RCC_CarControllerV3::get_reversingClip()
extern void RCC_CarControllerV3_get_reversingClip_m07263F79F2015DEBA669BD4B9D0B6681885457E1 (void);
// 0x0000014D UnityEngine.AudioClip RCC_CarControllerV3::get_windClip()
extern void RCC_CarControllerV3_get_windClip_mD9FD5FF6B82BF10A1A5778F7CE94D200762A04FF (void);
// 0x0000014E UnityEngine.AudioClip RCC_CarControllerV3::get_brakeClip()
extern void RCC_CarControllerV3_get_brakeClip_m46A0347507263F5F34AA46BAF2F0138EE776701E (void);
// 0x0000014F UnityEngine.AudioClip RCC_CarControllerV3::get_NOSClip()
extern void RCC_CarControllerV3_get_NOSClip_m304C134E8FF8B02B7DECFFE51664D32069081D97 (void);
// 0x00000150 UnityEngine.AudioClip RCC_CarControllerV3::get_turboClip()
extern void RCC_CarControllerV3_get_turboClip_mCD5B2636E980FF60E5EEF73DE2F454F497FE6854 (void);
// 0x00000151 UnityEngine.AudioClip RCC_CarControllerV3::get_blowClip()
extern void RCC_CarControllerV3_get_blowClip_mF8E718EE464F3B8DECA7EF520761A79933163C5C (void);
// 0x00000152 System.Single RCC_CarControllerV3::get__gasInput()
extern void RCC_CarControllerV3_get__gasInput_mE5EB59F11AF9F7CC75F3048ABEA3CD27F268CC29 (void);
// 0x00000153 System.Void RCC_CarControllerV3::set__gasInput(System.Single)
extern void RCC_CarControllerV3_set__gasInput_m6E9A1A1A8806E1CFAD73261FB87F7DE726555DE6 (void);
// 0x00000154 System.Single RCC_CarControllerV3::get__brakeInput()
extern void RCC_CarControllerV3_get__brakeInput_m956EADB713511FC5F8FB935F170D3E00FA06D613 (void);
// 0x00000155 System.Void RCC_CarControllerV3::set__brakeInput(System.Single)
extern void RCC_CarControllerV3_set__brakeInput_m75A82ACB52CF63944831899C394DCF680AE93A0D (void);
// 0x00000156 System.Single RCC_CarControllerV3::get__boostInput()
extern void RCC_CarControllerV3_get__boostInput_m115BFAE0A59FA28E695F7312BDD3B6A355DACF00 (void);
// 0x00000157 System.Void RCC_CarControllerV3::set__boostInput(System.Single)
extern void RCC_CarControllerV3_set__boostInput_m0F16A549427E260E4C0F1A5CE2578AF60F026487 (void);
// 0x00000158 System.Single RCC_CarControllerV3::get__steerInput()
extern void RCC_CarControllerV3_get__steerInput_m6CC00B7F98A7E5B37950F69EE9A518D4B747FBAB (void);
// 0x00000159 System.Single RCC_CarControllerV3::get__counterSteerInput()
extern void RCC_CarControllerV3_get__counterSteerInput_mB39663A406EE89E8246840C12AB0032F858BB940 (void);
// 0x0000015A System.Single RCC_CarControllerV3::get__fuelInput()
extern void RCC_CarControllerV3_get__fuelInput_mCF716B8972455D609873BC43240DD74C49126054 (void);
// 0x0000015B System.Void RCC_CarControllerV3::set__fuelInput(System.Single)
extern void RCC_CarControllerV3_set__fuelInput_mEA014CAED637E84F8B36EDD76C8995A50F8A7FD4 (void);
// 0x0000015C UnityEngine.GameObject RCC_CarControllerV3::get_contactSparkle()
extern void RCC_CarControllerV3_get_contactSparkle_m2E844C494617152E301599D2A4819920BBA26726 (void);
// 0x0000015D System.Void RCC_CarControllerV3::add_OnRCCPlayerSpawned(RCC_CarControllerV3/onRCCPlayerSpawned)
extern void RCC_CarControllerV3_add_OnRCCPlayerSpawned_mFDE1F6DAD5D918C29976EE4998677077769E45A7 (void);
// 0x0000015E System.Void RCC_CarControllerV3::remove_OnRCCPlayerSpawned(RCC_CarControllerV3/onRCCPlayerSpawned)
extern void RCC_CarControllerV3_remove_OnRCCPlayerSpawned_m59CF492DA2E14D35290ECA32A6506719A0A9CCD8 (void);
// 0x0000015F System.Void RCC_CarControllerV3::add_OnRCCPlayerDestroyed(RCC_CarControllerV3/onRCCPlayerDestroyed)
extern void RCC_CarControllerV3_add_OnRCCPlayerDestroyed_m1DE2E27CCB341E870B1A3112B65F6706C3B0F03A (void);
// 0x00000160 System.Void RCC_CarControllerV3::remove_OnRCCPlayerDestroyed(RCC_CarControllerV3/onRCCPlayerDestroyed)
extern void RCC_CarControllerV3_remove_OnRCCPlayerDestroyed_m638B2F4306EDFC49B3E1B44E943E79547EEF0B04 (void);
// 0x00000161 System.Void RCC_CarControllerV3::add_OnRCCPlayerCollision(RCC_CarControllerV3/onRCCPlayerCollision)
extern void RCC_CarControllerV3_add_OnRCCPlayerCollision_m4DC0247EA3B605AFE3088CB97148583553DB95A7 (void);
// 0x00000162 System.Void RCC_CarControllerV3::remove_OnRCCPlayerCollision(RCC_CarControllerV3/onRCCPlayerCollision)
extern void RCC_CarControllerV3_remove_OnRCCPlayerCollision_m5FC92A8B329061A31DD477FCE63991D12E893771 (void);
// 0x00000163 System.Void RCC_CarControllerV3::Awake()
extern void RCC_CarControllerV3_Awake_m53FEA0A0444A0A94EE9D675334F22B7F514D27BE (void);
// 0x00000164 System.Void RCC_CarControllerV3::OnEnable()
extern void RCC_CarControllerV3_OnEnable_mBFB89A39D189BA6C53311FDBD3BF3584254600E6 (void);
// 0x00000165 System.Collections.IEnumerator RCC_CarControllerV3::RCCPlayerSpawned()
extern void RCC_CarControllerV3_RCCPlayerSpawned_mEFB68FDC59E48F6F7B81FD56D502D3EAF80B3EA7 (void);
// 0x00000166 System.Void RCC_CarControllerV3::CreateWheelColliders()
extern void RCC_CarControllerV3_CreateWheelColliders_mDFCA318677C291DF3EB045B97874A18234D3C73D (void);
// 0x00000167 System.Void RCC_CarControllerV3::CreateAudios()
extern void RCC_CarControllerV3_CreateAudios_m16916A5DC7ABEE05D8F8DE73557C3440662CFB1E (void);
// 0x00000168 System.Void RCC_CarControllerV3::CheckBehavior()
extern void RCC_CarControllerV3_CheckBehavior_m9E85618FD58698321282515F3E4E218973EAE61B (void);
// 0x00000169 System.Void RCC_CarControllerV3::CreateGearCurves()
extern void RCC_CarControllerV3_CreateGearCurves_m15049F5AD4C881B72EE061FF91BD22FB8D88682A (void);
// 0x0000016A System.Void RCC_CarControllerV3::InitDamage()
extern void RCC_CarControllerV3_InitDamage_m989ED8AEA588765A2378DEC782B08F6C5430756C (void);
// 0x0000016B System.Void RCC_CarControllerV3::KillOrStartEngine()
extern void RCC_CarControllerV3_KillOrStartEngine_mCEB8071C55A39CB21DAE9C77140AB9109EEE470B (void);
// 0x0000016C System.Void RCC_CarControllerV3::StartEngine()
extern void RCC_CarControllerV3_StartEngine_m710E3C20477266E7DDC2BAF5E3F13202F424120F (void);
// 0x0000016D System.Void RCC_CarControllerV3::StartEngine(System.Boolean)
extern void RCC_CarControllerV3_StartEngine_m8E3FA89966C1E93376CF584CDD99DC8736D898B2 (void);
// 0x0000016E System.Collections.IEnumerator RCC_CarControllerV3::StartEngineDelayed()
extern void RCC_CarControllerV3_StartEngineDelayed_m700F24D67534B52A4C3D899D4F4C0F3DE321893A (void);
// 0x0000016F System.Void RCC_CarControllerV3::KillEngine()
extern void RCC_CarControllerV3_KillEngine_m7152928FA54CF72FE7C383A5605AB4CE44D0B320 (void);
// 0x00000170 System.Void RCC_CarControllerV3::LoadOriginalMeshData()
extern void RCC_CarControllerV3_LoadOriginalMeshData_mBCF27268F78EB9D0222C684BED29CE65378D6C79 (void);
// 0x00000171 System.Void RCC_CarControllerV3::Repair()
extern void RCC_CarControllerV3_Repair_m21E1687C2D15A3ACEED4D608DAB0A1B76B9F0EB1 (void);
// 0x00000172 System.Void RCC_CarControllerV3::DeformMesh(UnityEngine.Mesh,UnityEngine.Vector3[],UnityEngine.Collision,System.Single,UnityEngine.Transform,UnityEngine.Quaternion)
extern void RCC_CarControllerV3_DeformMesh_m0B0453212B81F71003A02AC84371CD86599608BA (void);
// 0x00000173 System.Void RCC_CarControllerV3::CollisionParticles(UnityEngine.Vector3)
extern void RCC_CarControllerV3_CollisionParticles_m99B979A6BC17408075700F6DF4ED8D0B758D9744 (void);
// 0x00000174 System.Void RCC_CarControllerV3::OtherVisuals()
extern void RCC_CarControllerV3_OtherVisuals_m4E1A6BEF1BC9F70763E2DC49A9C8690C3114F34C (void);
// 0x00000175 System.Void RCC_CarControllerV3::Update()
extern void RCC_CarControllerV3_Update_m8B3480D554D99B136078EFF8B619917AFAE1FC36 (void);
// 0x00000176 System.Void RCC_CarControllerV3::Inputs()
extern void RCC_CarControllerV3_Inputs_m8CBC6C10ABDC1EB67362C4C0977A9834BDA250E3 (void);
// 0x00000177 System.Void RCC_CarControllerV3::FixedUpdate()
extern void RCC_CarControllerV3_FixedUpdate_m5FC4218C34D9E8F400D2C9D0A13B8BF3A7547E9D (void);
// 0x00000178 System.Void RCC_CarControllerV3::Engine()
extern void RCC_CarControllerV3_Engine_mEF280D545090FF4D13CED288800A9B070EF74D8C (void);
// 0x00000179 System.Void RCC_CarControllerV3::Audio()
extern void RCC_CarControllerV3_Audio_mCC94529D4C0080D69793F809D8C80ADEF8575E48 (void);
// 0x0000017A System.Void RCC_CarControllerV3::ESPCheck(System.Single)
extern void RCC_CarControllerV3_ESPCheck_mD2160BE5BE5880EF7A2F00847BD1E877994242EF (void);
// 0x0000017B System.Void RCC_CarControllerV3::EngineSounds()
extern void RCC_CarControllerV3_EngineSounds_m7FC924E62D503DE84096CCA0052327AE6AEB89F0 (void);
// 0x0000017C System.Void RCC_CarControllerV3::AntiRollBars()
extern void RCC_CarControllerV3_AntiRollBars_m70D8F72CF22CB6E12A60F1B1C71924A9D22E8B99 (void);
// 0x0000017D System.Void RCC_CarControllerV3::SteerHelper()
extern void RCC_CarControllerV3_SteerHelper_m5F2D0F1A528DBD37366886997925B49F34F628CF (void);
// 0x0000017E System.Void RCC_CarControllerV3::TractionHelper()
extern void RCC_CarControllerV3_TractionHelper_m48FE4310F94F7921B84B9122785FC64AB3E9A579 (void);
// 0x0000017F System.Void RCC_CarControllerV3::AngularDragHelper()
extern void RCC_CarControllerV3_AngularDragHelper_m0BEFD33529955616A1A2F357D6E8775A84AFFC5D (void);
// 0x00000180 System.Void RCC_CarControllerV3::Clutch()
extern void RCC_CarControllerV3_Clutch_mF11F1952AE77710CFC200E741E2357F314CF052D (void);
// 0x00000181 System.Void RCC_CarControllerV3::GearBox()
extern void RCC_CarControllerV3_GearBox_mF9C65BD5B4A33B43C9EEFE90967865B0C97D1215 (void);
// 0x00000182 System.Collections.IEnumerator RCC_CarControllerV3::ChangeGear(System.Int32)
extern void RCC_CarControllerV3_ChangeGear_m4C80019C3A3C73BC9A4B97A683246C2241563FAB (void);
// 0x00000183 System.Void RCC_CarControllerV3::GearShiftUp()
extern void RCC_CarControllerV3_GearShiftUp_mF61FBF55DD3B091130F14F4292062BE59FCE4DBB (void);
// 0x00000184 System.Void RCC_CarControllerV3::GearShiftDown()
extern void RCC_CarControllerV3_GearShiftDown_mE962E6923FA1FDD8EE8EA288C049E48CF692A21B (void);
// 0x00000185 System.Void RCC_CarControllerV3::RevLimiter()
extern void RCC_CarControllerV3_RevLimiter_m5782D84F6D00F963A4FB545EFA970F4B40D41CDB (void);
// 0x00000186 System.Void RCC_CarControllerV3::NOS()
extern void RCC_CarControllerV3_NOS_m883CD619C5E644BECF9CDCB7450AC60F9C545EDC (void);
// 0x00000187 System.Void RCC_CarControllerV3::Turbo()
extern void RCC_CarControllerV3_Turbo_mA0E1319F09F7E6A76C5657B6115B429A1A7F590D (void);
// 0x00000188 System.Void RCC_CarControllerV3::Fuel()
extern void RCC_CarControllerV3_Fuel_mAC26FD861AC2749E7854FD8B77D6E60EAC4D891F (void);
// 0x00000189 System.Void RCC_CarControllerV3::EngineHeat()
extern void RCC_CarControllerV3_EngineHeat_m63B886EF06FC10274BE75239964D69E7E78579B1 (void);
// 0x0000018A System.Void RCC_CarControllerV3::DriftVariables()
extern void RCC_CarControllerV3_DriftVariables_m061AF40A4E1AF969E16410EF0567DBA5B21927C4 (void);
// 0x0000018B System.Void RCC_CarControllerV3::ResetCar()
extern void RCC_CarControllerV3_ResetCar_m2195404FEC0A6CBF97C45109A4D78BA477262970 (void);
// 0x0000018C System.Void RCC_CarControllerV3::OnCollisionEnter(UnityEngine.Collision)
extern void RCC_CarControllerV3_OnCollisionEnter_mF2DE15CCB55FFB7E985B7ED66BD6D6A8BF2F0F56 (void);
// 0x0000018D System.Void RCC_CarControllerV3::OnDrawGizmos()
extern void RCC_CarControllerV3_OnDrawGizmos_mC28716BE267F281F0A1EC433D5381FF2CF36437E (void);
// 0x0000018E System.Void RCC_CarControllerV3::PreviewSmokeParticle(System.Boolean)
extern void RCC_CarControllerV3_PreviewSmokeParticle_m08C8027B8404453CD947CDAC1575B7E6718A8572 (void);
// 0x0000018F System.Void RCC_CarControllerV3::DetachTrailer()
extern void RCC_CarControllerV3_DetachTrailer_m96A3372053CC7CD5D9E29E4ED4A4B27D3481D74D (void);
// 0x00000190 System.Void RCC_CarControllerV3::OnDestroy()
extern void RCC_CarControllerV3_OnDestroy_m25B0439A0277FC799EEA7FB2593CC630E32CE0A0 (void);
// 0x00000191 System.Void RCC_CarControllerV3::SetCanControl(System.Boolean)
extern void RCC_CarControllerV3_SetCanControl_m6B04DB5825B76AF5E3472DD7C7AB06C961C3FA5A (void);
// 0x00000192 System.Void RCC_CarControllerV3::SetEngine(System.Boolean)
extern void RCC_CarControllerV3_SetEngine_mDE8812E1393BB803677026A45E4B7542C0CC175F (void);
// 0x00000193 System.Void RCC_CarControllerV3::OnDisable()
extern void RCC_CarControllerV3_OnDisable_mB7002423CB554475ECCC21FCE8BDCA5C37ECE362 (void);
// 0x00000194 System.Void RCC_CarControllerV3::.ctor()
extern void RCC_CarControllerV3__ctor_m4504CF01980E4734BE531C22196014D4E48DD93A (void);
// 0x00000195 System.Void RCC_CarControllerV3/Gear::SetGear(System.Single,System.Int32,System.Int32)
extern void Gear_SetGear_m3EF16D0E3272E4FB74D0AF3F80F10A991C48A6D8 (void);
// 0x00000196 System.Void RCC_CarControllerV3/Gear::.ctor()
extern void Gear__ctor_m6F26AA931752127D0F0967F79B948D3EAD4B33A7 (void);
// 0x00000197 System.Void RCC_CarControllerV3/ConfigureVehicleSubsteps::.ctor()
extern void ConfigureVehicleSubsteps__ctor_m13B0EF52A9A14ED905248C8266D79334D2D6E406 (void);
// 0x00000198 System.Void RCC_CarControllerV3/onRCCPlayerSpawned::.ctor(System.Object,System.IntPtr)
extern void onRCCPlayerSpawned__ctor_mFEC9C9C369FBC7C50EC271E2FABBDBA230CF4D46 (void);
// 0x00000199 System.Void RCC_CarControllerV3/onRCCPlayerSpawned::Invoke(RCC_CarControllerV3)
extern void onRCCPlayerSpawned_Invoke_m138CC20598836842086155A047F9C175CC4ED34B (void);
// 0x0000019A System.IAsyncResult RCC_CarControllerV3/onRCCPlayerSpawned::BeginInvoke(RCC_CarControllerV3,System.AsyncCallback,System.Object)
extern void onRCCPlayerSpawned_BeginInvoke_mBE6D89C307370AA4F8E0F3A543266D0CFE0BD5F2 (void);
// 0x0000019B System.Void RCC_CarControllerV3/onRCCPlayerSpawned::EndInvoke(System.IAsyncResult)
extern void onRCCPlayerSpawned_EndInvoke_m77D8BD364BD3B053810F2DDD79A5214C10DD52BE (void);
// 0x0000019C System.Void RCC_CarControllerV3/onRCCPlayerDestroyed::.ctor(System.Object,System.IntPtr)
extern void onRCCPlayerDestroyed__ctor_mC66C1B68FB38F1D1C83458902D774A3DDC34D5E4 (void);
// 0x0000019D System.Void RCC_CarControllerV3/onRCCPlayerDestroyed::Invoke(RCC_CarControllerV3)
extern void onRCCPlayerDestroyed_Invoke_mF7C2B06AD208C6DA012C4B5602F82C2840495E37 (void);
// 0x0000019E System.IAsyncResult RCC_CarControllerV3/onRCCPlayerDestroyed::BeginInvoke(RCC_CarControllerV3,System.AsyncCallback,System.Object)
extern void onRCCPlayerDestroyed_BeginInvoke_mD3959A42384ABFAF14EF5E91B16A8407430CA28E (void);
// 0x0000019F System.Void RCC_CarControllerV3/onRCCPlayerDestroyed::EndInvoke(System.IAsyncResult)
extern void onRCCPlayerDestroyed_EndInvoke_m5621F340B1D4B9461D171F9C0EECFEF663D62679 (void);
// 0x000001A0 System.Void RCC_CarControllerV3/onRCCPlayerCollision::.ctor(System.Object,System.IntPtr)
extern void onRCCPlayerCollision__ctor_m4030865F1DA50CB4EA548AE887A49DEA9AC1CBF8 (void);
// 0x000001A1 System.Void RCC_CarControllerV3/onRCCPlayerCollision::Invoke(RCC_CarControllerV3,UnityEngine.Collision)
extern void onRCCPlayerCollision_Invoke_m1EBC459390A4AFB8653D313EE4FE65870C7D637B (void);
// 0x000001A2 System.IAsyncResult RCC_CarControllerV3/onRCCPlayerCollision::BeginInvoke(RCC_CarControllerV3,UnityEngine.Collision,System.AsyncCallback,System.Object)
extern void onRCCPlayerCollision_BeginInvoke_mB9627E2C4F1E8A89D3FA2C245ACD18BBD2C8B5F8 (void);
// 0x000001A3 System.Void RCC_CarControllerV3/onRCCPlayerCollision::EndInvoke(System.IAsyncResult)
extern void onRCCPlayerCollision_EndInvoke_m4E4647C24B283EA15E96762B208953892BA833B6 (void);
// 0x000001A4 System.Void RCC_CarControllerV3/<RCCPlayerSpawned>d__245::.ctor(System.Int32)
extern void U3CRCCPlayerSpawnedU3Ed__245__ctor_m84F6C98EF2E1E6AB823D57CCD4F336C37681C2F3 (void);
// 0x000001A5 System.Void RCC_CarControllerV3/<RCCPlayerSpawned>d__245::System.IDisposable.Dispose()
extern void U3CRCCPlayerSpawnedU3Ed__245_System_IDisposable_Dispose_m0D02C0D246B87634EE283883DF60CC2ADEC78D47 (void);
// 0x000001A6 System.Boolean RCC_CarControllerV3/<RCCPlayerSpawned>d__245::MoveNext()
extern void U3CRCCPlayerSpawnedU3Ed__245_MoveNext_m1EEF01106E55A95EA6A80FC128C2C2E67E57037F (void);
// 0x000001A7 System.Object RCC_CarControllerV3/<RCCPlayerSpawned>d__245::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRCCPlayerSpawnedU3Ed__245_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0953BFC21C7566C1EA4C20B5F2FAFDB84CABECCC (void);
// 0x000001A8 System.Void RCC_CarControllerV3/<RCCPlayerSpawned>d__245::System.Collections.IEnumerator.Reset()
extern void U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_Reset_m9BB8908191673DFD4029B0EFD2576BAF9F4F55AD (void);
// 0x000001A9 System.Object RCC_CarControllerV3/<RCCPlayerSpawned>d__245::System.Collections.IEnumerator.get_Current()
extern void U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_get_Current_m30EE7661ACAFF1FF3B5242188997BEC07415401E (void);
// 0x000001AA System.Void RCC_CarControllerV3/<StartEngineDelayed>d__254::.ctor(System.Int32)
extern void U3CStartEngineDelayedU3Ed__254__ctor_m01C19BCD75D3AD447DDB684436968DFD8D5CF58D (void);
// 0x000001AB System.Void RCC_CarControllerV3/<StartEngineDelayed>d__254::System.IDisposable.Dispose()
extern void U3CStartEngineDelayedU3Ed__254_System_IDisposable_Dispose_m1C21109B2BD3F928F6808B8E168E9FD976B97E7F (void);
// 0x000001AC System.Boolean RCC_CarControllerV3/<StartEngineDelayed>d__254::MoveNext()
extern void U3CStartEngineDelayedU3Ed__254_MoveNext_mF0381176F06F384511831D1EEAF0DCC0D8479B38 (void);
// 0x000001AD System.Object RCC_CarControllerV3/<StartEngineDelayed>d__254::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CStartEngineDelayedU3Ed__254_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mAADA60BDC6AFDB619885F62973196BA3CA9183AE (void);
// 0x000001AE System.Void RCC_CarControllerV3/<StartEngineDelayed>d__254::System.Collections.IEnumerator.Reset()
extern void U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_Reset_mE1CE9FCC838DAB413B5F31FDC43D874722C29EA8 (void);
// 0x000001AF System.Object RCC_CarControllerV3/<StartEngineDelayed>d__254::System.Collections.IEnumerator.get_Current()
extern void U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_get_Current_m77C2F1F6912869036D311A198CDD6F30CBA657E0 (void);
// 0x000001B0 System.Void RCC_CarControllerV3/<ChangeGear>d__274::.ctor(System.Int32)
extern void U3CChangeGearU3Ed__274__ctor_m42019740291409B8CA24D09A7ED3645BDE11DF87 (void);
// 0x000001B1 System.Void RCC_CarControllerV3/<ChangeGear>d__274::System.IDisposable.Dispose()
extern void U3CChangeGearU3Ed__274_System_IDisposable_Dispose_mD457816E6E4533B0AE7AFF0E6933D83B455898D2 (void);
// 0x000001B2 System.Boolean RCC_CarControllerV3/<ChangeGear>d__274::MoveNext()
extern void U3CChangeGearU3Ed__274_MoveNext_mD6AB39BE0AC3E3310127355A13C0601BAB7FA574 (void);
// 0x000001B3 System.Object RCC_CarControllerV3/<ChangeGear>d__274::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CChangeGearU3Ed__274_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0A82C933A082D8DA6B5144E20C9B5778342B4544 (void);
// 0x000001B4 System.Void RCC_CarControllerV3/<ChangeGear>d__274::System.Collections.IEnumerator.Reset()
extern void U3CChangeGearU3Ed__274_System_Collections_IEnumerator_Reset_m6632FDBB744826C15C1B065954F1523339070A79 (void);
// 0x000001B5 System.Object RCC_CarControllerV3/<ChangeGear>d__274::System.Collections.IEnumerator.get_Current()
extern void U3CChangeGearU3Ed__274_System_Collections_IEnumerator_get_Current_mA431DD0D991872FDA3AF4F7449666D51F3057CC7 (void);
// 0x000001B6 System.Void RCC_CarSelectionExample::Start()
extern void RCC_CarSelectionExample_Start_mB666F47D68CCA912F7B2B5B391623CD2BD893F4C (void);
// 0x000001B7 System.Void RCC_CarSelectionExample::CreateVehicles()
extern void RCC_CarSelectionExample_CreateVehicles_m3A6C2AE900E2993E820E725013146FAD248138C1 (void);
// 0x000001B8 System.Void RCC_CarSelectionExample::NextVehicle()
extern void RCC_CarSelectionExample_NextVehicle_mDB0DE454927968F14A37703FDA25F0611D0530A5 (void);
// 0x000001B9 System.Void RCC_CarSelectionExample::PreviousVehicle()
extern void RCC_CarSelectionExample_PreviousVehicle_m757DDA9C4713894CB8297F77B75C457BABA82F15 (void);
// 0x000001BA System.Void RCC_CarSelectionExample::SpawnVehicle()
extern void RCC_CarSelectionExample_SpawnVehicle_m90F901211509FE210DC585AA91F6FF5B0AC9EE4E (void);
// 0x000001BB System.Void RCC_CarSelectionExample::SelectVehicle()
extern void RCC_CarSelectionExample_SelectVehicle_m16D89746A9B91DF9325C6FA2CF140EDCB26CA7F0 (void);
// 0x000001BC System.Void RCC_CarSelectionExample::DeSelectVehicle()
extern void RCC_CarSelectionExample_DeSelectVehicle_mDA026D6B2F0113348D6BC6EB09E14CB65A4F1D07 (void);
// 0x000001BD System.Void RCC_CarSelectionExample::OpenScene()
extern void RCC_CarSelectionExample_OpenScene_mC51D46333D832470677181BC14F2CC2CFD26A03F (void);
// 0x000001BE System.Void RCC_CarSelectionExample::.ctor()
extern void RCC_CarSelectionExample__ctor_m7AA85875E7E419F2123120B1A208C2A17044F748 (void);
// 0x000001BF RCC_ChangableWheels RCC_ChangableWheels::get_Instance()
extern void RCC_ChangableWheels_get_Instance_m1432C7648E53AD344BA132D7567A7A2AA6CA1394 (void);
// 0x000001C0 System.Void RCC_ChangableWheels::.ctor()
extern void RCC_ChangableWheels__ctor_mB033A1966BC74E2852C751FEC9941F0122CD560A (void);
// 0x000001C1 System.Void RCC_ChangableWheels/ChangableWheels::.ctor()
extern void ChangableWheels__ctor_mCDAFF6043C03AC3CD48BAF73D0A13848BE3FB29F (void);
// 0x000001C2 System.Void RCC_CharacterController::Start()
extern void RCC_CharacterController_Start_m5F9053945F1948F57BC357B679D8FB84531B8206 (void);
// 0x000001C3 System.Void RCC_CharacterController::Update()
extern void RCC_CharacterController_Update_mEECA4F9671CB920F4972D1F59EB4356EB89225BE (void);
// 0x000001C4 System.Void RCC_CharacterController::OnCollisionEnter(UnityEngine.Collision)
extern void RCC_CharacterController_OnCollisionEnter_m9DF0958C5C5DD6B81B8DCDE061FE521365330E94 (void);
// 0x000001C5 System.Void RCC_CharacterController::.ctor()
extern void RCC_CharacterController__ctor_mFE6593C760EB1182749A059994D8922B25CD9EFC (void);
// 0x000001C6 RCC_Settings RCC_Chassis::get_RCCSettings()
extern void RCC_Chassis_get_RCCSettings_m29AEC0DCDCF45C76F52D60D314B74B340D67A3CA (void);
// 0x000001C7 System.Void RCC_Chassis::Start()
extern void RCC_Chassis_Start_m7F3935EC7F5630AE550682073F02FF1A9D453B2F (void);
// 0x000001C8 System.Void RCC_Chassis::OnEnable()
extern void RCC_Chassis_OnEnable_m6F281396CFD5DFD70B9B2D695174837564C33D30 (void);
// 0x000001C9 System.Collections.IEnumerator RCC_Chassis::ReEnable()
extern void RCC_Chassis_ReEnable_mDC685A708AA2048130E56BAEF2A9564648B41D8B (void);
// 0x000001CA System.Void RCC_Chassis::ChassisJoint()
extern void RCC_Chassis_ChassisJoint_m8E007FCD6F955705171F1C56D47F913CEC5D5938 (void);
// 0x000001CB System.Void RCC_Chassis::Update()
extern void RCC_Chassis_Update_m77FF7FA1B3FC517D71062BB644511E51D2E9D1FA (void);
// 0x000001CC System.Void RCC_Chassis::FixedUpdate()
extern void RCC_Chassis_FixedUpdate_mD3D8C0263CE88DBA50E0E31A507A19A7400E1BBA (void);
// 0x000001CD System.Void RCC_Chassis::LegacyChassis()
extern void RCC_Chassis_LegacyChassis_m994DC9FFFAB2E1FFEA85B2DB7CC1595BEC5521A6 (void);
// 0x000001CE System.Void RCC_Chassis::.ctor()
extern void RCC_Chassis__ctor_m8BDA4FE1237567A2FED82861EF554DEE136184F7 (void);
// 0x000001CF System.Void RCC_Chassis/<ReEnable>d__11::.ctor(System.Int32)
extern void U3CReEnableU3Ed__11__ctor_mECB7864140A80DFBD981DD0D1E32A9C657A9EE46 (void);
// 0x000001D0 System.Void RCC_Chassis/<ReEnable>d__11::System.IDisposable.Dispose()
extern void U3CReEnableU3Ed__11_System_IDisposable_Dispose_mACA2471CAD921534B9E0F254D841DED400E29576 (void);
// 0x000001D1 System.Boolean RCC_Chassis/<ReEnable>d__11::MoveNext()
extern void U3CReEnableU3Ed__11_MoveNext_m7095AF2DDE6A5E8E915A116A791868FDD7C4EC63 (void);
// 0x000001D2 System.Object RCC_Chassis/<ReEnable>d__11::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CReEnableU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFA07227D312084BF51EB4BA81ECE8C173C0F0C4C (void);
// 0x000001D3 System.Void RCC_Chassis/<ReEnable>d__11::System.Collections.IEnumerator.Reset()
extern void U3CReEnableU3Ed__11_System_Collections_IEnumerator_Reset_m848803CEDDC57D5DC87E5F9B21710A13560AEABE (void);
// 0x000001D4 System.Object RCC_Chassis/<ReEnable>d__11::System.Collections.IEnumerator.get_Current()
extern void U3CReEnableU3Ed__11_System_Collections_IEnumerator_get_Current_m239E706369E3C28CF8DD3218CC0A6BFFFE82D697 (void);
// 0x000001D5 System.Void RCC_CinematicCamera::Start()
extern void RCC_CinematicCamera_Start_m200FADB3230654681C5BCF45061BD541C294B7F7 (void);
// 0x000001D6 System.Void RCC_CinematicCamera::Update()
extern void RCC_CinematicCamera_Update_m154B4EAFCFC0E39C02282E7366DEF867352BB435 (void);
// 0x000001D7 System.Void RCC_CinematicCamera::.ctor()
extern void RCC_CinematicCamera__ctor_m0158B8518EBBA2A579FA2863E1DF8D06845559EF (void);
// 0x000001D8 System.Void RCC_ColorPickerBySliders::Update()
extern void RCC_ColorPickerBySliders_Update_mDE59A4A71F2F7675E187231AB6C0C0D88F26F42D (void);
// 0x000001D9 System.Void RCC_ColorPickerBySliders::.ctor()
extern void RCC_ColorPickerBySliders__ctor_m84AD5F79DAA431F6DC6CD0C88C9BBF9CA7CFCC65 (void);
// 0x000001DA UnityEngine.AudioSource RCC_CreateAudioSource::NewAudioSource(UnityEngine.GameObject,System.String,System.Single,System.Single,System.Single,UnityEngine.AudioClip,System.Boolean,System.Boolean,System.Boolean)
extern void RCC_CreateAudioSource_NewAudioSource_mC36992ABCE916AA31908D846BB5034C52F1A74D7 (void);
// 0x000001DB System.Void RCC_CreateAudioSource::NewHighPassFilter(UnityEngine.AudioSource,System.Single,System.Int32)
extern void RCC_CreateAudioSource_NewHighPassFilter_mEE025AECA142551E55F823471411485082471205 (void);
// 0x000001DC System.Void RCC_CreateAudioSource::NewLowPassFilter(UnityEngine.AudioSource,System.Single)
extern void RCC_CreateAudioSource_NewLowPassFilter_mB3C4D21508528B7686AA0801E62269DDFBF4E573 (void);
// 0x000001DD System.Void RCC_CreateAudioSource::.ctor()
extern void RCC_CreateAudioSource__ctor_mD9913BC1B1A89502243D52A2D7B210F3ED55B908 (void);
// 0x000001DE System.Void RCC_Customization::SetCustomizationMode(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetCustomizationMode_mB21407EED2C2BE81F9E301CEAD5747D3D64EC4B6 (void);
// 0x000001DF System.Void RCC_Customization::OverrideRCC(RCC_CarControllerV3)
extern void RCC_Customization_OverrideRCC_m16E5D497E3F0720CA2E7431D7426D4F04F0B6C4B (void);
// 0x000001E0 System.Void RCC_Customization::SetSmokeParticle(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetSmokeParticle_m85F0AF2A34B76B5B36C28300BED7F7DA738495B5 (void);
// 0x000001E1 System.Void RCC_Customization::SetSmokeColor(RCC_CarControllerV3,System.Int32,UnityEngine.Color)
extern void RCC_Customization_SetSmokeColor_mCEC4BBF3AFE0001D91BBFBFC921D0943FC6A3685 (void);
// 0x000001E2 System.Void RCC_Customization::SetHeadlightsColor(RCC_CarControllerV3,UnityEngine.Color)
extern void RCC_Customization_SetHeadlightsColor_m69EF080DC8EED1821D96048DA33B60C9175303FE (void);
// 0x000001E3 System.Void RCC_Customization::SetExhaustFlame(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetExhaustFlame_m4F407654F46E4DA23CF69031EE9C25537DC6FD35 (void);
// 0x000001E4 System.Void RCC_Customization::SetFrontCambers(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetFrontCambers_mABCBE12C94C38A604979A9C070C7997030613349 (void);
// 0x000001E5 System.Void RCC_Customization::SetRearCambers(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetRearCambers_m79D2B638322F2FD8DD6F77A970B224FCEE01EAD0 (void);
// 0x000001E6 System.Void RCC_Customization::ChangeWheels(RCC_CarControllerV3,UnityEngine.GameObject)
extern void RCC_Customization_ChangeWheels_mC26333E07DD57E72952B3385AE9801593525C46D (void);
// 0x000001E7 System.Void RCC_Customization::SetFrontSuspensionsTargetPos(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetFrontSuspensionsTargetPos_m38DC13FD21B4D347EAFF3AA91E2C49D71A759557 (void);
// 0x000001E8 System.Void RCC_Customization::SetRearSuspensionsTargetPos(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetRearSuspensionsTargetPos_mA50163E62468B1845DB47E0A19F6D8B7AA2CDABB (void);
// 0x000001E9 System.Void RCC_Customization::SetAllSuspensionsTargetPos(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetAllSuspensionsTargetPos_m96A59C156C8F8307A9B474291420C0A752EA5B45 (void);
// 0x000001EA System.Void RCC_Customization::SetFrontSuspensionsDistances(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetFrontSuspensionsDistances_mB9DCCFDAAC43843216CF64BE4C9DBA23B461506E (void);
// 0x000001EB System.Void RCC_Customization::SetRearSuspensionsDistances(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetRearSuspensionsDistances_m3150E71D7ABF9A22314276199D4806E6F47F167B (void);
// 0x000001EC System.Void RCC_Customization::SetDrivetrainMode(RCC_CarControllerV3,RCC_CarControllerV3/WheelType)
extern void RCC_Customization_SetDrivetrainMode_mFF9CB44157F46D5216AA139BD12734A116D85B18 (void);
// 0x000001ED System.Void RCC_Customization::SetGearShiftingThreshold(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetGearShiftingThreshold_m47400A7D7739DFB9DF7B3458C1C70C182D1C659B (void);
// 0x000001EE System.Void RCC_Customization::SetClutchThreshold(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetClutchThreshold_m0F3D14D172B2A098527BCFE79E365E2609B07B2E (void);
// 0x000001EF System.Void RCC_Customization::SetCounterSteering(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetCounterSteering_m0A12ACA1532E2892D7D161D501E37730580D784E (void);
// 0x000001F0 System.Void RCC_Customization::SetNOS(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetNOS_m93ADE9DD307BAF7566AECB3813BCF329BEAAF642 (void);
// 0x000001F1 System.Void RCC_Customization::SetTurbo(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetTurbo_m69DEFE5A31118094CB4D1B5B5E728A6453D2AE4B (void);
// 0x000001F2 System.Void RCC_Customization::SetUseExhaustFlame(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetUseExhaustFlame_mC59D7651FC5BAD14A1EC3DCD810B5871B75C42C3 (void);
// 0x000001F3 System.Void RCC_Customization::SetRevLimiter(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetRevLimiter_m29077498C3ED16A4E9F238FC9708CE2475A7C9E1 (void);
// 0x000001F4 System.Void RCC_Customization::SetClutchMargin(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetClutchMargin_m0AF497E48310DDA25503A98668B39856669357D3 (void);
// 0x000001F5 System.Void RCC_Customization::SetFrontSuspensionsSpringForce(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetFrontSuspensionsSpringForce_m6502BE5BF85D01AF598759D50121284CE8E5E329 (void);
// 0x000001F6 System.Void RCC_Customization::SetRearSuspensionsSpringForce(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetRearSuspensionsSpringForce_m6F984D2660376923C3791F0BBF2AF29F457F138E (void);
// 0x000001F7 System.Void RCC_Customization::SetFrontSuspensionsSpringDamper(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetFrontSuspensionsSpringDamper_m344010A9CB070A685D4B14B3CAC9A1E5EC58C989 (void);
// 0x000001F8 System.Void RCC_Customization::SetRearSuspensionsSpringDamper(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetRearSuspensionsSpringDamper_m557EF5D3897031603B68CB428D6EDC1C1D7E9C15 (void);
// 0x000001F9 System.Void RCC_Customization::SetMaximumSpeed(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetMaximumSpeed_m3E3AB7F63B8F9FB33B86CDD74486BA9D5C2459AC (void);
// 0x000001FA System.Void RCC_Customization::SetMaximumTorque(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetMaximumTorque_m42AAC05E4FC61CEF6BB94DB27576D9B32B74CDBB (void);
// 0x000001FB System.Void RCC_Customization::SetMaximumBrake(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetMaximumBrake_m00914798597FADF4463448E0F3BAB804ACAC4FF5 (void);
// 0x000001FC System.Void RCC_Customization::Repair(RCC_CarControllerV3)
extern void RCC_Customization_Repair_m845E460F5D3D7F972FB596A29EDAAD860DBC3851 (void);
// 0x000001FD System.Void RCC_Customization::SetESP(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetESP_m81726A153EFBBA8ED7172AC6378C5F2011C6D21F (void);
// 0x000001FE System.Void RCC_Customization::SetABS(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetABS_mAD52A661C0665407CFE70991B0EFBD6BAF9A8467 (void);
// 0x000001FF System.Void RCC_Customization::SetTCS(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetTCS_m1C26958FC4F433BA3DF2CFE7C7F39F7C83F4930E (void);
// 0x00000200 System.Void RCC_Customization::SetSH(RCC_CarControllerV3,System.Boolean)
extern void RCC_Customization_SetSH_m84C7FC8AB439EC7ECA354118A1DCC31FB9A63929 (void);
// 0x00000201 System.Void RCC_Customization::SetSHStrength(RCC_CarControllerV3,System.Single)
extern void RCC_Customization_SetSHStrength_m3CC99D2794B5EA6D0A6EA0B87916365D7E74F778 (void);
// 0x00000202 System.Void RCC_Customization::SetTransmission(System.Boolean)
extern void RCC_Customization_SetTransmission_m7ED71A390C65BB61DE3E4F337BD8D5C6B0BAF718 (void);
// 0x00000203 System.Void RCC_Customization::SaveStats(RCC_CarControllerV3)
extern void RCC_Customization_SaveStats_mE4086FC3FF4E2B6EA0921A8235D657A2642CA403 (void);
// 0x00000204 System.Void RCC_Customization::LoadStats(RCC_CarControllerV3)
extern void RCC_Customization_LoadStats_mAD1FA3E8A8A4D3FCA4E9DB03055AD6FBF1D476C2 (void);
// 0x00000205 System.Void RCC_Customization::ResetStats(RCC_CarControllerV3,RCC_CarControllerV3)
extern void RCC_Customization_ResetStats_mE522C05D7FD5018C2A5E4F950F002A4446A7CDB4 (void);
// 0x00000206 System.Boolean RCC_Customization::CheckVehicle(RCC_CarControllerV3)
extern void RCC_Customization_CheckVehicle_m096DAC899E9F3CB14D00A5F7E7F02DD08C1F14D5 (void);
// 0x00000207 System.Void RCC_Customization::.ctor()
extern void RCC_Customization__ctor_m775183F1542C9F29A2DEB8046C0E38B22F22DDA6 (void);
// 0x00000208 RCC_CustomizerExample RCC_CustomizerExample::get_Instance()
extern void RCC_CustomizerExample_get_Instance_mC0BD17F0CC39C7C8F00B27B15BDEE0E46985146F (void);
// 0x00000209 System.Void RCC_CustomizerExample::Start()
extern void RCC_CustomizerExample_Start_mD8B56396AA537F26C89FE3D872CCF10E1450F2F3 (void);
// 0x0000020A System.Void RCC_CustomizerExample::CheckUIs()
extern void RCC_CustomizerExample_CheckUIs_m48A1A45E3868964B80EBD46D1A4AAA4ACDC77C16 (void);
// 0x0000020B System.Void RCC_CustomizerExample::OpenMenu(UnityEngine.GameObject)
extern void RCC_CustomizerExample_OpenMenu_m8007C57B631328E9BA8C2185F8FD42D27CCA274E (void);
// 0x0000020C System.Void RCC_CustomizerExample::CloseAllMenus()
extern void RCC_CustomizerExample_CloseAllMenus_m5E25D15CFF0EE1EF38CED5DBC90DDEFBAA76F8D3 (void);
// 0x0000020D System.Void RCC_CustomizerExample::SetCustomizationMode(System.Boolean)
extern void RCC_CustomizerExample_SetCustomizationMode_mC29DAE1AAC95D434F1CC36FBC7982F1F9771732D (void);
// 0x0000020E System.Void RCC_CustomizerExample::SetFrontCambersBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetFrontCambersBySlider_mEA20406B96806505F656DA433F3746CF9CDB9FD4 (void);
// 0x0000020F System.Void RCC_CustomizerExample::SetRearCambersBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetRearCambersBySlider_m470286B209535F82A7E79CEA973066AC44185A1C (void);
// 0x00000210 System.Void RCC_CustomizerExample::TogglePreviewSmokeByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_TogglePreviewSmokeByToggle_m6C84D7CA77300F96E205FC5A24736CB3CC2568F8 (void);
// 0x00000211 System.Void RCC_CustomizerExample::TogglePreviewExhaustFlameByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_TogglePreviewExhaustFlameByToggle_m47F1A3A2F9AAA0F438EF060E9384D3D27801AD73 (void);
// 0x00000212 System.Void RCC_CustomizerExample::SetSmokeColorByColorPicker(RCC_ColorPickerBySliders)
extern void RCC_CustomizerExample_SetSmokeColorByColorPicker_m3F61002E2E5B2F2B6B4A4BDDA0017B8395E50A41 (void);
// 0x00000213 System.Void RCC_CustomizerExample::SetHeadlightColorByColorPicker(RCC_ColorPickerBySliders)
extern void RCC_CustomizerExample_SetHeadlightColorByColorPicker_mCF4477604439DB0EDE7C5BD2B99EEC29DB8456CE (void);
// 0x00000214 System.Void RCC_CustomizerExample::ChangeWheelsBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_ChangeWheelsBySlider_m1D2F14FA8E69357CB8069DFCAF479C3E6ED95846 (void);
// 0x00000215 System.Void RCC_CustomizerExample::SetFrontSuspensionTargetsBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetFrontSuspensionTargetsBySlider_m129579FD24858ABD9A2A4EAD1C2C31E593F0390F (void);
// 0x00000216 System.Void RCC_CustomizerExample::SetRearSuspensionTargetsBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetRearSuspensionTargetsBySlider_mF1715159452BB3F1D38EEBB4C4F073193AEB725A (void);
// 0x00000217 System.Void RCC_CustomizerExample::SetAllSuspensionTargetsByButton(System.Single)
extern void RCC_CustomizerExample_SetAllSuspensionTargetsByButton_mFA0385AA18506BB357537F7B8BACD43EE018D264 (void);
// 0x00000218 System.Void RCC_CustomizerExample::SetFrontSuspensionDistancesBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetFrontSuspensionDistancesBySlider_mAD5D07EA0554E95629D5CEBB5845E42CB47A0140 (void);
// 0x00000219 System.Void RCC_CustomizerExample::SetRearSuspensionDistancesBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetRearSuspensionDistancesBySlider_mD0311EA43443DF9931D49AE4698391A0D0B2928C (void);
// 0x0000021A System.Void RCC_CustomizerExample::SetGearShiftingThresholdBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetGearShiftingThresholdBySlider_m977E34866A1F993673233ECA3FB6AFDF54481A6E (void);
// 0x0000021B System.Void RCC_CustomizerExample::SetClutchThresholdBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetClutchThresholdBySlider_m6448B6C59B26BBBCBCE4DBF278AD9D77697FAD85 (void);
// 0x0000021C System.Void RCC_CustomizerExample::SetDriveTrainModeByDropdown(UnityEngine.UI.Dropdown)
extern void RCC_CustomizerExample_SetDriveTrainModeByDropdown_mB42A6C5F9A3953DCA561A1A38758DC5D6DED0F21 (void);
// 0x0000021D System.Void RCC_CustomizerExample::SetCounterSteeringByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetCounterSteeringByToggle_mBF222E10590EE56697B16994D96DBF72ECC3BE18 (void);
// 0x0000021E System.Void RCC_CustomizerExample::SetNOSByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetNOSByToggle_mD7CE1462FB5F62D0200C8227A8E25D218A36D7BD (void);
// 0x0000021F System.Void RCC_CustomizerExample::SetTurboByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetTurboByToggle_mAB9965F4852812FC40DF2681B42BC3160B2F6660 (void);
// 0x00000220 System.Void RCC_CustomizerExample::SetExhaustFlameByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetExhaustFlameByToggle_mE85769DE2EA6A0C445557CA8EF39380A8B45279E (void);
// 0x00000221 System.Void RCC_CustomizerExample::SetRevLimiterByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetRevLimiterByToggle_m1B3FD85F53353F014EED396AD9B53D15C2F319A8 (void);
// 0x00000222 System.Void RCC_CustomizerExample::SetClutchMarginByToggle(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetClutchMarginByToggle_mF0F1A2BAFAB49310D90C86E1530D95635AD127FF (void);
// 0x00000223 System.Void RCC_CustomizerExample::SetFrontSuspensionsSpringForceBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetFrontSuspensionsSpringForceBySlider_m0EEEBC225E3C3FF0FFDE85D560FCDA6D67B9F831 (void);
// 0x00000224 System.Void RCC_CustomizerExample::SetRearSuspensionsSpringForceBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetRearSuspensionsSpringForceBySlider_m533BC04A0389F95E52B5AE081161EA3B47DC6281 (void);
// 0x00000225 System.Void RCC_CustomizerExample::SetFrontSuspensionsSpringDamperBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetFrontSuspensionsSpringDamperBySlider_mAF6EFD6FA360A97ECB7861CC108F9523056E688A (void);
// 0x00000226 System.Void RCC_CustomizerExample::SetRearSuspensionsSpringDamperBySlider(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetRearSuspensionsSpringDamperBySlider_mC2122474C30595AD140B0907EEE2703CEE4B9C00 (void);
// 0x00000227 System.Void RCC_CustomizerExample::SetMaximumSpeedByInputField(UnityEngine.UI.InputField)
extern void RCC_CustomizerExample_SetMaximumSpeedByInputField_mC69296738466EF2A565E1303474BAB011E9A5370 (void);
// 0x00000228 System.Void RCC_CustomizerExample::SetMaximumTorqueByInputField(UnityEngine.UI.InputField)
extern void RCC_CustomizerExample_SetMaximumTorqueByInputField_m5666F59FA145410C900DD4B078F0E48A8591C4BF (void);
// 0x00000229 System.Void RCC_CustomizerExample::SetMaximumBrakeByInputField(UnityEngine.UI.InputField)
extern void RCC_CustomizerExample_SetMaximumBrakeByInputField_m2B839DB83037AEEF7CFE6FA2EEEBB531A73F69C4 (void);
// 0x0000022A System.Void RCC_CustomizerExample::RepairCar()
extern void RCC_CustomizerExample_RepairCar_m0C3F3137F7B4EB9453EDF0C09C585A50202732A3 (void);
// 0x0000022B System.Void RCC_CustomizerExample::SetESP(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetESP_m5B1DC169028F43B743047833405151CA4700FFF7 (void);
// 0x0000022C System.Void RCC_CustomizerExample::SetABS(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetABS_m6605B703687426ACD05A85427C06423FAD755556 (void);
// 0x0000022D System.Void RCC_CustomizerExample::SetTCS(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetTCS_mB9569CF3830ED4B4F815B12F29ECA5FF735C054E (void);
// 0x0000022E System.Void RCC_CustomizerExample::SetSH(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetSH_m2345E479EA70AF763624908DF8A7033D6F7F6612 (void);
// 0x0000022F System.Void RCC_CustomizerExample::SetSHStrength(UnityEngine.UI.Slider)
extern void RCC_CustomizerExample_SetSHStrength_m75F3D19883C1271900CE546FA14F71DD02489439 (void);
// 0x00000230 System.Void RCC_CustomizerExample::SetTransmission(UnityEngine.UI.Toggle)
extern void RCC_CustomizerExample_SetTransmission_m26A539473BEBA41047EE991E374B4B1964FFA912 (void);
// 0x00000231 System.Void RCC_CustomizerExample::SaveStats()
extern void RCC_CustomizerExample_SaveStats_m20666249920C11C3A4501AFDB90BE8E184F50449 (void);
// 0x00000232 System.Void RCC_CustomizerExample::LoadStats()
extern void RCC_CustomizerExample_LoadStats_m44CA881A41ED4F802AD0E0FF9A002B37D57EF4B8 (void);
// 0x00000233 System.Void RCC_CustomizerExample::ResetStats()
extern void RCC_CustomizerExample_ResetStats_mCF27539008A82DA0146CEB73C1D1FAFC39EBE4D6 (void);
// 0x00000234 System.Single RCC_CustomizerExample::StringToFloat(System.String,System.Single)
extern void RCC_CustomizerExample_StringToFloat_m68A8C6028E977877E3044A445B316F93B54A4B4A (void);
// 0x00000235 System.Void RCC_CustomizerExample::.ctor()
extern void RCC_CustomizerExample__ctor_mD9BB8424FD544DCB2E20866F59D0E5FFF5FEA4C6 (void);
// 0x00000236 System.Void RCC_DashboardColors::Start()
extern void RCC_DashboardColors_Start_m91A1D1AC8A40A76833AB57CBAFAB887D82710CDB (void);
// 0x00000237 System.Void RCC_DashboardColors::Update()
extern void RCC_DashboardColors_Update_m8418C5DB458ED990751D5C0624FCF55A05D8CBE2 (void);
// 0x00000238 System.Void RCC_DashboardColors::.ctor()
extern void RCC_DashboardColors__ctor_mFC001C2E19A6FE19CB793FB0DA36C26A132B6B7D (void);
// 0x00000239 RCC_Settings RCC_DashboardInputs::get_RCCSettings()
extern void RCC_DashboardInputs_get_RCCSettings_mA3FB44260289184FB7C796EFEDDC102F49D7E16A (void);
// 0x0000023A System.Void RCC_DashboardInputs::Update()
extern void RCC_DashboardInputs_Update_m61DC91DB6F7EB004250957890AD8423A7B84A7F7 (void);
// 0x0000023B System.Void RCC_DashboardInputs::GetValues()
extern void RCC_DashboardInputs_GetValues_mDD39266C22C8F9DCDFF0F8C1760964BF91F6B4D4 (void);
// 0x0000023C System.Void RCC_DashboardInputs::.ctor()
extern void RCC_DashboardInputs__ctor_mCD5486A3FA8878C07BB787943AE63989220B22CB (void);
// 0x0000023D RCC_Settings RCC_DashboardObjects::get_RCCSettings()
extern void RCC_DashboardObjects_get_RCCSettings_mECAB45DA4F7742C150A4DA88CBDCEF9471764584 (void);
// 0x0000023E System.Void RCC_DashboardObjects::Awake()
extern void RCC_DashboardObjects_Awake_mD49F4E72D6820DF63F4BB01FED5F28B611235BEB (void);
// 0x0000023F System.Void RCC_DashboardObjects::Update()
extern void RCC_DashboardObjects_Update_mAC406D2166B654E7E8FD2CD156FC6E37C9DFBA11 (void);
// 0x00000240 System.Void RCC_DashboardObjects::Dials()
extern void RCC_DashboardObjects_Dials_m39D252EEB01CE142397B69CFCA8902A882A7A587 (void);
// 0x00000241 System.Void RCC_DashboardObjects::Lights()
extern void RCC_DashboardObjects_Lights_m124A5F986EBAE58672CFD71702957517598F1505 (void);
// 0x00000242 System.Void RCC_DashboardObjects::.ctor()
extern void RCC_DashboardObjects__ctor_mF2107E27015286A52B0EE47EBE24195C37092333 (void);
// 0x00000243 System.Void RCC_DashboardObjects/RPMDial::Init()
extern void RPMDial_Init_m480C52509EE6BC11F9E20508040EE7615254090E (void);
// 0x00000244 System.Void RCC_DashboardObjects/RPMDial::Update(System.Single)
extern void RPMDial_Update_mB1A49BF48FD502A0AA183C1FC2EB1BE8049F845F (void);
// 0x00000245 System.Void RCC_DashboardObjects/RPMDial::.ctor()
extern void RPMDial__ctor_m7ACD1EF70C9244AC55B55A971400F03A878EC09C (void);
// 0x00000246 System.Void RCC_DashboardObjects/SpeedoMeterDial::Init()
extern void SpeedoMeterDial_Init_mDFF7856BEB6F8AA7618CEE0E30645D13F54C0D8F (void);
// 0x00000247 System.Void RCC_DashboardObjects/SpeedoMeterDial::Update(System.Single)
extern void SpeedoMeterDial_Update_mF09E2AEE1C62DE15E542556122B256B4F57B2DC7 (void);
// 0x00000248 System.Void RCC_DashboardObjects/SpeedoMeterDial::.ctor()
extern void SpeedoMeterDial__ctor_m5233D427EC3A405047C71154B1770F166A8026C4 (void);
// 0x00000249 System.Void RCC_DashboardObjects/FuelDial::Init()
extern void FuelDial_Init_mF6C0A5D2B0EC4A2217674813DDC43F2011D5517C (void);
// 0x0000024A System.Void RCC_DashboardObjects/FuelDial::Update(System.Single)
extern void FuelDial_Update_m78BF71958D226EC068D8DADBF922BF69903CCF0E (void);
// 0x0000024B System.Void RCC_DashboardObjects/FuelDial::.ctor()
extern void FuelDial__ctor_m558702E3EDDB15691618AE65D7B4FF3348FE5FAA (void);
// 0x0000024C System.Void RCC_DashboardObjects/HeatDial::Init()
extern void HeatDial_Init_m838D801AC6AE80A2E552AFB4A8DE4C092B9A3877 (void);
// 0x0000024D System.Void RCC_DashboardObjects/HeatDial::Update(System.Single)
extern void HeatDial_Update_mF294CAD678957F861D6C71DA911CDE90B2377EA3 (void);
// 0x0000024E System.Void RCC_DashboardObjects/HeatDial::.ctor()
extern void HeatDial__ctor_mBBD5B0B43D90E632B06D15020CE2CF962696510F (void);
// 0x0000024F System.Void RCC_DashboardObjects/InteriorLight::Init()
extern void InteriorLight_Init_mCAB76A01B287782DB30257ED23EA9F7E091DED95 (void);
// 0x00000250 System.Void RCC_DashboardObjects/InteriorLight::Update(System.Boolean)
extern void InteriorLight_Update_mD9BE0CF83E9E7ED999894E1DB02127A40509F101 (void);
// 0x00000251 System.Void RCC_DashboardObjects/InteriorLight::.ctor()
extern void InteriorLight__ctor_m05F3C13C039D10E71F170872ECE1BC386FF8ED38 (void);
// 0x00000252 System.Void RCC_Demo::SelectVehicle(System.Int32)
extern void RCC_Demo_SelectVehicle_mB299F29A8837F345F3EA3945DBCD5D276F61348A (void);
// 0x00000253 System.Void RCC_Demo::Spawn()
extern void RCC_Demo_Spawn_mCBA06420E1FCB92FDDD2289703C0F81826038543 (void);
// 0x00000254 System.Void RCC_Demo::SetBehavior(System.Int32)
extern void RCC_Demo_SetBehavior_m9F0D442ADDA506610A3474B95D70AB478BB100C3 (void);
// 0x00000255 System.Void RCC_Demo::InitBehavior()
extern void RCC_Demo_InitBehavior_m23862A0A7BC95F584EEFD0E07D58E44249078C44 (void);
// 0x00000256 System.Void RCC_Demo::SetController(System.Int32)
extern void RCC_Demo_SetController_m13267B3CDE0C82146189B9D1471BF00ACD4B2FA8 (void);
// 0x00000257 System.Void RCC_Demo::SetMobileController(System.Int32)
extern void RCC_Demo_SetMobileController_mE7DD477FB9426F36A1AA51C0DB2F5D692396870C (void);
// 0x00000258 System.Void RCC_Demo::RestartScene()
extern void RCC_Demo_RestartScene_m0B02F5AC76A5289882E67E41A8CB23EEA1E8145A (void);
// 0x00000259 System.Void RCC_Demo::Quit()
extern void RCC_Demo_Quit_mD1F54A807D36E472296817B01749F5B97A0EB488 (void);
// 0x0000025A System.Void RCC_Demo::.ctor()
extern void RCC_Demo__ctor_mF84BA96194A9AFFE6E02F632EACFBE68CF83D542 (void);
// 0x0000025B RCC_Settings RCC_Exhaust::get_RCCSettings()
extern void RCC_Exhaust_get_RCCSettings_m2FA8EC962E9EEAAC51BC867093C91436BC02BB45 (void);
// 0x0000025C System.Void RCC_Exhaust::Start()
extern void RCC_Exhaust_Start_m056CCCC7799A75273E72BD76500D059B3BAFD2E0 (void);
// 0x0000025D System.Void RCC_Exhaust::Update()
extern void RCC_Exhaust_Update_mD1D8D19D2532398BB54333ED56605EAA1CA65C3B (void);
// 0x0000025E System.Void RCC_Exhaust::Smoke()
extern void RCC_Exhaust_Smoke_m8F1C65FA1E302BB583F3C13CF226490EDD2F197C (void);
// 0x0000025F System.Void RCC_Exhaust::Flame()
extern void RCC_Exhaust_Flame_m9BF70EDFCEE2DBA1B4B1EFFA115CA0168F8151E6 (void);
// 0x00000260 System.Void RCC_Exhaust::LensFlare()
extern void RCC_Exhaust_LensFlare_m1262EFAB98CB1571AFFF3E820D8B29E6A90AD01D (void);
// 0x00000261 System.Void RCC_Exhaust::.ctor()
extern void RCC_Exhaust__ctor_m344FE19DDD75AEAB35D146C7AE7CF6AAD0829992 (void);
// 0x00000262 System.Void RCC_FOVForCinematicCamera::Awake()
extern void RCC_FOVForCinematicCamera_Awake_m4D0935D7E0CE988E683FF04C844E296E34F2CE32 (void);
// 0x00000263 System.Void RCC_FOVForCinematicCamera::Update()
extern void RCC_FOVForCinematicCamera_Update_mD7D77DA149F8E4C3485C00454160494F3691E5DC (void);
// 0x00000264 System.Void RCC_FOVForCinematicCamera::.ctor()
extern void RCC_FOVForCinematicCamera__ctor_m37D54CE0725D2DD970A3D02BE18F9A4B99BB83A8 (void);
// 0x00000265 System.Void RCC_FixedCamera::LateUpdate()
extern void RCC_FixedCamera_LateUpdate_mF86C9196E84429C0ADE3B0F0EEA25660F775CC66 (void);
// 0x00000266 System.Void RCC_FixedCamera::ChangePosition()
extern void RCC_FixedCamera_ChangePosition_mEE99522F8A01F2493B19F2231AE0C093028B719F (void);
// 0x00000267 System.Void RCC_FixedCamera::.ctor()
extern void RCC_FixedCamera__ctor_mF28C621E1B092EE342A26B2793EB5CB221C7527A (void);
// 0x00000268 System.Void RCC_FuelStation::OnTriggerStay(UnityEngine.Collider)
extern void RCC_FuelStation_OnTriggerStay_m034F737EE7F58D7ABDEFF42DC33DB8F2437C6437 (void);
// 0x00000269 System.Void RCC_FuelStation::OnTriggerExit(UnityEngine.Collider)
extern void RCC_FuelStation_OnTriggerExit_m452F36C3064B46E01228ABE121D4660BEC0D8E5F (void);
// 0x0000026A System.Void RCC_FuelStation::.ctor()
extern void RCC_FuelStation__ctor_m24E5C06BD18377C90BF808C605549B0778F3033C (void);
// 0x0000026B UnityEngine.Vector3 RCC_GetBounds::GetBoundsCenter(UnityEngine.Transform)
extern void RCC_GetBounds_GetBoundsCenter_mA06F0EC89CE0EE2C59742E6548097D7A7A9DD17F (void);
// 0x0000026C System.Single RCC_GetBounds::MaxBoundsExtent(UnityEngine.Transform)
extern void RCC_GetBounds_MaxBoundsExtent_m772D4001779C2C1E6E5780438A3FB64D0C12A34A (void);
// 0x0000026D System.Void RCC_GetBounds::.ctor()
extern void RCC_GetBounds__ctor_mB86B06A870BD6B2CE7979E2B30161BDA429C392F (void);
// 0x0000026E RCC_GroundMaterials RCC_GroundMaterials::get_Instance()
extern void RCC_GroundMaterials_get_Instance_m2432235A08EFEC7D02AECD0D1DCAF0E42CD76A06 (void);
// 0x0000026F System.Void RCC_GroundMaterials::.ctor()
extern void RCC_GroundMaterials__ctor_m70D5957232EF5F879B0F114D7E6FCF89059A5563 (void);
// 0x00000270 System.Void RCC_GroundMaterials/GroundMaterialFrictions::.ctor()
extern void GroundMaterialFrictions__ctor_mA8BAD532C61F8654D75D6CB9651C2811118EF1A7 (void);
// 0x00000271 System.Void RCC_GroundMaterials/TerrainFrictions::.ctor()
extern void TerrainFrictions__ctor_m5931F5AE973277F077A0EF6DD7967D6CEB4051BF (void);
// 0x00000272 System.Void RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes::.ctor()
extern void SplatmapIndexes__ctor_m8BD8062A6DFC40DE1E643424A675B5311D01B246 (void);
// 0x00000273 System.Void RCC_HoodCamera::FixShake()
extern void RCC_HoodCamera_FixShake_m5F20A7169C11C4F55B1BE960FFBB63EA2564BB7C (void);
// 0x00000274 System.Collections.IEnumerator RCC_HoodCamera::FixShakeDelayed()
extern void RCC_HoodCamera_FixShakeDelayed_mD689A3D2A58A116F11925616EE76776FE4E90659 (void);
// 0x00000275 System.Void RCC_HoodCamera::.ctor()
extern void RCC_HoodCamera__ctor_mFEFCEF66C879608C35D2086135C2E051B408435A (void);
// 0x00000276 System.Void RCC_HoodCamera/<FixShakeDelayed>d__1::.ctor(System.Int32)
extern void U3CFixShakeDelayedU3Ed__1__ctor_mAE1EA1789C6C9E6B76669634CF665CC58511FAF5 (void);
// 0x00000277 System.Void RCC_HoodCamera/<FixShakeDelayed>d__1::System.IDisposable.Dispose()
extern void U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m059CF06692AD6C920357FDF367C809C9B6621731 (void);
// 0x00000278 System.Boolean RCC_HoodCamera/<FixShakeDelayed>d__1::MoveNext()
extern void U3CFixShakeDelayedU3Ed__1_MoveNext_m7A0FCFFCF12DF7CBA23885267E953710A8B678E2 (void);
// 0x00000279 System.Object RCC_HoodCamera/<FixShakeDelayed>d__1::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC7026D71FC9F1BF49D5C0A39EFE43CFBBFAF5ABA (void);
// 0x0000027A System.Void RCC_HoodCamera/<FixShakeDelayed>d__1::System.Collections.IEnumerator.Reset()
extern void U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m7675E5E8EE65BB02E4CE3266F68749E5AD3EC311 (void);
// 0x0000027B System.Object RCC_HoodCamera/<FixShakeDelayed>d__1::System.Collections.IEnumerator.get_Current()
extern void U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m6465D6EB3B8B010CF242BC740AE221BF2F76BE8D (void);
// 0x0000027C RCC_InfoLabel RCC_InfoLabel::get_Instance()
extern void RCC_InfoLabel_get_Instance_m2F940CAF55B6142B75208A274F1E927EFE04C104 (void);
// 0x0000027D System.Void RCC_InfoLabel::Start()
extern void RCC_InfoLabel_Start_mC5DFB14080B65A95C0E102D913D3A246BCE2A19F (void);
// 0x0000027E System.Void RCC_InfoLabel::Update()
extern void RCC_InfoLabel_Update_m548B3DDF885CA3702980B14ECF8F08841683498B (void);
// 0x0000027F System.Void RCC_InfoLabel::ShowInfo(System.String)
extern void RCC_InfoLabel_ShowInfo_m2B69056C9065B79C9E4E443690825AF3F058226E (void);
// 0x00000280 System.Collections.IEnumerator RCC_InfoLabel::ShowInfoCo(System.String,System.Single)
extern void RCC_InfoLabel_ShowInfoCo_m66C62CE2BCD816FD11A849C7150D82225C8EBABB (void);
// 0x00000281 System.Void RCC_InfoLabel::.ctor()
extern void RCC_InfoLabel__ctor_m630AF3434E3508995BA771BCE75E8919F2094B49 (void);
// 0x00000282 System.Void RCC_InfoLabel/<ShowInfoCo>d__8::.ctor(System.Int32)
extern void U3CShowInfoCoU3Ed__8__ctor_m754A108198109E44982129332FE0725F84334DBF (void);
// 0x00000283 System.Void RCC_InfoLabel/<ShowInfoCo>d__8::System.IDisposable.Dispose()
extern void U3CShowInfoCoU3Ed__8_System_IDisposable_Dispose_m26676BBDB19173609B45E7F970C02E28E6259337 (void);
// 0x00000284 System.Boolean RCC_InfoLabel/<ShowInfoCo>d__8::MoveNext()
extern void U3CShowInfoCoU3Ed__8_MoveNext_mF114E3263812BFFE4AE76A6B2C28AD64B8F9597F (void);
// 0x00000285 System.Object RCC_InfoLabel/<ShowInfoCo>d__8::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CShowInfoCoU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7B0CEAF5DD5A83C6EC8337121F9CA17390640609 (void);
// 0x00000286 System.Void RCC_InfoLabel/<ShowInfoCo>d__8::System.Collections.IEnumerator.Reset()
extern void U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_Reset_m4AF8C4E672A789A3B4A328D5FDEC475DB50B9611 (void);
// 0x00000287 System.Object RCC_InfoLabel/<ShowInfoCo>d__8::System.Collections.IEnumerator.get_Current()
extern void U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_get_Current_m1ADE673386CC8EADC5576419114A9AF413936423 (void);
// 0x00000288 System.Void RCC_LevelLoader::LoadLevel(System.String)
extern void RCC_LevelLoader_LoadLevel_mF977352CA3EBB12E29B508B43885045955BD9854 (void);
// 0x00000289 System.Void RCC_LevelLoader::.ctor()
extern void RCC_LevelLoader__ctor_m8E7A2BD784D3B5EA92546557E85D23D1856BEEA0 (void);
// 0x0000028A RCC_Settings RCC_Light::get_RCCSettings()
extern void RCC_Light_get_RCCSettings_mB8A2EBCD8794E7B5FAE1E793F1AC4713229A1921 (void);
// 0x0000028B UnityEngine.AudioClip RCC_Light::get_indicatorClip()
extern void RCC_Light_get_indicatorClip_mB85AFB29F3869DACF5703C9C921BFBFF471804EE (void);
// 0x0000028C System.Void RCC_Light::Start()
extern void RCC_Light_Start_mC1C8360A32DB4F97AB0403198900595DE03A9399 (void);
// 0x0000028D System.Void RCC_Light::OnEnable()
extern void RCC_Light_OnEnable_mE839EDFEE8ED38B64FFA1577994A82556E5A82AC (void);
// 0x0000028E System.Void RCC_Light::Update()
extern void RCC_Light_Update_mF3A31B32019AEA4B105F03422CEFB7563AB48864 (void);
// 0x0000028F System.Void RCC_Light::Lighting(System.Single)
extern void RCC_Light_Lighting_mD642DE422D6D8D43AD8B44A4005BE2D860F21BC6 (void);
// 0x00000290 System.Void RCC_Light::Lighting(System.Single,System.Single,System.Single)
extern void RCC_Light_Lighting_m6FB624B9CC71BCA5788FBAC0989ED1878DF5D586 (void);
// 0x00000291 System.Void RCC_Light::Indicators()
extern void RCC_Light_Indicators_m39A1F774499D6655E24CEFB2EC934C02D49A6F8C (void);
// 0x00000292 System.Void RCC_Light::Projectors()
extern void RCC_Light_Projectors_mE1084BC7BF5D14695C180627EAB51B9D94DB3AA9 (void);
// 0x00000293 System.Void RCC_Light::LensFlare()
extern void RCC_Light_LensFlare_mEEE63CEF0292150209647DB2BF8E0068C15B41BE (void);
// 0x00000294 System.Void RCC_Light::CheckRotation()
extern void RCC_Light_CheckRotation_mBB4A78572992DEBA657B13FA0F271B6F144D0B70 (void);
// 0x00000295 System.Void RCC_Light::CheckLensFlare()
extern void RCC_Light_CheckLensFlare_m224C46CCD0868D8EC4AEC18A2256FFBBED7345EB (void);
// 0x00000296 System.Void RCC_Light::Reset()
extern void RCC_Light_Reset_m0830E2B20E27297597017FC11FA78E151D724A62 (void);
// 0x00000297 System.Void RCC_Light::.ctor()
extern void RCC_Light__ctor_m3BB065845035C6EF283F4C37AD15F9E823A3A83C (void);
// 0x00000298 System.Void RCC_LightEmission::Start()
extern void RCC_LightEmission_Start_m5111E3C7D886430FFA7CA3B52EBFB2BBFE4507C6 (void);
// 0x00000299 System.Void RCC_LightEmission::Update()
extern void RCC_LightEmission_Update_m0A678CDD7C1A560E79E0154591CFC1C2BB2299E4 (void);
// 0x0000029A System.Void RCC_LightEmission::.ctor()
extern void RCC_LightEmission__ctor_m76ED95337017B70D4DB545B75EEA3CE2AF35A8E9 (void);
// 0x0000029B System.Void RCC_Mirror::Awake()
extern void RCC_Mirror_Awake_mD4F77D9C4FFDC20A0099013D7E02275330DBA9F2 (void);
// 0x0000029C System.Void RCC_Mirror::OnEnable()
extern void RCC_Mirror_OnEnable_m7FA0E28A62BEC7FFAB8F0F3A5580F0BC727DB99D (void);
// 0x0000029D System.Collections.IEnumerator RCC_Mirror::FixDepth()
extern void RCC_Mirror_FixDepth_m6D176AE51609272DAA74B49F80D521584B9E2ADC (void);
// 0x0000029E System.Void RCC_Mirror::InvertCamera()
extern void RCC_Mirror_InvertCamera_m76A3E128AB2ED2D6A02404B55981EC06DE5A8CFF (void);
// 0x0000029F System.Void RCC_Mirror::OnPreRender()
extern void RCC_Mirror_OnPreRender_m7FCF32DB446B83566741DF0C508F8F3F2E0EC116 (void);
// 0x000002A0 System.Void RCC_Mirror::OnPostRender()
extern void RCC_Mirror_OnPostRender_mD5BCEBE0F4A66FBE044B6B2E0073BB11A36A6131 (void);
// 0x000002A1 System.Void RCC_Mirror::Update()
extern void RCC_Mirror_Update_mCDE47AB564A4C35B25B886574AB8C92D54B42BEF (void);
// 0x000002A2 System.Void RCC_Mirror::.ctor()
extern void RCC_Mirror__ctor_m8B91C0BF392ECD86520BAE05CC3B99CA4B373CC9 (void);
// 0x000002A3 System.Void RCC_Mirror/<FixDepth>d__4::.ctor(System.Int32)
extern void U3CFixDepthU3Ed__4__ctor_mFD7A770C3B171A8A9EAE242A7BDAB7756F588309 (void);
// 0x000002A4 System.Void RCC_Mirror/<FixDepth>d__4::System.IDisposable.Dispose()
extern void U3CFixDepthU3Ed__4_System_IDisposable_Dispose_mF0F38EF8288B4076B7D0573E37B2B46DD1D4025F (void);
// 0x000002A5 System.Boolean RCC_Mirror/<FixDepth>d__4::MoveNext()
extern void U3CFixDepthU3Ed__4_MoveNext_m6EFCA0F6168F3FB3956A8DDBF583AF24CA9D3A75 (void);
// 0x000002A6 System.Object RCC_Mirror/<FixDepth>d__4::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CFixDepthU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m359B793B68B60ED3881A48997B33E23942CA5184 (void);
// 0x000002A7 System.Void RCC_Mirror/<FixDepth>d__4::System.Collections.IEnumerator.Reset()
extern void U3CFixDepthU3Ed__4_System_Collections_IEnumerator_Reset_m389126C8BBAE3FFD380EE405A68318CC88493E15 (void);
// 0x000002A8 System.Object RCC_Mirror/<FixDepth>d__4::System.Collections.IEnumerator.get_Current()
extern void U3CFixDepthU3Ed__4_System_Collections_IEnumerator_get_Current_mE23AD3527CBF07E20752D481DFD3DB2D0EEC3A47 (void);
// 0x000002A9 RCC_Settings RCC_MobileButtons::get_RCCSettings()
extern void RCC_MobileButtons_get_RCCSettings_m2DAD66334247F36E942C69873CA28C5336F9523F (void);
// 0x000002AA System.Void RCC_MobileButtons::Start()
extern void RCC_MobileButtons_Start_m5CA20F0EC4EE67317D7E0B996C1185038A5BBE78 (void);
// 0x000002AB System.Void RCC_MobileButtons::OnEnable()
extern void RCC_MobileButtons_OnEnable_mC10739CFB1A45B3A14BBA646AB3DE676283C5B46 (void);
// 0x000002AC System.Void RCC_MobileButtons::CheckController()
extern void RCC_MobileButtons_CheckController_m637D8126FA8ACC9C32249C4559FC8BACF0F4D80A (void);
// 0x000002AD System.Void RCC_MobileButtons::DisableButtons()
extern void RCC_MobileButtons_DisableButtons_mC74EA3BE84F1D078BD7D23B3B846C74A3E210D44 (void);
// 0x000002AE System.Void RCC_MobileButtons::EnableButtons()
extern void RCC_MobileButtons_EnableButtons_m2B285E2CC6A134770E3DADF42F28A9A63F29F919 (void);
// 0x000002AF System.Void RCC_MobileButtons::Update()
extern void RCC_MobileButtons_Update_m425C61845F8A00A1BEE51675D4E4D8F680DF011A (void);
// 0x000002B0 System.Void RCC_MobileButtons::FeedRCC()
extern void RCC_MobileButtons_FeedRCC_m50BEB15AAD3074DF380CE97347CF4F9FC87E980A (void);
// 0x000002B1 System.Single RCC_MobileButtons::GetInput(RCC_UIController)
extern void RCC_MobileButtons_GetInput_mA3554B5CA109CB6BAC816C4F627C877F77E7B55E (void);
// 0x000002B2 System.Void RCC_MobileButtons::OnDisable()
extern void RCC_MobileButtons_OnDisable_m33A8BD4C1A7F9CA6AFF57392BCA667E7A65EF980 (void);
// 0x000002B3 System.Void RCC_MobileButtons::.ctor()
extern void RCC_MobileButtons__ctor_mA1756735EA6966E27E489A5C419261FE0EC0FD96 (void);
// 0x000002B4 System.Void RCC_MobileUIDrag::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void RCC_MobileUIDrag_OnDrag_m2B4BDD6C616AD12B1EAA57F931A27C78B5D95CF4 (void);
// 0x000002B5 System.Void RCC_MobileUIDrag::OnEndDrag(UnityEngine.EventSystems.PointerEventData)
extern void RCC_MobileUIDrag_OnEndDrag_m2C4589DC827997367BFACCBC12B2E8E4B0254988 (void);
// 0x000002B6 System.Void RCC_MobileUIDrag::.ctor()
extern void RCC_MobileUIDrag__ctor_mF872CA93C2A0E193D0D621AC79E52692618685A9 (void);
// 0x000002B7 System.Boolean RCC_PlayerPrefsX::SetBool(System.String,System.Boolean)
extern void RCC_PlayerPrefsX_SetBool_mE31AA7375593FEB4FA4FF93181AFBC668F34832C (void);
// 0x000002B8 System.Boolean RCC_PlayerPrefsX::GetBool(System.String)
extern void RCC_PlayerPrefsX_GetBool_m3F72A8C452741F401F2E7D245F7CC90374438114 (void);
// 0x000002B9 System.Boolean RCC_PlayerPrefsX::GetBool(System.String,System.Boolean)
extern void RCC_PlayerPrefsX_GetBool_mAEE188942341B7E2D1B1F706914CCCACA5D2288F (void);
// 0x000002BA System.Int64 RCC_PlayerPrefsX::GetLong(System.String,System.Int64)
extern void RCC_PlayerPrefsX_GetLong_mD99F0DB020D12D63FE5E20B2A35950185AF11903 (void);
// 0x000002BB System.Int64 RCC_PlayerPrefsX::GetLong(System.String)
extern void RCC_PlayerPrefsX_GetLong_mD60F03550EAF23F2E8CC48E10127EA55CDEE8406 (void);
// 0x000002BC System.Void RCC_PlayerPrefsX::SplitLong(System.Int64,System.Int32&,System.Int32&)
extern void RCC_PlayerPrefsX_SplitLong_m80FADDDB9B15DDDD99A9A2388F7D22B7508AB3A6 (void);
// 0x000002BD System.Void RCC_PlayerPrefsX::SetLong(System.String,System.Int64)
extern void RCC_PlayerPrefsX_SetLong_m1C716045DC4402199F550296332ED93B2A0912A0 (void);
// 0x000002BE System.Boolean RCC_PlayerPrefsX::SetVector2(System.String,UnityEngine.Vector2)
extern void RCC_PlayerPrefsX_SetVector2_mDD1B16AFB418D2BD4933A14D539BA9E22F98A00F (void);
// 0x000002BF UnityEngine.Vector2 RCC_PlayerPrefsX::GetVector2(System.String)
extern void RCC_PlayerPrefsX_GetVector2_m5C3531F704E3E88792D9E864F3DD7019EEC0D4FE (void);
// 0x000002C0 UnityEngine.Vector2 RCC_PlayerPrefsX::GetVector2(System.String,UnityEngine.Vector2)
extern void RCC_PlayerPrefsX_GetVector2_m630CFA614DE9480F24E96818CDB033465E279FE3 (void);
// 0x000002C1 System.Boolean RCC_PlayerPrefsX::SetVector3(System.String,UnityEngine.Vector3)
extern void RCC_PlayerPrefsX_SetVector3_m08B840E23545E02C46780BE77396C624F37B0257 (void);
// 0x000002C2 UnityEngine.Vector3 RCC_PlayerPrefsX::GetVector3(System.String)
extern void RCC_PlayerPrefsX_GetVector3_m1548B065FD14355A29BE67D0CDE22D3C036BA1C9 (void);
// 0x000002C3 UnityEngine.Vector3 RCC_PlayerPrefsX::GetVector3(System.String,UnityEngine.Vector3)
extern void RCC_PlayerPrefsX_GetVector3_m24EBF90B73E5C5F5C6E93A4EC8193FBD3271389D (void);
// 0x000002C4 System.Boolean RCC_PlayerPrefsX::SetQuaternion(System.String,UnityEngine.Quaternion)
extern void RCC_PlayerPrefsX_SetQuaternion_m1779E436C03BC48FAB7DE3D85C4088F0B5660493 (void);
// 0x000002C5 UnityEngine.Quaternion RCC_PlayerPrefsX::GetQuaternion(System.String)
extern void RCC_PlayerPrefsX_GetQuaternion_mE169542779B1D0E4CDB3799A10D48EB70D39740E (void);
// 0x000002C6 UnityEngine.Quaternion RCC_PlayerPrefsX::GetQuaternion(System.String,UnityEngine.Quaternion)
extern void RCC_PlayerPrefsX_GetQuaternion_m1A0756CEA9B9D783B8E7077DBF1EB8B9C335B650 (void);
// 0x000002C7 System.Boolean RCC_PlayerPrefsX::SetColor(System.String,UnityEngine.Color)
extern void RCC_PlayerPrefsX_SetColor_m4DB9959CBF456552287A0C869149334E94182882 (void);
// 0x000002C8 UnityEngine.Color RCC_PlayerPrefsX::GetColor(System.String)
extern void RCC_PlayerPrefsX_GetColor_m1B577556682D142BC97D1A4E7DC951C206EA3040 (void);
// 0x000002C9 UnityEngine.Color RCC_PlayerPrefsX::GetColor(System.String,UnityEngine.Color)
extern void RCC_PlayerPrefsX_GetColor_m9ABA157532A68B4DB701F674D94A35995660B036 (void);
// 0x000002CA System.Boolean RCC_PlayerPrefsX::SetBoolArray(System.String,System.Boolean[])
extern void RCC_PlayerPrefsX_SetBoolArray_m54707B241312CFFB71ED93E61132EBA2FDBDCEBE (void);
// 0x000002CB System.Boolean[] RCC_PlayerPrefsX::GetBoolArray(System.String)
extern void RCC_PlayerPrefsX_GetBoolArray_mA208C21D6BF9A8AFA7BF30A50B2D5ECBA4C73BBD (void);
// 0x000002CC System.Boolean[] RCC_PlayerPrefsX::GetBoolArray(System.String,System.Boolean,System.Int32)
extern void RCC_PlayerPrefsX_GetBoolArray_m6D44B65B6B18B08374E7FF172150B7BD2281B8F0 (void);
// 0x000002CD System.Boolean RCC_PlayerPrefsX::SetStringArray(System.String,System.String[])
extern void RCC_PlayerPrefsX_SetStringArray_mD99F60543FB71596CF87D0F9AEE0615A721FF00B (void);
// 0x000002CE System.String[] RCC_PlayerPrefsX::GetStringArray(System.String)
extern void RCC_PlayerPrefsX_GetStringArray_m8665AD060CF748F2C9E5C581A6A5CFFB0A1666F0 (void);
// 0x000002CF System.String[] RCC_PlayerPrefsX::GetStringArray(System.String,System.String,System.Int32)
extern void RCC_PlayerPrefsX_GetStringArray_m0161E44CB15F5878D2D667EEB3D927FFF2242B83 (void);
// 0x000002D0 System.Boolean RCC_PlayerPrefsX::SetIntArray(System.String,System.Int32[])
extern void RCC_PlayerPrefsX_SetIntArray_mE0D89D3B0758524A410743A5711220870AF52207 (void);
// 0x000002D1 System.Boolean RCC_PlayerPrefsX::SetFloatArray(System.String,System.Single[])
extern void RCC_PlayerPrefsX_SetFloatArray_m5AE32B1EE29512841052D8316634AEDFF7F6C80E (void);
// 0x000002D2 System.Boolean RCC_PlayerPrefsX::SetVector2Array(System.String,UnityEngine.Vector2[])
extern void RCC_PlayerPrefsX_SetVector2Array_mFF0C5B1AF6B9CCB305E219F8617BAC219EE959BE (void);
// 0x000002D3 System.Boolean RCC_PlayerPrefsX::SetVector3Array(System.String,UnityEngine.Vector3[])
extern void RCC_PlayerPrefsX_SetVector3Array_m877176118EB5A0402EA07AE9E7170640A3FF765D (void);
// 0x000002D4 System.Boolean RCC_PlayerPrefsX::SetQuaternionArray(System.String,UnityEngine.Quaternion[])
extern void RCC_PlayerPrefsX_SetQuaternionArray_m864DE32F58A22DCD657450BC311DB2AFC08ADCE5 (void);
// 0x000002D5 System.Boolean RCC_PlayerPrefsX::SetColorArray(System.String,UnityEngine.Color[])
extern void RCC_PlayerPrefsX_SetColorArray_m05ED653BD5854949D74A3FD65919DD82E655285E (void);
// 0x000002D6 System.Boolean RCC_PlayerPrefsX::SetValue(System.String,T,RCC_PlayerPrefsX/ArrayType,System.Int32,System.Action`3<T,System.Byte[],System.Int32>)
// 0x000002D7 System.Void RCC_PlayerPrefsX::ConvertFromInt(System.Int32[],System.Byte[],System.Int32)
extern void RCC_PlayerPrefsX_ConvertFromInt_mA8968FBB6471D05149A529DF8EE17707080101BC (void);
// 0x000002D8 System.Void RCC_PlayerPrefsX::ConvertFromFloat(System.Single[],System.Byte[],System.Int32)
extern void RCC_PlayerPrefsX_ConvertFromFloat_mDB8D6ADB50BFC115E1B3264B5FB74B599093F624 (void);
// 0x000002D9 System.Void RCC_PlayerPrefsX::ConvertFromVector2(UnityEngine.Vector2[],System.Byte[],System.Int32)
extern void RCC_PlayerPrefsX_ConvertFromVector2_m0FB437DBCEAA22E4C29853AC0D4960E07C5B65DC (void);
// 0x000002DA System.Void RCC_PlayerPrefsX::ConvertFromVector3(UnityEngine.Vector3[],System.Byte[],System.Int32)
extern void RCC_PlayerPrefsX_ConvertFromVector3_m8B3174CAA757F33156C2501E2A30FD210EE152EC (void);
// 0x000002DB System.Void RCC_PlayerPrefsX::ConvertFromQuaternion(UnityEngine.Quaternion[],System.Byte[],System.Int32)
extern void RCC_PlayerPrefsX_ConvertFromQuaternion_mA753C6F5672E7769421E8750CAE32CC83060DFC0 (void);
// 0x000002DC System.Void RCC_PlayerPrefsX::ConvertFromColor(UnityEngine.Color[],System.Byte[],System.Int32)
extern void RCC_PlayerPrefsX_ConvertFromColor_mBD1C77B545A3EF8920D5BE52557C4F6E72252684 (void);
// 0x000002DD System.Int32[] RCC_PlayerPrefsX::GetIntArray(System.String)
extern void RCC_PlayerPrefsX_GetIntArray_mCCC3EB0DBE6E982EDA9B769896A2DDAA893A01CC (void);
// 0x000002DE System.Int32[] RCC_PlayerPrefsX::GetIntArray(System.String,System.Int32,System.Int32)
extern void RCC_PlayerPrefsX_GetIntArray_mD6CFAD33608973502C32D658A0F72A00F812539E (void);
// 0x000002DF System.Single[] RCC_PlayerPrefsX::GetFloatArray(System.String)
extern void RCC_PlayerPrefsX_GetFloatArray_m7AC31050689B2A52E3B92C86F257DF0C36781868 (void);
// 0x000002E0 System.Single[] RCC_PlayerPrefsX::GetFloatArray(System.String,System.Single,System.Int32)
extern void RCC_PlayerPrefsX_GetFloatArray_m8B909AC57A1FC76DA8A07F0A9FD4DC5450F82120 (void);
// 0x000002E1 UnityEngine.Vector2[] RCC_PlayerPrefsX::GetVector2Array(System.String)
extern void RCC_PlayerPrefsX_GetVector2Array_m558A9731CB45B71403F1497F0136FBCF525DE7BB (void);
// 0x000002E2 UnityEngine.Vector2[] RCC_PlayerPrefsX::GetVector2Array(System.String,UnityEngine.Vector2,System.Int32)
extern void RCC_PlayerPrefsX_GetVector2Array_m2F34414ED17E1CA5139A03EC5F1A0DC944EEE27A (void);
// 0x000002E3 UnityEngine.Vector3[] RCC_PlayerPrefsX::GetVector3Array(System.String)
extern void RCC_PlayerPrefsX_GetVector3Array_m6A4D14D744B6CEB930AD094E024CEC6B1916C23B (void);
// 0x000002E4 UnityEngine.Vector3[] RCC_PlayerPrefsX::GetVector3Array(System.String,UnityEngine.Vector3,System.Int32)
extern void RCC_PlayerPrefsX_GetVector3Array_m10E004C1DF687007969D8082DC0F38F0FB998BDA (void);
// 0x000002E5 UnityEngine.Quaternion[] RCC_PlayerPrefsX::GetQuaternionArray(System.String)
extern void RCC_PlayerPrefsX_GetQuaternionArray_m965181F10B0DCBB29564109EA741D70565D38B10 (void);
// 0x000002E6 UnityEngine.Quaternion[] RCC_PlayerPrefsX::GetQuaternionArray(System.String,UnityEngine.Quaternion,System.Int32)
extern void RCC_PlayerPrefsX_GetQuaternionArray_m773BFD28132B20D0A64BC3A37A04EB9FFAFDF0D2 (void);
// 0x000002E7 UnityEngine.Color[] RCC_PlayerPrefsX::GetColorArray(System.String)
extern void RCC_PlayerPrefsX_GetColorArray_mA14E70A9D77C09149A698826F55180DC5EC13611 (void);
// 0x000002E8 UnityEngine.Color[] RCC_PlayerPrefsX::GetColorArray(System.String,UnityEngine.Color,System.Int32)
extern void RCC_PlayerPrefsX_GetColorArray_mB8846910B1CE083E8B6844B95577A1DAA0B6CE59 (void);
// 0x000002E9 System.Void RCC_PlayerPrefsX::GetValue(System.String,T,RCC_PlayerPrefsX/ArrayType,System.Int32,System.Action`2<T,System.Byte[]>)
// 0x000002EA System.Void RCC_PlayerPrefsX::ConvertToInt(System.Collections.Generic.List`1<System.Int32>,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertToInt_m4B1F02AE1866A10E3BE5D833DDD034153B96A398 (void);
// 0x000002EB System.Void RCC_PlayerPrefsX::ConvertToFloat(System.Collections.Generic.List`1<System.Single>,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertToFloat_mC597D49EED5A9DECE0F27081E1CE0C9B5069C0C4 (void);
// 0x000002EC System.Void RCC_PlayerPrefsX::ConvertToVector2(System.Collections.Generic.List`1<UnityEngine.Vector2>,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertToVector2_m0F995A60499F49329D747E3705C4065E6B9DD927 (void);
// 0x000002ED System.Void RCC_PlayerPrefsX::ConvertToVector3(System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertToVector3_mDC4F26769971C5A047B6ACC7DEC4B168B3E9F885 (void);
// 0x000002EE System.Void RCC_PlayerPrefsX::ConvertToQuaternion(System.Collections.Generic.List`1<UnityEngine.Quaternion>,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertToQuaternion_m4F1E6FAF695B0E7379DBC4E57DE5B9F74C45FA66 (void);
// 0x000002EF System.Void RCC_PlayerPrefsX::ConvertToColor(System.Collections.Generic.List`1<UnityEngine.Color>,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertToColor_m04007E380B4233C0C934F6641BB7284B3DF497D0 (void);
// 0x000002F0 System.Void RCC_PlayerPrefsX::ShowArrayType(System.String)
extern void RCC_PlayerPrefsX_ShowArrayType_m521E11466BBE682F81CD05D3AC086DFB62FB068B (void);
// 0x000002F1 System.Void RCC_PlayerPrefsX::Initialize()
extern void RCC_PlayerPrefsX_Initialize_m2A6F4A055799DE31B4FC860FA0E97577D18BF79A (void);
// 0x000002F2 System.Boolean RCC_PlayerPrefsX::SaveBytes(System.String,System.Byte[])
extern void RCC_PlayerPrefsX_SaveBytes_mF37BA0AB00CB10A0CCAACA88D258FA8A89190408 (void);
// 0x000002F3 System.Void RCC_PlayerPrefsX::ConvertFloatToBytes(System.Single,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertFloatToBytes_mCF184A3E49C9BAA4E1E3DC7CEB84266375A96957 (void);
// 0x000002F4 System.Single RCC_PlayerPrefsX::ConvertBytesToFloat(System.Byte[])
extern void RCC_PlayerPrefsX_ConvertBytesToFloat_mF85EA421D915C1BCE2C198738462578584E89E9E (void);
// 0x000002F5 System.Void RCC_PlayerPrefsX::ConvertInt32ToBytes(System.Int32,System.Byte[])
extern void RCC_PlayerPrefsX_ConvertInt32ToBytes_m40F466D3AB83B4F34BED3F34BB769A3C4BDD8BC2 (void);
// 0x000002F6 System.Int32 RCC_PlayerPrefsX::ConvertBytesToInt32(System.Byte[])
extern void RCC_PlayerPrefsX_ConvertBytesToInt32_mAC07103B3C93C8BAE0E460F3AC6D30128F3A0D32 (void);
// 0x000002F7 System.Void RCC_PlayerPrefsX::ConvertTo4Bytes(System.Byte[])
extern void RCC_PlayerPrefsX_ConvertTo4Bytes_mDB053E0739B3F562510D1B69A4E1AA13C686363D (void);
// 0x000002F8 System.Void RCC_PlayerPrefsX::ConvertFrom4Bytes(System.Byte[])
extern void RCC_PlayerPrefsX_ConvertFrom4Bytes_m12ED53CC6B89EF75D87BFDC858AFB305FC2B516A (void);
// 0x000002F9 System.Void RCC_PlayerPrefsX::.ctor()
extern void RCC_PlayerPrefsX__ctor_m61D246DACE6B0678E6247F460A9A90A3B85A5796 (void);
// 0x000002FA System.Void RCC_PoliceSiren::Start()
extern void RCC_PoliceSiren_Start_mE8A9EAD837EB1318DFE6C07D058FD425F28F4511 (void);
// 0x000002FB System.Void RCC_PoliceSiren::Update()
extern void RCC_PoliceSiren_Update_mE80DE45AFE84E55F620366112B7918A67CB1FB26 (void);
// 0x000002FC System.Void RCC_PoliceSiren::SetSiren(System.Boolean)
extern void RCC_PoliceSiren_SetSiren_m5377D180B725608579BC96104769E5E862C7A8F0 (void);
// 0x000002FD System.Void RCC_PoliceSiren::.ctor()
extern void RCC_PoliceSiren__ctor_mBF00285FFA05DE32CFDB0DD2B48EBED7698CBD3A (void);
// 0x000002FE System.Void RCC_Recorder::Record()
extern void RCC_Recorder_Record_m61434AA7F8175CDEF68537DCF3CC7A788C6C3B50 (void);
// 0x000002FF System.Void RCC_Recorder::SaveRecord()
extern void RCC_Recorder_SaveRecord_mE2C729980E7827613E8D783E813C2CE6A44EAF87 (void);
// 0x00000300 System.Void RCC_Recorder::Play()
extern void RCC_Recorder_Play_m7DFC888B4E732C474EDAA188B5439F10130465F1 (void);
// 0x00000301 System.Void RCC_Recorder::Play(RCC_Recorder/Recorded)
extern void RCC_Recorder_Play_m920E725A38011C2F056D95B6E7B1E25BDA98B155 (void);
// 0x00000302 System.Void RCC_Recorder::Stop()
extern void RCC_Recorder_Stop_m49DF89C77CDA8937A7C391A4D21A32001A56C5C1 (void);
// 0x00000303 System.Collections.IEnumerator RCC_Recorder::Replay()
extern void RCC_Recorder_Replay_mCB8F543BB938360131A26B17032F2D9E4E84D13B (void);
// 0x00000304 System.Collections.IEnumerator RCC_Recorder::Repos()
extern void RCC_Recorder_Repos_mACEA28AD16398978903BF022E8792B9E61156A1B (void);
// 0x00000305 System.Collections.IEnumerator RCC_Recorder::Revel()
extern void RCC_Recorder_Revel_mD605F0CBA5107BFBD8A1F9AD191ECCF9B1DF95F2 (void);
// 0x00000306 System.Void RCC_Recorder::FixedUpdate()
extern void RCC_Recorder_FixedUpdate_mCC085E2E48667EF9D7A73AE3B13FF4E27BC53897 (void);
// 0x00000307 System.Void RCC_Recorder::.ctor()
extern void RCC_Recorder__ctor_mC80F0DEDFE1963AE4AC48C043B611676BF9171FB (void);
// 0x00000308 System.Void RCC_Recorder/Recorded::.ctor(RCC_Recorder/PlayerInput[],RCC_Recorder/PlayerTransform[],RCC_Recorder/PlayerRigidBody[],System.String)
extern void Recorded__ctor_m19231BE67507A7673B8DD6975E31168DA91AC28D (void);
// 0x00000309 System.Void RCC_Recorder/PlayerInput::.ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.Int32,System.Boolean,RCC_CarControllerV3/IndicatorsOn,System.Boolean,System.Boolean)
extern void PlayerInput__ctor_m2400518145BB16F16E9D4228E87B3EA0A5421D70 (void);
// 0x0000030A System.Void RCC_Recorder/PlayerTransform::.ctor(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void PlayerTransform__ctor_mCB46F34F294529461B2BC01F7E7387EC0EDDB39F (void);
// 0x0000030B System.Void RCC_Recorder/PlayerRigidBody::.ctor(UnityEngine.Vector3,UnityEngine.Vector3)
extern void PlayerRigidBody__ctor_m835371E56E4A64642B2E183E8B68F73774773EA2 (void);
// 0x0000030C System.Void RCC_Recorder/<Replay>d__16::.ctor(System.Int32)
extern void U3CReplayU3Ed__16__ctor_mB9F8BF6A7961FC0BCDE24595914C6CDD4185DA2D (void);
// 0x0000030D System.Void RCC_Recorder/<Replay>d__16::System.IDisposable.Dispose()
extern void U3CReplayU3Ed__16_System_IDisposable_Dispose_mA568CC957DDCF7AEA1EC83C6014EA817142AC99D (void);
// 0x0000030E System.Boolean RCC_Recorder/<Replay>d__16::MoveNext()
extern void U3CReplayU3Ed__16_MoveNext_m8224989D08B562788D15759D8F0CE48D9564B673 (void);
// 0x0000030F System.Object RCC_Recorder/<Replay>d__16::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CReplayU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB652DB22113A751F5E62C0118A89C0A7852A9DCA (void);
// 0x00000310 System.Void RCC_Recorder/<Replay>d__16::System.Collections.IEnumerator.Reset()
extern void U3CReplayU3Ed__16_System_Collections_IEnumerator_Reset_mBD22EE0F1A4885817E0B124AD841CD0722581351 (void);
// 0x00000311 System.Object RCC_Recorder/<Replay>d__16::System.Collections.IEnumerator.get_Current()
extern void U3CReplayU3Ed__16_System_Collections_IEnumerator_get_Current_mA572D3B3D2BD541645BBAE9D72A8AAF66B6869B1 (void);
// 0x00000312 System.Void RCC_Recorder/<Repos>d__17::.ctor(System.Int32)
extern void U3CReposU3Ed__17__ctor_m0321A3A30C4717AAEB9D7837F58F67D03513E459 (void);
// 0x00000313 System.Void RCC_Recorder/<Repos>d__17::System.IDisposable.Dispose()
extern void U3CReposU3Ed__17_System_IDisposable_Dispose_mDA410B5BB748060FCF2F80273D2E9D13E2309201 (void);
// 0x00000314 System.Boolean RCC_Recorder/<Repos>d__17::MoveNext()
extern void U3CReposU3Ed__17_MoveNext_mB0B350958587F66DB883F14B14CFCC7FBEC076D9 (void);
// 0x00000315 System.Object RCC_Recorder/<Repos>d__17::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CReposU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEB158A68D5082D6CF6388D0B2B8BDB5435AFD296 (void);
// 0x00000316 System.Void RCC_Recorder/<Repos>d__17::System.Collections.IEnumerator.Reset()
extern void U3CReposU3Ed__17_System_Collections_IEnumerator_Reset_m451D855D4F5F9A10588987393698DDC05DA5C61C (void);
// 0x00000317 System.Object RCC_Recorder/<Repos>d__17::System.Collections.IEnumerator.get_Current()
extern void U3CReposU3Ed__17_System_Collections_IEnumerator_get_Current_mD260B401F286E2C92C928D9F687AF973A052C378 (void);
// 0x00000318 System.Void RCC_Recorder/<Revel>d__18::.ctor(System.Int32)
extern void U3CRevelU3Ed__18__ctor_mA59F93C7334102341D5DB01F68CE2B32648C2B41 (void);
// 0x00000319 System.Void RCC_Recorder/<Revel>d__18::System.IDisposable.Dispose()
extern void U3CRevelU3Ed__18_System_IDisposable_Dispose_m972AAB5504AC3EDBE56D21DB91C6679BE1899018 (void);
// 0x0000031A System.Boolean RCC_Recorder/<Revel>d__18::MoveNext()
extern void U3CRevelU3Ed__18_MoveNext_mC5C6288129749B79A1E5BB06E2790FBD9990CE07 (void);
// 0x0000031B System.Object RCC_Recorder/<Revel>d__18::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRevelU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB3545751513147303B9DC59D51AB7493A296931B (void);
// 0x0000031C System.Void RCC_Recorder/<Revel>d__18::System.Collections.IEnumerator.Reset()
extern void U3CRevelU3Ed__18_System_Collections_IEnumerator_Reset_m4BA9ED55300401BD6803495E21E63F5C89EBF25B (void);
// 0x0000031D System.Object RCC_Recorder/<Revel>d__18::System.Collections.IEnumerator.get_Current()
extern void U3CRevelU3Ed__18_System_Collections_IEnumerator_get_Current_m3117313D81F41949A5A3DE9640715A179B974331 (void);
// 0x0000031E RCC_Records RCC_Records::get_Instance()
extern void RCC_Records_get_Instance_mBEE6569C59FA0BBD160DD01E7DEC2891E3859685 (void);
// 0x0000031F System.Void RCC_Records::.ctor()
extern void RCC_Records__ctor_m59B5B713447E2B1A679E9607AD1EC3FE6CC295D7 (void);
// 0x00000320 RCC_SceneManager RCC_SceneManager::get_Instance()
extern void RCC_SceneManager_get_Instance_mAB016D740C87231352BBA591C1ED97E485675606 (void);
// 0x00000321 System.Void RCC_SceneManager::add_OnMainControllerChanged(RCC_SceneManager/onMainControllerChanged)
extern void RCC_SceneManager_add_OnMainControllerChanged_m89A15548A2B46FBBF43DA08C9DE5AE0DED73475C (void);
// 0x00000322 System.Void RCC_SceneManager::remove_OnMainControllerChanged(RCC_SceneManager/onMainControllerChanged)
extern void RCC_SceneManager_remove_OnMainControllerChanged_mED16E813B18487627B068BC21ED9245A171DED93 (void);
// 0x00000323 System.Void RCC_SceneManager::add_OnBehaviorChanged(RCC_SceneManager/onBehaviorChanged)
extern void RCC_SceneManager_add_OnBehaviorChanged_mAEC81E82068FEF375971E4CDA90E7804D1EA9D64 (void);
// 0x00000324 System.Void RCC_SceneManager::remove_OnBehaviorChanged(RCC_SceneManager/onBehaviorChanged)
extern void RCC_SceneManager_remove_OnBehaviorChanged_mC9EC4327421E5E6DB88D947DFBEF9B11F7786AF1 (void);
// 0x00000325 System.Void RCC_SceneManager::add_OnVehicleChanged(RCC_SceneManager/onVehicleChanged)
extern void RCC_SceneManager_add_OnVehicleChanged_m5A750372D5BAE35948ABD7F5C464D4A4DE8E667E (void);
// 0x00000326 System.Void RCC_SceneManager::remove_OnVehicleChanged(RCC_SceneManager/onVehicleChanged)
extern void RCC_SceneManager_remove_OnVehicleChanged_m05DCEFCDC113B7D0F6FFDD6D231AE5D31FA76F37 (void);
// 0x00000327 System.Void RCC_SceneManager::Awake()
extern void RCC_SceneManager_Awake_m381CC10229205108DC3DB3F7CA24129B8C60FAD1 (void);
// 0x00000328 System.Void RCC_SceneManager::RCC_CarControllerV3_OnRCCSpawned(RCC_CarControllerV3)
extern void RCC_SceneManager_RCC_CarControllerV3_OnRCCSpawned_m85D373B4A81CF73F67B770D1B02C7ED7625707FD (void);
// 0x00000329 System.Void RCC_SceneManager::RCC_AICarController_OnRCCAISpawned(RCC_AICarController)
extern void RCC_SceneManager_RCC_AICarController_OnRCCAISpawned_mC1B6A4365B976B6677816C3C562ACF3C116773D6 (void);
// 0x0000032A System.Void RCC_SceneManager::RCC_Camera_OnBCGCameraSpawned(UnityEngine.GameObject)
extern void RCC_SceneManager_RCC_Camera_OnBCGCameraSpawned_m605B85C72B2E7FA3D2557CC961D260F7148DA52C (void);
// 0x0000032B System.Void RCC_SceneManager::RCC_CarControllerV3_OnRCCPlayerDestroyed(RCC_CarControllerV3)
extern void RCC_SceneManager_RCC_CarControllerV3_OnRCCPlayerDestroyed_mE0CE70AB445FAAA14B31CB269E4B574C64726BFA (void);
// 0x0000032C System.Void RCC_SceneManager::RCC_AICarController_OnRCCAIDestroyed(RCC_AICarController)
extern void RCC_SceneManager_RCC_AICarController_OnRCCAIDestroyed_m61CF3B1A63CFA78E0C0884C5041C9C77E1CFE4DB (void);
// 0x0000032D System.Void RCC_SceneManager::Update()
extern void RCC_SceneManager_Update_mB73E41726C6DC5962D52310D5DB2134BF5E83C96 (void);
// 0x0000032E System.Void RCC_SceneManager::RegisterPlayer(RCC_CarControllerV3)
extern void RCC_SceneManager_RegisterPlayer_m20F906D2FF17B6D07494ADD30505519248318A84 (void);
// 0x0000032F System.Void RCC_SceneManager::RegisterPlayer(RCC_CarControllerV3,System.Boolean)
extern void RCC_SceneManager_RegisterPlayer_mFAF84E5163E78D81171F6821AF59EC9EB62BEB59 (void);
// 0x00000330 System.Void RCC_SceneManager::RegisterPlayer(RCC_CarControllerV3,System.Boolean,System.Boolean)
extern void RCC_SceneManager_RegisterPlayer_m457BFE2120DAF04F7BE7AF69ED1F937BE5132969 (void);
// 0x00000331 System.Void RCC_SceneManager::DeRegisterPlayer()
extern void RCC_SceneManager_DeRegisterPlayer_mE3C3B7EB6EA9C488B12B8B2B8C3BCED9F7FBF076 (void);
// 0x00000332 System.Void RCC_SceneManager::CheckCanvas()
extern void RCC_SceneManager_CheckCanvas_m51355EFDD7DD341D33E3F8913F209AAE4B0D4F31 (void);
// 0x00000333 System.Void RCC_SceneManager::SetBehavior(System.Int32)
extern void RCC_SceneManager_SetBehavior_m622EBE69EC552760357E8557E576EED5371A9C9E (void);
// 0x00000334 System.Void RCC_SceneManager::SetController(System.Int32)
extern void RCC_SceneManager_SetController_m8945F58EFCBEA218040BBF35EDEE9E1CCD1753FF (void);
// 0x00000335 System.Void RCC_SceneManager::ChangeCamera()
extern void RCC_SceneManager_ChangeCamera_m79584F2088846770F1D80BD9DCC1A969E93B150E (void);
// 0x00000336 System.Void RCC_SceneManager::OnDisable()
extern void RCC_SceneManager_OnDisable_m72DBF905D2E8C1E3037EBA753B20EF1FBEE78ACC (void);
// 0x00000337 System.Void RCC_SceneManager::.ctor()
extern void RCC_SceneManager__ctor_mBF27C69918D8C828E0842E1085B905C5A8548EA9 (void);
// 0x00000338 System.Void RCC_SceneManager/onMainControllerChanged::.ctor(System.Object,System.IntPtr)
extern void onMainControllerChanged__ctor_mC46F2C4F745DD52807E0442613C1C525A639F949 (void);
// 0x00000339 System.Void RCC_SceneManager/onMainControllerChanged::Invoke()
extern void onMainControllerChanged_Invoke_mBBC08F883F5D8AC750E4D97D5EAC9DEED31E2EA9 (void);
// 0x0000033A System.IAsyncResult RCC_SceneManager/onMainControllerChanged::BeginInvoke(System.AsyncCallback,System.Object)
extern void onMainControllerChanged_BeginInvoke_m0F1735473C6B717E60E987147EC26E8248D1E84A (void);
// 0x0000033B System.Void RCC_SceneManager/onMainControllerChanged::EndInvoke(System.IAsyncResult)
extern void onMainControllerChanged_EndInvoke_m9E5FA0DA183FE98BAA90180DFF6FE09168804A7D (void);
// 0x0000033C System.Void RCC_SceneManager/onBehaviorChanged::.ctor(System.Object,System.IntPtr)
extern void onBehaviorChanged__ctor_mD3C7853D23A9D3395EEF2D76B0DFBFC6C3F4AAEB (void);
// 0x0000033D System.Void RCC_SceneManager/onBehaviorChanged::Invoke()
extern void onBehaviorChanged_Invoke_m35F3C6131654B7F654CD1BFF7F9E454F502B76D4 (void);
// 0x0000033E System.IAsyncResult RCC_SceneManager/onBehaviorChanged::BeginInvoke(System.AsyncCallback,System.Object)
extern void onBehaviorChanged_BeginInvoke_mDD757D4A770368AFF08B6FEC2F49CC31836261BD (void);
// 0x0000033F System.Void RCC_SceneManager/onBehaviorChanged::EndInvoke(System.IAsyncResult)
extern void onBehaviorChanged_EndInvoke_m79895834E0870AE435E68409579BD8540BC668FF (void);
// 0x00000340 System.Void RCC_SceneManager/onVehicleChanged::.ctor(System.Object,System.IntPtr)
extern void onVehicleChanged__ctor_m6D6632BD74FA2AE357F135E96F9B4CB858B3D320 (void);
// 0x00000341 System.Void RCC_SceneManager/onVehicleChanged::Invoke()
extern void onVehicleChanged_Invoke_m4BC16FA9CA53CE07C9E7E5A39898D0111F751DC1 (void);
// 0x00000342 System.IAsyncResult RCC_SceneManager/onVehicleChanged::BeginInvoke(System.AsyncCallback,System.Object)
extern void onVehicleChanged_BeginInvoke_mDF6544CC7C76EDDDEA8DE1EF0A67F8F30FC3BBF8 (void);
// 0x00000343 System.Void RCC_SceneManager/onVehicleChanged::EndInvoke(System.IAsyncResult)
extern void onVehicleChanged_EndInvoke_m511A357A1CB26E12870F5B484128BC9DCA9763B2 (void);
// 0x00000344 RCC_Settings RCC_Settings::get_Instance()
extern void RCC_Settings_get_Instance_m882E38914DF2607B3766871A04C2CFA9898D5B6C (void);
// 0x00000345 RCC_Settings/BehaviorType RCC_Settings::get_selectedBehaviorType()
extern void RCC_Settings_get_selectedBehaviorType_mE5A10982296B874C56A04F80E06D393D8D3029A2 (void);
// 0x00000346 System.Void RCC_Settings::.ctor()
extern void RCC_Settings__ctor_m7BBD665AFB00FA612BD24A6A9DAE182D788C19E8 (void);
// 0x00000347 System.Void RCC_Settings/BehaviorType::.ctor()
extern void BehaviorType__ctor_m67B167B5C8D8921ACD7C65F32A98A00D23234F18 (void);
// 0x00000348 System.Void RCC_ShadowRotConst::Start()
extern void RCC_ShadowRotConst_Start_m4482E13C5569CAF9C08216BBA8BE26008B8BDE68 (void);
// 0x00000349 System.Void RCC_ShadowRotConst::Update()
extern void RCC_ShadowRotConst_Update_m4DE35DB651040477559144F42E921FC0A9ADDAAA (void);
// 0x0000034A System.Void RCC_ShadowRotConst::.ctor()
extern void RCC_ShadowRotConst__ctor_m34BAD29D38002603CB1DCE8DD984CF8B980068B4 (void);
// 0x0000034B System.Void RCC_Skidmarks::Awake()
extern void RCC_Skidmarks_Awake_m08FB0E7E42FC3AB8B9364B0D2180AA2092960CFC (void);
// 0x0000034C System.Void RCC_Skidmarks::Start()
extern void RCC_Skidmarks_Start_m4758B4540DABF8FABCD23BAAAB8E2A244C854FD9 (void);
// 0x0000034D System.Int32 RCC_Skidmarks::AddSkidMark(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void RCC_Skidmarks_AddSkidMark_m7416143FF5A75845B891D6062D6903DAE1B598A3 (void);
// 0x0000034E System.Void RCC_Skidmarks::LateUpdate()
extern void RCC_Skidmarks_LateUpdate_m16DB62E67C084F32F55582260FB20EDC8E76EB23 (void);
// 0x0000034F System.Void RCC_Skidmarks::.ctor()
extern void RCC_Skidmarks__ctor_mF584F911A52F5E6542ACD479417228BFB6AD550B (void);
// 0x00000350 System.Void RCC_Skidmarks/markSection::.ctor()
extern void markSection__ctor_mB0855996190A4606565138AB040D6983BF21FC65 (void);
// 0x00000351 System.Void RCC_SkidmarksManager::Start()
extern void RCC_SkidmarksManager_Start_mC94CA3593A9CCF14B40DE3BF0F37A2F6C5932B35 (void);
// 0x00000352 System.Int32 RCC_SkidmarksManager::AddSkidMark(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,System.Int32)
extern void RCC_SkidmarksManager_AddSkidMark_m39BF86084AC7DC51C5B8CD28A83EF5485FE5606B (void);
// 0x00000353 System.Void RCC_SkidmarksManager::.ctor()
extern void RCC_SkidmarksManager__ctor_m631C1901A6C905ED09801A42FA4B777225B49619 (void);
// 0x00000354 System.Void RCC_Spawner::Start()
extern void RCC_Spawner_Start_m2B64874288F36D82B544AD4CBDC78B320B4725A4 (void);
// 0x00000355 System.Void RCC_Spawner::.ctor()
extern void RCC_Spawner__ctor_m26E1074D7E9C772803A91A3A4B0AE5371C802D8E (void);
// 0x00000356 System.Void RCC_SuspensionArm::Start()
extern void RCC_SuspensionArm_Start_m7C3F0DF30D89533D7F85967C278AB41485960160 (void);
// 0x00000357 System.Void RCC_SuspensionArm::Update()
extern void RCC_SuspensionArm_Update_mD0B13CD0A04B7BD2BE94B79A9A554273A6CD22D3 (void);
// 0x00000358 System.Single RCC_SuspensionArm::GetSuspensionDistance()
extern void RCC_SuspensionArm_GetSuspensionDistance_m9F8E3267D4A3BBDEEC680A50D7149F52EED73C78 (void);
// 0x00000359 System.Void RCC_SuspensionArm::.ctor()
extern void RCC_SuspensionArm__ctor_m4FFA675E426EEA624AC46FA468DBF7A642E80745 (void);
// 0x0000035A System.Void RCC_TrailerAttachPoint::OnTriggerEnter(UnityEngine.Collider)
extern void RCC_TrailerAttachPoint_OnTriggerEnter_m3E781F021CF5DE6E9C7128622F6E1E5502159E93 (void);
// 0x0000035B System.Void RCC_TrailerAttachPoint::.ctor()
extern void RCC_TrailerAttachPoint__ctor_mBD1224CB055153AC6E5A42D02F98E40E9CB1CDE4 (void);
// 0x0000035C System.Void RCC_TruckTrailer::Start()
extern void RCC_TruckTrailer_Start_m0A84EFBD311768AA235185D0D6DCDF2C7E706E83 (void);
// 0x0000035D System.Void RCC_TruckTrailer::FixedUpdate()
extern void RCC_TruckTrailer_FixedUpdate_m8C8600F5BE8A044F767374D654235C0D3F9BDF0C (void);
// 0x0000035E System.Void RCC_TruckTrailer::Update()
extern void RCC_TruckTrailer_Update_m708DFAE1A0903498BF333FDC8F48F3EECB1B2BEC (void);
// 0x0000035F System.Void RCC_TruckTrailer::WheelAlign()
extern void RCC_TruckTrailer_WheelAlign_m0D95D186A2E759782E4D518B09FE9419295B7B39 (void);
// 0x00000360 System.Void RCC_TruckTrailer::DetachTrailer()
extern void RCC_TruckTrailer_DetachTrailer_mADF583A94CED051F555353F09C67FC83B7C95CA0 (void);
// 0x00000361 System.Void RCC_TruckTrailer::AttachTrailer(RCC_CarControllerV3)
extern void RCC_TruckTrailer_AttachTrailer_mC177E7426F294EABF414389D72AC38CC2071583A (void);
// 0x00000362 System.Void RCC_TruckTrailer::AntiRollBars()
extern void RCC_TruckTrailer_AntiRollBars_mCE3BEE8B489B9F9C2A1A615BDBE9E2CCCC509C26 (void);
// 0x00000363 System.Void RCC_TruckTrailer::OnTriggerEnter(UnityEngine.Collider)
extern void RCC_TruckTrailer_OnTriggerEnter_m73A8DD89C00D6C5DEDDA32B467F3761E8A86E2F2 (void);
// 0x00000364 System.Void RCC_TruckTrailer::.ctor()
extern void RCC_TruckTrailer__ctor_mCA0F75E7BA4B49FC1CFC454CEAC690F0AE880EE3 (void);
// 0x00000365 System.Void RCC_TruckTrailer/TrailerWheel::AddTorque(System.Single)
extern void TrailerWheel_AddTorque_m0803C940B07B3473D48F7C3E967B4A36CEF40F7B (void);
// 0x00000366 System.Void RCC_TruckTrailer/TrailerWheel::.ctor()
extern void TrailerWheel__ctor_mDB4358B5637FAB4147C07BC7F4183CD14D883DFB (void);
// 0x00000367 System.Void RCC_TruckTrailer/JointRestrictions::Get(UnityEngine.ConfigurableJoint)
extern void JointRestrictions_Get_m1243804932D083E05C45ECCED87081D147101D06 (void);
// 0x00000368 System.Void RCC_TruckTrailer/JointRestrictions::Set(UnityEngine.ConfigurableJoint)
extern void JointRestrictions_Set_m4981283E59878B9BA3E08DE5420A48D5046CAF00 (void);
// 0x00000369 System.Void RCC_TruckTrailer/JointRestrictions::Reset(UnityEngine.ConfigurableJoint)
extern void JointRestrictions_Reset_m52666139C8113C926EBC43DBB665B88FABB0B8FF (void);
// 0x0000036A System.Void RCC_TruckTrailer/JointRestrictions::.ctor()
extern void JointRestrictions__ctor_mFF44CA1A6E28DC4ECD98A6FB2E4456DEC61196C9 (void);
// 0x0000036B RCC_Settings RCC_UIController::get_RCCSettings()
extern void RCC_UIController_get_RCCSettings_m38460900FC4AF19262A1A40EBED49BA942136DE4 (void);
// 0x0000036C System.Single RCC_UIController::get_sensitivity()
extern void RCC_UIController_get_sensitivity_m0B4397117C7854380ABA07416918DA044ACB0826 (void);
// 0x0000036D System.Single RCC_UIController::get_gravity()
extern void RCC_UIController_get_gravity_m7B89A17D9DA9F65074614700386E0EA73FC42A82 (void);
// 0x0000036E System.Void RCC_UIController::Awake()
extern void RCC_UIController_Awake_m16C312AD3CA6AF9744524A1DD602AD639B2C6A60 (void);
// 0x0000036F System.Void RCC_UIController::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void RCC_UIController_OnPointerDown_m5F2B1B2EF2BB5E68F14D16F21A9C58E830156A3E (void);
// 0x00000370 System.Void RCC_UIController::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
extern void RCC_UIController_OnPointerUp_m313678CBE1CF7B2285001822BEEAAC164D938116 (void);
// 0x00000371 System.Void RCC_UIController::OnPress(System.Boolean)
extern void RCC_UIController_OnPress_m0457E02287DA873EA354AC12C9797C3269FBFE2A (void);
// 0x00000372 System.Void RCC_UIController::Update()
extern void RCC_UIController_Update_m69DFCB92311D4BB7D8397181E5D300F51C8B3616 (void);
// 0x00000373 System.Void RCC_UIController::OnDisable()
extern void RCC_UIController_OnDisable_m15C156B50BE07435F3E9ED1408D8E7C48F0499F4 (void);
// 0x00000374 System.Void RCC_UIController::.ctor()
extern void RCC_UIController__ctor_mC5F785D18CA7A8E4358BE18609528BA5751922CE (void);
// 0x00000375 System.Void RCC_UIDashboardButton::Start()
extern void RCC_UIDashboardButton_Start_mCC53B89F700A845C3BD0277874DF17F23C312D15 (void);
// 0x00000376 System.Void RCC_UIDashboardButton::OnEnable()
extern void RCC_UIDashboardButton_OnEnable_mF38D16E079BF72FF6F6B8734F43C374949C396E6 (void);
// 0x00000377 System.Void RCC_UIDashboardButton::OnClicked()
extern void RCC_UIDashboardButton_OnClicked_mA10BBD8DE5AD178D30D13B449DC6164BCA48D692 (void);
// 0x00000378 System.Void RCC_UIDashboardButton::Check()
extern void RCC_UIDashboardButton_Check_mEDF307BBFF315478977C2FAB530607A68C9E47D5 (void);
// 0x00000379 System.Void RCC_UIDashboardButton::ChangeGear()
extern void RCC_UIDashboardButton_ChangeGear_mA89640C0FC1AB37B47A8BEB7A259CA6FA9547BDD (void);
// 0x0000037A System.Void RCC_UIDashboardButton::OnDisable()
extern void RCC_UIDashboardButton_OnDisable_m0DDC7429E6512E4E3BA7A659B20FD7F536BBE9E5 (void);
// 0x0000037B System.Void RCC_UIDashboardButton::GearDrive()
extern void RCC_UIDashboardButton_GearDrive_m8D82FE73D59CE91AF35131F1BAFFBB3B7E2CF435 (void);
// 0x0000037C System.Void RCC_UIDashboardButton::Gearreverse()
extern void RCC_UIDashboardButton_Gearreverse_mD475687637BDF2C29832F58F351CA5D0E5A11338 (void);
// 0x0000037D System.Void RCC_UIDashboardButton::.ctor()
extern void RCC_UIDashboardButton__ctor_m45C165996F117A5704BDDD4E145C771C3A476E6F (void);
// 0x0000037E System.Void RCC_UIDashboardButton::<Start>b__4_0(System.Single)
extern void RCC_UIDashboardButton_U3CStartU3Eb__4_0_m8B583736442F9A204A460DF737E434AF0436CA98 (void);
// 0x0000037F RCC_Settings RCC_UIDashboardDisplay::get_RCCSettings()
extern void RCC_UIDashboardDisplay_get_RCCSettings_m9C2F5830CD86D3A4BEB945A167EE5D447A5850CC (void);
// 0x00000380 System.Void RCC_UIDashboardDisplay::Awake()
extern void RCC_UIDashboardDisplay_Awake_m95946C21589EB7D9377D3E5689ECDD745F0D7EDA (void);
// 0x00000381 System.Void RCC_UIDashboardDisplay::Start()
extern void RCC_UIDashboardDisplay_Start_m0CE64E8F318A6F5A29B970B655957E1CB7237C53 (void);
// 0x00000382 System.Void RCC_UIDashboardDisplay::OnEnable()
extern void RCC_UIDashboardDisplay_OnEnable_mA4DAA8AD741577BA848D0B40282A79E069CBDC52 (void);
// 0x00000383 System.Void RCC_UIDashboardDisplay::CheckController()
extern void RCC_UIDashboardDisplay_CheckController_m9EB39B5F27949F87BE02A1CD3A8DA6CB279F582A (void);
// 0x00000384 System.Void RCC_UIDashboardDisplay::Update()
extern void RCC_UIDashboardDisplay_Update_mDFC648C515E3D88E58BEE69013AEC01DF0BB5C5E (void);
// 0x00000385 System.Void RCC_UIDashboardDisplay::LateUpdate()
extern void RCC_UIDashboardDisplay_LateUpdate_m85FA55E553C16C5802521B72F56C495AA10E8464 (void);
// 0x00000386 System.Void RCC_UIDashboardDisplay::SetDisplayType(RCC_UIDashboardDisplay/DisplayType)
extern void RCC_UIDashboardDisplay_SetDisplayType_m442E16DF00EA5292DFF2201D70FA1F35AB324390 (void);
// 0x00000387 System.Void RCC_UIDashboardDisplay::OnDisable()
extern void RCC_UIDashboardDisplay_OnDisable_m6B535682E943FD3EC1272454AFBE9B3B910599DC (void);
// 0x00000388 System.Void RCC_UIDashboardDisplay::.ctor()
extern void RCC_UIDashboardDisplay__ctor_mA0E9F674B39DC0C0FA25158A51A6D20AE8A14646 (void);
// 0x00000389 System.Single RCC_UIJoystick::get_inputHorizontal()
extern void RCC_UIJoystick_get_inputHorizontal_m5F1F24A587306D44D7EB84B4126669A5A45787CF (void);
// 0x0000038A System.Single RCC_UIJoystick::get_inputVertical()
extern void RCC_UIJoystick_get_inputVertical_m2115BDAE356A6B8415EF6EB49D46AD28A7B0D732 (void);
// 0x0000038B System.Void RCC_UIJoystick::Start()
extern void RCC_UIJoystick_Start_m59702FD58D82A585E21E36CAB1DD4E5296D1409E (void);
// 0x0000038C System.Void RCC_UIJoystick::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void RCC_UIJoystick_OnDrag_m54F949553EF889C70B0D1E722C4FD2D402AF561A (void);
// 0x0000038D System.Void RCC_UIJoystick::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
extern void RCC_UIJoystick_OnPointerUp_mF07E3A46A9F798BE10F00FADB4B3CAA7DF361E80 (void);
// 0x0000038E System.Void RCC_UIJoystick::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void RCC_UIJoystick_OnPointerDown_m052B4734B687C342F692B814C372454BE12EFD2D (void);
// 0x0000038F System.Void RCC_UIJoystick::.ctor()
extern void RCC_UIJoystick__ctor_mD588EB4B3E3FCCEAA7507B5927A9D99193765D7C (void);
// 0x00000390 System.Void RCC_UISliderTextReader::Awake()
extern void RCC_UISliderTextReader_Awake_mB047FC5AE23C45C5EBB700C1FB462B1CA3A74177 (void);
// 0x00000391 System.Void RCC_UISliderTextReader::Update()
extern void RCC_UISliderTextReader_Update_mF05421A65F736F4FE27A9FB2A99FD6E452FD0FB8 (void);
// 0x00000392 System.Void RCC_UISliderTextReader::.ctor()
extern void RCC_UISliderTextReader__ctor_mB54846B203930A581FDB8076D2E67D3EDD7FDA5A (void);
// 0x00000393 RCC_Settings RCC_UISteeringWheelController::get_RCCSettings()
extern void RCC_UISteeringWheelController_get_RCCSettings_m9D7A5ED6A589224D0759AD31464895FDE56A66D9 (void);
// 0x00000394 System.Void RCC_UISteeringWheelController::Awake()
extern void RCC_UISteeringWheelController_Awake_m4C2A879F64EB27DF4D4592F1E924D4FF27962365 (void);
// 0x00000395 System.Void RCC_UISteeringWheelController::Update()
extern void RCC_UISteeringWheelController_Update_m634B0E47D1E41C51B7ECB0F18B1D8D43FF0AB7D1 (void);
// 0x00000396 System.Void RCC_UISteeringWheelController::SteeringWheelInit()
extern void RCC_UISteeringWheelController_SteeringWheelInit_mBE4837B44EBBCEEBB953C41C0617D8D7605B2A26 (void);
// 0x00000397 System.Void RCC_UISteeringWheelController::SteeringWheelEventsInit()
extern void RCC_UISteeringWheelController_SteeringWheelEventsInit_m8B325F244418E6E467B2AC57B51A9C00322E18AC (void);
// 0x00000398 System.Single RCC_UISteeringWheelController::GetSteeringWheelInput()
extern void RCC_UISteeringWheelController_GetSteeringWheelInput_m3AC76DF957ED074B622E82470C6CAF3824E8DC42 (void);
// 0x00000399 System.Boolean RCC_UISteeringWheelController::isSteeringWheelPressed()
extern void RCC_UISteeringWheelController_isSteeringWheelPressed_m9F4B79C2E28956CB1C4A5FDB753F57140A4EB132 (void);
// 0x0000039A System.Void RCC_UISteeringWheelController::SteeringWheelControlling()
extern void RCC_UISteeringWheelController_SteeringWheelControlling_m36B4F81A68037D87F42A2CD509A3AEEA4EE2C02C (void);
// 0x0000039B System.Void RCC_UISteeringWheelController::OnDisable()
extern void RCC_UISteeringWheelController_OnDisable_mC5AEFC70C98FA0F8C62B2AC425F35883FD64C947 (void);
// 0x0000039C System.Void RCC_UISteeringWheelController::.ctor()
extern void RCC_UISteeringWheelController__ctor_m8D491805988BCD2178CE1731D151246893AD536B (void);
// 0x0000039D System.Void RCC_UISteeringWheelController::<SteeringWheelEventsInit>b__21_0(UnityEngine.EventSystems.BaseEventData)
extern void RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_0_m22BF9241C6050BDF05C4CE723689FA865A361402 (void);
// 0x0000039E System.Void RCC_UISteeringWheelController::<SteeringWheelEventsInit>b__21_1(UnityEngine.EventSystems.BaseEventData)
extern void RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_1_mC3C4DFB94E509F77DADC020AD49074B13DE338B2 (void);
// 0x0000039F System.Void RCC_UISteeringWheelController::<SteeringWheelEventsInit>b__21_2(UnityEngine.EventSystems.BaseEventData)
extern void RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_2_mBC078C2B056DF8D07E00F130A48B601A989E7701 (void);
// 0x000003A0 System.Void RCC_Useless::Awake()
extern void RCC_Useless_Awake_mB230AA74DE817959A3A74C7DC709C3AA3F499E69 (void);
// 0x000003A1 System.Void RCC_Useless::.ctor()
extern void RCC_Useless__ctor_m4AD74BB2B27A4BD839838F61E4CDC3A62D95F99E (void);
// 0x000003A2 RCC_Vehicles RCC_Vehicles::get_Instance()
extern void RCC_Vehicles_get_Instance_m30712556C65E837C051857A7C54DB1AE24FC4015 (void);
// 0x000003A3 System.Void RCC_Vehicles::.ctor()
extern void RCC_Vehicles__ctor_m670FA46CE5D2D646F281F6CF42D74F9A01C53CD4 (void);
// 0x000003A4 System.Void RCC_WheelCamera::FixShake()
extern void RCC_WheelCamera_FixShake_m769326A6E32E65CFCCD0A1910EB2895B5E054169 (void);
// 0x000003A5 System.Collections.IEnumerator RCC_WheelCamera::FixShakeDelayed()
extern void RCC_WheelCamera_FixShakeDelayed_m2E0A03ECD03FE70C28057C91BFF5646F2C4C4F28 (void);
// 0x000003A6 System.Void RCC_WheelCamera::.ctor()
extern void RCC_WheelCamera__ctor_mBD2C3C0E7757357C3A5903BC0BDC991709E87C60 (void);
// 0x000003A7 System.Void RCC_WheelCamera/<FixShakeDelayed>d__1::.ctor(System.Int32)
extern void U3CFixShakeDelayedU3Ed__1__ctor_m204A4662380E106CFB28EB14B004A2E9DB6BD546 (void);
// 0x000003A8 System.Void RCC_WheelCamera/<FixShakeDelayed>d__1::System.IDisposable.Dispose()
extern void U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m897889779F5BADDE9EF6DFF5CC32935906FF3DB2 (void);
// 0x000003A9 System.Boolean RCC_WheelCamera/<FixShakeDelayed>d__1::MoveNext()
extern void U3CFixShakeDelayedU3Ed__1_MoveNext_m3936AD2C077E517D46C214B63E2D38B783DF4FBE (void);
// 0x000003AA System.Object RCC_WheelCamera/<FixShakeDelayed>d__1::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB0CA96DCB23FB13704FA6C9A44E4F1AFF97388C0 (void);
// 0x000003AB System.Void RCC_WheelCamera/<FixShakeDelayed>d__1::System.Collections.IEnumerator.Reset()
extern void U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m5430D52BA502C196D76FC653209F18AE9C630819 (void);
// 0x000003AC System.Object RCC_WheelCamera/<FixShakeDelayed>d__1::System.Collections.IEnumerator.get_Current()
extern void U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m27B86E74B26471D822D64FE4AEFCA6D397A37C45 (void);
// 0x000003AD RCC_Settings RCC_WheelCollider::get_RCCSettings()
extern void RCC_WheelCollider_get_RCCSettings_mB992026C58E13ECC4B47F2688446B0A604E7BFE6 (void);
// 0x000003AE RCC_GroundMaterials RCC_WheelCollider::get_RCCGroundMaterials()
extern void RCC_WheelCollider_get_RCCGroundMaterials_mBEBF63ACB8393556D5C2059BDEC88C309A9077BB (void);
// 0x000003AF UnityEngine.WheelCollider RCC_WheelCollider::get_wheelCollider()
extern void RCC_WheelCollider_get_wheelCollider_mCFAC39B39E518979C0EEA45654B68FEF0D7AA544 (void);
// 0x000003B0 RCC_GroundMaterials RCC_WheelCollider::get_physicsMaterials()
extern void RCC_WheelCollider_get_physicsMaterials_mA474B9553E581CA4CBA8A856297BA178D785EADD (void);
// 0x000003B1 RCC_GroundMaterials/GroundMaterialFrictions[] RCC_WheelCollider::get_physicsFrictions()
extern void RCC_WheelCollider_get_physicsFrictions_mEDB9E0F4E83AAC61C21CEB9F8F736F6C3C5FC3A9 (void);
// 0x000003B2 System.Single RCC_WheelCollider::get_steeringSmoother()
extern void RCC_WheelCollider_get_steeringSmoother_m8F49707DEC853446B6C934FD2E20F3EEC5DCD8DE (void);
// 0x000003B3 System.Void RCC_WheelCollider::Start()
extern void RCC_WheelCollider_Start_m0B5FA9235BE8387391CBCFC21A1021729C06FEDE (void);
// 0x000003B4 System.Void RCC_WheelCollider::OnEnable()
extern void RCC_WheelCollider_OnEnable_mBD1E13067C64DCA40BFB5573AA3F158C82640A59 (void);
// 0x000003B5 System.Void RCC_WheelCollider::CheckBehavior()
extern void RCC_WheelCollider_CheckBehavior_m908BA6A5051BB59603D7CCF70B9DBF2AD7ACE9AA (void);
// 0x000003B6 System.Void RCC_WheelCollider::Update()
extern void RCC_WheelCollider_Update_m877AA9A48865D4A04E13FF937C95E7151B09FB1A (void);
// 0x000003B7 System.Void RCC_WheelCollider::FixedUpdate()
extern void RCC_WheelCollider_FixedUpdate_mAB4662EC6EF431609D970F6EF54270F9FB7A1BE3 (void);
// 0x000003B8 System.Void RCC_WheelCollider::WheelAlign()
extern void RCC_WheelCollider_WheelAlign_mBC51FFE9248F66FB51C6EF74E3CBDD4FD2257FC2 (void);
// 0x000003B9 System.Void RCC_WheelCollider::SkidMarks()
extern void RCC_WheelCollider_SkidMarks_m9422E631F9026E65D93DCA8D4FFD6943965BD9E1 (void);
// 0x000003BA System.Void RCC_WheelCollider::Frictions()
extern void RCC_WheelCollider_Frictions_m543A7929A1C3F0B3957E549BE4939049D6E6D8CB (void);
// 0x000003BB System.Void RCC_WheelCollider::Smoke()
extern void RCC_WheelCollider_Smoke_m771513B91731E8E75B91F96FF088BA304418B624 (void);
// 0x000003BC System.Void RCC_WheelCollider::Drift()
extern void RCC_WheelCollider_Drift_m0EBB7633EB9051561BEA188CC3F687459955282B (void);
// 0x000003BD System.Void RCC_WheelCollider::Audio()
extern void RCC_WheelCollider_Audio_mB1E60BEC13D751FD77A6AB6B71521E5796654234 (void);
// 0x000003BE System.Boolean RCC_WheelCollider::isSkidding()
extern void RCC_WheelCollider_isSkidding_mFCDEE720AB1D290B9FC47F74A9D5BC0E49C7827D (void);
// 0x000003BF System.Void RCC_WheelCollider::ApplyMotorTorque(System.Single)
extern void RCC_WheelCollider_ApplyMotorTorque_mB7779FB63CF53DEE4337E5FD36F40DCC033D65F6 (void);
// 0x000003C0 System.Void RCC_WheelCollider::ApplySteering()
extern void RCC_WheelCollider_ApplySteering_mC24FAEE3288258C57E98270514154C70036DCEA3 (void);
// 0x000003C1 System.Void RCC_WheelCollider::ApplyBrakeTorque(System.Single)
extern void RCC_WheelCollider_ApplyBrakeTorque_mC08EDAECF4A4EE0D3BEACE5A08B0659895FDDD0B (void);
// 0x000003C2 System.Boolean RCC_WheelCollider::OverTorque()
extern void RCC_WheelCollider_OverTorque_m5021C8F81D55F494D2EEA74570AE5933C2BA76D5 (void);
// 0x000003C3 System.Void RCC_WheelCollider::GetTerrainData()
extern void RCC_WheelCollider_GetTerrainData_mB172AC0C631476884B3D830CBF46675D2784EC1D (void);
// 0x000003C4 UnityEngine.Vector3 RCC_WheelCollider::ConvertToSplatMapCoordinate(UnityEngine.Vector3)
extern void RCC_WheelCollider_ConvertToSplatMapCoordinate_m1C7E3A8E16743FD9590C077CDBE27E56E98BADB1 (void);
// 0x000003C5 System.Int32 RCC_WheelCollider::GetGroundMaterialIndex()
extern void RCC_WheelCollider_GetGroundMaterialIndex_m464B05DCBDFC9A34CC21F08CF7E7512BB08745A2 (void);
// 0x000003C6 UnityEngine.WheelFrictionCurve RCC_WheelCollider::SetFrictionCurves(UnityEngine.WheelFrictionCurve,System.Single,System.Single,System.Single,System.Single)
extern void RCC_WheelCollider_SetFrictionCurves_mBD8B98FBB3381CC30151CEC0320DD6176EF561C8 (void);
// 0x000003C7 System.Void RCC_WheelCollider::OnDisable()
extern void RCC_WheelCollider_OnDisable_m4F3DED87A74EC5288A073A6A4EAF871229C888A9 (void);
// 0x000003C8 System.Void RCC_WheelCollider::.ctor()
extern void RCC_WheelCollider__ctor_m2DEAB3FB4B0035563C0BBC6FA7384CA5D2EBA0A9 (void);
// 0x000003C9 System.Void RCC_XRToggle::Start()
extern void RCC_XRToggle_Start_mC5A3B7C0F03020286CC36E2C38BDE6A2EADD3C15 (void);
// 0x000003CA System.Void RCC_XRToggle::Update()
extern void RCC_XRToggle_Update_m3013CE8952D43B8A91286011F85EBAFC9B44CBAC (void);
// 0x000003CB System.Void RCC_XRToggle::ToggleXR()
extern void RCC_XRToggle_ToggleXR_mB5F16490F91C6977CC90FFC655961C024C4D4DA2 (void);
// 0x000003CC System.Void RCC_XRToggle::.ctor()
extern void RCC_XRToggle__ctor_mD98DB6DE423CBFB721C95F75EC96A7AA2BDE56CF (void);
// 0x000003CD Fail Fail::get_Instance()
extern void Fail_get_Instance_mF7A2AAB284DFEC07138EA0FC5A1961D69F98007E (void);
// 0x000003CE System.Void Fail::Start()
extern void Fail_Start_mE24CF1C39402D4743BF0CA0FDC89FD6307388CEF (void);
// 0x000003CF System.Void Fail::FixedUpdate()
extern void Fail_FixedUpdate_m7BF8893C10CF5138E2D9963974CE5EC4F6C9C9FD (void);
// 0x000003D0 System.Void Fail::OnTriggerEnter(UnityEngine.Collider)
extern void Fail_OnTriggerEnter_m26F4BB9209530D92986996043CE5F6662581E1A7 (void);
// 0x000003D1 System.Collections.IEnumerator Fail::OTHER()
extern void Fail_OTHER_m9302EB2FA190E6331828330607495633CC78098A (void);
// 0x000003D2 System.Void Fail::.ctor()
extern void Fail__ctor_mE3B565CF62F238D2D83DFBC440E0E4D9B21B65B4 (void);
// 0x000003D3 System.Void Fail/<OTHER>d__11::.ctor(System.Int32)
extern void U3COTHERU3Ed__11__ctor_m0B6E55F410EF7E22AD3A9754F608909D926D5FFF (void);
// 0x000003D4 System.Void Fail/<OTHER>d__11::System.IDisposable.Dispose()
extern void U3COTHERU3Ed__11_System_IDisposable_Dispose_m0B9B07668BD86422B5AB242B5C0F897B301D587C (void);
// 0x000003D5 System.Boolean Fail/<OTHER>d__11::MoveNext()
extern void U3COTHERU3Ed__11_MoveNext_mB80D38B604E66D3BB111B9226518518185C5CBEA (void);
// 0x000003D6 System.Object Fail/<OTHER>d__11::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3COTHERU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1B623C693AAEF9EF516DE1D821E354D497C399D (void);
// 0x000003D7 System.Void Fail/<OTHER>d__11::System.Collections.IEnumerator.Reset()
extern void U3COTHERU3Ed__11_System_Collections_IEnumerator_Reset_m771865FB69A0E526862906D154CBD9C36231DD17 (void);
// 0x000003D8 System.Object Fail/<OTHER>d__11::System.Collections.IEnumerator.get_Current()
extern void U3COTHERU3Ed__11_System_Collections_IEnumerator_get_Current_m0EE232A7B5409AB7194EC5DFE2E38D2CF303240A (void);
// 0x000003D9 System.Void SliderRelease::Start()
extern void SliderRelease_Start_m0F1289A6BC91A2E2B83BA80B9CA0FBD56B0E75C2 (void);
// 0x000003DA System.Void SliderRelease::Update()
extern void SliderRelease_Update_mD6B2AEC27F1E44276E5CA03C312AEE0AC5064BA4 (void);
// 0x000003DB System.Void SliderRelease::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
extern void SliderRelease_OnPointerUp_m41E4410028C33EA7C67D7C96FBB4CBB77AFDF6C2 (void);
// 0x000003DC System.Collections.IEnumerator SliderRelease::ReleaseSlider()
extern void SliderRelease_ReleaseSlider_m4736AD6D8FDB819574EB0412E32DA2F6FE0D598C (void);
// 0x000003DD System.Void SliderRelease::.ctor()
extern void SliderRelease__ctor_mAA6065440F8A23861AC017AD8F281172C863ABD6 (void);
// 0x000003DE System.Void SliderRelease/<ReleaseSlider>d__13::.ctor(System.Int32)
extern void U3CReleaseSliderU3Ed__13__ctor_mE4197199E78602577ED2A3A635EB216C77F39FEF (void);
// 0x000003DF System.Void SliderRelease/<ReleaseSlider>d__13::System.IDisposable.Dispose()
extern void U3CReleaseSliderU3Ed__13_System_IDisposable_Dispose_m34F9BA03563BAF7CD102DF712A39ECFE541B0418 (void);
// 0x000003E0 System.Boolean SliderRelease/<ReleaseSlider>d__13::MoveNext()
extern void U3CReleaseSliderU3Ed__13_MoveNext_m73FAB59C6B2F934B1AD74311106C325AD31E565C (void);
// 0x000003E1 System.Object SliderRelease/<ReleaseSlider>d__13::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CReleaseSliderU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m46AD7346655EE2AC283D6C40CD1F9D1E27B296CA (void);
// 0x000003E2 System.Void SliderRelease/<ReleaseSlider>d__13::System.Collections.IEnumerator.Reset()
extern void U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_Reset_mDF5695C2AB546ADE270505C73425F4B3797CA92E (void);
// 0x000003E3 System.Object SliderRelease/<ReleaseSlider>d__13::System.Collections.IEnumerator.get_Current()
extern void U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_get_Current_mEEAC84F67676031DDAF637E24168B12AAC567883 (void);
// 0x000003E4 System.Void TimerSeconds::Start()
extern void TimerSeconds_Start_m20A725BD4CA96229C29155CC013DAC282879B4CE (void);
// 0x000003E5 System.Void TimerSeconds::Update()
extern void TimerSeconds_Update_m592F9A2B0654AC3CF08C8E07403510BA92AB94DE (void);
// 0x000003E6 System.Void TimerSeconds::DisplayTime(System.Single)
extern void TimerSeconds_DisplayTime_m1A4993809379460BE5116FC12C7159293B90604B (void);
// 0x000003E7 System.Void TimerSeconds::.ctor()
extern void TimerSeconds__ctor_m2D260311B47BEBCCF13B3ECC21C18D8E9470C390 (void);
// 0x000003E8 playermain playermain::get_Instance()
extern void playermain_get_Instance_mFD90471C7C306EA0DAA5A31C8B8B59B5237176E6 (void);
// 0x000003E9 System.Void playermain::Start()
extern void playermain_Start_m056FBC6B84B9C90C47D6EB2C7BA9D004D70F5A78 (void);
// 0x000003EA System.Void playermain::UpdateMass(System.Single)
extern void playermain_UpdateMass_mB41B70989EF5C9B85BC72F384ED4F1107D88A8F7 (void);
// 0x000003EB System.Void playermain::FixedUpdate()
extern void playermain_FixedUpdate_m635C9558270B894945DF8D6CC4FE46EF7EC70F9D (void);
// 0x000003EC System.Void playermain::OnTriggerEnter(UnityEngine.Collider)
extern void playermain_OnTriggerEnter_mF49F1A6A9F2FE8C766F050577543BC2EE619481A (void);
// 0x000003ED System.Void playermain::faildely()
extern void playermain_faildely_m06057F9352F4C08972CB3D78592C98A84D58AC25 (void);
// 0x000003EE System.Collections.IEnumerator playermain::OTHER()
extern void playermain_OTHER_mE22E734AA6FEB4168A845F06FE53D5B77EC2460A (void);
// 0x000003EF System.Void playermain::.ctor()
extern void playermain__ctor_m51DD0E0A91BC1EE46E46004BDEB9A08829BCE9AD (void);
// 0x000003F0 System.Void playermain/<OTHER>d__48::.ctor(System.Int32)
extern void U3COTHERU3Ed__48__ctor_m5614BDC61EE0311FE73BDF2E8CD344AE041045BD (void);
// 0x000003F1 System.Void playermain/<OTHER>d__48::System.IDisposable.Dispose()
extern void U3COTHERU3Ed__48_System_IDisposable_Dispose_mFA8C120D991A2CD9F00B084ED447859CC63AB39A (void);
// 0x000003F2 System.Boolean playermain/<OTHER>d__48::MoveNext()
extern void U3COTHERU3Ed__48_MoveNext_m759D536E650FAA4B0244F70617EBA83D4AF15248 (void);
// 0x000003F3 System.Object playermain/<OTHER>d__48::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3COTHERU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDFF4827EC256709E77F32DC3C36DE0390C28EA80 (void);
// 0x000003F4 System.Void playermain/<OTHER>d__48::System.Collections.IEnumerator.Reset()
extern void U3COTHERU3Ed__48_System_Collections_IEnumerator_Reset_m4674F834B072F810A67AE4BF0618E260D5865472 (void);
// 0x000003F5 System.Object playermain/<OTHER>d__48::System.Collections.IEnumerator.get_Current()
extern void U3COTHERU3Ed__48_System_Collections_IEnumerator_get_Current_mF3D78D6C443475337EF24152BDF6F16C6D63B190 (void);
// 0x000003F6 System.Void targetcam::Update()
extern void targetcam_Update_m401D954437AE228C3C5012D11055CB65E94DF50E (void);
// 0x000003F7 System.Void targetcam::.ctor()
extern void targetcam__ctor_mE87AE0CBC9E6161675A2168DB54C4BD414F89BBA (void);
// 0x000003F8 System.Void wheelAi::FixedUpdate()
extern void wheelAi_FixedUpdate_mEF53F96D82693BD0D9BEBF7292B5367E44F3BDB1 (void);
// 0x000003F9 System.Void wheelAi::.ctor()
extern void wheelAi__ctor_m865E614104D88AC4F11E8F48E894F68A5451CC41 (void);
// 0x000003FA System.Void IgnoreCollisions::Start()
extern void IgnoreCollisions_Start_m9EB3EA7B79E4C7DCE99B83DF7CDDB178AB1EFBDA (void);
// 0x000003FB System.Void IgnoreCollisions::.ctor()
extern void IgnoreCollisions__ctor_m1169033953B26ADA56EC6B49E8325FBC97B9D9C3 (void);
// 0x000003FC System.Void Waypoint::OnDrawGizmos()
extern void Waypoint_OnDrawGizmos_m546B609CA0CF39637507D157EDF3D12E0330F9BA (void);
// 0x000003FD System.Void Waypoint::.ctor()
extern void Waypoint__ctor_m89EA42D47EFB28A7076838800458399F153E973C (void);
// 0x000003FE System.Void WaypointMover::Start()
extern void WaypointMover_Start_m627D60B42E4ECA361DE01B072F4336FC63C4D3E3 (void);
// 0x000003FF System.Void WaypointMover::Update()
extern void WaypointMover_Update_m7F3E710EFF203AC490FDFB0FE75BDC42774B123C (void);
// 0x00000400 System.Void WaypointMover::ReverseDirection()
extern void WaypointMover_ReverseDirection_m15BE3BE19D51F58E73DCE256386368483E333418 (void);
// 0x00000401 System.Void WaypointMover::SetDirection(System.Int32)
extern void WaypointMover_SetDirection_mF54AB60301835AB0AC04170258F83E02650E7A40 (void);
// 0x00000402 System.Boolean WaypointMover::IsOnWaypoint()
extern void WaypointMover_IsOnWaypoint_m2369AAA586AB7BF542640BC4BED32779D47FBC38 (void);
// 0x00000403 System.Void WaypointMover::ReturnToPreviousWaypoint()
extern void WaypointMover_ReturnToPreviousWaypoint_m17B01F7F73D08C3F9A40FA6C61F502E830E976FE (void);
// 0x00000404 System.Boolean WaypointMover::isMoving()
extern void WaypointMover_isMoving_m2F283F050A6A90AC8090861C0B3722EC253A7CEC (void);
// 0x00000405 System.Void WaypointMover::Suspend(System.Boolean)
extern void WaypointMover_Suspend_mF6B0AFB0A5656DE45511DC89041D5265861DB3D4 (void);
// 0x00000406 System.Void WaypointMover::Pause()
extern void WaypointMover_Pause_mA6CBD593E73A3D9410F6CB9ECBF3794244E81865 (void);
// 0x00000407 System.Void WaypointMover::Unpause()
extern void WaypointMover_Unpause_m91D26C3F266C7F43075A4C9005533E84BE0236FA (void);
// 0x00000408 System.Void WaypointMover::ChangeWaypointMoverSpeed(System.Single)
extern void WaypointMover_ChangeWaypointMoverSpeed_mE968E84CC2D668F81B579409AF3CAC31021C0FA7 (void);
// 0x00000409 UnityEngine.Vector3 WaypointMover::IgnorePositionByAxis(UnityEngine.Vector3)
extern void WaypointMover_IgnorePositionByAxis_m04B7C06062F9F73DF85B55F9852DC2BA9F055483 (void);
// 0x0000040A System.Void WaypointMover::SmoothLookAt2D(UnityEngine.Transform,UnityEngine.Vector2,System.Single)
extern void WaypointMover_SmoothLookAt2D_m2CB9B23DBF1E9A86DFD8D44445CA89B610B4B3FA (void);
// 0x0000040B System.Void WaypointMover::.ctor()
extern void WaypointMover__ctor_m7EE206982A15350A17F01F192BB0890A7C72D43A (void);
// 0x0000040C System.Void WaypointsHolder::Awake()
extern void WaypointsHolder_Awake_m4948B860D1B226374C4C5758A2B3152B1F4C9C37 (void);
// 0x0000040D System.Void WaypointsHolder::Clean()
extern void WaypointsHolder_Clean_mE0C6238F7B90DDFDE736D826CC208AA675370836 (void);
// 0x0000040E System.Void WaypointsHolder::AddWaypoint(Waypoint)
extern void WaypointsHolder_AddWaypoint_m19A500CF26F9211E06AB660348C24AAC47BB4F1C (void);
// 0x0000040F System.Void WaypointsHolder::CreateWaypoint(UnityEngine.Vector3,System.String)
extern void WaypointsHolder_CreateWaypoint_m0587558C6286B20533AA26CD1971BA14777D74DB (void);
// 0x00000410 System.Void WaypointsHolder::OnDrawGizmos()
extern void WaypointsHolder_OnDrawGizmos_m8AD42290E21802DEE79803F23B39402A9D3522AB (void);
// 0x00000411 System.Void WaypointsHolder::.ctor()
extern void WaypointsHolder__ctor_m736A9CFD364D7F41D70A3518873E5D3BC4ABE2EF (void);
// 0x00000412 System.Void Animationtexture::LateUpdate()
extern void Animationtexture_LateUpdate_mB8641B6FA3CEF026F0ED36570F102A4CD02AC5BE (void);
// 0x00000413 System.Void Animationtexture::.ctor()
extern void Animationtexture__ctor_m9257566EF3015D7CD30ABD65D04B1CFEDA7611AE (void);
// 0x00000414 System.Void AutoTypeText::OnEnable()
extern void AutoTypeText_OnEnable_m3AC16591CF26C1B5488137DD57BB7E511641FACA (void);
// 0x00000415 System.Void AutoTypeText::OnDisable()
extern void AutoTypeText_OnDisable_mD34F2B008287361BD663EAD0C15965FD6A11F680 (void);
// 0x00000416 System.Collections.IEnumerator AutoTypeText::abc()
extern void AutoTypeText_abc_m046B8D1B8B3FF5F4EAF9AE9DBC4328EFF526DCF6 (void);
// 0x00000417 System.Collections.IEnumerator AutoTypeText::TypeText()
extern void AutoTypeText_TypeText_m9848A0BF62B3A385570F90DFD4D488DAED70D0B5 (void);
// 0x00000418 System.Void AutoTypeText::.ctor()
extern void AutoTypeText__ctor_mC2A8E1BE89011264AAB9C8DF628CBEDCDC5EFB37 (void);
// 0x00000419 System.Void AutoTypeText/<abc>d__6::.ctor(System.Int32)
extern void U3CabcU3Ed__6__ctor_m43A18AC38E38869EC38704D231C3192BD88B34FF (void);
// 0x0000041A System.Void AutoTypeText/<abc>d__6::System.IDisposable.Dispose()
extern void U3CabcU3Ed__6_System_IDisposable_Dispose_m334377B0DD65CB59F9A08190AA84717DFCDE6FA4 (void);
// 0x0000041B System.Boolean AutoTypeText/<abc>d__6::MoveNext()
extern void U3CabcU3Ed__6_MoveNext_mE75A82D858BD10D46808261F0D6055138CDA9A77 (void);
// 0x0000041C System.Object AutoTypeText/<abc>d__6::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CabcU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E6B56B3DC93F0BC5F924495777906F176C7113D (void);
// 0x0000041D System.Void AutoTypeText/<abc>d__6::System.Collections.IEnumerator.Reset()
extern void U3CabcU3Ed__6_System_Collections_IEnumerator_Reset_mECD8B6EB875F70004883F06C7A3BDFE201021FF4 (void);
// 0x0000041E System.Object AutoTypeText/<abc>d__6::System.Collections.IEnumerator.get_Current()
extern void U3CabcU3Ed__6_System_Collections_IEnumerator_get_Current_mD9320F71B108229AB2F622829C492F69E5AAE9C8 (void);
// 0x0000041F System.Void AutoTypeText/<TypeText>d__7::.ctor(System.Int32)
extern void U3CTypeTextU3Ed__7__ctor_mE690467BF2D539923CA7D99FDC7E59A01CAF1771 (void);
// 0x00000420 System.Void AutoTypeText/<TypeText>d__7::System.IDisposable.Dispose()
extern void U3CTypeTextU3Ed__7_System_IDisposable_Dispose_mD8CC279851A8912BF3A06D31485098AFA39E7B15 (void);
// 0x00000421 System.Boolean AutoTypeText/<TypeText>d__7::MoveNext()
extern void U3CTypeTextU3Ed__7_MoveNext_m1D7A9267BDDC1E9974B08F39A52F96F9E514472E (void);
// 0x00000422 System.Object AutoTypeText/<TypeText>d__7::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTypeTextU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBDB53438EAEEBD2F8D9349B79FE49779359ED9D3 (void);
// 0x00000423 System.Void AutoTypeText/<TypeText>d__7::System.Collections.IEnumerator.Reset()
extern void U3CTypeTextU3Ed__7_System_Collections_IEnumerator_Reset_m0AED2DACDF8C6C8E405ABC72555E846E51193C9C (void);
// 0x00000424 System.Object AutoTypeText/<TypeText>d__7::System.Collections.IEnumerator.get_Current()
extern void U3CTypeTextU3Ed__7_System_Collections_IEnumerator_get_Current_mFF4349924EBC96992A6D7061D898499D0A02A58C (void);
// 0x00000425 System.Void BannerAds::OnEnable()
extern void BannerAds_OnEnable_m457785E2F53AC74661F140FA06B9336F71000D30 (void);
// 0x00000426 System.Void BannerAds::.ctor()
extern void BannerAds__ctor_mEB77347D4750ABEDFA41CBC418B6D6CA31A26642 (void);
// 0x00000427 System.Void Changetexture::Start()
extern void Changetexture_Start_m5EDBAC0A7CF704DF5E6B15A6420A229AE75AD66C (void);
// 0x00000428 System.Void Changetexture::.ctor()
extern void Changetexture__ctor_mAE9F06B721BAE582A67F6CDEB23563560FF45396 (void);
// 0x00000429 System.Void FireBaseCustomScript::Start()
extern void FireBaseCustomScript_Start_m76D59FE3FDFFB847B9D8962333D6B8822213C1CD (void);
// 0x0000042A System.Void FireBaseCustomScript::Update()
extern void FireBaseCustomScript_Update_m24A78C8BF9C59CEA7BAEE81EC2876947C5B92606 (void);
// 0x0000042B System.Void FireBaseCustomScript::.ctor()
extern void FireBaseCustomScript__ctor_mACC1CD972576B11FD01E1462B35BBBBC06F3B437 (void);
// 0x0000042C System.Void FireBaseCustomScript/<>c::.cctor()
extern void U3CU3Ec__cctor_m7A9753C492DAB1618A99944EEC7DB487D2C6022C (void);
// 0x0000042D System.Void FireBaseCustomScript/<>c::.ctor()
extern void U3CU3Ec__ctor_mAD629CC497BE30C51A1B42600588430E26CF8E55 (void);
// 0x0000042E System.Void FireBaseCustomScript/<>c::<Start>b__0_0(System.Threading.Tasks.Task`1<Firebase.DependencyStatus>)
extern void U3CU3Ec_U3CStartU3Eb__0_0_m83DFD7D39F261A1FE7C0DFFECBE1700B9F46F7CB (void);
// 0x0000042F System.Void GDPRScript::Start()
extern void GDPRScript_Start_mA46522EF288B65A761737600AB3A0A088E089FAE (void);
// 0x00000430 System.Void GDPRScript::OnConsentInfoUpdated(GoogleMobileAds.Ump.Api.FormError)
extern void GDPRScript_OnConsentInfoUpdated_mB79863D0449F94C12A198205ACAF91F60D989C30 (void);
// 0x00000431 System.Void GDPRScript::LoadConsentForm()
extern void GDPRScript_LoadConsentForm_m73880CED644DA3E89869374D2C86AA015939ED3F (void);
// 0x00000432 System.Void GDPRScript::OnLoadConsentForm(GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError)
extern void GDPRScript_OnLoadConsentForm_m4781476B6FA7D1CF327D2265343528E225429149 (void);
// 0x00000433 System.Void GDPRScript::OnShowForm(GoogleMobileAds.Ump.Api.FormError)
extern void GDPRScript_OnShowForm_mCD37F93956CD85E8ED2AF4B9973227CB18FFF0B8 (void);
// 0x00000434 System.Void GDPRScript::Update()
extern void GDPRScript_Update_mC8E3E7E2A8391D521153616181E4897D66448B10 (void);
// 0x00000435 System.Void GDPRScript::.ctor()
extern void GDPRScript__ctor_m944A71B824C689C109CF0E9BEF7285A28D4A2AF0 (void);
// 0x00000436 System.Void LevelLock::OnEnable()
extern void LevelLock_OnEnable_m7E88F0E884A3F9F40ACDB625BDDD2E9BDDC27727 (void);
// 0x00000437 System.Void LevelLock::.ctor()
extern void LevelLock__ctor_m4D74F02291B20311658E8C42CE1A592DC50EBC78 (void);
// 0x00000438 System.Void LevelLockImages::Start()
extern void LevelLockImages_Start_mEF4CDC41F5FF59A98EFD48F9082C4AD91B9130D4 (void);
// 0x00000439 System.Void LevelLockImages::unlock(System.Int32)
extern void LevelLockImages_unlock_m791F57911F76860180FD41871317226110ACD130 (void);
// 0x0000043A System.Void LevelLockImages::.ctor()
extern void LevelLockImages__ctor_m109CFDDF066F6B4DD48048A90A8CFBED5C0B7A58 (void);
// 0x0000043B System.Void LivingOcean::Start()
extern void LivingOcean_Start_m7064E1220FD818142C18E698053FB624B15B6C6D (void);
// 0x0000043C System.Void LivingOcean::Update()
extern void LivingOcean_Update_mAB5E82F6BC4952659FC0D71A21FF4C65FD72A9FD (void);
// 0x0000043D System.Void LivingOcean::.ctor()
extern void LivingOcean__ctor_mC84A94742CF290D04D02883ACF553D1D26562B64 (void);
// 0x0000043E System.Void WC15PRO::Start()
extern void WC15PRO_Start_m527EF0C158891660925E9A2D57A0396BB3FA5FFB (void);
// 0x0000043F System.Void WC15PRO::Update()
extern void WC15PRO_Update_mABC7F7332A70CD7CB0D994C24E8B944C14B63118 (void);
// 0x00000440 System.Void WC15PRO::.ctor()
extern void WC15PRO__ctor_mCD60A019BDA530243705863BDBDA82412068F9FE (void);
// 0x00000441 System.Void brake::BrakeStart()
extern void brake_BrakeStart_mC07E925DF151D63302058633D554E7D3FE2039D3 (void);
// 0x00000442 System.Void brake::BrakeEnd()
extern void brake_BrakeEnd_mD509FF97A326C8C75BDC53FD17F985A6CA0625BF (void);
// 0x00000443 System.Void brake::.ctor()
extern void brake__ctor_mEFD130E71513BA98B0F55F1D8F37977E535ADCB5 (void);
// 0x00000444 System.Void cronspray::OnTriggerEnter(UnityEngine.Collider)
extern void cronspray_OnTriggerEnter_mDC434E2FC9FC4B63683ACFA7C49206E764882A72 (void);
// 0x00000445 System.Void cronspray::.ctor()
extern void cronspray__ctor_m95523D8C77A2D59CD82AD867649903A93D55FF07 (void);
// 0x00000446 System.Void gameplay::Start()
extern void gameplay_Start_mFFB5832671CD80199ACC2B5313147AD1E591B036 (void);
// 0x00000447 System.Void gameplay::Update()
extern void gameplay_Update_mBB5A5982E606FCA80775E4153F722957CB7D2C5F (void);
// 0x00000448 System.Void gameplay::restart()
extern void gameplay_restart_mC400A391483FA7CE7EA17B9583DE5F54D1622282 (void);
// 0x00000449 System.Void gameplay::pause()
extern void gameplay_pause_mE102E4CD01F9009476D77908FE82F4154E70893A (void);
// 0x0000044A System.Void gameplay::ok()
extern void gameplay_ok_m412BA5D15B0B6765E30A4C228C9FEA4B2D533E8D (void);
// 0x0000044B System.Void gameplay::home()
extern void gameplay_home_m9FA04AE6BE11565042A1F63834D3808EF85DCD0A (void);
// 0x0000044C System.Void gameplay::resume()
extern void gameplay_resume_m5E7BA244C060F8625B23837A8A364874B9B360F1 (void);
// 0x0000044D System.Void gameplay::next()
extern void gameplay_next_mAD1F682F18671D98EC8E2EB1262E7662E17B0CC2 (void);
// 0x0000044E System.Void gameplay::Steer()
extern void gameplay_Steer_m83E27326B5F3FCF3EA2EE66F5FB40E43828A8391 (void);
// 0x0000044F System.Void gameplay::Btns()
extern void gameplay_Btns_mDB326C3977882FD72AEAC18633C215020DC5941C (void);
// 0x00000450 System.Void gameplay::Tilt()
extern void gameplay_Tilt_mD38AFC29A4F2701B40F6E885F86F07244B6BE92E (void);
// 0x00000451 System.Collections.IEnumerator gameplay::lvl1()
extern void gameplay_lvl1_m222961C305B83C3AB7B11B8A3B26EA4633BFD51C (void);
// 0x00000452 System.Collections.IEnumerator gameplay::lvl2()
extern void gameplay_lvl2_mF5C4185CDD7BE11833ECDB4F9A609821A637BC7F (void);
// 0x00000453 System.Collections.IEnumerator gameplay::lvl5()
extern void gameplay_lvl5_mE465A99D7A5FDA173F615C95A56582FAA3553193 (void);
// 0x00000454 System.Collections.IEnumerator gameplay::lvl7()
extern void gameplay_lvl7_mD2457D7BEC6A0AE46E473CC4E2CDCF3EE8DBF7F0 (void);
// 0x00000455 System.Collections.IEnumerator gameplay::lvl0()
extern void gameplay_lvl0_mEEB025DEF93243F8959C9CF6E309A0582C316691 (void);
// 0x00000456 System.Void gameplay::.ctor()
extern void gameplay__ctor_mFD6E0DCA59095A12544391955B34ED1B3F472BCA (void);
// 0x00000457 System.Void gameplay/<lvl1>d__56::.ctor(System.Int32)
extern void U3Clvl1U3Ed__56__ctor_mE4E1A42457E3124976DD73E8A9A59F196E027090 (void);
// 0x00000458 System.Void gameplay/<lvl1>d__56::System.IDisposable.Dispose()
extern void U3Clvl1U3Ed__56_System_IDisposable_Dispose_mD644E8350E522FC488F9A31B7F82D5B0400F3A7D (void);
// 0x00000459 System.Boolean gameplay/<lvl1>d__56::MoveNext()
extern void U3Clvl1U3Ed__56_MoveNext_mA3B2ABC83D3CA44FB4415BD7B9B79603446CB7BA (void);
// 0x0000045A System.Object gameplay/<lvl1>d__56::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clvl1U3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEA76BFC814DD367A3B382F1EE6399BE317112A7E (void);
// 0x0000045B System.Void gameplay/<lvl1>d__56::System.Collections.IEnumerator.Reset()
extern void U3Clvl1U3Ed__56_System_Collections_IEnumerator_Reset_mE9C4D92ACE2895DEDF2B1FE7F2E3B8B434AF9A9A (void);
// 0x0000045C System.Object gameplay/<lvl1>d__56::System.Collections.IEnumerator.get_Current()
extern void U3Clvl1U3Ed__56_System_Collections_IEnumerator_get_Current_mF653D72D087211FC9AE7331E286474EE4EC350F8 (void);
// 0x0000045D System.Void gameplay/<lvl2>d__57::.ctor(System.Int32)
extern void U3Clvl2U3Ed__57__ctor_m2EFFDF87AA2BFC5D3DA40264E825A3C26362C0CE (void);
// 0x0000045E System.Void gameplay/<lvl2>d__57::System.IDisposable.Dispose()
extern void U3Clvl2U3Ed__57_System_IDisposable_Dispose_m4D1C8CFB2CDE3D156A311E59B92937267663962A (void);
// 0x0000045F System.Boolean gameplay/<lvl2>d__57::MoveNext()
extern void U3Clvl2U3Ed__57_MoveNext_mBF92F0E9CD589A47A4C3C51B8A3972091AAB102A (void);
// 0x00000460 System.Object gameplay/<lvl2>d__57::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clvl2U3Ed__57_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m502500F20DE9F50F19AC1A274D7864CFF008708F (void);
// 0x00000461 System.Void gameplay/<lvl2>d__57::System.Collections.IEnumerator.Reset()
extern void U3Clvl2U3Ed__57_System_Collections_IEnumerator_Reset_mBDD3D39B888218936D37127AF3FC42C46452EFB1 (void);
// 0x00000462 System.Object gameplay/<lvl2>d__57::System.Collections.IEnumerator.get_Current()
extern void U3Clvl2U3Ed__57_System_Collections_IEnumerator_get_Current_m7F1B9220F2F4A62551EA065C1A917ACB67DE5F45 (void);
// 0x00000463 System.Void gameplay/<lvl5>d__58::.ctor(System.Int32)
extern void U3Clvl5U3Ed__58__ctor_m5F5BC85F8D246E3C0D35E5FCF5650E48438DEE75 (void);
// 0x00000464 System.Void gameplay/<lvl5>d__58::System.IDisposable.Dispose()
extern void U3Clvl5U3Ed__58_System_IDisposable_Dispose_mBAEEC551D6A403B453B735AADF2DEF366AC952DE (void);
// 0x00000465 System.Boolean gameplay/<lvl5>d__58::MoveNext()
extern void U3Clvl5U3Ed__58_MoveNext_mE7C2D860F0B09C89B6CD167A1EC1CE8F31B5BFB7 (void);
// 0x00000466 System.Object gameplay/<lvl5>d__58::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clvl5U3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m21C3F4676E11B20E8C9DD88CC8483F6644AF9B18 (void);
// 0x00000467 System.Void gameplay/<lvl5>d__58::System.Collections.IEnumerator.Reset()
extern void U3Clvl5U3Ed__58_System_Collections_IEnumerator_Reset_m58488997363FEC377CD7CD8938796727DEACD2E4 (void);
// 0x00000468 System.Object gameplay/<lvl5>d__58::System.Collections.IEnumerator.get_Current()
extern void U3Clvl5U3Ed__58_System_Collections_IEnumerator_get_Current_mEFACCB740AA3DF190553CB5471E81B414FBAC7D6 (void);
// 0x00000469 System.Void gameplay/<lvl7>d__59::.ctor(System.Int32)
extern void U3Clvl7U3Ed__59__ctor_mEF40FF7E99BD7112CA3029749A5F8EDF590A9591 (void);
// 0x0000046A System.Void gameplay/<lvl7>d__59::System.IDisposable.Dispose()
extern void U3Clvl7U3Ed__59_System_IDisposable_Dispose_m1DD570AFC814E1D59B9622652298FD8D398DA3A6 (void);
// 0x0000046B System.Boolean gameplay/<lvl7>d__59::MoveNext()
extern void U3Clvl7U3Ed__59_MoveNext_mF8C606F84687908BBE9C90668719F1C036FAD8F7 (void);
// 0x0000046C System.Object gameplay/<lvl7>d__59::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clvl7U3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4EE7A5B50DA55D5482C2183630F78A16A2800F15 (void);
// 0x0000046D System.Void gameplay/<lvl7>d__59::System.Collections.IEnumerator.Reset()
extern void U3Clvl7U3Ed__59_System_Collections_IEnumerator_Reset_m5D77977BFF43716E13D5C93FA3B833D219CED75A (void);
// 0x0000046E System.Object gameplay/<lvl7>d__59::System.Collections.IEnumerator.get_Current()
extern void U3Clvl7U3Ed__59_System_Collections_IEnumerator_get_Current_m9AC0E3A9E524228C023189A55B8D0C946175E6F7 (void);
// 0x0000046F System.Void gameplay/<lvl0>d__60::.ctor(System.Int32)
extern void U3Clvl0U3Ed__60__ctor_m9DCD1CE8813F87353DE64E94CB456BDC65C8AADD (void);
// 0x00000470 System.Void gameplay/<lvl0>d__60::System.IDisposable.Dispose()
extern void U3Clvl0U3Ed__60_System_IDisposable_Dispose_m0978BEAAFB3BAAD4F4D90C39CF341A1C41058A59 (void);
// 0x00000471 System.Boolean gameplay/<lvl0>d__60::MoveNext()
extern void U3Clvl0U3Ed__60_MoveNext_mBAFE8AE59EF634DCACA4B763F775031489E23D7D (void);
// 0x00000472 System.Object gameplay/<lvl0>d__60::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clvl0U3Ed__60_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mABE72F3BB290F9274B298E22045957414E685854 (void);
// 0x00000473 System.Void gameplay/<lvl0>d__60::System.Collections.IEnumerator.Reset()
extern void U3Clvl0U3Ed__60_System_Collections_IEnumerator_Reset_m2249E8A74EB47EAF45BB4E180FE7D0C7C0B14C2B (void);
// 0x00000474 System.Object gameplay/<lvl0>d__60::System.Collections.IEnumerator.get_Current()
extern void U3Clvl0U3Ed__60_System_Collections_IEnumerator_get_Current_mA7B9E52589C82967F50793D3AEC50D52CB0083F6 (void);
// 0x00000475 System.Void mainmenu::Start()
extern void mainmenu_Start_mE676AB13A0BBE134B67DC212E75F4D7B6C55D678 (void);
// 0x00000476 System.Void mainmenu::play()
extern void mainmenu_play_m7261CCDD25A56890F76B532315B1889755B58F20 (void);
// 0x00000477 System.Void mainmenu::exit()
extern void mainmenu_exit_mD7D5A28C0EE46577051DB25C80D10FD4802A8479 (void);
// 0x00000478 System.Void mainmenu::No()
extern void mainmenu_No_m3A8510663F463FE0DC5C7DE835AF4E3EE7E4AF3F (void);
// 0x00000479 System.Void mainmenu::modeback()
extern void mainmenu_modeback_m7002FED76085C29B785538E29DA8AD8BB76BB4EF (void);
// 0x0000047A System.Void mainmenu::lvlback()
extern void mainmenu_lvlback_mB0E00D5A783A07604440B8FB0500FA3E09A35FBD (void);
// 0x0000047B System.Void mainmenu::lvl2back()
extern void mainmenu_lvl2back_m668ADEE90CCD11A67274B0E2BBDDB5407D23AAC2 (void);
// 0x0000047C System.Void mainmenu::setting()
extern void mainmenu_setting_m572FE3087ACEB2E6D6F2FB0E0D9D6418E5B4C9BB (void);
// 0x0000047D System.Void mainmenu::save()
extern void mainmenu_save_m7D0B44C4C1E1A924820E35923D12870FA1F8FC5E (void);
// 0x0000047E System.Void mainmenu::yes()
extern void mainmenu_yes_m715C547333E6162C1C52FBEFB5A1B213C6234EE1 (void);
// 0x0000047F System.Void mainmenu::Levels(System.Int32)
extern void mainmenu_Levels_mDB425FDD7B190B76D615A020C877D1F1E978DC48 (void);
// 0x00000480 System.Void mainmenu::fill()
extern void mainmenu_fill_m285CFDB4C93A4A18D19FFE6305AA1B401FFD4FBE (void);
// 0x00000481 System.Void mainmenu::Lvel(System.Int32)
extern void mainmenu_Lvel_m2FD1596CDC7B38677C373C65212FD2BD9586E58A (void);
// 0x00000482 System.Void mainmenu::select()
extern void mainmenu_select_m6E89A2254BD17C4EDD9C026A50F53CC4A6D4B8FF (void);
// 0x00000483 System.Void mainmenu::caremode()
extern void mainmenu_caremode_m135D95DECE48DC9F3666F65D9E41EE6D4DF26FE6 (void);
// 0x00000484 System.Void mainmenu::mode2()
extern void mainmenu_mode2_m64AB7149BF76459A20BDFDD6431F125A8DA72EB4 (void);
// 0x00000485 System.Void mainmenu::filldec()
extern void mainmenu_filldec_m55EC326D8E4115A86C72801D689559E86BFE28A9 (void);
// 0x00000486 System.Void mainmenu::fl()
extern void mainmenu_fl_mED7072A7D60B781BC5F75F08641DB599D10AC2F4 (void);
// 0x00000487 System.Void mainmenu::fldec()
extern void mainmenu_fldec_m1A4227DBAEBC5CF2945B214D85284B011282694C (void);
// 0x00000488 System.Void mainmenu::Steer()
extern void mainmenu_Steer_mA89A558C1066237511EEDE85A6D34F20C9E1298B (void);
// 0x00000489 System.Void mainmenu::Btns()
extern void mainmenu_Btns_m5A7B2B135B70C6BCC807209A18D1F291C63F10A5 (void);
// 0x0000048A System.Void mainmenu::Tilt()
extern void mainmenu_Tilt_mE9BD04004DC3F3D77ACC39E2B41C3AE9EDDB0CC5 (void);
// 0x0000048B System.Collections.IEnumerator mainmenu::loading()
extern void mainmenu_loading_m974C5533A63E1BCCD69F13414389E7A4894AA90B (void);
// 0x0000048C System.Void mainmenu::rateus()
extern void mainmenu_rateus_mC2251D455AF135BB4D11D814B8596CC4B0DA6802 (void);
// 0x0000048D System.Void mainmenu::privacy()
extern void mainmenu_privacy_m3CAABF71417F44F35A535F2043ECCAA2CBEE3ECF (void);
// 0x0000048E System.Void mainmenu::moregams()
extern void mainmenu_moregams_m6B4ABE148E83FB0A54F4B2D892EC1F9104496122 (void);
// 0x0000048F System.Void mainmenu::mudjeep()
extern void mainmenu_mudjeep_mC8E43510C671AA913649757244DEBD2221278B47 (void);
// 0x00000490 System.Void mainmenu::trainadd()
extern void mainmenu_trainadd_mA51D5360BC8EE6FFD835DA9B57BC2F1E5F187897 (void);
// 0x00000491 System.Collections.IEnumerator mainmenu::loding()
extern void mainmenu_loding_m3F53A9567827B070999CFDD722D504533DF87C9E (void);
// 0x00000492 System.Collections.IEnumerator mainmenu::load()
extern void mainmenu_load_m2C09862F74DAA82291C77B68A06CABE82B9CCA8C (void);
// 0x00000493 System.Collections.IEnumerator mainmenu::load1()
extern void mainmenu_load1_mFBDBDF9E120DF76AAA9786952432BCA6A89B0C66 (void);
// 0x00000494 System.Collections.IEnumerator mainmenu::lod2()
extern void mainmenu_lod2_m3C32CA7424199D1DF6BCAC484E1A712B1BE51FA8 (void);
// 0x00000495 System.Void mainmenu::.ctor()
extern void mainmenu__ctor_m8C25CE093C895EB2C3F69525880DBCA3188614F8 (void);
// 0x00000496 System.Void mainmenu/<loading>d__39::.ctor(System.Int32)
extern void U3CloadingU3Ed__39__ctor_m6E3FC03533E32165DBA82BC30E8E728E245D1DB0 (void);
// 0x00000497 System.Void mainmenu/<loading>d__39::System.IDisposable.Dispose()
extern void U3CloadingU3Ed__39_System_IDisposable_Dispose_m340482F2B6E36928BC7A77E7843026EDE08DEBD1 (void);
// 0x00000498 System.Boolean mainmenu/<loading>d__39::MoveNext()
extern void U3CloadingU3Ed__39_MoveNext_m3C0F230456D638E46F2954EE29BE24040C09C4FD (void);
// 0x00000499 System.Object mainmenu/<loading>d__39::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CloadingU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D74B40166DEA5F08B1626BE899B7BD3E4EC784B (void);
// 0x0000049A System.Void mainmenu/<loading>d__39::System.Collections.IEnumerator.Reset()
extern void U3CloadingU3Ed__39_System_Collections_IEnumerator_Reset_m7EC41A340C0613CDB99CD1EE5E5A95E952AD47EE (void);
// 0x0000049B System.Object mainmenu/<loading>d__39::System.Collections.IEnumerator.get_Current()
extern void U3CloadingU3Ed__39_System_Collections_IEnumerator_get_Current_m4D01BCCC9E254743B500D32E295079258EFC34BA (void);
// 0x0000049C System.Void mainmenu/<loding>d__45::.ctor(System.Int32)
extern void U3ClodingU3Ed__45__ctor_mA348507D99FF90AEB48918426A1CA4F39937C41A (void);
// 0x0000049D System.Void mainmenu/<loding>d__45::System.IDisposable.Dispose()
extern void U3ClodingU3Ed__45_System_IDisposable_Dispose_mBF0A271F3AB8B910EC9D49AE9D2110162FBAE4A4 (void);
// 0x0000049E System.Boolean mainmenu/<loding>d__45::MoveNext()
extern void U3ClodingU3Ed__45_MoveNext_mD0A1FB99261C69ADD2D7B430846AC751431D9A7B (void);
// 0x0000049F System.Object mainmenu/<loding>d__45::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3ClodingU3Ed__45_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCBD6CD24E193E34EBC9C494309CF7AECBB8074CF (void);
// 0x000004A0 System.Void mainmenu/<loding>d__45::System.Collections.IEnumerator.Reset()
extern void U3ClodingU3Ed__45_System_Collections_IEnumerator_Reset_m8757C7459472A549127A813CB0C53400F5957B64 (void);
// 0x000004A1 System.Object mainmenu/<loding>d__45::System.Collections.IEnumerator.get_Current()
extern void U3ClodingU3Ed__45_System_Collections_IEnumerator_get_Current_m85E0209029F5CEAD7E6370A74C02ABB2911F0349 (void);
// 0x000004A2 System.Void mainmenu/<load>d__46::.ctor(System.Int32)
extern void U3CloadU3Ed__46__ctor_m3F8833D5E42271B150929700613389E90A753EA9 (void);
// 0x000004A3 System.Void mainmenu/<load>d__46::System.IDisposable.Dispose()
extern void U3CloadU3Ed__46_System_IDisposable_Dispose_m64A9A989D0F422E2A10DBA8312AB0A4D55C9C566 (void);
// 0x000004A4 System.Boolean mainmenu/<load>d__46::MoveNext()
extern void U3CloadU3Ed__46_MoveNext_mF926F8EF42D729ACF1543D67BFEF38D38E58F9CA (void);
// 0x000004A5 System.Object mainmenu/<load>d__46::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CloadU3Ed__46_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF7D814B5290AFD4675C4A1ACA51D91854381876C (void);
// 0x000004A6 System.Void mainmenu/<load>d__46::System.Collections.IEnumerator.Reset()
extern void U3CloadU3Ed__46_System_Collections_IEnumerator_Reset_m6F7822F745718F8D484D30E140AB8537A53468E9 (void);
// 0x000004A7 System.Object mainmenu/<load>d__46::System.Collections.IEnumerator.get_Current()
extern void U3CloadU3Ed__46_System_Collections_IEnumerator_get_Current_m7DE394C9A1824F61F51EA0DC310A4BD59494C796 (void);
// 0x000004A8 System.Void mainmenu/<load1>d__47::.ctor(System.Int32)
extern void U3Cload1U3Ed__47__ctor_mEBA0E592445B2C566D58411EB5D4C82078FBBEF6 (void);
// 0x000004A9 System.Void mainmenu/<load1>d__47::System.IDisposable.Dispose()
extern void U3Cload1U3Ed__47_System_IDisposable_Dispose_m4BEB4DB183B0F6F71E186A3C18BB359254514962 (void);
// 0x000004AA System.Boolean mainmenu/<load1>d__47::MoveNext()
extern void U3Cload1U3Ed__47_MoveNext_m560459958FF03D72222F04AA9AB198DFFB6F933D (void);
// 0x000004AB System.Object mainmenu/<load1>d__47::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Cload1U3Ed__47_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCF06F55E47D6EB22E57CD961BC3C6DF6768AFAAF (void);
// 0x000004AC System.Void mainmenu/<load1>d__47::System.Collections.IEnumerator.Reset()
extern void U3Cload1U3Ed__47_System_Collections_IEnumerator_Reset_m08878A97DB160EE3BC983A626AB66D00CBCCAD3A (void);
// 0x000004AD System.Object mainmenu/<load1>d__47::System.Collections.IEnumerator.get_Current()
extern void U3Cload1U3Ed__47_System_Collections_IEnumerator_get_Current_m7F6A1C3904D02C53B01621FC9D3AFFB6E9960748 (void);
// 0x000004AE System.Void mainmenu/<lod2>d__48::.ctor(System.Int32)
extern void U3Clod2U3Ed__48__ctor_mD1723DAC9616E26A433A7E11CEB236671FB5B476 (void);
// 0x000004AF System.Void mainmenu/<lod2>d__48::System.IDisposable.Dispose()
extern void U3Clod2U3Ed__48_System_IDisposable_Dispose_m8BA719AA6E68CA80163FDC5EB3844D066E72244A (void);
// 0x000004B0 System.Boolean mainmenu/<lod2>d__48::MoveNext()
extern void U3Clod2U3Ed__48_MoveNext_m0388E3AD51F9A3043553B6B24E2C129FE881367E (void);
// 0x000004B1 System.Object mainmenu/<lod2>d__48::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clod2U3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m32C1CBC447D7A1F0933C26FB4009744C8B998E23 (void);
// 0x000004B2 System.Void mainmenu/<lod2>d__48::System.Collections.IEnumerator.Reset()
extern void U3Clod2U3Ed__48_System_Collections_IEnumerator_Reset_m6119113503F3F7DD48CD9298A57E31C5CC2DD099 (void);
// 0x000004B3 System.Object mainmenu/<lod2>d__48::System.Collections.IEnumerator.get_Current()
extern void U3Clod2U3Ed__48_System_Collections_IEnumerator_get_Current_m31F6464795D1660A336D499BC584E47E8E211BAF (void);
// 0x000004B4 System.Void manger2::Start()
extern void manger2_Start_mA7E429CD078EC38ED7CC1B24CB70DB95B9E51BD8 (void);
// 0x000004B5 System.Void manger2::pause()
extern void manger2_pause_m6897565431D5D01FECC8BFFC49BB880DA63EFF8C (void);
// 0x000004B6 System.Void manger2::restart()
extern void manger2_restart_mA279523795E7961FB46F0B144D1203368BEA6D57 (void);
// 0x000004B7 System.Void manger2::next()
extern void manger2_next_m553AFC823E40268EE9F208868D72699772BADDCF (void);
// 0x000004B8 System.Void manger2::resume()
extern void manger2_resume_m63B5C1A2462113DDA759B35A945D70701351B8EB (void);
// 0x000004B9 System.Void manger2::home()
extern void manger2_home_m9EB1CDFA7D913F7B904848029CCC755AC1F5C2B3 (void);
// 0x000004BA System.Collections.IEnumerator manger2::start()
extern void manger2_start_mF7905CD41FC4F9C5B6E99A3B72144A1C09860052 (void);
// 0x000004BB System.Collections.IEnumerator manger2::obect()
extern void manger2_obect_mE9A71697220BFB6C64C7B84A184C7EC578E071EA (void);
// 0x000004BC System.Collections.IEnumerator manger2::strt()
extern void manger2_strt_m039C3A25F67681C51A80999F2376E17ABDFDD1E6 (void);
// 0x000004BD System.Void manger2::.ctor()
extern void manger2__ctor_mB6107261188F36DEE5786649F269CFC3AE4AE571 (void);
// 0x000004BE System.Void manger2/<start>d__24::.ctor(System.Int32)
extern void U3CstartU3Ed__24__ctor_m5DEE7DDB19BF271D5A33A477561A6E315342189A (void);
// 0x000004BF System.Void manger2/<start>d__24::System.IDisposable.Dispose()
extern void U3CstartU3Ed__24_System_IDisposable_Dispose_mFACAD12B5AD1C8EEC0441BA4AA524B261C8FDB77 (void);
// 0x000004C0 System.Boolean manger2/<start>d__24::MoveNext()
extern void U3CstartU3Ed__24_MoveNext_mF77A2C7AB364092B7C10877F4031D667E7A8D62B (void);
// 0x000004C1 System.Object manger2/<start>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CstartU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD8FC10AF493EFB7391EEB109EE8D9028C3D552A8 (void);
// 0x000004C2 System.Void manger2/<start>d__24::System.Collections.IEnumerator.Reset()
extern void U3CstartU3Ed__24_System_Collections_IEnumerator_Reset_mF6780D537B9EBF1E49349F59FC3D0B8ED362428A (void);
// 0x000004C3 System.Object manger2/<start>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CstartU3Ed__24_System_Collections_IEnumerator_get_Current_m4C1BA7A30A06294A299D337756AE0AF96A99572C (void);
// 0x000004C4 System.Void manger2/<obect>d__25::.ctor(System.Int32)
extern void U3CobectU3Ed__25__ctor_mE5DC237276F154F4CC97B951EBEBD42410A4B0DE (void);
// 0x000004C5 System.Void manger2/<obect>d__25::System.IDisposable.Dispose()
extern void U3CobectU3Ed__25_System_IDisposable_Dispose_m42743905108FA175D898410D377F0696B456B0E9 (void);
// 0x000004C6 System.Boolean manger2/<obect>d__25::MoveNext()
extern void U3CobectU3Ed__25_MoveNext_mABB5C255960734E6AF355288587AB7083CD65D26 (void);
// 0x000004C7 System.Object manger2/<obect>d__25::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CobectU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m92463448801F7F98AEFE06D2D1F5F6E3B148BA22 (void);
// 0x000004C8 System.Void manger2/<obect>d__25::System.Collections.IEnumerator.Reset()
extern void U3CobectU3Ed__25_System_Collections_IEnumerator_Reset_mC27CAB5EED358B0E5F7AE37AB0E4BB481C66F505 (void);
// 0x000004C9 System.Object manger2/<obect>d__25::System.Collections.IEnumerator.get_Current()
extern void U3CobectU3Ed__25_System_Collections_IEnumerator_get_Current_mBA65AE75917D2F878E8A835886C7A4D6DFDB4AB2 (void);
// 0x000004CA System.Void manger2/<strt>d__26::.ctor(System.Int32)
extern void U3CstrtU3Ed__26__ctor_mCCDCD27B5FD203C4A6FAD5DFBBD5E7452B315620 (void);
// 0x000004CB System.Void manger2/<strt>d__26::System.IDisposable.Dispose()
extern void U3CstrtU3Ed__26_System_IDisposable_Dispose_m6C707852E0BE32F4725F21A6042848C63FA3DD0B (void);
// 0x000004CC System.Boolean manger2/<strt>d__26::MoveNext()
extern void U3CstrtU3Ed__26_MoveNext_m920B871C44159CD4CE95D3910129215DBDEA3207 (void);
// 0x000004CD System.Object manger2/<strt>d__26::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CstrtU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1E7F837CAD35AC52B569095A33494BA6747F2B0F (void);
// 0x000004CE System.Void manger2/<strt>d__26::System.Collections.IEnumerator.Reset()
extern void U3CstrtU3Ed__26_System_Collections_IEnumerator_Reset_mFAD7C77823E3FE573B35E48CA2AE175F79A346BE (void);
// 0x000004CF System.Object manger2/<strt>d__26::System.Collections.IEnumerator.get_Current()
extern void U3CstrtU3Ed__26_System_Collections_IEnumerator_get_Current_mCB29ECBEA6C4DCFD31BB26971E2E6993390CC739 (void);
// 0x000004D0 System.Void mode2lock::OnEnable()
extern void mode2lock_OnEnable_m7226A39AB1C43B5DB6F2CCF0128975EB3A4B4BDF (void);
// 0x000004D1 System.Void mode2lock::.ctor()
extern void mode2lock__ctor_m2F01AB4CFC71FE8C30C9B9EDA16CE926DAC7AE68 (void);
// 0x000004D2 System.Void player::Start()
extern void player_Start_m70A61B77EB8E3359FE4CBB98768C3D17ECD8F65A (void);
// 0x000004D3 System.Void player::OnTriggerEnter(UnityEngine.Collider)
extern void player_OnTriggerEnter_mE6E5E2468AD3FE9BECD21DE4D1E3BAB255FA2651 (void);
// 0x000004D4 System.Collections.IEnumerator player::end()
extern void player_end_mE3A73A581F4D2AE9D208F1B10F6C62E4AB189E31 (void);
// 0x000004D5 System.Collections.IEnumerator player::end1()
extern void player_end1_m570774867FE7BA7D3DBCEB2E4E75AA766332DE00 (void);
// 0x000004D6 System.Collections.IEnumerator player::lvl6()
extern void player_lvl6_m33375427C70655644B18D709087A612DC8FA5A5E (void);
// 0x000004D7 System.Void player::.ctor()
extern void player__ctor_m714EC555B4D536FDD17E68FB029561E4AC0124FB (void);
// 0x000004D8 System.Void player/<end>d__17::.ctor(System.Int32)
extern void U3CendU3Ed__17__ctor_m54D9016ECDB432ED712D6F06007B82FCF226CAFC (void);
// 0x000004D9 System.Void player/<end>d__17::System.IDisposable.Dispose()
extern void U3CendU3Ed__17_System_IDisposable_Dispose_m87B1BAD34325D7644464870E5F275AD5AB6F24DB (void);
// 0x000004DA System.Boolean player/<end>d__17::MoveNext()
extern void U3CendU3Ed__17_MoveNext_m57B9FBBD0FB00A9819DCDCAA4851339BC378D09F (void);
// 0x000004DB System.Object player/<end>d__17::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CendU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A3DBD6F5B7F6E46C4FB207F9540541464F2B53A (void);
// 0x000004DC System.Void player/<end>d__17::System.Collections.IEnumerator.Reset()
extern void U3CendU3Ed__17_System_Collections_IEnumerator_Reset_mAC65AD5E5A699707CA188140BA46B67B990C35D0 (void);
// 0x000004DD System.Object player/<end>d__17::System.Collections.IEnumerator.get_Current()
extern void U3CendU3Ed__17_System_Collections_IEnumerator_get_Current_mFCB8DA308C1E2FB13319B250288E37A9625CDFCB (void);
// 0x000004DE System.Void player/<end1>d__18::.ctor(System.Int32)
extern void U3Cend1U3Ed__18__ctor_mCA0B5DC2E2851ABDD7A79BD4D598C1257DCD25BE (void);
// 0x000004DF System.Void player/<end1>d__18::System.IDisposable.Dispose()
extern void U3Cend1U3Ed__18_System_IDisposable_Dispose_m0CA518025250D42F03A0892B90BC9ED04D6292B5 (void);
// 0x000004E0 System.Boolean player/<end1>d__18::MoveNext()
extern void U3Cend1U3Ed__18_MoveNext_m14E110FA84127AE0C30158C40134C50A0D744B56 (void);
// 0x000004E1 System.Object player/<end1>d__18::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Cend1U3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7A720503E941B74F8E5D33A81BD6697774CE4B8C (void);
// 0x000004E2 System.Void player/<end1>d__18::System.Collections.IEnumerator.Reset()
extern void U3Cend1U3Ed__18_System_Collections_IEnumerator_Reset_m07DA0736EF0D66CAB0F90F42C5A5A49835DE169E (void);
// 0x000004E3 System.Object player/<end1>d__18::System.Collections.IEnumerator.get_Current()
extern void U3Cend1U3Ed__18_System_Collections_IEnumerator_get_Current_m5DE3F0165B0786FA5F6FA1581D13F939182EBF8E (void);
// 0x000004E4 System.Void player/<lvl6>d__19::.ctor(System.Int32)
extern void U3Clvl6U3Ed__19__ctor_mDB18D4251B4F1069B69F9B95D57511CEDB1ECBB4 (void);
// 0x000004E5 System.Void player/<lvl6>d__19::System.IDisposable.Dispose()
extern void U3Clvl6U3Ed__19_System_IDisposable_Dispose_m4E1D48EA935AB6220B5C7EB5417B3E828BF7C6C3 (void);
// 0x000004E6 System.Boolean player/<lvl6>d__19::MoveNext()
extern void U3Clvl6U3Ed__19_MoveNext_mA8D597A94CA776EA50A2E13C1E930684D4D0ADAC (void);
// 0x000004E7 System.Object player/<lvl6>d__19::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3Clvl6U3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4056B2F8F96C4274CABDD0D14C45F05481003ABB (void);
// 0x000004E8 System.Void player/<lvl6>d__19::System.Collections.IEnumerator.Reset()
extern void U3Clvl6U3Ed__19_System_Collections_IEnumerator_Reset_mC12E146F0D6D1CB27AFB7C7BA03DA91F10D7E4E2 (void);
// 0x000004E9 System.Object player/<lvl6>d__19::System.Collections.IEnumerator.get_Current()
extern void U3Clvl6U3Ed__19_System_Collections_IEnumerator_get_Current_mA46297F7242F0AF07D15F6FC61D55D8DEA4164D1 (void);
// 0x000004EA System.Void startscene::Start()
extern void startscene_Start_mCA3EB25F76D13565AC3A7D6B2E26B462719289B5 (void);
// 0x000004EB System.Void startscene::accept()
extern void startscene_accept_m626A215293B8F4070CE0FDADE63092EB0FB36789 (void);
// 0x000004EC System.Collections.IEnumerator startscene::load()
extern void startscene_load_mD919890EC64F9A4913422ED0A59861A6DE185C3B (void);
// 0x000004ED System.Void startscene::privacy()
extern void startscene_privacy_m5AFB9F9821B6177644A6F164EEEB106DC930B941 (void);
// 0x000004EE System.Void startscene::.ctor()
extern void startscene__ctor_m24A2FECEF25E362C49F3279DD9D8E2FBF9723A30 (void);
// 0x000004EF System.Void startscene/<load>d__4::.ctor(System.Int32)
extern void U3CloadU3Ed__4__ctor_mDDEB6A99F7CD51746E3F63C14596586EBE32CD8C (void);
// 0x000004F0 System.Void startscene/<load>d__4::System.IDisposable.Dispose()
extern void U3CloadU3Ed__4_System_IDisposable_Dispose_mF0978BF8E970E01288786C36C522FFF679326B42 (void);
// 0x000004F1 System.Boolean startscene/<load>d__4::MoveNext()
extern void U3CloadU3Ed__4_MoveNext_mE59C466EC3D71E544802AD4A83729A4DADAAF415 (void);
// 0x000004F2 System.Object startscene/<load>d__4::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CloadU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A7B178E9FC4BF7AA0E8E01F4149B530309C422C (void);
// 0x000004F3 System.Void startscene/<load>d__4::System.Collections.IEnumerator.Reset()
extern void U3CloadU3Ed__4_System_Collections_IEnumerator_Reset_m19068CB4D993EBF7D6154E6C7C183B0F09053E90 (void);
// 0x000004F4 System.Object startscene/<load>d__4::System.Collections.IEnumerator.get_Current()
extern void U3CloadU3Ed__4_System_Collections_IEnumerator_get_Current_mB92520266F090E771D54FE565B244BE04ABEED44 (void);
// 0x000004F5 System.Void wheelrotator::Update()
extern void wheelrotator_Update_m6D7476FD86D382BD126CDEA349C82D95E4C915CD (void);
// 0x000004F6 System.Void wheelrotator::.ctor()
extern void wheelrotator__ctor_m8B6A002D79228576E25ACCA4DA05EA38C77A9DAF (void);
// 0x000004F7 System.Void VisCircle.PowerUpAnimation::Awake()
extern void PowerUpAnimation_Awake_m86D57123EB9867C7249AC5CE3A89585892BE85C9 (void);
// 0x000004F8 System.Void VisCircle.PowerUpAnimation::Update()
extern void PowerUpAnimation_Update_m23F1732F1AEDA9DDD264B102F4D2E318487E4B8A (void);
// 0x000004F9 System.Boolean VisCircle.PowerUpAnimation::GetAnimateScale()
extern void PowerUpAnimation_GetAnimateScale_mFE8415868C3D80ACAB2EB043467573C881FFC372 (void);
// 0x000004FA System.Void VisCircle.PowerUpAnimation::SetAnimateScale(System.Boolean)
extern void PowerUpAnimation_SetAnimateScale_m5D976F9D34363C37DB653092F840250809E88630 (void);
// 0x000004FB System.Boolean VisCircle.PowerUpAnimation::GetAnimateYOffset()
extern void PowerUpAnimation_GetAnimateYOffset_mAA21F55DF9EA0B02E33FF0DE3CA63C87429949D4 (void);
// 0x000004FC System.Void VisCircle.PowerUpAnimation::SetAnimateYOffset(System.Boolean)
extern void PowerUpAnimation_SetAnimateYOffset_mD5CCE23D925997D7F1427197978668B3B7B18ED9 (void);
// 0x000004FD System.Boolean VisCircle.PowerUpAnimation::GetAnimateRotation()
extern void PowerUpAnimation_GetAnimateRotation_m950258CAAF23083363550F10DC8F9E308793C8EA (void);
// 0x000004FE System.Void VisCircle.PowerUpAnimation::SetAnimateRotation(System.Boolean)
extern void PowerUpAnimation_SetAnimateRotation_m0A68C8EA0D46F36B260499A6CD16EDF37CFA781B (void);
// 0x000004FF System.Void VisCircle.PowerUpAnimation::.ctor()
extern void PowerUpAnimation__ctor_m295920A8847ADB76E2B834ED030883A90EA85937 (void);
// 0x00000500 System.Void SciFiArsenal.SciFiButtonScript::Start()
extern void SciFiButtonScript_Start_m7BA1597132823AD8A18BDB4C9D5C5D9B3B350F70 (void);
// 0x00000501 System.Void SciFiArsenal.SciFiButtonScript::Update()
extern void SciFiButtonScript_Update_m53F0945741785A523374899C9E286A9C70F8CED7 (void);
// 0x00000502 System.Void SciFiArsenal.SciFiButtonScript::getProjectileNames()
extern void SciFiButtonScript_getProjectileNames_m4914663374B7C9443259B666295471DB67C0CACD (void);
// 0x00000503 System.Boolean SciFiArsenal.SciFiButtonScript::overButton()
extern void SciFiButtonScript_overButton_m158214D06C2514E4D613CED37594D9E2EBBDE7BE (void);
// 0x00000504 System.Void SciFiArsenal.SciFiButtonScript::.ctor()
extern void SciFiButtonScript__ctor_m369266C4350421B8708F7B1D197CC5C4E32A5B32 (void);
// 0x00000505 System.Void SciFiArsenal.SciFiDragMouseOrbit::Start()
extern void SciFiDragMouseOrbit_Start_m326700180FEF0036398C20C052116C41F48467CA (void);
// 0x00000506 System.Void SciFiArsenal.SciFiDragMouseOrbit::LateUpdate()
extern void SciFiDragMouseOrbit_LateUpdate_mBE29061BC33758FEA1402C7577CAA7035313CDDC (void);
// 0x00000507 System.Single SciFiArsenal.SciFiDragMouseOrbit::ClampAngle(System.Single,System.Single,System.Single)
extern void SciFiDragMouseOrbit_ClampAngle_m884C9992200886970132850907155B00FCB28AB6 (void);
// 0x00000508 System.Void SciFiArsenal.SciFiDragMouseOrbit::.ctor()
extern void SciFiDragMouseOrbit__ctor_m580265C87EA4D5FEBC0D80754ADC4F0412AD441A (void);
// 0x00000509 System.Void SciFiArsenal.SciFiFireProjectile::Start()
extern void SciFiFireProjectile_Start_m95FDFFE85F8E65A19BF7B6E2AEC5E53F2430F055 (void);
// 0x0000050A System.Void SciFiArsenal.SciFiFireProjectile::Update()
extern void SciFiFireProjectile_Update_m011F0B463F3429430E8E0DE950B68B9F159F419B (void);
// 0x0000050B System.Void SciFiArsenal.SciFiFireProjectile::nextEffect()
extern void SciFiFireProjectile_nextEffect_m8A4EAB2B4D050A395F7737AC17106E726598E663 (void);
// 0x0000050C System.Void SciFiArsenal.SciFiFireProjectile::previousEffect()
extern void SciFiFireProjectile_previousEffect_m973612AA69ED7DFC06025E60F10D6B787E50F3D9 (void);
// 0x0000050D System.Void SciFiArsenal.SciFiFireProjectile::AdjustSpeed(System.Single)
extern void SciFiFireProjectile_AdjustSpeed_m8C9AE252A0F76ADC1E6B05626A4A03D6E70B42A4 (void);
// 0x0000050E System.Void SciFiArsenal.SciFiFireProjectile::.ctor()
extern void SciFiFireProjectile__ctor_m26B5E81EBCAB2622A88E4E8D323370DFF8AEBC8E (void);
// 0x0000050F System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiProjectiles()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiProjectiles_m93914A6C5CB67EFEDD76764A89F7D0A19358E1D5 (void);
// 0x00000510 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiBeamup()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiBeamup_mF9EC8CFEECEC54A3CB59272F00DB927ADE377B1A (void);
// 0x00000511 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiBuff()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiBuff_m6066299E70EDBAB24CF864CBFCB0B5C3AA0BBCB2 (void);
// 0x00000512 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiFlamethrowers2()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiFlamethrowers2_mD938FB543EF0091AA597F3C516D2142AD7D8DDCC (void);
// 0x00000513 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiQuestZone()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiQuestZone_mC52A779F264A962164A2864FCCF12C86567501B2 (void);
// 0x00000514 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiLightjump()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiLightjump_m8010FC2BF0382EB66EC486E98C7CCD0F83B05EC1 (void);
// 0x00000515 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiLoot()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiLoot_m79A1122E96AD3A489F0AB9576B260A118F324CEE (void);
// 0x00000516 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiBeams()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiBeams_m4169243C2F1220243D521E431667E53CDE8D2EDA (void);
// 0x00000517 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiPortals()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiPortals_m1B7AAEC563EFAA4CF80A7411DBA20DDF379DFBA4 (void);
// 0x00000518 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiRegenerate()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiRegenerate_mAE8BF2283EE990583162F1C3531A8AE51FD5751B (void);
// 0x00000519 System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiShields()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiShields_m72B4327170992D9B693C60813ABF732576456F04 (void);
// 0x0000051A System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiSwirlyAura()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiSwirlyAura_mE34C3E63675B797F09EFB2D49A78D189E0876E02 (void);
// 0x0000051B System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiWarpgates()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiWarpgates_m1C088F622AEF31198B2AE306EC37CCE4AA841FEB (void);
// 0x0000051C System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiJetflame()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiJetflame_mB7D76379A24F94EE6D8518EA8C500BBB6A9A367D (void);
// 0x0000051D System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiUltimateNova()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiUltimateNova_mE28A7B4BF62E38E5139985522F1D7BB16B5B2686 (void);
// 0x0000051E System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiFire()
extern void SciFiLoadSceneOnClick_LoadSceneSciFiFire_m85822917432EA9CFEEB4E33FE6CA1BAD849055EA (void);
// 0x0000051F System.Void SciFiArsenal.SciFiLoadSceneOnClick::.ctor()
extern void SciFiLoadSceneOnClick__ctor_m8381A44903E961A976698EB33A2FAF3D85B08096 (void);
// 0x00000520 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate1()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate1_mBFEEDE488C9EBB50977A36E7866F7878B438258E (void);
// 0x00000521 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate2()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate2_mE55DC601D3322B3FDE12D51C11474CF58F0AAB2D (void);
// 0x00000522 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate3()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate3_m515D307D0B1A253CEB83C9712E209F5812A8F8A1 (void);
// 0x00000523 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate4()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate4_m06A36AA374DDA831648253325ADF3335119C4B4E (void);
// 0x00000524 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate5()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate5_mF47CC38EE3F604BD25DF66E9C02CCE978393C811 (void);
// 0x00000525 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate6()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate6_m6D1312659D7FD87DE136C8B210749F08855DF150 (void);
// 0x00000526 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate7()
extern void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate7_m71F94D4395FA3509F6B4F5EEC014F567EB22DA8C (void);
// 0x00000527 System.Void SciFiArsenal.SciFiLoadSceneOnClick2::.ctor()
extern void SciFiLoadSceneOnClick2__ctor_mE63C013AF5B353C5F91141B7B584BBBCC9EB5052 (void);
// 0x00000528 System.Void SciFiArsenal.SciFiLoopScript::Start()
extern void SciFiLoopScript_Start_m92BD74B68970E1645DB506039D732588644F3083 (void);
// 0x00000529 System.Void SciFiArsenal.SciFiLoopScript::PlayEffect()
extern void SciFiLoopScript_PlayEffect_mADC143884E30129DD40FF80FF9363432B5A582A1 (void);
// 0x0000052A System.Collections.IEnumerator SciFiArsenal.SciFiLoopScript::EffectLoop()
extern void SciFiLoopScript_EffectLoop_m73BC3A0884B7A62594CA5F184D431DB81E2A6ED7 (void);
// 0x0000052B System.Void SciFiArsenal.SciFiLoopScript::.ctor()
extern void SciFiLoopScript__ctor_m7839608A2E7C9BC279192801647BA4FD010F3423 (void);
// 0x0000052C System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::.ctor(System.Int32)
extern void U3CEffectLoopU3Ed__4__ctor_m9FDDAD0DE8BB6450657E8E7B7B83BC85838F6B6C (void);
// 0x0000052D System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.IDisposable.Dispose()
extern void U3CEffectLoopU3Ed__4_System_IDisposable_Dispose_m4B490EBA83EF4F6E7753CA05239EFB051DFDBCA1 (void);
// 0x0000052E System.Boolean SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::MoveNext()
extern void U3CEffectLoopU3Ed__4_MoveNext_m8E017D4C966F7F2F30AEC01CD91481C62A77F37A (void);
// 0x0000052F System.Object SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CEffectLoopU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5C6B23587D0C866DFF63356BDAEEB61CA603BB3B (void);
// 0x00000530 System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.Collections.IEnumerator.Reset()
extern void U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m992C579A5BA9707614B0E1C087C94BED47BC8C11 (void);
// 0x00000531 System.Object SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.Collections.IEnumerator.get_Current()
extern void U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_get_Current_m79C803F68611B6834219B1F249C098DAF2E52BC7 (void);
// 0x00000532 System.Void SciFiArsenal.SciFiProjectileScript::Start()
extern void SciFiProjectileScript_Start_m51A2AB6645CD8812DBA218816593DE0A31F8DEB4 (void);
// 0x00000533 System.Void SciFiArsenal.SciFiProjectileScript::OnCollisionEnter(UnityEngine.Collision)
extern void SciFiProjectileScript_OnCollisionEnter_mBB251D2DE353830B3A498EEDB87707697151D113 (void);
// 0x00000534 System.Void SciFiArsenal.SciFiProjectileScript::.ctor()
extern void SciFiProjectileScript__ctor_mBC875DA805361CFAFE69143920225E7D5E8991A1 (void);
// 0x00000535 System.Void SciFiArsenal.SciFiLightFade::Start()
extern void SciFiLightFade_Start_mCE8FCD03FA6B948DAE410C5E2DDAD4507D5EACE2 (void);
// 0x00000536 System.Void SciFiArsenal.SciFiLightFade::Update()
extern void SciFiLightFade_Update_m3CFC23B76DCCAD432CE8A6E3BCFDBD6566CE828C (void);
// 0x00000537 System.Void SciFiArsenal.SciFiLightFade::.ctor()
extern void SciFiLightFade__ctor_m742F8297FDBDD4BA15CDDFE01C1C89B0728EF1F6 (void);
// 0x00000538 System.Void SciFiArsenal.SciFiRotation::Start()
extern void SciFiRotation_Start_m0A8F763B7C4609CA6EC4FD58115808655954836B (void);
// 0x00000539 System.Void SciFiArsenal.SciFiRotation::Update()
extern void SciFiRotation_Update_mF8A782B9AA713AC15A8CB7E64B09802BB9FED2F5 (void);
// 0x0000053A System.Void SciFiArsenal.SciFiRotation::.ctor()
extern void SciFiRotation__ctor_m4C33B40B7C32D1DDFF206AA954984946EC3FF2C3 (void);
static Il2CppMethodPointer s_methodPointers[1338] = 
{
	SciFiBeamScript_Start_m670D10209F881EA1FA95473F38599CA57E5F8B42,
	SciFiBeamScript_Update_m0639C5CBDCA0D5ED211C73B4765BE79A358DCF66,
	SciFiBeamScript_nextBeam_m886ACE8FF1FA35D1E81EFDCE6394BD5E63739F70,
	SciFiBeamScript_previousBeam_mE13F29D12E1ACC902B236F937B315F2C5368E85A,
	SciFiBeamScript_UpdateEndOffset_m56BEADDCA773730BC3C1465D6C7627F75BCC6255,
	SciFiBeamScript_UpdateScrollSpeed_m5553A758C651F353B0EC04F28CDF86CEAE674AC7,
	SciFiBeamScript_ShootBeamInDir_m493F228B41452BC0C98AD08DED9960DEF96E7D32,
	SciFiBeamScript__ctor_mCDA142C63DC33AD973FA8254D083404DAC90D917,
	SciFiLightFlicker_Start_mFA08B8D196F758A37ADC7B270F9FFBDD0A6CE18C,
	SciFiLightFlicker_Update_m3E217B9164B415B2181B36B78B1277B825D807F9,
	SciFiLightFlicker_EvalWave_m6904FDCF56316DF952FB4A332AABA6A9945068AA,
	SciFiLightFlicker__ctor_m96F1D9B988BF135C27544FB279DB48C9AA7A9705,
	AdsManager_Awake_m643441EC8FDE1A0243445E78E206F17CCA9C2A8D,
	AdsManager_InitializeAdmob_mEB4E7AE9BD6AE2A340A399028B52C7D4909AFFC3,
	AdsManager_OnInitializationComp_mB6838C29E6A0E6D66DBF0719D8478BAA0FD06F1B,
	AdsManager_OnApplicationPause_mF959291217B4D051FFD7B85E5AE48AD9C1EF61E7,
	AdsManager_DelayInAppOpen_mB50D270993AA9ADE9727CAFB4413970C01E6AA54,
	AdsManager_checkForAdIds_mBF6A2BACC70A056A611F87F43E56AA5ED4BF2C75,
	AdsManager_CreateAdRequest_m29A2B3664ED7BD0FCF2039E56393928721707486,
	AdsManager_ShowAdmobBannerCollapsible_m07F29E5ADB0BF2A41F3EC330B1489B4B8686ECC0,
	AdsManager_showAdmobBanner_mAC8AFC4F68852F1C99CEC723AD6118C58DCE0880,
	AdsManager_changeBannerBools_mC1914C3CF9D7EB0E821A7CE3D9F285A95F62D7DB,
	AdsManager_ListenToAdEvents_mD16614710782E68296FCCC2A8E230872CA1061BD,
	AdsManager_hideAdMobBanner_m1ABD2CF9B363C03CF13074CE2A09E71DDD46F098,
	AdsManager_showAdMobBannerTop_m0083564C06FDC248192D4698DAFC2BC9500A7251,
	AdsManager_showAdMobBannerBottom_mE843E7CA754A0ADB907FEF1B260235824FCBA1E5,
	AdsManager_showAdMobBannerTopLeft_mAE2691DA490B6DCFD99552689FB03C1CA4B959B1,
	AdsManager_showAdMobBannerTopRight_m7B91DDB4C55762536FFCC60C3F61649480E5D623,
	AdsManager_showAdMobBannerBottomLeft_mC7E18E771D9BE6CE036555D9930B5A4770714AB3,
	AdsManager_showAdMobBannerBottomRight_mB9AA02C345C35D2127B4B9C21D4A378830744FCF,
	AdsManager_showAdMobBannerCenter_mD8383F99BB0781FF5201619CB54986CD41DF7204,
	AdsManager_showAdMobRectangleBannerTop_m5DEAC503A1B25AD4C8596B22E3AE17D7AC056CA2,
	AdsManager_showAdMobRectangleBannerBottom_mFC104D12F36458B8C0AD5ED9D555B3C1B5399141,
	AdsManager_showAdMobRectangleBannerTopLeft_mD1690EC887B9A9B8025EC4280154C06909C0ABB1,
	AdsManager_showAdMobRectangleBannerTopRight_m61395B91B75BB4CADE95E0181569FB8C04B8E5FE,
	AdsManager_showAdMobRectangleBannerBottomLeft_m936CCC705691EC742E3335AFF51021AECE918C80,
	AdsManager_showAdMobRectangleBannerBottomRight_mBB51A3719CFBF6C11C327DBFD70961E85BE01764,
	AdsManager_showAdMobRectangleBannerCenter_m045D6E6E3AF9A947753E22D9A15A7CA7642EF751,
	AdsManager_showAdmobAdpativeBannerTop_m30D5218CF89802F24AC89EC830ABACC2892DCF88,
	AdsManager_showAdmobAdpativeBannerBottom_m366D8F9839C8830AD9AA0082B801DA324493622B,
	AdsManager_showAdmobAdpativeBannerCenter_mF06B3F4EFB1E90895A27154592E7D1C50ACE0156,
	AdsManager_showAdmobAdpativeBannerTopCustomWidth_mA717C9203328D306FF40BF578D620F163D392AC0,
	AdsManager_showAdmobAdpativeBannerTopLeftCustomWidth_mEDE6DC227916BEE345C524BFB7256365DFE14D1C,
	AdsManager_showAdmobAdpativeBannerTopRightCustomWidth_m3CDD65661FEB728602EE9B1E29B658ED52546800,
	AdsManager_showAdmobAdpativeBannerBottomLeftCustomWidth_m64395DC89D88AFB2616A65D879FAA2BAEF41CBDE,
	AdsManager_showAdmobAdpativeBannerBottomRightCustomWidth_m9F90ACEBB6A79561B81C8CD4AC38C0B2BB7417A4,
	AdsManager_showAdmobAdpativeBannerBottomCustomWidth_m5B4CFFE08558AC832C4657471909710260B99BE7,
	AdsManager_showAdmobSmartBannerTop_m5EA7944A1B08C7B05B807D1E37390D0440F4A857,
	AdsManager_showAdmobSmartBannerBottom_m07F55B144A5712AC8CFBCB767EC8B0155486C5CC,
	AdsManager_hideAllAdmobBanners_mAC5079836EE333CFFB4EEA6BD45A1D352AFD62B2,
	AdsManager_hideAdmobTopBanner_m0837854BAE6595104B2A46197658964797373719,
	AdsManager_hideAdmobBottomBanner_m5E2382AD6B8C3B456D5BDA2A8EB6D9D9379B312B,
	AdsManager_hideAdmobTopLeftBanner_mF8D2B8C63D7F82E17CB94E1FF3C7FC8EF6E35100,
	AdsManager_hideAdmobTopRightBanner_mF41143348E0662EB63527FE7ECFA42A7CDBA075C,
	AdsManager_hideAdmobBottomLeftBanner_mF46371587842FE2FA7DC505CF031415D7ED49EC5,
	AdsManager_hideAdmobBottomRightBanner_mAE508D1C870338166FFB51EEAA0CE95019061F1F,
	AdsManager_hideAdmobCenterBanner_mEE5CB0E98F7C6A74AC20AB56436D089A26F2D8A0,
	AdsManager_tryToLoadAllInterstitials_mFB94E2DEADEC3C1FA3FA6F6998D12769E79AE306,
	AdsManager_loadInterstitialAdCustom0_mE991818F64F3E6476D499F569331A5C73539C020,
	AdsManager_loadInterstitialAdCustom1_mEF2158FF6369947070B95216C098A34B63EC09D0,
	AdsManager_loadInterstitialAdCustom2_m7CAAA431F71C48697A1AF5DD7FD995B7CF770D56,
	AdsManager_loadInterstitialAdCustom3_m5865C98F12B3397E39672B6AFB2797AFF6827AFA,
	AdsManager_loadInterstitialAdCustom4_m75EDFA9E35F54A639279A1AE2FD477230F347548,
	AdsManager_loadAdMobInterstital0_m70EFF50BC10509477774B4798CFCD1CB30A80332,
	AdsManager_loadAdMobInterstital1_m026239F8C45EB46A94E087B338A33F3979D522B5,
	AdsManager_loadAdMobInterstital2_m53481517DF44CAE9841886AF321C8202F773417B,
	AdsManager_loadAdMobInterstital3_mF848275FB0871A56378691A2E942E58777A9F065,
	AdsManager_loadAdMobInterstital4_m5F57210B7EE953E074EA51E47C9FF81051407F98,
	AdsManager_RegisterInterEvents0_m1FBD4E0DAAFB4E270228007443A075A66F67F34A,
	AdsManager_RegisterInterEvents1_m36998DE8C0FF0800E14C8E9989D3FB692F594C29,
	AdsManager_RegisterInterEvents2_m4CE063AF4F1D1076FA5B23371F7E2793A004387C,
	AdsManager_RegisterInterEvents3_m4738D13BBE6CA48C260D610130F9EE7DE4B28DE0,
	AdsManager_RegisterInterEvents4_m64E0E0502F815AF227069F62A675CE89EE6304EA,
	AdsManager_showAdmobInterstitial_m2780E9C84639C368C943D76BCAB46AE998E6DDF2,
	AdsManager_showAdmobInter0_m8AB83B7D31E883D2F20D797C6D92D06C0D7C4561,
	AdsManager_showAdmobInter1_m408F8655BFB864722F73CC283C0D352189C36ACE,
	AdsManager_showAdmobInter2_m87D802F9F8E68FDDD3B6DBB067367CACD4FD9E5B,
	AdsManager_showAdmobInter3_mF8C8C9FAA4B782975D85454CA1DAC6F011C180F4,
	AdsManager_showAdmobInter4_mC6BAAD8BEDF09545603C5E90367BDCF3D13C9C60,
	AdsManager_loadAdmobRewardedInterstitial_m88A1BFF464E795E027501E1A032213F59E2BC4EA,
	AdsManager_RegisterRewardedInterEventHandlers_m36C0D9C583F79C3FD77238D1F796CC999108AE06,
	AdsManager_checkIfAdmobRewardedInterstitialIsLoaded_m99A8D5233912C18A1B0874B360E97545A09851E6,
	AdsManager_ShowAdmobRewardedInterstitial_m20FD5DB10520931BE57A7F07C8F9448527C6AB90,
	AdsManager_get_IsAdAvailable_m1E84A19C513E64DBADBC1D84F2DDECABC68FDBAC,
	AdsManager_LoadAppOpenAd_mAC8258D9523A204DD5C4A2488CBD7F2E0E636D52,
	AdsManager_RegisterAppOpenEventHandlers_mCA117C699C39FEC371DF9FF3240A5196CE297716,
	AdsManager_ShowAdmobAppOpenAd_m249F6D941DF151ED993C831C1E38E270C315ADA7,
	AdsManager_HideBannersForAppOpenAd_mB6207CF9FD0AA2C8305DD390243B42524C4A45A6,
	AdsManager_ShowBannersForAppOpenAd_mEE99F7574E93895E8E21E549EF8031BF95C21AF0,
	AdsManager_LoadVideoAD_m93C3BBE2975448AC7DF253671A69D34AD7C3AF70,
	AdsManager_RegisterRewardedVideoEventHandlers_m8AA196DADE837F29CCF052D57F4E7B97FB3FE619,
	AdsManager_CheckIfAdmobRewardVideoRewardInterIsLoaded_mFAE99E35A3B4143F3B5CBAA84C2E9A63243E07A0,
	AdsManager_ShowAdmobRewardVideoRewardInter_m86C5FB69CB70EAAE1661907C98D6C1CD002E11DD,
	AdsManager_ShowAdmobRewardedVideoAd_m71032B0A83CDE5FE62C2CFB82FA515E29CF452C0,
	AdsManager_CheckIfAdmobRewardedVideoIsReady_m98EA7849FC6BFC51F5048BC8E6AE3DE43CB68AD0,
	AdsManager__ctor_m1324EE3D9841045940C44D5E0379750DBE0C0A53,
	AdsManager_U3CInitializeAdmobU3Eb__100_0_mF577590EF5F11DF96E37B33EEA5C71DA2D8AEC40,
	AdsManager_U3CloadInterstitialAdCustom0U3Eb__147_0_mBDEDAF6696EFBC73EF156014A62FDA2F4AD947B7,
	AdsManager_U3CloadInterstitialAdCustom1U3Eb__148_0_m8EEBB36A49F7E395FCE0A0AA027C77C8871B29D9,
	AdsManager_U3CloadInterstitialAdCustom2U3Eb__149_0_m2CCA33817CCD0488526FEE4AD3F2455634A28555,
	AdsManager_U3CloadInterstitialAdCustom3U3Eb__150_0_m0EF8B952E0E063BF6AE3B0B2C0EA691ABFF194EC,
	AdsManager_U3CloadInterstitialAdCustom4U3Eb__151_0_m2B74D4185FF4066E99619AC6865D404EC90AF29B,
	AdsManager_U3CRegisterInterEvents0U3Eb__157_0_m714D425E028864A4322427107A7B3665C695FB95,
	AdsManager_U3CRegisterInterEvents0U3Eb__157_1_m27F080C096CD99A962D07567E25302E752537804,
	AdsManager_U3CRegisterInterEvents1U3Eb__158_0_m08D3A39AC12EED321E5528B61E0FEE4A603FC43F,
	AdsManager_U3CRegisterInterEvents1U3Eb__158_1_m41B4A5503A31428083DB809226A94D26751B4FE4,
	AdsManager_U3CRegisterInterEvents2U3Eb__159_0_mBFCF289E0DCDC4004398B12F160BF4BC21AE4049,
	AdsManager_U3CRegisterInterEvents2U3Eb__159_1_m8441521F95ABB7A0ABEBE38658C71A58C02E5200,
	AdsManager_U3CRegisterInterEvents3U3Eb__160_0_m0798E776B41548D1E08C1F73CC605F60D57605EC,
	AdsManager_U3CRegisterInterEvents3U3Eb__160_1_m1A3526D78454752D5A8AA0029BA1472D6511EDC5,
	AdsManager_U3CRegisterInterEvents4U3Eb__161_0_m244035B19BFCD05D5CB7C5FDAABF6D427C7D11E5,
	AdsManager_U3CRegisterInterEvents4U3Eb__161_1_m0059419AFA9A891B2C28977DCF1CDC1EBB46DCA1,
	AdsManager_U3CloadAdmobRewardedInterstitialU3Eb__168_0_m66C00DEC641BC221A256D8DCBCBDD7AA8F11D05B,
	AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_0_m097621CA9ECA00FE09EBD8AE84210F0DE215DE3D,
	AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_1_mCBBEEB536D155E2A27845760525C2FC7DB885D93,
	AdsManager_U3CLoadAppOpenAdU3Eb__175_0_m9A72DAD7C216B659D391C69C6615FBDE5EDB5924,
	AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_0_m606895315A0B8258891E783CAEC43339AD0D0C8E,
	AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_1_m2009D1B41B4DD4EBFBE781876AA80D153E8529E9,
	AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_2_m9174B75FA8AC1D6E6F4C39A082F4EC1209677853,
	AdsManager_U3CLoadVideoADU3Eb__180_0_m42A6234A90D26054953322931FDA7918B423852D,
	AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_0_m6ED4779AD56F7554C6E523BC2EB21274138EE9A9,
	AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_1_mDB2D1DE644BA6DE800E6E24443218E3E9685965B,
	U3CU3Ec__DisplayClass111_0__ctor_m47D935565DC706503A6E1E4DE6AF60CEEC8D1B67,
	U3CU3Ec__DisplayClass111_0_U3CListenToAdEventsU3Eb__0_mC96F5C66D92487735E496062A9744FD3A5FB4955,
	U3CU3Ec__DisplayClass111_0_U3CListenToAdEventsU3Eb__1_m366214F722CDF91143392D35172686CA38106711,
	U3CU3Ec__cctor_m508766EBBE3CEBE4A90C808491E7FD4A9A019C44,
	U3CU3Ec__ctor_m7190908711A853BE0728E534D2747D9FCE792CAC,
	U3CU3Ec_U3CShowAdmobRewardedInterstitialU3Eb__171_0_mD764E9EC5A2CDB473A66422A59050ECB097FEA61,
	U3CU3Ec_U3CShowAdmobRewardedVideoAdU3Eb__184_0_m5BCDA4FBAA974BFD12A010F2A9FA9E732CAFBAB3,
	UmpManager_Start_mD8B450E588913A9C8AAD46BD6884F5A51E4F0E93,
	UmpManager_FuncStart_m6EA89D1E261B4291689998BFA4075A7B108330B0,
	UmpManager_OnConsentInfoUpdated_m103F1B1A61AA12BA3719A78E33CDB15113CE516D,
	UmpManager__ctor_m18B3E5B472346E89A3A2CF050335F51700465812,
	U3CU3Ec__DisplayClass3_0__ctor_mF22DD9384541AF453CF11E06F9CC0B512D6520CA,
	U3CU3Ec__DisplayClass3_0_U3COnConsentInfoUpdatedU3Eb__0_m204141F1F8F44F2F0E559F346C9480783BC35F66,
	starFxController_Awake_m56F5D4BFED423AB72CAB9C7C571B629E254B22BA,
	starFxController_Start_m1F138E6BC6C9B1D780B1126AA562E8F7D1E0D4C4,
	starFxController_Update_mBDC7A5CD199D4B1D52E0C26DB00478F3EB87F39A,
	starFxController_Reset_mCEE6E1689A3C3EF0A4E55A4DBB20AF39D206E4F7,
	starFxController__ctor_m77701D9BF1F5CCD5E55E1402F1B709DF4DF242F1,
	vfxController_Start_m31CFE24DDAB49C81B733A80FE841CF3339080793,
	vfxController_ChangedStarImage_m2EDFEE3F7992E2540F3B5E27A8033D7515323D24,
	vfxController_ChangedStarFX_m4EEA9050B0348B07CFECE4380CDA0E98F8DD5031,
	vfxController_ChangedLevel_m2B5FB6F4D0D745E807ADB957676C6F9F66B223E0,
	vfxController_ChangedBgFx_m460821C0F2A0E34050C680EB2EB288CA98892CCD,
	vfxController_PlayStarFX_mC1F8AE36362C10F443DC83D282E4676B00E41F10,
	vfxController__ctor_m5AEC8D58EB383F07E8AAC27066553C5E16690F80,
	starFxControllerMy_Awake_m954F7565F73D46A873E0F9A053E714AA708311FD,
	starFxControllerMy_Start_m66FAE607A68967EA0179B4DBD773B07BBF97EE77,
	starFxControllerMy_Update_m59FD9BC0418C85641EF8890277B4EA399D53964A,
	starFxControllerMy_Reset_mEFA34C40E480AF18FCE8C38971C584121FE47419,
	starFxControllerMy__ctor_m8D0D009F81E96793A7F09FD447FE8089DC284CEA,
	ScrollUV__ctor_m215B33742BB31E7AE93401FCFD7D98AF05012B09,
	ScrollUV_Update_m05BEA8033AAC9BBF331E2F9CB7C83F8A2AD4ED41,
	ScrollUV_Main_mF2C07392C3037F88B9296070E8EB999CFD83117C,
	LightAnimation_Start_m5C6E7A45FEA1611A1781A9810637B3E85B3409E0,
	LightAnimation_Update_m86DA0A3814A9ACEF41C8139D13319F7EC7343258,
	LightAnimation__ctor_m867EA18EAF4F1DAAD42AF1C4EB46C5CECC2470B1,
	Rotator_Update_m676DE57B82807D8024D38A4AFE6BD8B0918F4290,
	Rotator__ctor_mBB7C78CE11B1B78DD832ECEA175A9F8FF08355F7,
	DemoController_ChangeParticle_mB12ABE451BEBD34EF056C0037679D99FE9299FAC,
	DemoController__ctor_mDCA85316804A352961C2DA6570E01FF23E7C9853,
	MeshCombiner_get_CreateMultiMaterialMesh_mEDB45502D254A42398F37EBC47819BAB014FED36,
	MeshCombiner_set_CreateMultiMaterialMesh_mE76A3CEC1E7B10346FDF630163FB206BDC5B0DD7,
	MeshCombiner_get_CombineInactiveChildren_m17EFE50D9EAA3647640A0A435705CD9A5B5885EE,
	MeshCombiner_set_CombineInactiveChildren_m70EF4464B62076B045B39A397DCEF400E25D314D,
	MeshCombiner_get_DeactivateCombinedChildren_mD696FAE201247221959180FF3832649F493D23FB,
	MeshCombiner_set_DeactivateCombinedChildren_m18348793B426B6292F94C1E55E14C3DE43EC66F6,
	MeshCombiner_get_DeactivateCombinedChildrenMeshRenderers_mFA56E16EA0EA45A272E0625E201A6CDED344361D,
	MeshCombiner_set_DeactivateCombinedChildrenMeshRenderers_m36996592333EB91A0561538D735062ADAEAA5C5C,
	MeshCombiner_get_GenerateUVMap_m980DF912B63C57989480AE61EC107F2957B6988E,
	MeshCombiner_set_GenerateUVMap_m9865F21650EB815DC6DAD98A91598C5A2951CEFD,
	MeshCombiner_get_DestroyCombinedChildren_m8C24F88E8D69D8042FE918CF63D82E64E2483F23,
	MeshCombiner_set_DestroyCombinedChildren_mDC0F4586EBB8F4DFB1F2692FE180B6BD8ED9839E,
	MeshCombiner_get_FolderPath_mA2285CF62DCD93966B153C053B963443BF0AED1C,
	MeshCombiner_set_FolderPath_mB92D54973076F15F39E34A7A2F4D2447B2FB2B3E,
	MeshCombiner_CheckDeactivateCombinedChildren_m56D06FE355DBD6F3B8F6E1AE52F6EC27AB2D1F2A,
	MeshCombiner_CheckDestroyCombinedChildren_m46493720DE2C2A63BA7D99EA23F549A4B42749AE,
	MeshCombiner_CombineMeshes_mABB61494B5B5A9C007919B88413C4C9132E62989,
	MeshCombiner_GetMeshFiltersToCombine_mD28F858198DA4E57619450465DA794177BB7DE72,
	MeshCombiner_CombineMeshesWithSingleMaterial_mD7927B466258A14116C575B9DC084DB968C6782F,
	MeshCombiner_CombineMeshesWithMutliMaterial_m550862E2A444C10861A9290BEC2FC8BEF39B9E4B,
	MeshCombiner_DeactivateCombinedGameObjects_mC700A980273A4FE6FEA14999E1D0599956DC09BC,
	MeshCombiner_GenerateUV_m651D43E54555179FAFE11E9D9EDEE0BCFF9CF374,
	MeshCombiner__ctor_mB13129257E7B635A0B644F987594B21ACE6F4BB1,
	U3CU3Ec__DisplayClass33_0__ctor_m4FF5AAE6916D1387510E64676EAB39A53DC18014,
	U3CU3Ec__DisplayClass33_0_U3CGetMeshFiltersToCombineU3Eb__0_m257ED5A742B494F6E5C12C847B723D84583EF17D,
	U3CU3Ec__DisplayClass33_1__ctor_mF255176B1E0ED28C4A103DB0A66071354CA13A93,
	U3CU3Ec__DisplayClass33_1_U3CGetMeshFiltersToCombineU3Eb__2_m499A70231221E766848D5EB60BB711481249796A,
	U3CU3Ec__cctor_mA064A116AE9D5AFF5D3180F3DDA35EE843E6D06F,
	U3CU3Ec__ctor_m92943B6FDBCF5BB35117AF45865D1E4BAFFBDA9F,
	U3CU3Ec_U3CGetMeshFiltersToCombineU3Eb__33_1_m9330A19419806D557A51C004767734F45BB225F7,
	RCC_SpawnRCC_m3F6E78E00CB08CED849F05AE9DF0FE006B1991E5,
	RCC_RegisterPlayerVehicle_m8FD23761FBFBA2AC20C377366C5DC12449FFB1AC,
	RCC_RegisterPlayerVehicle_m63F6692C9E25D1640477ABB4D05E193735EB66DA,
	RCC_RegisterPlayerVehicle_mC516450CCAD9BBE344676A14BDD25294EBBF7CBC,
	RCC_DeRegisterPlayerVehicle_mAFEC9AB4DC222CA2681272137C7A34B43091A642,
	RCC_SetControl_m948786C3886D96548734260E0D9DFE0AFA77DCD7,
	RCC_SetEngine_mED854F74ABA31DED2A38CEC424E447F8D0BDE0B5,
	RCC_SetMobileController_mF13A14901B46E42363F8CE9CDEB82590D39E4753,
	RCC_SetUnits_m1FB9458EA1CC38496A6AA44805667B12A2BEC0F4,
	RCC_SetAutomaticGear_mC72492CDD5DA75F8789ABCE9397A7117BF6343ED,
	RCC_StartStopRecord_m2E18F53E563D1C8B2FC6F567DCECA8F5028F4756,
	RCC_StartStopReplay_m10F0C8EEE8C98D0E6A1B9E0B7D24D5218EF7219F,
	RCC_StartStopReplay_m618408A98EE054F92D2011219538901576C94B71,
	RCC_StartStopReplay_m258786FF07B12EBDC015034F11A22D923F49FCED,
	RCC_StopRecordReplay_m90B291EF49427376C3EBEE21B501E66688FF752C,
	RCC_SetBehavior_mFCDD3209C6F8DA50833F061D4074EF4AC29740A9,
	RCC_SetController_m8F6F80D4A2393FF11C9CB3BEECB2930A59F7CCFA,
	RCC_ChangeCamera_mDB162E02DD99BA9C0BAC425B46B5D92CF2A1C030,
	RCC__ctor_mC028620308C25010A68FB84148BA3625D690C739,
	RCC_AIBrakeZone__ctor_mCB04F16CA317A16F285DB89711BA883957958320,
	RCC_AIBrakeZonesContainer_OnDrawGizmos_mC7A89421501241A70EB5CE92DBF9E70FAE8269D2,
	RCC_AIBrakeZonesContainer__ctor_mF8FCE4365B87320CAAA1DCADC08BCFC027C128D3,
	RCC_AICarController_add_OnRCCAISpawned_m3BBD0EA7218909E9EC80375B0554ED8B038802B4,
	RCC_AICarController_remove_OnRCCAISpawned_m12038CFCBA20EF03A2964B286F80A3B7E6482E36,
	RCC_AICarController_add_OnRCCAIDestroyed_mB643B14ED1DF88403006531517DB79B66D111908,
	RCC_AICarController_remove_OnRCCAIDestroyed_m560E810569BF799FDE187E14A5191DC49EADA4C2,
	RCC_AICarController_Start_mB205F42087DD4684FEB0C464B96A05A019CDA226,
	RCC_AICarController_OnEnable_mD69466CFA4EEF5B438A3F3993DD262DC0C0ED917,
	RCC_AICarController_Update_mF9765F8B081C5B6F2450D382B57CCDC753A6B448,
	RCC_AICarController_FixedUpdate_m6E1A5570F03D551166962ADB53E423EB6A76AE25,
	RCC_AICarController_Navigation_m141A3C7BBC94B1F7898470BA0F9A4BCA2F29967A,
	RCC_AICarController_Resetting_mFF888B4699720C43323D1784163DFBEF23900550,
	RCC_AICarController_FixedRaycasts_m1357A6F5095E06216D48552A1D4E5201EF69B88D,
	RCC_AICarController_FeedRCC_m4A33FE2DFC150BC7F614D1711478AD7AD9104601,
	RCC_AICarController_Stop_mABD22C7E74025E5EC7963C16DF39DF24B7794F62,
	RCC_AICarController_OnTriggerEnter_mDEE606ADFCAB7AA005DD8DFBECBC955F474E330B,
	RCC_AICarController_OnTriggerExit_m4B5F0FDA6B93BA2EB72B956D4874C43946F82627,
	RCC_AICarController_GetClosestEnemy_m06AAC3DB7D92B23B95000474EAD038AB02F05C76,
	RCC_AICarController_OnDestroy_m590BB777572FBBCB67999FB664B415F882E7B013,
	RCC_AICarController__ctor_m929E7EB6C6D6E2D34DD6E276EE03BB57FFDC0563,
	onRCCAISpawned__ctor_mB156F9D87D23FDE7CCBEC871B65F75438CD122B5,
	onRCCAISpawned_Invoke_mDF0E988DBAF7DB54C3568F2C1354C5BAEA24BA5A,
	onRCCAISpawned_BeginInvoke_mC5B3067AAA42C360518AC23F8592B93817C55C99,
	onRCCAISpawned_EndInvoke_m32BAF57C398832C53A0979D1D88D883A96F9EAA4,
	onRCCAIDestroyed__ctor_m12143C7195B27907C06004D6898BF380A8B03ABA,
	onRCCAIDestroyed_Invoke_m69B8D4FEA0320CC739D42BD7E29E401F08947A44,
	onRCCAIDestroyed_BeginInvoke_mC923F69511DE6EB2B3B60C89414EF0BFD258D89F,
	onRCCAIDestroyed_EndInvoke_mF612A9B4AAA65BBD00F2FA3926FF1F78E0B73EB2,
	RCC_AIO_Start_m21F15EBDB44F0F72457727F5126FA4FDFECFD554,
	RCC_AIO_Update_mB0974A0CDCC08EB880531A4E9EE845D6E0B6F1FB,
	RCC_AIO_LoadLevel_m3283C640B3D375648E5D0A1232501AB7F5396A05,
	RCC_AIO_ToggleMenu_m207A9F88A9562E0427ED28BBDA0683FDC161C0CC,
	RCC_AIO_Quit_m21290FA8AE635763F45C8E702D1906ADB3AA6ADA,
	RCC_AIO__ctor_mDDDF1636B838283B91292C56D8A6D8339D4E1375,
	RCC_AIWaypointsContainer_OnDrawGizmos_mDE6AF20CD59B9CD5AE8CDF47C79F50CDA20DC214,
	RCC_AIWaypointsContainer__ctor_mB00BFDF853275D68088953E32FC64D7763DA60F4,
	RCC_APIExample_Spawn_mCA5082677EA89733D7CE710AADB160002F95B7F2,
	RCC_APIExample_SetPlayer_m0C12D4E6E5AFFB3F2D409FA59ADAEC419EF3CB17,
	RCC_APIExample_SetControl_mEDE59DC592A5E571845693C05E5C7D204A43A0C9,
	RCC_APIExample_SetEngine_mB267FE9299B4A240D60AE1B5969029A93F08E99B,
	RCC_APIExample_DeRegisterPlayer_mDDB542B8D27EC3B2441D1959E299B7A3A2BB3604,
	RCC_APIExample__ctor_m200CA303F9E9EA4B5724157900E26411C02A1B57,
	RCC_Caliper_Start_mF1B541F6E2436F9C250B548E5C03E6F7EA6DF0EB,
	RCC_Caliper_Update_m1CCB2380CE087314FADF51193D7C3C9423868C12,
	RCC_Caliper__ctor_m1DB569DB2F740801597680ED575D5F2380F04625,
	RCC_Camera_get_RCCSettings_mC878F5E635C505C7EB912901D884BF4E46F9B205,
	RCC_Camera_add_OnBCGCameraSpawned_m549CD035B0CD04B8480C8180120D4DB55F875854,
	RCC_Camera_remove_OnBCGCameraSpawned_m48CF21668CA531B403CBA86D6255D1B0E61A14B9,
	RCC_Camera_Awake_mFADC75C4D8C046ED1E7CB108358F7ED3237C9F1B,
	RCC_Camera_OnEnable_m16FB7271A2A73A9622339B93D07B41158C99E20B,
	RCC_Camera_RCC_CarControllerV3_OnRCCPlayerCollision_m2A68C01DEAD0C4BBE4FEA7DAD479DFBDD26A9A50,
	RCC_Camera_GetTarget_mE7E40A0F1273E9A2F8FA10F5D63FE1DAA5800506,
	RCC_Camera_SetTarget_mB08AF595795FCB977AE7F175F24B3013A049CD3D,
	RCC_Camera_RemoveTarget_m22F4C0A75C42B07A9D92C9C94A056A4D9CAA8FC9,
	RCC_Camera_Update_mCD39559F9AE53F0BFBEC68844046315633E452CD,
	RCC_Camera_LateUpdate_m1E3DCCD6D04F60ADBAB947B3C5D54875B6B68945,
	RCC_Camera_Inputs_m7549CDF596148BA7DF6230FCAD14605D4635A959,
	RCC_Camera_ChangeCamera_mE8EC96C08DB85C763CAD94FCBBF641410A0700C7,
	RCC_Camera_ChangeCamera_m55B866A76A0598F3C02936D4CD162F3BA9508313,
	RCC_Camera_FPS_m1C0E5821B979424F9D8A4DF9E3CBAAEEF09472C4,
	RCC_Camera_WHEEL_m7D33528BA2D233331568420B039EF0ACDBEA3A1E,
	RCC_Camera_TPS_mE0FF84544115292ABF3DDB973D25C0EEF2AF6113,
	RCC_Camera_FIXED_mB5317D8747BF1668FE47796EC9A7D2C13BA63354,
	RCC_Camera_TOP_mD0976668EAFA911DE541CE145B00DE7E5E696E30,
	RCC_Camera_ORBIT_mCA99A660E676743DC3A014D334ABF340780457BB,
	RCC_Camera_OnDrag_mA25DF21C05FE37010621343AA8E13099A61EFBEE,
	RCC_Camera_CINEMATIC_mD5AFBCFE33D4893B990A508E8BAA11EAA8703398,
	RCC_Camera_Collision_m9E6A630F7FD0A50B94426205B713E9C2B68B7890,
	RCC_Camera_ResetCamera_mECBE136E8C8B657A9189C040B9E437EA72CF3E84,
	RCC_Camera_ToggleCamera_mA41D43F5502C489A7A04CB1438FEF108C07ED021,
	RCC_Camera_OccludeRay_m82A4010B258BA1278DEC18E508E3D68F27E11379,
	RCC_Camera_Occluding_m80EFF6F963BA1666AB18D0B47057DC37EE852E6D,
	RCC_Camera_AutoFocus_m81900DC1EFC048D1FEB5DDC99C0EE145AEB2C2FD,
	RCC_Camera_AutoFocus_mF615890DD4E3C817537453620BA1C4541FACA563,
	RCC_Camera_AutoFocus_m6605134E41B5EA121A4E35112EA369D026476F3C,
	RCC_Camera_AutoFocus_mC500C30540E4A110FFA05675A12DA784F4BB0B44,
	RCC_Camera_OnDisable_m6595E38A1D7653299AC88F493614B08B1E688EC2,
	RCC_Camera__ctor_mF09251ED677A819C9B0EF26C9CA1C1BB3989A50F,
	onBCGCameraSpawned__ctor_mCD9CC693BB70A14AB854D7F10AD748B6A55E0553,
	onBCGCameraSpawned_Invoke_mE1829B979CE867EF6DDD538D9CA005C276E5DA75,
	onBCGCameraSpawned_BeginInvoke_mA3BDE1DF5564D69AAF557F100884DC0201DCD438,
	onBCGCameraSpawned_EndInvoke_m035983D13FD26ABC90AF00418D468FC90B9B0325,
	U3CAutoFocusU3Ed__103__ctor_mB86DAE1538A1817A4DE2965F70A7554F43963218,
	U3CAutoFocusU3Ed__103_System_IDisposable_Dispose_m5BB60050071E0246912AB3BDEA18A1C6D632BC82,
	U3CAutoFocusU3Ed__103_MoveNext_m2BB8147C3BE0E20ECC7A088B987F3AD6AC48D64D,
	U3CAutoFocusU3Ed__103_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m994C4DFCB355539E7E93873A49246D0A88AFE3B0,
	U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_Reset_m9829DB058D07F760EBBFC42C7178D3C8A7CCB4F0,
	U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_get_Current_m94AE930C9BC639C3E657621CA82377820CDAB375,
	U3CAutoFocusU3Ed__104__ctor_mFEFEB8210262FB261D9927ACB4922D120C7BE39F,
	U3CAutoFocusU3Ed__104_System_IDisposable_Dispose_m1E79DE15E4F22EAA324B8B6907B6DAFFFD02CBD7,
	U3CAutoFocusU3Ed__104_MoveNext_m7F97F41BA14A3CA3783CC56BDDAB0D5DCD0D5D28,
	U3CAutoFocusU3Ed__104_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3B2A71C4778AFB90E1632278C7A4BDF8AA1E33BA,
	U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_Reset_m019D201542FE2E5E4FF8C0ECD1E4CF7F113208C5,
	U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_get_Current_mA3C32942E6C8F8367E80DFA28F7F43687BBEC68C,
	U3CAutoFocusU3Ed__105__ctor_m857814C291E57FD0CE48F0CC72F0A75B8677C5A7,
	U3CAutoFocusU3Ed__105_System_IDisposable_Dispose_m5F30716C2F6EA674FE1D07F890F36ECEC8835154,
	U3CAutoFocusU3Ed__105_MoveNext_m9FA030EF912674D75448CFF467A66E9042F31ED2,
	U3CAutoFocusU3Ed__105_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43C55302F19223A3F9E6A753C1E33495E15D5334,
	U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_Reset_m37567EB21D0C21FBAF45612A28B6D990F8473682,
	U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_get_Current_m1303A4AFE8A3BEA65829C1A2ECB0A431CBC3B22D,
	U3CAutoFocusU3Ed__106__ctor_mD1116B9E48DA8B41D2403A93A8D7AA21FCD0D0AD,
	U3CAutoFocusU3Ed__106_System_IDisposable_Dispose_mC71F8B2F7180442670680DD6C0FA2638D844B9D6,
	U3CAutoFocusU3Ed__106_MoveNext_m6FE04CC110AE02F7DF0181CDBBC6DC58EDA8E74B,
	U3CAutoFocusU3Ed__106_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB71E03BE2D95E1613CDB315DC1D0F856FD2B0CDE,
	U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_Reset_m72A38018ED33FC3DAB06663FA585036AB746828E,
	U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_get_Current_m356DEFF51672783BA4A8008CCF6B9B8F7FB829FC,
	RCC_CameraCarSelection_Start_mAF10156BCC6A87631917C23EB891362BBA6A4076,
	RCC_CameraCarSelection_LateUpdate_mBE0C17585648B0C6E83A94DA2817633F4D5FD418,
	RCC_CameraCarSelection_ClampAngle_m4A45B0D401BBD7957850C634CBC06E8890CDE829,
	RCC_CameraCarSelection_OnDrag_m2CFEC7930103E565B3F41DC9723B8FD0C4142260,
	RCC_CameraCarSelection__ctor_m4FDDCED0483AD16D697E78978589F4FA84A3D287,
	RCC_CarControllerV3_get_RCCSettings_mCDBFA9A172D3E7C2C9D1A68C827ECE9F7707F310,
	RCC_CarControllerV3_get_AIController_mEA0D5918A4C4F76654B014FB2B03A923F20FD525,
	RCC_CarControllerV3_set_AIController_m2A8722D37ADCC364D4A5CBC398F2433B9DD01CE1,
	RCC_CarControllerV3_get_runEngineAtAwake_mDC83B91681A0DC2E801F0000C123C636188003DB,
	RCC_CarControllerV3_get_autoReverse_m100E37A5915AD86940C32F70354595CFAD10CB46,
	RCC_CarControllerV3_get_automaticGear_m82A0642CC393EF678A4E19CF5A786A0D35E851BB,
	RCC_CarControllerV3_get_gearShiftingClips_m5DCE2052AB637E0F5DA91D390717FA9FF57BFA56,
	RCC_CarControllerV3_get_crashClips_mC656B50A556AB736D9251383A99E8D5157D2FDD0,
	RCC_CarControllerV3_get_reversingClip_m07263F79F2015DEBA669BD4B9D0B6681885457E1,
	RCC_CarControllerV3_get_windClip_mD9FD5FF6B82BF10A1A5778F7CE94D200762A04FF,
	RCC_CarControllerV3_get_brakeClip_m46A0347507263F5F34AA46BAF2F0138EE776701E,
	RCC_CarControllerV3_get_NOSClip_m304C134E8FF8B02B7DECFFE51664D32069081D97,
	RCC_CarControllerV3_get_turboClip_mCD5B2636E980FF60E5EEF73DE2F454F497FE6854,
	RCC_CarControllerV3_get_blowClip_mF8E718EE464F3B8DECA7EF520761A79933163C5C,
	RCC_CarControllerV3_get__gasInput_mE5EB59F11AF9F7CC75F3048ABEA3CD27F268CC29,
	RCC_CarControllerV3_set__gasInput_m6E9A1A1A8806E1CFAD73261FB87F7DE726555DE6,
	RCC_CarControllerV3_get__brakeInput_m956EADB713511FC5F8FB935F170D3E00FA06D613,
	RCC_CarControllerV3_set__brakeInput_m75A82ACB52CF63944831899C394DCF680AE93A0D,
	RCC_CarControllerV3_get__boostInput_m115BFAE0A59FA28E695F7312BDD3B6A355DACF00,
	RCC_CarControllerV3_set__boostInput_m0F16A549427E260E4C0F1A5CE2578AF60F026487,
	RCC_CarControllerV3_get__steerInput_m6CC00B7F98A7E5B37950F69EE9A518D4B747FBAB,
	RCC_CarControllerV3_get__counterSteerInput_mB39663A406EE89E8246840C12AB0032F858BB940,
	RCC_CarControllerV3_get__fuelInput_mCF716B8972455D609873BC43240DD74C49126054,
	RCC_CarControllerV3_set__fuelInput_mEA014CAED637E84F8B36EDD76C8995A50F8A7FD4,
	RCC_CarControllerV3_get_contactSparkle_m2E844C494617152E301599D2A4819920BBA26726,
	RCC_CarControllerV3_add_OnRCCPlayerSpawned_mFDE1F6DAD5D918C29976EE4998677077769E45A7,
	RCC_CarControllerV3_remove_OnRCCPlayerSpawned_m59CF492DA2E14D35290ECA32A6506719A0A9CCD8,
	RCC_CarControllerV3_add_OnRCCPlayerDestroyed_m1DE2E27CCB341E870B1A3112B65F6706C3B0F03A,
	RCC_CarControllerV3_remove_OnRCCPlayerDestroyed_m638B2F4306EDFC49B3E1B44E943E79547EEF0B04,
	RCC_CarControllerV3_add_OnRCCPlayerCollision_m4DC0247EA3B605AFE3088CB97148583553DB95A7,
	RCC_CarControllerV3_remove_OnRCCPlayerCollision_m5FC92A8B329061A31DD477FCE63991D12E893771,
	RCC_CarControllerV3_Awake_m53FEA0A0444A0A94EE9D675334F22B7F514D27BE,
	RCC_CarControllerV3_OnEnable_mBFB89A39D189BA6C53311FDBD3BF3584254600E6,
	RCC_CarControllerV3_RCCPlayerSpawned_mEFB68FDC59E48F6F7B81FD56D502D3EAF80B3EA7,
	RCC_CarControllerV3_CreateWheelColliders_mDFCA318677C291DF3EB045B97874A18234D3C73D,
	RCC_CarControllerV3_CreateAudios_m16916A5DC7ABEE05D8F8DE73557C3440662CFB1E,
	RCC_CarControllerV3_CheckBehavior_m9E85618FD58698321282515F3E4E218973EAE61B,
	RCC_CarControllerV3_CreateGearCurves_m15049F5AD4C881B72EE061FF91BD22FB8D88682A,
	RCC_CarControllerV3_InitDamage_m989ED8AEA588765A2378DEC782B08F6C5430756C,
	RCC_CarControllerV3_KillOrStartEngine_mCEB8071C55A39CB21DAE9C77140AB9109EEE470B,
	RCC_CarControllerV3_StartEngine_m710E3C20477266E7DDC2BAF5E3F13202F424120F,
	RCC_CarControllerV3_StartEngine_m8E3FA89966C1E93376CF584CDD99DC8736D898B2,
	RCC_CarControllerV3_StartEngineDelayed_m700F24D67534B52A4C3D899D4F4C0F3DE321893A,
	RCC_CarControllerV3_KillEngine_m7152928FA54CF72FE7C383A5605AB4CE44D0B320,
	RCC_CarControllerV3_LoadOriginalMeshData_mBCF27268F78EB9D0222C684BED29CE65378D6C79,
	RCC_CarControllerV3_Repair_m21E1687C2D15A3ACEED4D608DAB0A1B76B9F0EB1,
	RCC_CarControllerV3_DeformMesh_m0B0453212B81F71003A02AC84371CD86599608BA,
	RCC_CarControllerV3_CollisionParticles_m99B979A6BC17408075700F6DF4ED8D0B758D9744,
	RCC_CarControllerV3_OtherVisuals_m4E1A6BEF1BC9F70763E2DC49A9C8690C3114F34C,
	RCC_CarControllerV3_Update_m8B3480D554D99B136078EFF8B619917AFAE1FC36,
	RCC_CarControllerV3_Inputs_m8CBC6C10ABDC1EB67362C4C0977A9834BDA250E3,
	RCC_CarControllerV3_FixedUpdate_m5FC4218C34D9E8F400D2C9D0A13B8BF3A7547E9D,
	RCC_CarControllerV3_Engine_mEF280D545090FF4D13CED288800A9B070EF74D8C,
	RCC_CarControllerV3_Audio_mCC94529D4C0080D69793F809D8C80ADEF8575E48,
	RCC_CarControllerV3_ESPCheck_mD2160BE5BE5880EF7A2F00847BD1E877994242EF,
	RCC_CarControllerV3_EngineSounds_m7FC924E62D503DE84096CCA0052327AE6AEB89F0,
	RCC_CarControllerV3_AntiRollBars_m70D8F72CF22CB6E12A60F1B1C71924A9D22E8B99,
	RCC_CarControllerV3_SteerHelper_m5F2D0F1A528DBD37366886997925B49F34F628CF,
	RCC_CarControllerV3_TractionHelper_m48FE4310F94F7921B84B9122785FC64AB3E9A579,
	RCC_CarControllerV3_AngularDragHelper_m0BEFD33529955616A1A2F357D6E8775A84AFFC5D,
	RCC_CarControllerV3_Clutch_mF11F1952AE77710CFC200E741E2357F314CF052D,
	RCC_CarControllerV3_GearBox_mF9C65BD5B4A33B43C9EEFE90967865B0C97D1215,
	RCC_CarControllerV3_ChangeGear_m4C80019C3A3C73BC9A4B97A683246C2241563FAB,
	RCC_CarControllerV3_GearShiftUp_mF61FBF55DD3B091130F14F4292062BE59FCE4DBB,
	RCC_CarControllerV3_GearShiftDown_mE962E6923FA1FDD8EE8EA288C049E48CF692A21B,
	RCC_CarControllerV3_RevLimiter_m5782D84F6D00F963A4FB545EFA970F4B40D41CDB,
	RCC_CarControllerV3_NOS_m883CD619C5E644BECF9CDCB7450AC60F9C545EDC,
	RCC_CarControllerV3_Turbo_mA0E1319F09F7E6A76C5657B6115B429A1A7F590D,
	RCC_CarControllerV3_Fuel_mAC26FD861AC2749E7854FD8B77D6E60EAC4D891F,
	RCC_CarControllerV3_EngineHeat_m63B886EF06FC10274BE75239964D69E7E78579B1,
	RCC_CarControllerV3_DriftVariables_m061AF40A4E1AF969E16410EF0567DBA5B21927C4,
	RCC_CarControllerV3_ResetCar_m2195404FEC0A6CBF97C45109A4D78BA477262970,
	RCC_CarControllerV3_OnCollisionEnter_mF2DE15CCB55FFB7E985B7ED66BD6D6A8BF2F0F56,
	RCC_CarControllerV3_OnDrawGizmos_mC28716BE267F281F0A1EC433D5381FF2CF36437E,
	RCC_CarControllerV3_PreviewSmokeParticle_m08C8027B8404453CD947CDAC1575B7E6718A8572,
	RCC_CarControllerV3_DetachTrailer_m96A3372053CC7CD5D9E29E4ED4A4B27D3481D74D,
	RCC_CarControllerV3_OnDestroy_m25B0439A0277FC799EEA7FB2593CC630E32CE0A0,
	RCC_CarControllerV3_SetCanControl_m6B04DB5825B76AF5E3472DD7C7AB06C961C3FA5A,
	RCC_CarControllerV3_SetEngine_mDE8812E1393BB803677026A45E4B7542C0CC175F,
	RCC_CarControllerV3_OnDisable_mB7002423CB554475ECCC21FCE8BDCA5C37ECE362,
	RCC_CarControllerV3__ctor_m4504CF01980E4734BE531C22196014D4E48DD93A,
	Gear_SetGear_m3EF16D0E3272E4FB74D0AF3F80F10A991C48A6D8,
	Gear__ctor_m6F26AA931752127D0F0967F79B948D3EAD4B33A7,
	ConfigureVehicleSubsteps__ctor_m13B0EF52A9A14ED905248C8266D79334D2D6E406,
	onRCCPlayerSpawned__ctor_mFEC9C9C369FBC7C50EC271E2FABBDBA230CF4D46,
	onRCCPlayerSpawned_Invoke_m138CC20598836842086155A047F9C175CC4ED34B,
	onRCCPlayerSpawned_BeginInvoke_mBE6D89C307370AA4F8E0F3A543266D0CFE0BD5F2,
	onRCCPlayerSpawned_EndInvoke_m77D8BD364BD3B053810F2DDD79A5214C10DD52BE,
	onRCCPlayerDestroyed__ctor_mC66C1B68FB38F1D1C83458902D774A3DDC34D5E4,
	onRCCPlayerDestroyed_Invoke_mF7C2B06AD208C6DA012C4B5602F82C2840495E37,
	onRCCPlayerDestroyed_BeginInvoke_mD3959A42384ABFAF14EF5E91B16A8407430CA28E,
	onRCCPlayerDestroyed_EndInvoke_m5621F340B1D4B9461D171F9C0EECFEF663D62679,
	onRCCPlayerCollision__ctor_m4030865F1DA50CB4EA548AE887A49DEA9AC1CBF8,
	onRCCPlayerCollision_Invoke_m1EBC459390A4AFB8653D313EE4FE65870C7D637B,
	onRCCPlayerCollision_BeginInvoke_mB9627E2C4F1E8A89D3FA2C245ACD18BBD2C8B5F8,
	onRCCPlayerCollision_EndInvoke_m4E4647C24B283EA15E96762B208953892BA833B6,
	U3CRCCPlayerSpawnedU3Ed__245__ctor_m84F6C98EF2E1E6AB823D57CCD4F336C37681C2F3,
	U3CRCCPlayerSpawnedU3Ed__245_System_IDisposable_Dispose_m0D02C0D246B87634EE283883DF60CC2ADEC78D47,
	U3CRCCPlayerSpawnedU3Ed__245_MoveNext_m1EEF01106E55A95EA6A80FC128C2C2E67E57037F,
	U3CRCCPlayerSpawnedU3Ed__245_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0953BFC21C7566C1EA4C20B5F2FAFDB84CABECCC,
	U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_Reset_m9BB8908191673DFD4029B0EFD2576BAF9F4F55AD,
	U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_get_Current_m30EE7661ACAFF1FF3B5242188997BEC07415401E,
	U3CStartEngineDelayedU3Ed__254__ctor_m01C19BCD75D3AD447DDB684436968DFD8D5CF58D,
	U3CStartEngineDelayedU3Ed__254_System_IDisposable_Dispose_m1C21109B2BD3F928F6808B8E168E9FD976B97E7F,
	U3CStartEngineDelayedU3Ed__254_MoveNext_mF0381176F06F384511831D1EEAF0DCC0D8479B38,
	U3CStartEngineDelayedU3Ed__254_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mAADA60BDC6AFDB619885F62973196BA3CA9183AE,
	U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_Reset_mE1CE9FCC838DAB413B5F31FDC43D874722C29EA8,
	U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_get_Current_m77C2F1F6912869036D311A198CDD6F30CBA657E0,
	U3CChangeGearU3Ed__274__ctor_m42019740291409B8CA24D09A7ED3645BDE11DF87,
	U3CChangeGearU3Ed__274_System_IDisposable_Dispose_mD457816E6E4533B0AE7AFF0E6933D83B455898D2,
	U3CChangeGearU3Ed__274_MoveNext_mD6AB39BE0AC3E3310127355A13C0601BAB7FA574,
	U3CChangeGearU3Ed__274_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0A82C933A082D8DA6B5144E20C9B5778342B4544,
	U3CChangeGearU3Ed__274_System_Collections_IEnumerator_Reset_m6632FDBB744826C15C1B065954F1523339070A79,
	U3CChangeGearU3Ed__274_System_Collections_IEnumerator_get_Current_mA431DD0D991872FDA3AF4F7449666D51F3057CC7,
	RCC_CarSelectionExample_Start_mB666F47D68CCA912F7B2B5B391623CD2BD893F4C,
	RCC_CarSelectionExample_CreateVehicles_m3A6C2AE900E2993E820E725013146FAD248138C1,
	RCC_CarSelectionExample_NextVehicle_mDB0DE454927968F14A37703FDA25F0611D0530A5,
	RCC_CarSelectionExample_PreviousVehicle_m757DDA9C4713894CB8297F77B75C457BABA82F15,
	RCC_CarSelectionExample_SpawnVehicle_m90F901211509FE210DC585AA91F6FF5B0AC9EE4E,
	RCC_CarSelectionExample_SelectVehicle_m16D89746A9B91DF9325C6FA2CF140EDCB26CA7F0,
	RCC_CarSelectionExample_DeSelectVehicle_mDA026D6B2F0113348D6BC6EB09E14CB65A4F1D07,
	RCC_CarSelectionExample_OpenScene_mC51D46333D832470677181BC14F2CC2CFD26A03F,
	RCC_CarSelectionExample__ctor_m7AA85875E7E419F2123120B1A208C2A17044F748,
	RCC_ChangableWheels_get_Instance_m1432C7648E53AD344BA132D7567A7A2AA6CA1394,
	RCC_ChangableWheels__ctor_mB033A1966BC74E2852C751FEC9941F0122CD560A,
	ChangableWheels__ctor_mCDAFF6043C03AC3CD48BAF73D0A13848BE3FB29F,
	RCC_CharacterController_Start_m5F9053945F1948F57BC357B679D8FB84531B8206,
	RCC_CharacterController_Update_mEECA4F9671CB920F4972D1F59EB4356EB89225BE,
	RCC_CharacterController_OnCollisionEnter_m9DF0958C5C5DD6B81B8DCDE061FE521365330E94,
	RCC_CharacterController__ctor_mFE6593C760EB1182749A059994D8922B25CD9EFC,
	RCC_Chassis_get_RCCSettings_m29AEC0DCDCF45C76F52D60D314B74B340D67A3CA,
	RCC_Chassis_Start_m7F3935EC7F5630AE550682073F02FF1A9D453B2F,
	RCC_Chassis_OnEnable_m6F281396CFD5DFD70B9B2D695174837564C33D30,
	RCC_Chassis_ReEnable_mDC685A708AA2048130E56BAEF2A9564648B41D8B,
	RCC_Chassis_ChassisJoint_m8E007FCD6F955705171F1C56D47F913CEC5D5938,
	RCC_Chassis_Update_m77FF7FA1B3FC517D71062BB644511E51D2E9D1FA,
	RCC_Chassis_FixedUpdate_mD3D8C0263CE88DBA50E0E31A507A19A7400E1BBA,
	RCC_Chassis_LegacyChassis_m994DC9FFFAB2E1FFEA85B2DB7CC1595BEC5521A6,
	RCC_Chassis__ctor_m8BDA4FE1237567A2FED82861EF554DEE136184F7,
	U3CReEnableU3Ed__11__ctor_mECB7864140A80DFBD981DD0D1E32A9C657A9EE46,
	U3CReEnableU3Ed__11_System_IDisposable_Dispose_mACA2471CAD921534B9E0F254D841DED400E29576,
	U3CReEnableU3Ed__11_MoveNext_m7095AF2DDE6A5E8E915A116A791868FDD7C4EC63,
	U3CReEnableU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFA07227D312084BF51EB4BA81ECE8C173C0F0C4C,
	U3CReEnableU3Ed__11_System_Collections_IEnumerator_Reset_m848803CEDDC57D5DC87E5F9B21710A13560AEABE,
	U3CReEnableU3Ed__11_System_Collections_IEnumerator_get_Current_m239E706369E3C28CF8DD3218CC0A6BFFFE82D697,
	RCC_CinematicCamera_Start_m200FADB3230654681C5BCF45061BD541C294B7F7,
	RCC_CinematicCamera_Update_m154B4EAFCFC0E39C02282E7366DEF867352BB435,
	RCC_CinematicCamera__ctor_m0158B8518EBBA2A579FA2863E1DF8D06845559EF,
	RCC_ColorPickerBySliders_Update_mDE59A4A71F2F7675E187231AB6C0C0D88F26F42D,
	RCC_ColorPickerBySliders__ctor_m84AD5F79DAA431F6DC6CD0C88C9BBF9CA7CFCC65,
	RCC_CreateAudioSource_NewAudioSource_mC36992ABCE916AA31908D846BB5034C52F1A74D7,
	RCC_CreateAudioSource_NewHighPassFilter_mEE025AECA142551E55F823471411485082471205,
	RCC_CreateAudioSource_NewLowPassFilter_mB3C4D21508528B7686AA0801E62269DDFBF4E573,
	RCC_CreateAudioSource__ctor_mD9913BC1B1A89502243D52A2D7B210F3ED55B908,
	RCC_Customization_SetCustomizationMode_mB21407EED2C2BE81F9E301CEAD5747D3D64EC4B6,
	RCC_Customization_OverrideRCC_m16E5D497E3F0720CA2E7431D7426D4F04F0B6C4B,
	RCC_Customization_SetSmokeParticle_m85F0AF2A34B76B5B36C28300BED7F7DA738495B5,
	RCC_Customization_SetSmokeColor_mCEC4BBF3AFE0001D91BBFBFC921D0943FC6A3685,
	RCC_Customization_SetHeadlightsColor_m69EF080DC8EED1821D96048DA33B60C9175303FE,
	RCC_Customization_SetExhaustFlame_m4F407654F46E4DA23CF69031EE9C25537DC6FD35,
	RCC_Customization_SetFrontCambers_mABCBE12C94C38A604979A9C070C7997030613349,
	RCC_Customization_SetRearCambers_m79D2B638322F2FD8DD6F77A970B224FCEE01EAD0,
	RCC_Customization_ChangeWheels_mC26333E07DD57E72952B3385AE9801593525C46D,
	RCC_Customization_SetFrontSuspensionsTargetPos_m38DC13FD21B4D347EAFF3AA91E2C49D71A759557,
	RCC_Customization_SetRearSuspensionsTargetPos_mA50163E62468B1845DB47E0A19F6D8B7AA2CDABB,
	RCC_Customization_SetAllSuspensionsTargetPos_m96A59C156C8F8307A9B474291420C0A752EA5B45,
	RCC_Customization_SetFrontSuspensionsDistances_mB9DCCFDAAC43843216CF64BE4C9DBA23B461506E,
	RCC_Customization_SetRearSuspensionsDistances_m3150E71D7ABF9A22314276199D4806E6F47F167B,
	RCC_Customization_SetDrivetrainMode_mFF9CB44157F46D5216AA139BD12734A116D85B18,
	RCC_Customization_SetGearShiftingThreshold_m47400A7D7739DFB9DF7B3458C1C70C182D1C659B,
	RCC_Customization_SetClutchThreshold_m0F3D14D172B2A098527BCFE79E365E2609B07B2E,
	RCC_Customization_SetCounterSteering_m0A12ACA1532E2892D7D161D501E37730580D784E,
	RCC_Customization_SetNOS_m93ADE9DD307BAF7566AECB3813BCF329BEAAF642,
	RCC_Customization_SetTurbo_m69DEFE5A31118094CB4D1B5B5E728A6453D2AE4B,
	RCC_Customization_SetUseExhaustFlame_mC59D7651FC5BAD14A1EC3DCD810B5871B75C42C3,
	RCC_Customization_SetRevLimiter_m29077498C3ED16A4E9F238FC9708CE2475A7C9E1,
	RCC_Customization_SetClutchMargin_m0AF497E48310DDA25503A98668B39856669357D3,
	RCC_Customization_SetFrontSuspensionsSpringForce_m6502BE5BF85D01AF598759D50121284CE8E5E329,
	RCC_Customization_SetRearSuspensionsSpringForce_m6F984D2660376923C3791F0BBF2AF29F457F138E,
	RCC_Customization_SetFrontSuspensionsSpringDamper_m344010A9CB070A685D4B14B3CAC9A1E5EC58C989,
	RCC_Customization_SetRearSuspensionsSpringDamper_m557EF5D3897031603B68CB428D6EDC1C1D7E9C15,
	RCC_Customization_SetMaximumSpeed_m3E3AB7F63B8F9FB33B86CDD74486BA9D5C2459AC,
	RCC_Customization_SetMaximumTorque_m42AAC05E4FC61CEF6BB94DB27576D9B32B74CDBB,
	RCC_Customization_SetMaximumBrake_m00914798597FADF4463448E0F3BAB804ACAC4FF5,
	RCC_Customization_Repair_m845E460F5D3D7F972FB596A29EDAAD860DBC3851,
	RCC_Customization_SetESP_m81726A153EFBBA8ED7172AC6378C5F2011C6D21F,
	RCC_Customization_SetABS_mAD52A661C0665407CFE70991B0EFBD6BAF9A8467,
	RCC_Customization_SetTCS_m1C26958FC4F433BA3DF2CFE7C7F39F7C83F4930E,
	RCC_Customization_SetSH_m84C7FC8AB439EC7ECA354118A1DCC31FB9A63929,
	RCC_Customization_SetSHStrength_m3CC99D2794B5EA6D0A6EA0B87916365D7E74F778,
	RCC_Customization_SetTransmission_m7ED71A390C65BB61DE3E4F337BD8D5C6B0BAF718,
	RCC_Customization_SaveStats_mE4086FC3FF4E2B6EA0921A8235D657A2642CA403,
	RCC_Customization_LoadStats_mAD1FA3E8A8A4D3FCA4E9DB03055AD6FBF1D476C2,
	RCC_Customization_ResetStats_mE522C05D7FD5018C2A5E4F950F002A4446A7CDB4,
	RCC_Customization_CheckVehicle_m096DAC899E9F3CB14D00A5F7E7F02DD08C1F14D5,
	RCC_Customization__ctor_m775183F1542C9F29A2DEB8046C0E38B22F22DDA6,
	RCC_CustomizerExample_get_Instance_mC0BD17F0CC39C7C8F00B27B15BDEE0E46985146F,
	RCC_CustomizerExample_Start_mD8B56396AA537F26C89FE3D872CCF10E1450F2F3,
	RCC_CustomizerExample_CheckUIs_m48A1A45E3868964B80EBD46D1A4AAA4ACDC77C16,
	RCC_CustomizerExample_OpenMenu_m8007C57B631328E9BA8C2185F8FD42D27CCA274E,
	RCC_CustomizerExample_CloseAllMenus_m5E25D15CFF0EE1EF38CED5DBC90DDEFBAA76F8D3,
	RCC_CustomizerExample_SetCustomizationMode_mC29DAE1AAC95D434F1CC36FBC7982F1F9771732D,
	RCC_CustomizerExample_SetFrontCambersBySlider_mEA20406B96806505F656DA433F3746CF9CDB9FD4,
	RCC_CustomizerExample_SetRearCambersBySlider_m470286B209535F82A7E79CEA973066AC44185A1C,
	RCC_CustomizerExample_TogglePreviewSmokeByToggle_m6C84D7CA77300F96E205FC5A24736CB3CC2568F8,
	RCC_CustomizerExample_TogglePreviewExhaustFlameByToggle_m47F1A3A2F9AAA0F438EF060E9384D3D27801AD73,
	RCC_CustomizerExample_SetSmokeColorByColorPicker_m3F61002E2E5B2F2B6B4A4BDDA0017B8395E50A41,
	RCC_CustomizerExample_SetHeadlightColorByColorPicker_mCF4477604439DB0EDE7C5BD2B99EEC29DB8456CE,
	RCC_CustomizerExample_ChangeWheelsBySlider_m1D2F14FA8E69357CB8069DFCAF479C3E6ED95846,
	RCC_CustomizerExample_SetFrontSuspensionTargetsBySlider_m129579FD24858ABD9A2A4EAD1C2C31E593F0390F,
	RCC_CustomizerExample_SetRearSuspensionTargetsBySlider_mF1715159452BB3F1D38EEBB4C4F073193AEB725A,
	RCC_CustomizerExample_SetAllSuspensionTargetsByButton_mFA0385AA18506BB357537F7B8BACD43EE018D264,
	RCC_CustomizerExample_SetFrontSuspensionDistancesBySlider_mAD5D07EA0554E95629D5CEBB5845E42CB47A0140,
	RCC_CustomizerExample_SetRearSuspensionDistancesBySlider_mD0311EA43443DF9931D49AE4698391A0D0B2928C,
	RCC_CustomizerExample_SetGearShiftingThresholdBySlider_m977E34866A1F993673233ECA3FB6AFDF54481A6E,
	RCC_CustomizerExample_SetClutchThresholdBySlider_m6448B6C59B26BBBCBCE4DBF278AD9D77697FAD85,
	RCC_CustomizerExample_SetDriveTrainModeByDropdown_mB42A6C5F9A3953DCA561A1A38758DC5D6DED0F21,
	RCC_CustomizerExample_SetCounterSteeringByToggle_mBF222E10590EE56697B16994D96DBF72ECC3BE18,
	RCC_CustomizerExample_SetNOSByToggle_mD7CE1462FB5F62D0200C8227A8E25D218A36D7BD,
	RCC_CustomizerExample_SetTurboByToggle_mAB9965F4852812FC40DF2681B42BC3160B2F6660,
	RCC_CustomizerExample_SetExhaustFlameByToggle_mE85769DE2EA6A0C445557CA8EF39380A8B45279E,
	RCC_CustomizerExample_SetRevLimiterByToggle_m1B3FD85F53353F014EED396AD9B53D15C2F319A8,
	RCC_CustomizerExample_SetClutchMarginByToggle_mF0F1A2BAFAB49310D90C86E1530D95635AD127FF,
	RCC_CustomizerExample_SetFrontSuspensionsSpringForceBySlider_m0EEEBC225E3C3FF0FFDE85D560FCDA6D67B9F831,
	RCC_CustomizerExample_SetRearSuspensionsSpringForceBySlider_m533BC04A0389F95E52B5AE081161EA3B47DC6281,
	RCC_CustomizerExample_SetFrontSuspensionsSpringDamperBySlider_mAF6EFD6FA360A97ECB7861CC108F9523056E688A,
	RCC_CustomizerExample_SetRearSuspensionsSpringDamperBySlider_mC2122474C30595AD140B0907EEE2703CEE4B9C00,
	RCC_CustomizerExample_SetMaximumSpeedByInputField_mC69296738466EF2A565E1303474BAB011E9A5370,
	RCC_CustomizerExample_SetMaximumTorqueByInputField_m5666F59FA145410C900DD4B078F0E48A8591C4BF,
	RCC_CustomizerExample_SetMaximumBrakeByInputField_m2B839DB83037AEEF7CFE6FA2EEEBB531A73F69C4,
	RCC_CustomizerExample_RepairCar_m0C3F3137F7B4EB9453EDF0C09C585A50202732A3,
	RCC_CustomizerExample_SetESP_m5B1DC169028F43B743047833405151CA4700FFF7,
	RCC_CustomizerExample_SetABS_m6605B703687426ACD05A85427C06423FAD755556,
	RCC_CustomizerExample_SetTCS_mB9569CF3830ED4B4F815B12F29ECA5FF735C054E,
	RCC_CustomizerExample_SetSH_m2345E479EA70AF763624908DF8A7033D6F7F6612,
	RCC_CustomizerExample_SetSHStrength_m75F3D19883C1271900CE546FA14F71DD02489439,
	RCC_CustomizerExample_SetTransmission_m26A539473BEBA41047EE991E374B4B1964FFA912,
	RCC_CustomizerExample_SaveStats_m20666249920C11C3A4501AFDB90BE8E184F50449,
	RCC_CustomizerExample_LoadStats_m44CA881A41ED4F802AD0E0FF9A002B37D57EF4B8,
	RCC_CustomizerExample_ResetStats_mCF27539008A82DA0146CEB73C1D1FAFC39EBE4D6,
	RCC_CustomizerExample_StringToFloat_m68A8C6028E977877E3044A445B316F93B54A4B4A,
	RCC_CustomizerExample__ctor_mD9BB8424FD544DCB2E20866F59D0E5FFF5FEA4C6,
	RCC_DashboardColors_Start_m91A1D1AC8A40A76833AB57CBAFAB887D82710CDB,
	RCC_DashboardColors_Update_m8418C5DB458ED990751D5C0624FCF55A05D8CBE2,
	RCC_DashboardColors__ctor_mFC001C2E19A6FE19CB793FB0DA36C26A132B6B7D,
	RCC_DashboardInputs_get_RCCSettings_mA3FB44260289184FB7C796EFEDDC102F49D7E16A,
	RCC_DashboardInputs_Update_m61DC91DB6F7EB004250957890AD8423A7B84A7F7,
	RCC_DashboardInputs_GetValues_mDD39266C22C8F9DCDFF0F8C1760964BF91F6B4D4,
	RCC_DashboardInputs__ctor_mCD5486A3FA8878C07BB787943AE63989220B22CB,
	RCC_DashboardObjects_get_RCCSettings_mECAB45DA4F7742C150A4DA88CBDCEF9471764584,
	RCC_DashboardObjects_Awake_mD49F4E72D6820DF63F4BB01FED5F28B611235BEB,
	RCC_DashboardObjects_Update_mAC406D2166B654E7E8FD2CD156FC6E37C9DFBA11,
	RCC_DashboardObjects_Dials_m39D252EEB01CE142397B69CFCA8902A882A7A587,
	RCC_DashboardObjects_Lights_m124A5F986EBAE58672CFD71702957517598F1505,
	RCC_DashboardObjects__ctor_mF2107E27015286A52B0EE47EBE24195C37092333,
	RPMDial_Init_m480C52509EE6BC11F9E20508040EE7615254090E,
	RPMDial_Update_mB1A49BF48FD502A0AA183C1FC2EB1BE8049F845F,
	RPMDial__ctor_m7ACD1EF70C9244AC55B55A971400F03A878EC09C,
	SpeedoMeterDial_Init_mDFF7856BEB6F8AA7618CEE0E30645D13F54C0D8F,
	SpeedoMeterDial_Update_mF09E2AEE1C62DE15E542556122B256B4F57B2DC7,
	SpeedoMeterDial__ctor_m5233D427EC3A405047C71154B1770F166A8026C4,
	FuelDial_Init_mF6C0A5D2B0EC4A2217674813DDC43F2011D5517C,
	FuelDial_Update_m78BF71958D226EC068D8DADBF922BF69903CCF0E,
	FuelDial__ctor_m558702E3EDDB15691618AE65D7B4FF3348FE5FAA,
	HeatDial_Init_m838D801AC6AE80A2E552AFB4A8DE4C092B9A3877,
	HeatDial_Update_mF294CAD678957F861D6C71DA911CDE90B2377EA3,
	HeatDial__ctor_mBBD5B0B43D90E632B06D15020CE2CF962696510F,
	InteriorLight_Init_mCAB76A01B287782DB30257ED23EA9F7E091DED95,
	InteriorLight_Update_mD9BE0CF83E9E7ED999894E1DB02127A40509F101,
	InteriorLight__ctor_m05F3C13C039D10E71F170872ECE1BC386FF8ED38,
	RCC_Demo_SelectVehicle_mB299F29A8837F345F3EA3945DBCD5D276F61348A,
	RCC_Demo_Spawn_mCBA06420E1FCB92FDDD2289703C0F81826038543,
	RCC_Demo_SetBehavior_m9F0D442ADDA506610A3474B95D70AB478BB100C3,
	RCC_Demo_InitBehavior_m23862A0A7BC95F584EEFD0E07D58E44249078C44,
	RCC_Demo_SetController_m13267B3CDE0C82146189B9D1471BF00ACD4B2FA8,
	RCC_Demo_SetMobileController_mE7DD477FB9426F36A1AA51C0DB2F5D692396870C,
	RCC_Demo_RestartScene_m0B02F5AC76A5289882E67E41A8CB23EEA1E8145A,
	RCC_Demo_Quit_mD1F54A807D36E472296817B01749F5B97A0EB488,
	RCC_Demo__ctor_mF84BA96194A9AFFE6E02F632EACFBE68CF83D542,
	RCC_Exhaust_get_RCCSettings_m2FA8EC962E9EEAAC51BC867093C91436BC02BB45,
	RCC_Exhaust_Start_m056CCCC7799A75273E72BD76500D059B3BAFD2E0,
	RCC_Exhaust_Update_mD1D8D19D2532398BB54333ED56605EAA1CA65C3B,
	RCC_Exhaust_Smoke_m8F1C65FA1E302BB583F3C13CF226490EDD2F197C,
	RCC_Exhaust_Flame_m9BF70EDFCEE2DBA1B4B1EFFA115CA0168F8151E6,
	RCC_Exhaust_LensFlare_m1262EFAB98CB1571AFFF3E820D8B29E6A90AD01D,
	RCC_Exhaust__ctor_m344FE19DDD75AEAB35D146C7AE7CF6AAD0829992,
	RCC_FOVForCinematicCamera_Awake_m4D0935D7E0CE988E683FF04C844E296E34F2CE32,
	RCC_FOVForCinematicCamera_Update_mD7D77DA149F8E4C3485C00454160494F3691E5DC,
	RCC_FOVForCinematicCamera__ctor_m37D54CE0725D2DD970A3D02BE18F9A4B99BB83A8,
	RCC_FixedCamera_LateUpdate_mF86C9196E84429C0ADE3B0F0EEA25660F775CC66,
	RCC_FixedCamera_ChangePosition_mEE99522F8A01F2493B19F2231AE0C093028B719F,
	RCC_FixedCamera__ctor_mF28C621E1B092EE342A26B2793EB5CB221C7527A,
	RCC_FuelStation_OnTriggerStay_m034F737EE7F58D7ABDEFF42DC33DB8F2437C6437,
	RCC_FuelStation_OnTriggerExit_m452F36C3064B46E01228ABE121D4660BEC0D8E5F,
	RCC_FuelStation__ctor_m24E5C06BD18377C90BF808C605549B0778F3033C,
	RCC_GetBounds_GetBoundsCenter_mA06F0EC89CE0EE2C59742E6548097D7A7A9DD17F,
	RCC_GetBounds_MaxBoundsExtent_m772D4001779C2C1E6E5780438A3FB64D0C12A34A,
	RCC_GetBounds__ctor_mB86B06A870BD6B2CE7979E2B30161BDA429C392F,
	RCC_GroundMaterials_get_Instance_m2432235A08EFEC7D02AECD0D1DCAF0E42CD76A06,
	RCC_GroundMaterials__ctor_m70D5957232EF5F879B0F114D7E6FCF89059A5563,
	GroundMaterialFrictions__ctor_mA8BAD532C61F8654D75D6CB9651C2811118EF1A7,
	TerrainFrictions__ctor_m5931F5AE973277F077A0EF6DD7967D6CEB4051BF,
	SplatmapIndexes__ctor_m8BD8062A6DFC40DE1E643424A675B5311D01B246,
	RCC_HoodCamera_FixShake_m5F20A7169C11C4F55B1BE960FFBB63EA2564BB7C,
	RCC_HoodCamera_FixShakeDelayed_mD689A3D2A58A116F11925616EE76776FE4E90659,
	RCC_HoodCamera__ctor_mFEFCEF66C879608C35D2086135C2E051B408435A,
	U3CFixShakeDelayedU3Ed__1__ctor_mAE1EA1789C6C9E6B76669634CF665CC58511FAF5,
	U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m059CF06692AD6C920357FDF367C809C9B6621731,
	U3CFixShakeDelayedU3Ed__1_MoveNext_m7A0FCFFCF12DF7CBA23885267E953710A8B678E2,
	U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC7026D71FC9F1BF49D5C0A39EFE43CFBBFAF5ABA,
	U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m7675E5E8EE65BB02E4CE3266F68749E5AD3EC311,
	U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m6465D6EB3B8B010CF242BC740AE221BF2F76BE8D,
	RCC_InfoLabel_get_Instance_m2F940CAF55B6142B75208A274F1E927EFE04C104,
	RCC_InfoLabel_Start_mC5DFB14080B65A95C0E102D913D3A246BCE2A19F,
	RCC_InfoLabel_Update_m548B3DDF885CA3702980B14ECF8F08841683498B,
	RCC_InfoLabel_ShowInfo_m2B69056C9065B79C9E4E443690825AF3F058226E,
	RCC_InfoLabel_ShowInfoCo_m66C62CE2BCD816FD11A849C7150D82225C8EBABB,
	RCC_InfoLabel__ctor_m630AF3434E3508995BA771BCE75E8919F2094B49,
	U3CShowInfoCoU3Ed__8__ctor_m754A108198109E44982129332FE0725F84334DBF,
	U3CShowInfoCoU3Ed__8_System_IDisposable_Dispose_m26676BBDB19173609B45E7F970C02E28E6259337,
	U3CShowInfoCoU3Ed__8_MoveNext_mF114E3263812BFFE4AE76A6B2C28AD64B8F9597F,
	U3CShowInfoCoU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7B0CEAF5DD5A83C6EC8337121F9CA17390640609,
	U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_Reset_m4AF8C4E672A789A3B4A328D5FDEC475DB50B9611,
	U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_get_Current_m1ADE673386CC8EADC5576419114A9AF413936423,
	RCC_LevelLoader_LoadLevel_mF977352CA3EBB12E29B508B43885045955BD9854,
	RCC_LevelLoader__ctor_m8E7A2BD784D3B5EA92546557E85D23D1856BEEA0,
	RCC_Light_get_RCCSettings_mB8A2EBCD8794E7B5FAE1E793F1AC4713229A1921,
	RCC_Light_get_indicatorClip_mB85AFB29F3869DACF5703C9C921BFBFF471804EE,
	RCC_Light_Start_mC1C8360A32DB4F97AB0403198900595DE03A9399,
	RCC_Light_OnEnable_mE839EDFEE8ED38B64FFA1577994A82556E5A82AC,
	RCC_Light_Update_mF3A31B32019AEA4B105F03422CEFB7563AB48864,
	RCC_Light_Lighting_mD642DE422D6D8D43AD8B44A4005BE2D860F21BC6,
	RCC_Light_Lighting_m6FB624B9CC71BCA5788FBAC0989ED1878DF5D586,
	RCC_Light_Indicators_m39A1F774499D6655E24CEFB2EC934C02D49A6F8C,
	RCC_Light_Projectors_mE1084BC7BF5D14695C180627EAB51B9D94DB3AA9,
	RCC_Light_LensFlare_mEEE63CEF0292150209647DB2BF8E0068C15B41BE,
	RCC_Light_CheckRotation_mBB4A78572992DEBA657B13FA0F271B6F144D0B70,
	RCC_Light_CheckLensFlare_m224C46CCD0868D8EC4AEC18A2256FFBBED7345EB,
	RCC_Light_Reset_m0830E2B20E27297597017FC11FA78E151D724A62,
	RCC_Light__ctor_m3BB065845035C6EF283F4C37AD15F9E823A3A83C,
	RCC_LightEmission_Start_m5111E3C7D886430FFA7CA3B52EBFB2BBFE4507C6,
	RCC_LightEmission_Update_m0A678CDD7C1A560E79E0154591CFC1C2BB2299E4,
	RCC_LightEmission__ctor_m76ED95337017B70D4DB545B75EEA3CE2AF35A8E9,
	RCC_Mirror_Awake_mD4F77D9C4FFDC20A0099013D7E02275330DBA9F2,
	RCC_Mirror_OnEnable_m7FA0E28A62BEC7FFAB8F0F3A5580F0BC727DB99D,
	RCC_Mirror_FixDepth_m6D176AE51609272DAA74B49F80D521584B9E2ADC,
	RCC_Mirror_InvertCamera_m76A3E128AB2ED2D6A02404B55981EC06DE5A8CFF,
	RCC_Mirror_OnPreRender_m7FCF32DB446B83566741DF0C508F8F3F2E0EC116,
	RCC_Mirror_OnPostRender_mD5BCEBE0F4A66FBE044B6B2E0073BB11A36A6131,
	RCC_Mirror_Update_mCDE47AB564A4C35B25B886574AB8C92D54B42BEF,
	RCC_Mirror__ctor_m8B91C0BF392ECD86520BAE05CC3B99CA4B373CC9,
	U3CFixDepthU3Ed__4__ctor_mFD7A770C3B171A8A9EAE242A7BDAB7756F588309,
	U3CFixDepthU3Ed__4_System_IDisposable_Dispose_mF0F38EF8288B4076B7D0573E37B2B46DD1D4025F,
	U3CFixDepthU3Ed__4_MoveNext_m6EFCA0F6168F3FB3956A8DDBF583AF24CA9D3A75,
	U3CFixDepthU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m359B793B68B60ED3881A48997B33E23942CA5184,
	U3CFixDepthU3Ed__4_System_Collections_IEnumerator_Reset_m389126C8BBAE3FFD380EE405A68318CC88493E15,
	U3CFixDepthU3Ed__4_System_Collections_IEnumerator_get_Current_mE23AD3527CBF07E20752D481DFD3DB2D0EEC3A47,
	RCC_MobileButtons_get_RCCSettings_m2DAD66334247F36E942C69873CA28C5336F9523F,
	RCC_MobileButtons_Start_m5CA20F0EC4EE67317D7E0B996C1185038A5BBE78,
	RCC_MobileButtons_OnEnable_mC10739CFB1A45B3A14BBA646AB3DE676283C5B46,
	RCC_MobileButtons_CheckController_m637D8126FA8ACC9C32249C4559FC8BACF0F4D80A,
	RCC_MobileButtons_DisableButtons_mC74EA3BE84F1D078BD7D23B3B846C74A3E210D44,
	RCC_MobileButtons_EnableButtons_m2B285E2CC6A134770E3DADF42F28A9A63F29F919,
	RCC_MobileButtons_Update_m425C61845F8A00A1BEE51675D4E4D8F680DF011A,
	RCC_MobileButtons_FeedRCC_m50BEB15AAD3074DF380CE97347CF4F9FC87E980A,
	RCC_MobileButtons_GetInput_mA3554B5CA109CB6BAC816C4F627C877F77E7B55E,
	RCC_MobileButtons_OnDisable_m33A8BD4C1A7F9CA6AFF57392BCA667E7A65EF980,
	RCC_MobileButtons__ctor_mA1756735EA6966E27E489A5C419261FE0EC0FD96,
	RCC_MobileUIDrag_OnDrag_m2B4BDD6C616AD12B1EAA57F931A27C78B5D95CF4,
	RCC_MobileUIDrag_OnEndDrag_m2C4589DC827997367BFACCBC12B2E8E4B0254988,
	RCC_MobileUIDrag__ctor_mF872CA93C2A0E193D0D621AC79E52692618685A9,
	RCC_PlayerPrefsX_SetBool_mE31AA7375593FEB4FA4FF93181AFBC668F34832C,
	RCC_PlayerPrefsX_GetBool_m3F72A8C452741F401F2E7D245F7CC90374438114,
	RCC_PlayerPrefsX_GetBool_mAEE188942341B7E2D1B1F706914CCCACA5D2288F,
	RCC_PlayerPrefsX_GetLong_mD99F0DB020D12D63FE5E20B2A35950185AF11903,
	RCC_PlayerPrefsX_GetLong_mD60F03550EAF23F2E8CC48E10127EA55CDEE8406,
	RCC_PlayerPrefsX_SplitLong_m80FADDDB9B15DDDD99A9A2388F7D22B7508AB3A6,
	RCC_PlayerPrefsX_SetLong_m1C716045DC4402199F550296332ED93B2A0912A0,
	RCC_PlayerPrefsX_SetVector2_mDD1B16AFB418D2BD4933A14D539BA9E22F98A00F,
	RCC_PlayerPrefsX_GetVector2_m5C3531F704E3E88792D9E864F3DD7019EEC0D4FE,
	RCC_PlayerPrefsX_GetVector2_m630CFA614DE9480F24E96818CDB033465E279FE3,
	RCC_PlayerPrefsX_SetVector3_m08B840E23545E02C46780BE77396C624F37B0257,
	RCC_PlayerPrefsX_GetVector3_m1548B065FD14355A29BE67D0CDE22D3C036BA1C9,
	RCC_PlayerPrefsX_GetVector3_m24EBF90B73E5C5F5C6E93A4EC8193FBD3271389D,
	RCC_PlayerPrefsX_SetQuaternion_m1779E436C03BC48FAB7DE3D85C4088F0B5660493,
	RCC_PlayerPrefsX_GetQuaternion_mE169542779B1D0E4CDB3799A10D48EB70D39740E,
	RCC_PlayerPrefsX_GetQuaternion_m1A0756CEA9B9D783B8E7077DBF1EB8B9C335B650,
	RCC_PlayerPrefsX_SetColor_m4DB9959CBF456552287A0C869149334E94182882,
	RCC_PlayerPrefsX_GetColor_m1B577556682D142BC97D1A4E7DC951C206EA3040,
	RCC_PlayerPrefsX_GetColor_m9ABA157532A68B4DB701F674D94A35995660B036,
	RCC_PlayerPrefsX_SetBoolArray_m54707B241312CFFB71ED93E61132EBA2FDBDCEBE,
	RCC_PlayerPrefsX_GetBoolArray_mA208C21D6BF9A8AFA7BF30A50B2D5ECBA4C73BBD,
	RCC_PlayerPrefsX_GetBoolArray_m6D44B65B6B18B08374E7FF172150B7BD2281B8F0,
	RCC_PlayerPrefsX_SetStringArray_mD99F60543FB71596CF87D0F9AEE0615A721FF00B,
	RCC_PlayerPrefsX_GetStringArray_m8665AD060CF748F2C9E5C581A6A5CFFB0A1666F0,
	RCC_PlayerPrefsX_GetStringArray_m0161E44CB15F5878D2D667EEB3D927FFF2242B83,
	RCC_PlayerPrefsX_SetIntArray_mE0D89D3B0758524A410743A5711220870AF52207,
	RCC_PlayerPrefsX_SetFloatArray_m5AE32B1EE29512841052D8316634AEDFF7F6C80E,
	RCC_PlayerPrefsX_SetVector2Array_mFF0C5B1AF6B9CCB305E219F8617BAC219EE959BE,
	RCC_PlayerPrefsX_SetVector3Array_m877176118EB5A0402EA07AE9E7170640A3FF765D,
	RCC_PlayerPrefsX_SetQuaternionArray_m864DE32F58A22DCD657450BC311DB2AFC08ADCE5,
	RCC_PlayerPrefsX_SetColorArray_m05ED653BD5854949D74A3FD65919DD82E655285E,
	NULL,
	RCC_PlayerPrefsX_ConvertFromInt_mA8968FBB6471D05149A529DF8EE17707080101BC,
	RCC_PlayerPrefsX_ConvertFromFloat_mDB8D6ADB50BFC115E1B3264B5FB74B599093F624,
	RCC_PlayerPrefsX_ConvertFromVector2_m0FB437DBCEAA22E4C29853AC0D4960E07C5B65DC,
	RCC_PlayerPrefsX_ConvertFromVector3_m8B3174CAA757F33156C2501E2A30FD210EE152EC,
	RCC_PlayerPrefsX_ConvertFromQuaternion_mA753C6F5672E7769421E8750CAE32CC83060DFC0,
	RCC_PlayerPrefsX_ConvertFromColor_mBD1C77B545A3EF8920D5BE52557C4F6E72252684,
	RCC_PlayerPrefsX_GetIntArray_mCCC3EB0DBE6E982EDA9B769896A2DDAA893A01CC,
	RCC_PlayerPrefsX_GetIntArray_mD6CFAD33608973502C32D658A0F72A00F812539E,
	RCC_PlayerPrefsX_GetFloatArray_m7AC31050689B2A52E3B92C86F257DF0C36781868,
	RCC_PlayerPrefsX_GetFloatArray_m8B909AC57A1FC76DA8A07F0A9FD4DC5450F82120,
	RCC_PlayerPrefsX_GetVector2Array_m558A9731CB45B71403F1497F0136FBCF525DE7BB,
	RCC_PlayerPrefsX_GetVector2Array_m2F34414ED17E1CA5139A03EC5F1A0DC944EEE27A,
	RCC_PlayerPrefsX_GetVector3Array_m6A4D14D744B6CEB930AD094E024CEC6B1916C23B,
	RCC_PlayerPrefsX_GetVector3Array_m10E004C1DF687007969D8082DC0F38F0FB998BDA,
	RCC_PlayerPrefsX_GetQuaternionArray_m965181F10B0DCBB29564109EA741D70565D38B10,
	RCC_PlayerPrefsX_GetQuaternionArray_m773BFD28132B20D0A64BC3A37A04EB9FFAFDF0D2,
	RCC_PlayerPrefsX_GetColorArray_mA14E70A9D77C09149A698826F55180DC5EC13611,
	RCC_PlayerPrefsX_GetColorArray_mB8846910B1CE083E8B6844B95577A1DAA0B6CE59,
	NULL,
	RCC_PlayerPrefsX_ConvertToInt_m4B1F02AE1866A10E3BE5D833DDD034153B96A398,
	RCC_PlayerPrefsX_ConvertToFloat_mC597D49EED5A9DECE0F27081E1CE0C9B5069C0C4,
	RCC_PlayerPrefsX_ConvertToVector2_m0F995A60499F49329D747E3705C4065E6B9DD927,
	RCC_PlayerPrefsX_ConvertToVector3_mDC4F26769971C5A047B6ACC7DEC4B168B3E9F885,
	RCC_PlayerPrefsX_ConvertToQuaternion_m4F1E6FAF695B0E7379DBC4E57DE5B9F74C45FA66,
	RCC_PlayerPrefsX_ConvertToColor_m04007E380B4233C0C934F6641BB7284B3DF497D0,
	RCC_PlayerPrefsX_ShowArrayType_m521E11466BBE682F81CD05D3AC086DFB62FB068B,
	RCC_PlayerPrefsX_Initialize_m2A6F4A055799DE31B4FC860FA0E97577D18BF79A,
	RCC_PlayerPrefsX_SaveBytes_mF37BA0AB00CB10A0CCAACA88D258FA8A89190408,
	RCC_PlayerPrefsX_ConvertFloatToBytes_mCF184A3E49C9BAA4E1E3DC7CEB84266375A96957,
	RCC_PlayerPrefsX_ConvertBytesToFloat_mF85EA421D915C1BCE2C198738462578584E89E9E,
	RCC_PlayerPrefsX_ConvertInt32ToBytes_m40F466D3AB83B4F34BED3F34BB769A3C4BDD8BC2,
	RCC_PlayerPrefsX_ConvertBytesToInt32_mAC07103B3C93C8BAE0E460F3AC6D30128F3A0D32,
	RCC_PlayerPrefsX_ConvertTo4Bytes_mDB053E0739B3F562510D1B69A4E1AA13C686363D,
	RCC_PlayerPrefsX_ConvertFrom4Bytes_m12ED53CC6B89EF75D87BFDC858AFB305FC2B516A,
	RCC_PlayerPrefsX__ctor_m61D246DACE6B0678E6247F460A9A90A3B85A5796,
	RCC_PoliceSiren_Start_mE8A9EAD837EB1318DFE6C07D058FD425F28F4511,
	RCC_PoliceSiren_Update_mE80DE45AFE84E55F620366112B7918A67CB1FB26,
	RCC_PoliceSiren_SetSiren_m5377D180B725608579BC96104769E5E862C7A8F0,
	RCC_PoliceSiren__ctor_mBF00285FFA05DE32CFDB0DD2B48EBED7698CBD3A,
	RCC_Recorder_Record_m61434AA7F8175CDEF68537DCF3CC7A788C6C3B50,
	RCC_Recorder_SaveRecord_mE2C729980E7827613E8D783E813C2CE6A44EAF87,
	RCC_Recorder_Play_m7DFC888B4E732C474EDAA188B5439F10130465F1,
	RCC_Recorder_Play_m920E725A38011C2F056D95B6E7B1E25BDA98B155,
	RCC_Recorder_Stop_m49DF89C77CDA8937A7C391A4D21A32001A56C5C1,
	RCC_Recorder_Replay_mCB8F543BB938360131A26B17032F2D9E4E84D13B,
	RCC_Recorder_Repos_mACEA28AD16398978903BF022E8792B9E61156A1B,
	RCC_Recorder_Revel_mD605F0CBA5107BFBD8A1F9AD191ECCF9B1DF95F2,
	RCC_Recorder_FixedUpdate_mCC085E2E48667EF9D7A73AE3B13FF4E27BC53897,
	RCC_Recorder__ctor_mC80F0DEDFE1963AE4AC48C043B611676BF9171FB,
	Recorded__ctor_m19231BE67507A7673B8DD6975E31168DA91AC28D,
	PlayerInput__ctor_m2400518145BB16F16E9D4228E87B3EA0A5421D70,
	PlayerTransform__ctor_mCB46F34F294529461B2BC01F7E7387EC0EDDB39F,
	PlayerRigidBody__ctor_m835371E56E4A64642B2E183E8B68F73774773EA2,
	U3CReplayU3Ed__16__ctor_mB9F8BF6A7961FC0BCDE24595914C6CDD4185DA2D,
	U3CReplayU3Ed__16_System_IDisposable_Dispose_mA568CC957DDCF7AEA1EC83C6014EA817142AC99D,
	U3CReplayU3Ed__16_MoveNext_m8224989D08B562788D15759D8F0CE48D9564B673,
	U3CReplayU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB652DB22113A751F5E62C0118A89C0A7852A9DCA,
	U3CReplayU3Ed__16_System_Collections_IEnumerator_Reset_mBD22EE0F1A4885817E0B124AD841CD0722581351,
	U3CReplayU3Ed__16_System_Collections_IEnumerator_get_Current_mA572D3B3D2BD541645BBAE9D72A8AAF66B6869B1,
	U3CReposU3Ed__17__ctor_m0321A3A30C4717AAEB9D7837F58F67D03513E459,
	U3CReposU3Ed__17_System_IDisposable_Dispose_mDA410B5BB748060FCF2F80273D2E9D13E2309201,
	U3CReposU3Ed__17_MoveNext_mB0B350958587F66DB883F14B14CFCC7FBEC076D9,
	U3CReposU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEB158A68D5082D6CF6388D0B2B8BDB5435AFD296,
	U3CReposU3Ed__17_System_Collections_IEnumerator_Reset_m451D855D4F5F9A10588987393698DDC05DA5C61C,
	U3CReposU3Ed__17_System_Collections_IEnumerator_get_Current_mD260B401F286E2C92C928D9F687AF973A052C378,
	U3CRevelU3Ed__18__ctor_mA59F93C7334102341D5DB01F68CE2B32648C2B41,
	U3CRevelU3Ed__18_System_IDisposable_Dispose_m972AAB5504AC3EDBE56D21DB91C6679BE1899018,
	U3CRevelU3Ed__18_MoveNext_mC5C6288129749B79A1E5BB06E2790FBD9990CE07,
	U3CRevelU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB3545751513147303B9DC59D51AB7493A296931B,
	U3CRevelU3Ed__18_System_Collections_IEnumerator_Reset_m4BA9ED55300401BD6803495E21E63F5C89EBF25B,
	U3CRevelU3Ed__18_System_Collections_IEnumerator_get_Current_m3117313D81F41949A5A3DE9640715A179B974331,
	RCC_Records_get_Instance_mBEE6569C59FA0BBD160DD01E7DEC2891E3859685,
	RCC_Records__ctor_m59B5B713447E2B1A679E9607AD1EC3FE6CC295D7,
	RCC_SceneManager_get_Instance_mAB016D740C87231352BBA591C1ED97E485675606,
	RCC_SceneManager_add_OnMainControllerChanged_m89A15548A2B46FBBF43DA08C9DE5AE0DED73475C,
	RCC_SceneManager_remove_OnMainControllerChanged_mED16E813B18487627B068BC21ED9245A171DED93,
	RCC_SceneManager_add_OnBehaviorChanged_mAEC81E82068FEF375971E4CDA90E7804D1EA9D64,
	RCC_SceneManager_remove_OnBehaviorChanged_mC9EC4327421E5E6DB88D947DFBEF9B11F7786AF1,
	RCC_SceneManager_add_OnVehicleChanged_m5A750372D5BAE35948ABD7F5C464D4A4DE8E667E,
	RCC_SceneManager_remove_OnVehicleChanged_m05DCEFCDC113B7D0F6FFDD6D231AE5D31FA76F37,
	RCC_SceneManager_Awake_m381CC10229205108DC3DB3F7CA24129B8C60FAD1,
	RCC_SceneManager_RCC_CarControllerV3_OnRCCSpawned_m85D373B4A81CF73F67B770D1B02C7ED7625707FD,
	RCC_SceneManager_RCC_AICarController_OnRCCAISpawned_mC1B6A4365B976B6677816C3C562ACF3C116773D6,
	RCC_SceneManager_RCC_Camera_OnBCGCameraSpawned_m605B85C72B2E7FA3D2557CC961D260F7148DA52C,
	RCC_SceneManager_RCC_CarControllerV3_OnRCCPlayerDestroyed_mE0CE70AB445FAAA14B31CB269E4B574C64726BFA,
	RCC_SceneManager_RCC_AICarController_OnRCCAIDestroyed_m61CF3B1A63CFA78E0C0884C5041C9C77E1CFE4DB,
	RCC_SceneManager_Update_mB73E41726C6DC5962D52310D5DB2134BF5E83C96,
	RCC_SceneManager_RegisterPlayer_m20F906D2FF17B6D07494ADD30505519248318A84,
	RCC_SceneManager_RegisterPlayer_mFAF84E5163E78D81171F6821AF59EC9EB62BEB59,
	RCC_SceneManager_RegisterPlayer_m457BFE2120DAF04F7BE7AF69ED1F937BE5132969,
	RCC_SceneManager_DeRegisterPlayer_mE3C3B7EB6EA9C488B12B8B2B8C3BCED9F7FBF076,
	RCC_SceneManager_CheckCanvas_m51355EFDD7DD341D33E3F8913F209AAE4B0D4F31,
	RCC_SceneManager_SetBehavior_m622EBE69EC552760357E8557E576EED5371A9C9E,
	RCC_SceneManager_SetController_m8945F58EFCBEA218040BBF35EDEE9E1CCD1753FF,
	RCC_SceneManager_ChangeCamera_m79584F2088846770F1D80BD9DCC1A969E93B150E,
	RCC_SceneManager_OnDisable_m72DBF905D2E8C1E3037EBA753B20EF1FBEE78ACC,
	RCC_SceneManager__ctor_mBF27C69918D8C828E0842E1085B905C5A8548EA9,
	onMainControllerChanged__ctor_mC46F2C4F745DD52807E0442613C1C525A639F949,
	onMainControllerChanged_Invoke_mBBC08F883F5D8AC750E4D97D5EAC9DEED31E2EA9,
	onMainControllerChanged_BeginInvoke_m0F1735473C6B717E60E987147EC26E8248D1E84A,
	onMainControllerChanged_EndInvoke_m9E5FA0DA183FE98BAA90180DFF6FE09168804A7D,
	onBehaviorChanged__ctor_mD3C7853D23A9D3395EEF2D76B0DFBFC6C3F4AAEB,
	onBehaviorChanged_Invoke_m35F3C6131654B7F654CD1BFF7F9E454F502B76D4,
	onBehaviorChanged_BeginInvoke_mDD757D4A770368AFF08B6FEC2F49CC31836261BD,
	onBehaviorChanged_EndInvoke_m79895834E0870AE435E68409579BD8540BC668FF,
	onVehicleChanged__ctor_m6D6632BD74FA2AE357F135E96F9B4CB858B3D320,
	onVehicleChanged_Invoke_m4BC16FA9CA53CE07C9E7E5A39898D0111F751DC1,
	onVehicleChanged_BeginInvoke_mDF6544CC7C76EDDDEA8DE1EF0A67F8F30FC3BBF8,
	onVehicleChanged_EndInvoke_m511A357A1CB26E12870F5B484128BC9DCA9763B2,
	RCC_Settings_get_Instance_m882E38914DF2607B3766871A04C2CFA9898D5B6C,
	RCC_Settings_get_selectedBehaviorType_mE5A10982296B874C56A04F80E06D393D8D3029A2,
	RCC_Settings__ctor_m7BBD665AFB00FA612BD24A6A9DAE182D788C19E8,
	BehaviorType__ctor_m67B167B5C8D8921ACD7C65F32A98A00D23234F18,
	RCC_ShadowRotConst_Start_m4482E13C5569CAF9C08216BBA8BE26008B8BDE68,
	RCC_ShadowRotConst_Update_m4DE35DB651040477559144F42E921FC0A9ADDAAA,
	RCC_ShadowRotConst__ctor_m34BAD29D38002603CB1DCE8DD984CF8B980068B4,
	RCC_Skidmarks_Awake_m08FB0E7E42FC3AB8B9364B0D2180AA2092960CFC,
	RCC_Skidmarks_Start_m4758B4540DABF8FABCD23BAAAB8E2A244C854FD9,
	RCC_Skidmarks_AddSkidMark_m7416143FF5A75845B891D6062D6903DAE1B598A3,
	RCC_Skidmarks_LateUpdate_m16DB62E67C084F32F55582260FB20EDC8E76EB23,
	RCC_Skidmarks__ctor_mF584F911A52F5E6542ACD479417228BFB6AD550B,
	markSection__ctor_mB0855996190A4606565138AB040D6983BF21FC65,
	RCC_SkidmarksManager_Start_mC94CA3593A9CCF14B40DE3BF0F37A2F6C5932B35,
	RCC_SkidmarksManager_AddSkidMark_m39BF86084AC7DC51C5B8CD28A83EF5485FE5606B,
	RCC_SkidmarksManager__ctor_m631C1901A6C905ED09801A42FA4B777225B49619,
	RCC_Spawner_Start_m2B64874288F36D82B544AD4CBDC78B320B4725A4,
	RCC_Spawner__ctor_m26E1074D7E9C772803A91A3A4B0AE5371C802D8E,
	RCC_SuspensionArm_Start_m7C3F0DF30D89533D7F85967C278AB41485960160,
	RCC_SuspensionArm_Update_mD0B13CD0A04B7BD2BE94B79A9A554273A6CD22D3,
	RCC_SuspensionArm_GetSuspensionDistance_m9F8E3267D4A3BBDEEC680A50D7149F52EED73C78,
	RCC_SuspensionArm__ctor_m4FFA675E426EEA624AC46FA468DBF7A642E80745,
	RCC_TrailerAttachPoint_OnTriggerEnter_m3E781F021CF5DE6E9C7128622F6E1E5502159E93,
	RCC_TrailerAttachPoint__ctor_mBD1224CB055153AC6E5A42D02F98E40E9CB1CDE4,
	RCC_TruckTrailer_Start_m0A84EFBD311768AA235185D0D6DCDF2C7E706E83,
	RCC_TruckTrailer_FixedUpdate_m8C8600F5BE8A044F767374D654235C0D3F9BDF0C,
	RCC_TruckTrailer_Update_m708DFAE1A0903498BF333FDC8F48F3EECB1B2BEC,
	RCC_TruckTrailer_WheelAlign_m0D95D186A2E759782E4D518B09FE9419295B7B39,
	RCC_TruckTrailer_DetachTrailer_mADF583A94CED051F555353F09C67FC83B7C95CA0,
	RCC_TruckTrailer_AttachTrailer_mC177E7426F294EABF414389D72AC38CC2071583A,
	RCC_TruckTrailer_AntiRollBars_mCE3BEE8B489B9F9C2A1A615BDBE9E2CCCC509C26,
	RCC_TruckTrailer_OnTriggerEnter_m73A8DD89C00D6C5DEDDA32B467F3761E8A86E2F2,
	RCC_TruckTrailer__ctor_mCA0F75E7BA4B49FC1CFC454CEAC690F0AE880EE3,
	TrailerWheel_AddTorque_m0803C940B07B3473D48F7C3E967B4A36CEF40F7B,
	TrailerWheel__ctor_mDB4358B5637FAB4147C07BC7F4183CD14D883DFB,
	JointRestrictions_Get_m1243804932D083E05C45ECCED87081D147101D06,
	JointRestrictions_Set_m4981283E59878B9BA3E08DE5420A48D5046CAF00,
	JointRestrictions_Reset_m52666139C8113C926EBC43DBB665B88FABB0B8FF,
	JointRestrictions__ctor_mFF44CA1A6E28DC4ECD98A6FB2E4456DEC61196C9,
	RCC_UIController_get_RCCSettings_m38460900FC4AF19262A1A40EBED49BA942136DE4,
	RCC_UIController_get_sensitivity_m0B4397117C7854380ABA07416918DA044ACB0826,
	RCC_UIController_get_gravity_m7B89A17D9DA9F65074614700386E0EA73FC42A82,
	RCC_UIController_Awake_m16C312AD3CA6AF9744524A1DD602AD639B2C6A60,
	RCC_UIController_OnPointerDown_m5F2B1B2EF2BB5E68F14D16F21A9C58E830156A3E,
	RCC_UIController_OnPointerUp_m313678CBE1CF7B2285001822BEEAAC164D938116,
	RCC_UIController_OnPress_m0457E02287DA873EA354AC12C9797C3269FBFE2A,
	RCC_UIController_Update_m69DFCB92311D4BB7D8397181E5D300F51C8B3616,
	RCC_UIController_OnDisable_m15C156B50BE07435F3E9ED1408D8E7C48F0499F4,
	RCC_UIController__ctor_mC5F785D18CA7A8E4358BE18609528BA5751922CE,
	RCC_UIDashboardButton_Start_mCC53B89F700A845C3BD0277874DF17F23C312D15,
	RCC_UIDashboardButton_OnEnable_mF38D16E079BF72FF6F6B8734F43C374949C396E6,
	RCC_UIDashboardButton_OnClicked_mA10BBD8DE5AD178D30D13B449DC6164BCA48D692,
	RCC_UIDashboardButton_Check_mEDF307BBFF315478977C2FAB530607A68C9E47D5,
	RCC_UIDashboardButton_ChangeGear_mA89640C0FC1AB37B47A8BEB7A259CA6FA9547BDD,
	RCC_UIDashboardButton_OnDisable_m0DDC7429E6512E4E3BA7A659B20FD7F536BBE9E5,
	RCC_UIDashboardButton_GearDrive_m8D82FE73D59CE91AF35131F1BAFFBB3B7E2CF435,
	RCC_UIDashboardButton_Gearreverse_mD475687637BDF2C29832F58F351CA5D0E5A11338,
	RCC_UIDashboardButton__ctor_m45C165996F117A5704BDDD4E145C771C3A476E6F,
	RCC_UIDashboardButton_U3CStartU3Eb__4_0_m8B583736442F9A204A460DF737E434AF0436CA98,
	RCC_UIDashboardDisplay_get_RCCSettings_m9C2F5830CD86D3A4BEB945A167EE5D447A5850CC,
	RCC_UIDashboardDisplay_Awake_m95946C21589EB7D9377D3E5689ECDD745F0D7EDA,
	RCC_UIDashboardDisplay_Start_m0CE64E8F318A6F5A29B970B655957E1CB7237C53,
	RCC_UIDashboardDisplay_OnEnable_mA4DAA8AD741577BA848D0B40282A79E069CBDC52,
	RCC_UIDashboardDisplay_CheckController_m9EB39B5F27949F87BE02A1CD3A8DA6CB279F582A,
	RCC_UIDashboardDisplay_Update_mDFC648C515E3D88E58BEE69013AEC01DF0BB5C5E,
	RCC_UIDashboardDisplay_LateUpdate_m85FA55E553C16C5802521B72F56C495AA10E8464,
	RCC_UIDashboardDisplay_SetDisplayType_m442E16DF00EA5292DFF2201D70FA1F35AB324390,
	RCC_UIDashboardDisplay_OnDisable_m6B535682E943FD3EC1272454AFBE9B3B910599DC,
	RCC_UIDashboardDisplay__ctor_mA0E9F674B39DC0C0FA25158A51A6D20AE8A14646,
	RCC_UIJoystick_get_inputHorizontal_m5F1F24A587306D44D7EB84B4126669A5A45787CF,
	RCC_UIJoystick_get_inputVertical_m2115BDAE356A6B8415EF6EB49D46AD28A7B0D732,
	RCC_UIJoystick_Start_m59702FD58D82A585E21E36CAB1DD4E5296D1409E,
	RCC_UIJoystick_OnDrag_m54F949553EF889C70B0D1E722C4FD2D402AF561A,
	RCC_UIJoystick_OnPointerUp_mF07E3A46A9F798BE10F00FADB4B3CAA7DF361E80,
	RCC_UIJoystick_OnPointerDown_m052B4734B687C342F692B814C372454BE12EFD2D,
	RCC_UIJoystick__ctor_mD588EB4B3E3FCCEAA7507B5927A9D99193765D7C,
	RCC_UISliderTextReader_Awake_mB047FC5AE23C45C5EBB700C1FB462B1CA3A74177,
	RCC_UISliderTextReader_Update_mF05421A65F736F4FE27A9FB2A99FD6E452FD0FB8,
	RCC_UISliderTextReader__ctor_mB54846B203930A581FDB8076D2E67D3EDD7FDA5A,
	RCC_UISteeringWheelController_get_RCCSettings_m9D7A5ED6A589224D0759AD31464895FDE56A66D9,
	RCC_UISteeringWheelController_Awake_m4C2A879F64EB27DF4D4592F1E924D4FF27962365,
	RCC_UISteeringWheelController_Update_m634B0E47D1E41C51B7ECB0F18B1D8D43FF0AB7D1,
	RCC_UISteeringWheelController_SteeringWheelInit_mBE4837B44EBBCEEBB953C41C0617D8D7605B2A26,
	RCC_UISteeringWheelController_SteeringWheelEventsInit_m8B325F244418E6E467B2AC57B51A9C00322E18AC,
	RCC_UISteeringWheelController_GetSteeringWheelInput_m3AC76DF957ED074B622E82470C6CAF3824E8DC42,
	RCC_UISteeringWheelController_isSteeringWheelPressed_m9F4B79C2E28956CB1C4A5FDB753F57140A4EB132,
	RCC_UISteeringWheelController_SteeringWheelControlling_m36B4F81A68037D87F42A2CD509A3AEEA4EE2C02C,
	RCC_UISteeringWheelController_OnDisable_mC5AEFC70C98FA0F8C62B2AC425F35883FD64C947,
	RCC_UISteeringWheelController__ctor_m8D491805988BCD2178CE1731D151246893AD536B,
	RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_0_m22BF9241C6050BDF05C4CE723689FA865A361402,
	RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_1_mC3C4DFB94E509F77DADC020AD49074B13DE338B2,
	RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_2_mBC078C2B056DF8D07E00F130A48B601A989E7701,
	RCC_Useless_Awake_mB230AA74DE817959A3A74C7DC709C3AA3F499E69,
	RCC_Useless__ctor_m4AD74BB2B27A4BD839838F61E4CDC3A62D95F99E,
	RCC_Vehicles_get_Instance_m30712556C65E837C051857A7C54DB1AE24FC4015,
	RCC_Vehicles__ctor_m670FA46CE5D2D646F281F6CF42D74F9A01C53CD4,
	RCC_WheelCamera_FixShake_m769326A6E32E65CFCCD0A1910EB2895B5E054169,
	RCC_WheelCamera_FixShakeDelayed_m2E0A03ECD03FE70C28057C91BFF5646F2C4C4F28,
	RCC_WheelCamera__ctor_mBD2C3C0E7757357C3A5903BC0BDC991709E87C60,
	U3CFixShakeDelayedU3Ed__1__ctor_m204A4662380E106CFB28EB14B004A2E9DB6BD546,
	U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m897889779F5BADDE9EF6DFF5CC32935906FF3DB2,
	U3CFixShakeDelayedU3Ed__1_MoveNext_m3936AD2C077E517D46C214B63E2D38B783DF4FBE,
	U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB0CA96DCB23FB13704FA6C9A44E4F1AFF97388C0,
	U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m5430D52BA502C196D76FC653209F18AE9C630819,
	U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m27B86E74B26471D822D64FE4AEFCA6D397A37C45,
	RCC_WheelCollider_get_RCCSettings_mB992026C58E13ECC4B47F2688446B0A604E7BFE6,
	RCC_WheelCollider_get_RCCGroundMaterials_mBEBF63ACB8393556D5C2059BDEC88C309A9077BB,
	RCC_WheelCollider_get_wheelCollider_mCFAC39B39E518979C0EEA45654B68FEF0D7AA544,
	RCC_WheelCollider_get_physicsMaterials_mA474B9553E581CA4CBA8A856297BA178D785EADD,
	RCC_WheelCollider_get_physicsFrictions_mEDB9E0F4E83AAC61C21CEB9F8F736F6C3C5FC3A9,
	RCC_WheelCollider_get_steeringSmoother_m8F49707DEC853446B6C934FD2E20F3EEC5DCD8DE,
	RCC_WheelCollider_Start_m0B5FA9235BE8387391CBCFC21A1021729C06FEDE,
	RCC_WheelCollider_OnEnable_mBD1E13067C64DCA40BFB5573AA3F158C82640A59,
	RCC_WheelCollider_CheckBehavior_m908BA6A5051BB59603D7CCF70B9DBF2AD7ACE9AA,
	RCC_WheelCollider_Update_m877AA9A48865D4A04E13FF937C95E7151B09FB1A,
	RCC_WheelCollider_FixedUpdate_mAB4662EC6EF431609D970F6EF54270F9FB7A1BE3,
	RCC_WheelCollider_WheelAlign_mBC51FFE9248F66FB51C6EF74E3CBDD4FD2257FC2,
	RCC_WheelCollider_SkidMarks_m9422E631F9026E65D93DCA8D4FFD6943965BD9E1,
	RCC_WheelCollider_Frictions_m543A7929A1C3F0B3957E549BE4939049D6E6D8CB,
	RCC_WheelCollider_Smoke_m771513B91731E8E75B91F96FF088BA304418B624,
	RCC_WheelCollider_Drift_m0EBB7633EB9051561BEA188CC3F687459955282B,
	RCC_WheelCollider_Audio_mB1E60BEC13D751FD77A6AB6B71521E5796654234,
	RCC_WheelCollider_isSkidding_mFCDEE720AB1D290B9FC47F74A9D5BC0E49C7827D,
	RCC_WheelCollider_ApplyMotorTorque_mB7779FB63CF53DEE4337E5FD36F40DCC033D65F6,
	RCC_WheelCollider_ApplySteering_mC24FAEE3288258C57E98270514154C70036DCEA3,
	RCC_WheelCollider_ApplyBrakeTorque_mC08EDAECF4A4EE0D3BEACE5A08B0659895FDDD0B,
	RCC_WheelCollider_OverTorque_m5021C8F81D55F494D2EEA74570AE5933C2BA76D5,
	RCC_WheelCollider_GetTerrainData_mB172AC0C631476884B3D830CBF46675D2784EC1D,
	RCC_WheelCollider_ConvertToSplatMapCoordinate_m1C7E3A8E16743FD9590C077CDBE27E56E98BADB1,
	RCC_WheelCollider_GetGroundMaterialIndex_m464B05DCBDFC9A34CC21F08CF7E7512BB08745A2,
	RCC_WheelCollider_SetFrictionCurves_mBD8B98FBB3381CC30151CEC0320DD6176EF561C8,
	RCC_WheelCollider_OnDisable_m4F3DED87A74EC5288A073A6A4EAF871229C888A9,
	RCC_WheelCollider__ctor_m2DEAB3FB4B0035563C0BBC6FA7384CA5D2EBA0A9,
	RCC_XRToggle_Start_mC5A3B7C0F03020286CC36E2C38BDE6A2EADD3C15,
	RCC_XRToggle_Update_m3013CE8952D43B8A91286011F85EBAFC9B44CBAC,
	RCC_XRToggle_ToggleXR_mB5F16490F91C6977CC90FFC655961C024C4D4DA2,
	RCC_XRToggle__ctor_mD98DB6DE423CBFB721C95F75EC96A7AA2BDE56CF,
	Fail_get_Instance_mF7A2AAB284DFEC07138EA0FC5A1961D69F98007E,
	Fail_Start_mE24CF1C39402D4743BF0CA0FDC89FD6307388CEF,
	Fail_FixedUpdate_m7BF8893C10CF5138E2D9963974CE5EC4F6C9C9FD,
	Fail_OnTriggerEnter_m26F4BB9209530D92986996043CE5F6662581E1A7,
	Fail_OTHER_m9302EB2FA190E6331828330607495633CC78098A,
	Fail__ctor_mE3B565CF62F238D2D83DFBC440E0E4D9B21B65B4,
	U3COTHERU3Ed__11__ctor_m0B6E55F410EF7E22AD3A9754F608909D926D5FFF,
	U3COTHERU3Ed__11_System_IDisposable_Dispose_m0B9B07668BD86422B5AB242B5C0F897B301D587C,
	U3COTHERU3Ed__11_MoveNext_mB80D38B604E66D3BB111B9226518518185C5CBEA,
	U3COTHERU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1B623C693AAEF9EF516DE1D821E354D497C399D,
	U3COTHERU3Ed__11_System_Collections_IEnumerator_Reset_m771865FB69A0E526862906D154CBD9C36231DD17,
	U3COTHERU3Ed__11_System_Collections_IEnumerator_get_Current_m0EE232A7B5409AB7194EC5DFE2E38D2CF303240A,
	SliderRelease_Start_m0F1289A6BC91A2E2B83BA80B9CA0FBD56B0E75C2,
	SliderRelease_Update_mD6B2AEC27F1E44276E5CA03C312AEE0AC5064BA4,
	SliderRelease_OnPointerUp_m41E4410028C33EA7C67D7C96FBB4CBB77AFDF6C2,
	SliderRelease_ReleaseSlider_m4736AD6D8FDB819574EB0412E32DA2F6FE0D598C,
	SliderRelease__ctor_mAA6065440F8A23861AC017AD8F281172C863ABD6,
	U3CReleaseSliderU3Ed__13__ctor_mE4197199E78602577ED2A3A635EB216C77F39FEF,
	U3CReleaseSliderU3Ed__13_System_IDisposable_Dispose_m34F9BA03563BAF7CD102DF712A39ECFE541B0418,
	U3CReleaseSliderU3Ed__13_MoveNext_m73FAB59C6B2F934B1AD74311106C325AD31E565C,
	U3CReleaseSliderU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m46AD7346655EE2AC283D6C40CD1F9D1E27B296CA,
	U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_Reset_mDF5695C2AB546ADE270505C73425F4B3797CA92E,
	U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_get_Current_mEEAC84F67676031DDAF637E24168B12AAC567883,
	TimerSeconds_Start_m20A725BD4CA96229C29155CC013DAC282879B4CE,
	TimerSeconds_Update_m592F9A2B0654AC3CF08C8E07403510BA92AB94DE,
	TimerSeconds_DisplayTime_m1A4993809379460BE5116FC12C7159293B90604B,
	TimerSeconds__ctor_m2D260311B47BEBCCF13B3ECC21C18D8E9470C390,
	playermain_get_Instance_mFD90471C7C306EA0DAA5A31C8B8B59B5237176E6,
	playermain_Start_m056FBC6B84B9C90C47D6EB2C7BA9D004D70F5A78,
	playermain_UpdateMass_mB41B70989EF5C9B85BC72F384ED4F1107D88A8F7,
	playermain_FixedUpdate_m635C9558270B894945DF8D6CC4FE46EF7EC70F9D,
	playermain_OnTriggerEnter_mF49F1A6A9F2FE8C766F050577543BC2EE619481A,
	playermain_faildely_m06057F9352F4C08972CB3D78592C98A84D58AC25,
	playermain_OTHER_mE22E734AA6FEB4168A845F06FE53D5B77EC2460A,
	playermain__ctor_m51DD0E0A91BC1EE46E46004BDEB9A08829BCE9AD,
	U3COTHERU3Ed__48__ctor_m5614BDC61EE0311FE73BDF2E8CD344AE041045BD,
	U3COTHERU3Ed__48_System_IDisposable_Dispose_mFA8C120D991A2CD9F00B084ED447859CC63AB39A,
	U3COTHERU3Ed__48_MoveNext_m759D536E650FAA4B0244F70617EBA83D4AF15248,
	U3COTHERU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDFF4827EC256709E77F32DC3C36DE0390C28EA80,
	U3COTHERU3Ed__48_System_Collections_IEnumerator_Reset_m4674F834B072F810A67AE4BF0618E260D5865472,
	U3COTHERU3Ed__48_System_Collections_IEnumerator_get_Current_mF3D78D6C443475337EF24152BDF6F16C6D63B190,
	targetcam_Update_m401D954437AE228C3C5012D11055CB65E94DF50E,
	targetcam__ctor_mE87AE0CBC9E6161675A2168DB54C4BD414F89BBA,
	wheelAi_FixedUpdate_mEF53F96D82693BD0D9BEBF7292B5367E44F3BDB1,
	wheelAi__ctor_m865E614104D88AC4F11E8F48E894F68A5451CC41,
	IgnoreCollisions_Start_m9EB3EA7B79E4C7DCE99B83DF7CDDB178AB1EFBDA,
	IgnoreCollisions__ctor_m1169033953B26ADA56EC6B49E8325FBC97B9D9C3,
	Waypoint_OnDrawGizmos_m546B609CA0CF39637507D157EDF3D12E0330F9BA,
	Waypoint__ctor_m89EA42D47EFB28A7076838800458399F153E973C,
	WaypointMover_Start_m627D60B42E4ECA361DE01B072F4336FC63C4D3E3,
	WaypointMover_Update_m7F3E710EFF203AC490FDFB0FE75BDC42774B123C,
	WaypointMover_ReverseDirection_m15BE3BE19D51F58E73DCE256386368483E333418,
	WaypointMover_SetDirection_mF54AB60301835AB0AC04170258F83E02650E7A40,
	WaypointMover_IsOnWaypoint_m2369AAA586AB7BF542640BC4BED32779D47FBC38,
	WaypointMover_ReturnToPreviousWaypoint_m17B01F7F73D08C3F9A40FA6C61F502E830E976FE,
	WaypointMover_isMoving_m2F283F050A6A90AC8090861C0B3722EC253A7CEC,
	WaypointMover_Suspend_mF6B0AFB0A5656DE45511DC89041D5265861DB3D4,
	WaypointMover_Pause_mA6CBD593E73A3D9410F6CB9ECBF3794244E81865,
	WaypointMover_Unpause_m91D26C3F266C7F43075A4C9005533E84BE0236FA,
	WaypointMover_ChangeWaypointMoverSpeed_mE968E84CC2D668F81B579409AF3CAC31021C0FA7,
	WaypointMover_IgnorePositionByAxis_m04B7C06062F9F73DF85B55F9852DC2BA9F055483,
	WaypointMover_SmoothLookAt2D_m2CB9B23DBF1E9A86DFD8D44445CA89B610B4B3FA,
	WaypointMover__ctor_m7EE206982A15350A17F01F192BB0890A7C72D43A,
	WaypointsHolder_Awake_m4948B860D1B226374C4C5758A2B3152B1F4C9C37,
	WaypointsHolder_Clean_mE0C6238F7B90DDFDE736D826CC208AA675370836,
	WaypointsHolder_AddWaypoint_m19A500CF26F9211E06AB660348C24AAC47BB4F1C,
	WaypointsHolder_CreateWaypoint_m0587558C6286B20533AA26CD1971BA14777D74DB,
	WaypointsHolder_OnDrawGizmos_m8AD42290E21802DEE79803F23B39402A9D3522AB,
	WaypointsHolder__ctor_m736A9CFD364D7F41D70A3518873E5D3BC4ABE2EF,
	Animationtexture_LateUpdate_mB8641B6FA3CEF026F0ED36570F102A4CD02AC5BE,
	Animationtexture__ctor_m9257566EF3015D7CD30ABD65D04B1CFEDA7611AE,
	AutoTypeText_OnEnable_m3AC16591CF26C1B5488137DD57BB7E511641FACA,
	AutoTypeText_OnDisable_mD34F2B008287361BD663EAD0C15965FD6A11F680,
	AutoTypeText_abc_m046B8D1B8B3FF5F4EAF9AE9DBC4328EFF526DCF6,
	AutoTypeText_TypeText_m9848A0BF62B3A385570F90DFD4D488DAED70D0B5,
	AutoTypeText__ctor_mC2A8E1BE89011264AAB9C8DF628CBEDCDC5EFB37,
	U3CabcU3Ed__6__ctor_m43A18AC38E38869EC38704D231C3192BD88B34FF,
	U3CabcU3Ed__6_System_IDisposable_Dispose_m334377B0DD65CB59F9A08190AA84717DFCDE6FA4,
	U3CabcU3Ed__6_MoveNext_mE75A82D858BD10D46808261F0D6055138CDA9A77,
	U3CabcU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E6B56B3DC93F0BC5F924495777906F176C7113D,
	U3CabcU3Ed__6_System_Collections_IEnumerator_Reset_mECD8B6EB875F70004883F06C7A3BDFE201021FF4,
	U3CabcU3Ed__6_System_Collections_IEnumerator_get_Current_mD9320F71B108229AB2F622829C492F69E5AAE9C8,
	U3CTypeTextU3Ed__7__ctor_mE690467BF2D539923CA7D99FDC7E59A01CAF1771,
	U3CTypeTextU3Ed__7_System_IDisposable_Dispose_mD8CC279851A8912BF3A06D31485098AFA39E7B15,
	U3CTypeTextU3Ed__7_MoveNext_m1D7A9267BDDC1E9974B08F39A52F96F9E514472E,
	U3CTypeTextU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBDB53438EAEEBD2F8D9349B79FE49779359ED9D3,
	U3CTypeTextU3Ed__7_System_Collections_IEnumerator_Reset_m0AED2DACDF8C6C8E405ABC72555E846E51193C9C,
	U3CTypeTextU3Ed__7_System_Collections_IEnumerator_get_Current_mFF4349924EBC96992A6D7061D898499D0A02A58C,
	BannerAds_OnEnable_m457785E2F53AC74661F140FA06B9336F71000D30,
	BannerAds__ctor_mEB77347D4750ABEDFA41CBC418B6D6CA31A26642,
	Changetexture_Start_m5EDBAC0A7CF704DF5E6B15A6420A229AE75AD66C,
	Changetexture__ctor_mAE9F06B721BAE582A67F6CDEB23563560FF45396,
	FireBaseCustomScript_Start_m76D59FE3FDFFB847B9D8962333D6B8822213C1CD,
	FireBaseCustomScript_Update_m24A78C8BF9C59CEA7BAEE81EC2876947C5B92606,
	FireBaseCustomScript__ctor_mACC1CD972576B11FD01E1462B35BBBBC06F3B437,
	U3CU3Ec__cctor_m7A9753C492DAB1618A99944EEC7DB487D2C6022C,
	U3CU3Ec__ctor_mAD629CC497BE30C51A1B42600588430E26CF8E55,
	U3CU3Ec_U3CStartU3Eb__0_0_m83DFD7D39F261A1FE7C0DFFECBE1700B9F46F7CB,
	GDPRScript_Start_mA46522EF288B65A761737600AB3A0A088E089FAE,
	GDPRScript_OnConsentInfoUpdated_mB79863D0449F94C12A198205ACAF91F60D989C30,
	GDPRScript_LoadConsentForm_m73880CED644DA3E89869374D2C86AA015939ED3F,
	GDPRScript_OnLoadConsentForm_m4781476B6FA7D1CF327D2265343528E225429149,
	GDPRScript_OnShowForm_mCD37F93956CD85E8ED2AF4B9973227CB18FFF0B8,
	GDPRScript_Update_mC8E3E7E2A8391D521153616181E4897D66448B10,
	GDPRScript__ctor_m944A71B824C689C109CF0E9BEF7285A28D4A2AF0,
	LevelLock_OnEnable_m7E88F0E884A3F9F40ACDB625BDDD2E9BDDC27727,
	LevelLock__ctor_m4D74F02291B20311658E8C42CE1A592DC50EBC78,
	LevelLockImages_Start_mEF4CDC41F5FF59A98EFD48F9082C4AD91B9130D4,
	LevelLockImages_unlock_m791F57911F76860180FD41871317226110ACD130,
	LevelLockImages__ctor_m109CFDDF066F6B4DD48048A90A8CFBED5C0B7A58,
	LivingOcean_Start_m7064E1220FD818142C18E698053FB624B15B6C6D,
	LivingOcean_Update_mAB5E82F6BC4952659FC0D71A21FF4C65FD72A9FD,
	LivingOcean__ctor_mC84A94742CF290D04D02883ACF553D1D26562B64,
	WC15PRO_Start_m527EF0C158891660925E9A2D57A0396BB3FA5FFB,
	WC15PRO_Update_mABC7F7332A70CD7CB0D994C24E8B944C14B63118,
	WC15PRO__ctor_mCD60A019BDA530243705863BDBDA82412068F9FE,
	brake_BrakeStart_mC07E925DF151D63302058633D554E7D3FE2039D3,
	brake_BrakeEnd_mD509FF97A326C8C75BDC53FD17F985A6CA0625BF,
	brake__ctor_mEFD130E71513BA98B0F55F1D8F37977E535ADCB5,
	cronspray_OnTriggerEnter_mDC434E2FC9FC4B63683ACFA7C49206E764882A72,
	cronspray__ctor_m95523D8C77A2D59CD82AD867649903A93D55FF07,
	gameplay_Start_mFFB5832671CD80199ACC2B5313147AD1E591B036,
	gameplay_Update_mBB5A5982E606FCA80775E4153F722957CB7D2C5F,
	gameplay_restart_mC400A391483FA7CE7EA17B9583DE5F54D1622282,
	gameplay_pause_mE102E4CD01F9009476D77908FE82F4154E70893A,
	gameplay_ok_m412BA5D15B0B6765E30A4C228C9FEA4B2D533E8D,
	gameplay_home_m9FA04AE6BE11565042A1F63834D3808EF85DCD0A,
	gameplay_resume_m5E7BA244C060F8625B23837A8A364874B9B360F1,
	gameplay_next_mAD1F682F18671D98EC8E2EB1262E7662E17B0CC2,
	gameplay_Steer_m83E27326B5F3FCF3EA2EE66F5FB40E43828A8391,
	gameplay_Btns_mDB326C3977882FD72AEAC18633C215020DC5941C,
	gameplay_Tilt_mD38AFC29A4F2701B40F6E885F86F07244B6BE92E,
	gameplay_lvl1_m222961C305B83C3AB7B11B8A3B26EA4633BFD51C,
	gameplay_lvl2_mF5C4185CDD7BE11833ECDB4F9A609821A637BC7F,
	gameplay_lvl5_mE465A99D7A5FDA173F615C95A56582FAA3553193,
	gameplay_lvl7_mD2457D7BEC6A0AE46E473CC4E2CDCF3EE8DBF7F0,
	gameplay_lvl0_mEEB025DEF93243F8959C9CF6E309A0582C316691,
	gameplay__ctor_mFD6E0DCA59095A12544391955B34ED1B3F472BCA,
	U3Clvl1U3Ed__56__ctor_mE4E1A42457E3124976DD73E8A9A59F196E027090,
	U3Clvl1U3Ed__56_System_IDisposable_Dispose_mD644E8350E522FC488F9A31B7F82D5B0400F3A7D,
	U3Clvl1U3Ed__56_MoveNext_mA3B2ABC83D3CA44FB4415BD7B9B79603446CB7BA,
	U3Clvl1U3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEA76BFC814DD367A3B382F1EE6399BE317112A7E,
	U3Clvl1U3Ed__56_System_Collections_IEnumerator_Reset_mE9C4D92ACE2895DEDF2B1FE7F2E3B8B434AF9A9A,
	U3Clvl1U3Ed__56_System_Collections_IEnumerator_get_Current_mF653D72D087211FC9AE7331E286474EE4EC350F8,
	U3Clvl2U3Ed__57__ctor_m2EFFDF87AA2BFC5D3DA40264E825A3C26362C0CE,
	U3Clvl2U3Ed__57_System_IDisposable_Dispose_m4D1C8CFB2CDE3D156A311E59B92937267663962A,
	U3Clvl2U3Ed__57_MoveNext_mBF92F0E9CD589A47A4C3C51B8A3972091AAB102A,
	U3Clvl2U3Ed__57_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m502500F20DE9F50F19AC1A274D7864CFF008708F,
	U3Clvl2U3Ed__57_System_Collections_IEnumerator_Reset_mBDD3D39B888218936D37127AF3FC42C46452EFB1,
	U3Clvl2U3Ed__57_System_Collections_IEnumerator_get_Current_m7F1B9220F2F4A62551EA065C1A917ACB67DE5F45,
	U3Clvl5U3Ed__58__ctor_m5F5BC85F8D246E3C0D35E5FCF5650E48438DEE75,
	U3Clvl5U3Ed__58_System_IDisposable_Dispose_mBAEEC551D6A403B453B735AADF2DEF366AC952DE,
	U3Clvl5U3Ed__58_MoveNext_mE7C2D860F0B09C89B6CD167A1EC1CE8F31B5BFB7,
	U3Clvl5U3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m21C3F4676E11B20E8C9DD88CC8483F6644AF9B18,
	U3Clvl5U3Ed__58_System_Collections_IEnumerator_Reset_m58488997363FEC377CD7CD8938796727DEACD2E4,
	U3Clvl5U3Ed__58_System_Collections_IEnumerator_get_Current_mEFACCB740AA3DF190553CB5471E81B414FBAC7D6,
	U3Clvl7U3Ed__59__ctor_mEF40FF7E99BD7112CA3029749A5F8EDF590A9591,
	U3Clvl7U3Ed__59_System_IDisposable_Dispose_m1DD570AFC814E1D59B9622652298FD8D398DA3A6,
	U3Clvl7U3Ed__59_MoveNext_mF8C606F84687908BBE9C90668719F1C036FAD8F7,
	U3Clvl7U3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4EE7A5B50DA55D5482C2183630F78A16A2800F15,
	U3Clvl7U3Ed__59_System_Collections_IEnumerator_Reset_m5D77977BFF43716E13D5C93FA3B833D219CED75A,
	U3Clvl7U3Ed__59_System_Collections_IEnumerator_get_Current_m9AC0E3A9E524228C023189A55B8D0C946175E6F7,
	U3Clvl0U3Ed__60__ctor_m9DCD1CE8813F87353DE64E94CB456BDC65C8AADD,
	U3Clvl0U3Ed__60_System_IDisposable_Dispose_m0978BEAAFB3BAAD4F4D90C39CF341A1C41058A59,
	U3Clvl0U3Ed__60_MoveNext_mBAFE8AE59EF634DCACA4B763F775031489E23D7D,
	U3Clvl0U3Ed__60_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mABE72F3BB290F9274B298E22045957414E685854,
	U3Clvl0U3Ed__60_System_Collections_IEnumerator_Reset_m2249E8A74EB47EAF45BB4E180FE7D0C7C0B14C2B,
	U3Clvl0U3Ed__60_System_Collections_IEnumerator_get_Current_mA7B9E52589C82967F50793D3AEC50D52CB0083F6,
	mainmenu_Start_mE676AB13A0BBE134B67DC212E75F4D7B6C55D678,
	mainmenu_play_m7261CCDD25A56890F76B532315B1889755B58F20,
	mainmenu_exit_mD7D5A28C0EE46577051DB25C80D10FD4802A8479,
	mainmenu_No_m3A8510663F463FE0DC5C7DE835AF4E3EE7E4AF3F,
	mainmenu_modeback_m7002FED76085C29B785538E29DA8AD8BB76BB4EF,
	mainmenu_lvlback_mB0E00D5A783A07604440B8FB0500FA3E09A35FBD,
	mainmenu_lvl2back_m668ADEE90CCD11A67274B0E2BBDDB5407D23AAC2,
	mainmenu_setting_m572FE3087ACEB2E6D6F2FB0E0D9D6418E5B4C9BB,
	mainmenu_save_m7D0B44C4C1E1A924820E35923D12870FA1F8FC5E,
	mainmenu_yes_m715C547333E6162C1C52FBEFB5A1B213C6234EE1,
	mainmenu_Levels_mDB425FDD7B190B76D615A020C877D1F1E978DC48,
	mainmenu_fill_m285CFDB4C93A4A18D19FFE6305AA1B401FFD4FBE,
	mainmenu_Lvel_m2FD1596CDC7B38677C373C65212FD2BD9586E58A,
	mainmenu_select_m6E89A2254BD17C4EDD9C026A50F53CC4A6D4B8FF,
	mainmenu_caremode_m135D95DECE48DC9F3666F65D9E41EE6D4DF26FE6,
	mainmenu_mode2_m64AB7149BF76459A20BDFDD6431F125A8DA72EB4,
	mainmenu_filldec_m55EC326D8E4115A86C72801D689559E86BFE28A9,
	mainmenu_fl_mED7072A7D60B781BC5F75F08641DB599D10AC2F4,
	mainmenu_fldec_m1A4227DBAEBC5CF2945B214D85284B011282694C,
	mainmenu_Steer_mA89A558C1066237511EEDE85A6D34F20C9E1298B,
	mainmenu_Btns_m5A7B2B135B70C6BCC807209A18D1F291C63F10A5,
	mainmenu_Tilt_mE9BD04004DC3F3D77ACC39E2B41C3AE9EDDB0CC5,
	mainmenu_loading_m974C5533A63E1BCCD69F13414389E7A4894AA90B,
	mainmenu_rateus_mC2251D455AF135BB4D11D814B8596CC4B0DA6802,
	mainmenu_privacy_m3CAABF71417F44F35A535F2043ECCAA2CBEE3ECF,
	mainmenu_moregams_m6B4ABE148E83FB0A54F4B2D892EC1F9104496122,
	mainmenu_mudjeep_mC8E43510C671AA913649757244DEBD2221278B47,
	mainmenu_trainadd_mA51D5360BC8EE6FFD835DA9B57BC2F1E5F187897,
	mainmenu_loding_m3F53A9567827B070999CFDD722D504533DF87C9E,
	mainmenu_load_m2C09862F74DAA82291C77B68A06CABE82B9CCA8C,
	mainmenu_load1_mFBDBDF9E120DF76AAA9786952432BCA6A89B0C66,
	mainmenu_lod2_m3C32CA7424199D1DF6BCAC484E1A712B1BE51FA8,
	mainmenu__ctor_m8C25CE093C895EB2C3F69525880DBCA3188614F8,
	U3CloadingU3Ed__39__ctor_m6E3FC03533E32165DBA82BC30E8E728E245D1DB0,
	U3CloadingU3Ed__39_System_IDisposable_Dispose_m340482F2B6E36928BC7A77E7843026EDE08DEBD1,
	U3CloadingU3Ed__39_MoveNext_m3C0F230456D638E46F2954EE29BE24040C09C4FD,
	U3CloadingU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D74B40166DEA5F08B1626BE899B7BD3E4EC784B,
	U3CloadingU3Ed__39_System_Collections_IEnumerator_Reset_m7EC41A340C0613CDB99CD1EE5E5A95E952AD47EE,
	U3CloadingU3Ed__39_System_Collections_IEnumerator_get_Current_m4D01BCCC9E254743B500D32E295079258EFC34BA,
	U3ClodingU3Ed__45__ctor_mA348507D99FF90AEB48918426A1CA4F39937C41A,
	U3ClodingU3Ed__45_System_IDisposable_Dispose_mBF0A271F3AB8B910EC9D49AE9D2110162FBAE4A4,
	U3ClodingU3Ed__45_MoveNext_mD0A1FB99261C69ADD2D7B430846AC751431D9A7B,
	U3ClodingU3Ed__45_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCBD6CD24E193E34EBC9C494309CF7AECBB8074CF,
	U3ClodingU3Ed__45_System_Collections_IEnumerator_Reset_m8757C7459472A549127A813CB0C53400F5957B64,
	U3ClodingU3Ed__45_System_Collections_IEnumerator_get_Current_m85E0209029F5CEAD7E6370A74C02ABB2911F0349,
	U3CloadU3Ed__46__ctor_m3F8833D5E42271B150929700613389E90A753EA9,
	U3CloadU3Ed__46_System_IDisposable_Dispose_m64A9A989D0F422E2A10DBA8312AB0A4D55C9C566,
	U3CloadU3Ed__46_MoveNext_mF926F8EF42D729ACF1543D67BFEF38D38E58F9CA,
	U3CloadU3Ed__46_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF7D814B5290AFD4675C4A1ACA51D91854381876C,
	U3CloadU3Ed__46_System_Collections_IEnumerator_Reset_m6F7822F745718F8D484D30E140AB8537A53468E9,
	U3CloadU3Ed__46_System_Collections_IEnumerator_get_Current_m7DE394C9A1824F61F51EA0DC310A4BD59494C796,
	U3Cload1U3Ed__47__ctor_mEBA0E592445B2C566D58411EB5D4C82078FBBEF6,
	U3Cload1U3Ed__47_System_IDisposable_Dispose_m4BEB4DB183B0F6F71E186A3C18BB359254514962,
	U3Cload1U3Ed__47_MoveNext_m560459958FF03D72222F04AA9AB198DFFB6F933D,
	U3Cload1U3Ed__47_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCF06F55E47D6EB22E57CD961BC3C6DF6768AFAAF,
	U3Cload1U3Ed__47_System_Collections_IEnumerator_Reset_m08878A97DB160EE3BC983A626AB66D00CBCCAD3A,
	U3Cload1U3Ed__47_System_Collections_IEnumerator_get_Current_m7F6A1C3904D02C53B01621FC9D3AFFB6E9960748,
	U3Clod2U3Ed__48__ctor_mD1723DAC9616E26A433A7E11CEB236671FB5B476,
	U3Clod2U3Ed__48_System_IDisposable_Dispose_m8BA719AA6E68CA80163FDC5EB3844D066E72244A,
	U3Clod2U3Ed__48_MoveNext_m0388E3AD51F9A3043553B6B24E2C129FE881367E,
	U3Clod2U3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m32C1CBC447D7A1F0933C26FB4009744C8B998E23,
	U3Clod2U3Ed__48_System_Collections_IEnumerator_Reset_m6119113503F3F7DD48CD9298A57E31C5CC2DD099,
	U3Clod2U3Ed__48_System_Collections_IEnumerator_get_Current_m31F6464795D1660A336D499BC584E47E8E211BAF,
	manger2_Start_mA7E429CD078EC38ED7CC1B24CB70DB95B9E51BD8,
	manger2_pause_m6897565431D5D01FECC8BFFC49BB880DA63EFF8C,
	manger2_restart_mA279523795E7961FB46F0B144D1203368BEA6D57,
	manger2_next_m553AFC823E40268EE9F208868D72699772BADDCF,
	manger2_resume_m63B5C1A2462113DDA759B35A945D70701351B8EB,
	manger2_home_m9EB1CDFA7D913F7B904848029CCC755AC1F5C2B3,
	manger2_start_mF7905CD41FC4F9C5B6E99A3B72144A1C09860052,
	manger2_obect_mE9A71697220BFB6C64C7B84A184C7EC578E071EA,
	manger2_strt_m039C3A25F67681C51A80999F2376E17ABDFDD1E6,
	manger2__ctor_mB6107261188F36DEE5786649F269CFC3AE4AE571,
	U3CstartU3Ed__24__ctor_m5DEE7DDB19BF271D5A33A477561A6E315342189A,
	U3CstartU3Ed__24_System_IDisposable_Dispose_mFACAD12B5AD1C8EEC0441BA4AA524B261C8FDB77,
	U3CstartU3Ed__24_MoveNext_mF77A2C7AB364092B7C10877F4031D667E7A8D62B,
	U3CstartU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD8FC10AF493EFB7391EEB109EE8D9028C3D552A8,
	U3CstartU3Ed__24_System_Collections_IEnumerator_Reset_mF6780D537B9EBF1E49349F59FC3D0B8ED362428A,
	U3CstartU3Ed__24_System_Collections_IEnumerator_get_Current_m4C1BA7A30A06294A299D337756AE0AF96A99572C,
	U3CobectU3Ed__25__ctor_mE5DC237276F154F4CC97B951EBEBD42410A4B0DE,
	U3CobectU3Ed__25_System_IDisposable_Dispose_m42743905108FA175D898410D377F0696B456B0E9,
	U3CobectU3Ed__25_MoveNext_mABB5C255960734E6AF355288587AB7083CD65D26,
	U3CobectU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m92463448801F7F98AEFE06D2D1F5F6E3B148BA22,
	U3CobectU3Ed__25_System_Collections_IEnumerator_Reset_mC27CAB5EED358B0E5F7AE37AB0E4BB481C66F505,
	U3CobectU3Ed__25_System_Collections_IEnumerator_get_Current_mBA65AE75917D2F878E8A835886C7A4D6DFDB4AB2,
	U3CstrtU3Ed__26__ctor_mCCDCD27B5FD203C4A6FAD5DFBBD5E7452B315620,
	U3CstrtU3Ed__26_System_IDisposable_Dispose_m6C707852E0BE32F4725F21A6042848C63FA3DD0B,
	U3CstrtU3Ed__26_MoveNext_m920B871C44159CD4CE95D3910129215DBDEA3207,
	U3CstrtU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1E7F837CAD35AC52B569095A33494BA6747F2B0F,
	U3CstrtU3Ed__26_System_Collections_IEnumerator_Reset_mFAD7C77823E3FE573B35E48CA2AE175F79A346BE,
	U3CstrtU3Ed__26_System_Collections_IEnumerator_get_Current_mCB29ECBEA6C4DCFD31BB26971E2E6993390CC739,
	mode2lock_OnEnable_m7226A39AB1C43B5DB6F2CCF0128975EB3A4B4BDF,
	mode2lock__ctor_m2F01AB4CFC71FE8C30C9B9EDA16CE926DAC7AE68,
	player_Start_m70A61B77EB8E3359FE4CBB98768C3D17ECD8F65A,
	player_OnTriggerEnter_mE6E5E2468AD3FE9BECD21DE4D1E3BAB255FA2651,
	player_end_mE3A73A581F4D2AE9D208F1B10F6C62E4AB189E31,
	player_end1_m570774867FE7BA7D3DBCEB2E4E75AA766332DE00,
	player_lvl6_m33375427C70655644B18D709087A612DC8FA5A5E,
	player__ctor_m714EC555B4D536FDD17E68FB029561E4AC0124FB,
	U3CendU3Ed__17__ctor_m54D9016ECDB432ED712D6F06007B82FCF226CAFC,
	U3CendU3Ed__17_System_IDisposable_Dispose_m87B1BAD34325D7644464870E5F275AD5AB6F24DB,
	U3CendU3Ed__17_MoveNext_m57B9FBBD0FB00A9819DCDCAA4851339BC378D09F,
	U3CendU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A3DBD6F5B7F6E46C4FB207F9540541464F2B53A,
	U3CendU3Ed__17_System_Collections_IEnumerator_Reset_mAC65AD5E5A699707CA188140BA46B67B990C35D0,
	U3CendU3Ed__17_System_Collections_IEnumerator_get_Current_mFCB8DA308C1E2FB13319B250288E37A9625CDFCB,
	U3Cend1U3Ed__18__ctor_mCA0B5DC2E2851ABDD7A79BD4D598C1257DCD25BE,
	U3Cend1U3Ed__18_System_IDisposable_Dispose_m0CA518025250D42F03A0892B90BC9ED04D6292B5,
	U3Cend1U3Ed__18_MoveNext_m14E110FA84127AE0C30158C40134C50A0D744B56,
	U3Cend1U3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7A720503E941B74F8E5D33A81BD6697774CE4B8C,
	U3Cend1U3Ed__18_System_Collections_IEnumerator_Reset_m07DA0736EF0D66CAB0F90F42C5A5A49835DE169E,
	U3Cend1U3Ed__18_System_Collections_IEnumerator_get_Current_m5DE3F0165B0786FA5F6FA1581D13F939182EBF8E,
	U3Clvl6U3Ed__19__ctor_mDB18D4251B4F1069B69F9B95D57511CEDB1ECBB4,
	U3Clvl6U3Ed__19_System_IDisposable_Dispose_m4E1D48EA935AB6220B5C7EB5417B3E828BF7C6C3,
	U3Clvl6U3Ed__19_MoveNext_mA8D597A94CA776EA50A2E13C1E930684D4D0ADAC,
	U3Clvl6U3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4056B2F8F96C4274CABDD0D14C45F05481003ABB,
	U3Clvl6U3Ed__19_System_Collections_IEnumerator_Reset_mC12E146F0D6D1CB27AFB7C7BA03DA91F10D7E4E2,
	U3Clvl6U3Ed__19_System_Collections_IEnumerator_get_Current_mA46297F7242F0AF07D15F6FC61D55D8DEA4164D1,
	startscene_Start_mCA3EB25F76D13565AC3A7D6B2E26B462719289B5,
	startscene_accept_m626A215293B8F4070CE0FDADE63092EB0FB36789,
	startscene_load_mD919890EC64F9A4913422ED0A59861A6DE185C3B,
	startscene_privacy_m5AFB9F9821B6177644A6F164EEEB106DC930B941,
	startscene__ctor_m24A2FECEF25E362C49F3279DD9D8E2FBF9723A30,
	U3CloadU3Ed__4__ctor_mDDEB6A99F7CD51746E3F63C14596586EBE32CD8C,
	U3CloadU3Ed__4_System_IDisposable_Dispose_mF0978BF8E970E01288786C36C522FFF679326B42,
	U3CloadU3Ed__4_MoveNext_mE59C466EC3D71E544802AD4A83729A4DADAAF415,
	U3CloadU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A7B178E9FC4BF7AA0E8E01F4149B530309C422C,
	U3CloadU3Ed__4_System_Collections_IEnumerator_Reset_m19068CB4D993EBF7D6154E6C7C183B0F09053E90,
	U3CloadU3Ed__4_System_Collections_IEnumerator_get_Current_mB92520266F090E771D54FE565B244BE04ABEED44,
	wheelrotator_Update_m6D7476FD86D382BD126CDEA349C82D95E4C915CD,
	wheelrotator__ctor_m8B6A002D79228576E25ACCA4DA05EA38C77A9DAF,
	PowerUpAnimation_Awake_m86D57123EB9867C7249AC5CE3A89585892BE85C9,
	PowerUpAnimation_Update_m23F1732F1AEDA9DDD264B102F4D2E318487E4B8A,
	PowerUpAnimation_GetAnimateScale_mFE8415868C3D80ACAB2EB043467573C881FFC372,
	PowerUpAnimation_SetAnimateScale_m5D976F9D34363C37DB653092F840250809E88630,
	PowerUpAnimation_GetAnimateYOffset_mAA21F55DF9EA0B02E33FF0DE3CA63C87429949D4,
	PowerUpAnimation_SetAnimateYOffset_mD5CCE23D925997D7F1427197978668B3B7B18ED9,
	PowerUpAnimation_GetAnimateRotation_m950258CAAF23083363550F10DC8F9E308793C8EA,
	PowerUpAnimation_SetAnimateRotation_m0A68C8EA0D46F36B260499A6CD16EDF37CFA781B,
	PowerUpAnimation__ctor_m295920A8847ADB76E2B834ED030883A90EA85937,
	SciFiButtonScript_Start_m7BA1597132823AD8A18BDB4C9D5C5D9B3B350F70,
	SciFiButtonScript_Update_m53F0945741785A523374899C9E286A9C70F8CED7,
	SciFiButtonScript_getProjectileNames_m4914663374B7C9443259B666295471DB67C0CACD,
	SciFiButtonScript_overButton_m158214D06C2514E4D613CED37594D9E2EBBDE7BE,
	SciFiButtonScript__ctor_m369266C4350421B8708F7B1D197CC5C4E32A5B32,
	SciFiDragMouseOrbit_Start_m326700180FEF0036398C20C052116C41F48467CA,
	SciFiDragMouseOrbit_LateUpdate_mBE29061BC33758FEA1402C7577CAA7035313CDDC,
	SciFiDragMouseOrbit_ClampAngle_m884C9992200886970132850907155B00FCB28AB6,
	SciFiDragMouseOrbit__ctor_m580265C87EA4D5FEBC0D80754ADC4F0412AD441A,
	SciFiFireProjectile_Start_m95FDFFE85F8E65A19BF7B6E2AEC5E53F2430F055,
	SciFiFireProjectile_Update_m011F0B463F3429430E8E0DE950B68B9F159F419B,
	SciFiFireProjectile_nextEffect_m8A4EAB2B4D050A395F7737AC17106E726598E663,
	SciFiFireProjectile_previousEffect_m973612AA69ED7DFC06025E60F10D6B787E50F3D9,
	SciFiFireProjectile_AdjustSpeed_m8C9AE252A0F76ADC1E6B05626A4A03D6E70B42A4,
	SciFiFireProjectile__ctor_m26B5E81EBCAB2622A88E4E8D323370DFF8AEBC8E,
	SciFiLoadSceneOnClick_LoadSceneSciFiProjectiles_m93914A6C5CB67EFEDD76764A89F7D0A19358E1D5,
	SciFiLoadSceneOnClick_LoadSceneSciFiBeamup_mF9EC8CFEECEC54A3CB59272F00DB927ADE377B1A,
	SciFiLoadSceneOnClick_LoadSceneSciFiBuff_m6066299E70EDBAB24CF864CBFCB0B5C3AA0BBCB2,
	SciFiLoadSceneOnClick_LoadSceneSciFiFlamethrowers2_mD938FB543EF0091AA597F3C516D2142AD7D8DDCC,
	SciFiLoadSceneOnClick_LoadSceneSciFiQuestZone_mC52A779F264A962164A2864FCCF12C86567501B2,
	SciFiLoadSceneOnClick_LoadSceneSciFiLightjump_m8010FC2BF0382EB66EC486E98C7CCD0F83B05EC1,
	SciFiLoadSceneOnClick_LoadSceneSciFiLoot_m79A1122E96AD3A489F0AB9576B260A118F324CEE,
	SciFiLoadSceneOnClick_LoadSceneSciFiBeams_m4169243C2F1220243D521E431667E53CDE8D2EDA,
	SciFiLoadSceneOnClick_LoadSceneSciFiPortals_m1B7AAEC563EFAA4CF80A7411DBA20DDF379DFBA4,
	SciFiLoadSceneOnClick_LoadSceneSciFiRegenerate_mAE8BF2283EE990583162F1C3531A8AE51FD5751B,
	SciFiLoadSceneOnClick_LoadSceneSciFiShields_m72B4327170992D9B693C60813ABF732576456F04,
	SciFiLoadSceneOnClick_LoadSceneSciFiSwirlyAura_mE34C3E63675B797F09EFB2D49A78D189E0876E02,
	SciFiLoadSceneOnClick_LoadSceneSciFiWarpgates_m1C088F622AEF31198B2AE306EC37CCE4AA841FEB,
	SciFiLoadSceneOnClick_LoadSceneSciFiJetflame_mB7D76379A24F94EE6D8518EA8C500BBB6A9A367D,
	SciFiLoadSceneOnClick_LoadSceneSciFiUltimateNova_mE28A7B4BF62E38E5139985522F1D7BB16B5B2686,
	SciFiLoadSceneOnClick_LoadSceneSciFiFire_m85822917432EA9CFEEB4E33FE6CA1BAD849055EA,
	SciFiLoadSceneOnClick__ctor_m8381A44903E961A976698EB33A2FAF3D85B08096,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate1_mBFEEDE488C9EBB50977A36E7866F7878B438258E,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate2_mE55DC601D3322B3FDE12D51C11474CF58F0AAB2D,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate3_m515D307D0B1A253CEB83C9712E209F5812A8F8A1,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate4_m06A36AA374DDA831648253325ADF3335119C4B4E,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate5_mF47CC38EE3F604BD25DF66E9C02CCE978393C811,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate6_m6D1312659D7FD87DE136C8B210749F08855DF150,
	SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate7_m71F94D4395FA3509F6B4F5EEC014F567EB22DA8C,
	SciFiLoadSceneOnClick2__ctor_mE63C013AF5B353C5F91141B7B584BBBCC9EB5052,
	SciFiLoopScript_Start_m92BD74B68970E1645DB506039D732588644F3083,
	SciFiLoopScript_PlayEffect_mADC143884E30129DD40FF80FF9363432B5A582A1,
	SciFiLoopScript_EffectLoop_m73BC3A0884B7A62594CA5F184D431DB81E2A6ED7,
	SciFiLoopScript__ctor_m7839608A2E7C9BC279192801647BA4FD010F3423,
	U3CEffectLoopU3Ed__4__ctor_m9FDDAD0DE8BB6450657E8E7B7B83BC85838F6B6C,
	U3CEffectLoopU3Ed__4_System_IDisposable_Dispose_m4B490EBA83EF4F6E7753CA05239EFB051DFDBCA1,
	U3CEffectLoopU3Ed__4_MoveNext_m8E017D4C966F7F2F30AEC01CD91481C62A77F37A,
	U3CEffectLoopU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5C6B23587D0C866DFF63356BDAEEB61CA603BB3B,
	U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m992C579A5BA9707614B0E1C087C94BED47BC8C11,
	U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_get_Current_m79C803F68611B6834219B1F249C098DAF2E52BC7,
	SciFiProjectileScript_Start_m51A2AB6645CD8812DBA218816593DE0A31F8DEB4,
	SciFiProjectileScript_OnCollisionEnter_mBB251D2DE353830B3A498EEDB87707697151D113,
	SciFiProjectileScript__ctor_mBC875DA805361CFAFE69143920225E7D5E8991A1,
	SciFiLightFade_Start_mCE8FCD03FA6B948DAE410C5E2DDAD4507D5EACE2,
	SciFiLightFade_Update_m3CFC23B76DCCAD432CE8A6E3BCFDBD6566CE828C,
	SciFiLightFade__ctor_m742F8297FDBDD4BA15CDDFE01C1C89B0728EF1F6,
	SciFiRotation_Start_m0A8F763B7C4609CA6EC4FD58115808655954836B,
	SciFiRotation_Update_mF8A782B9AA713AC15A8CB7E64B09802BB9FED2F5,
	SciFiRotation__ctor_m4C33B40B7C32D1DDFF206AA954984946EC3FF2C3,
};
static const int32_t s_InvokerIndices[1338] = 
{
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	968,
	1935,
	1935,
	1935,
	1921,
	1935,
	1935,
	1935,
	1935,
	1571,
	1935,
	1935,
	1900,
	489,
	489,
	925,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	1866,
	1866,
	1866,
	1866,
	1866,
	1935,
	1610,
	1866,
	1935,
	1866,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1610,
	1866,
	1935,
	1935,
	1866,
	1935,
	1610,
	934,
	934,
	934,
	934,
	934,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	934,
	1935,
	1935,
	934,
	1935,
	1935,
	1610,
	934,
	1935,
	1935,
	1935,
	1935,
	1610,
	3231,
	1935,
	1610,
	1610,
	1935,
	1935,
	1610,
	1935,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1597,
	1597,
	1597,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1866,
	1571,
	1866,
	1571,
	1866,
	1571,
	1866,
	1571,
	1866,
	1571,
	1866,
	1571,
	1900,
	1610,
	1935,
	1935,
	1571,
	1900,
	1571,
	1571,
	1610,
	1610,
	1935,
	1935,
	1182,
	1935,
	1182,
	3231,
	1935,
	1182,
	2052,
	3189,
	2908,
	2584,
	3231,
	2908,
	2908,
	3185,
	3231,
	3231,
	3231,
	3231,
	3189,
	3185,
	3231,
	3185,
	3185,
	3231,
	1935,
	1935,
	1935,
	1935,
	3189,
	3189,
	3189,
	3189,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1610,
	1431,
	1935,
	1935,
	933,
	1610,
	490,
	1610,
	933,
	1610,
	490,
	1610,
	1935,
	1935,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1571,
	1571,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	3189,
	3189,
	1935,
	1935,
	934,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1610,
	1935,
	1571,
	1641,
	1218,
	1900,
	1431,
	748,
	490,
	1935,
	1935,
	933,
	1610,
	490,
	1610,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	2526,
	1610,
	1935,
	1900,
	1866,
	1571,
	1866,
	1866,
	1866,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1921,
	1628,
	1921,
	1628,
	1921,
	1628,
	1921,
	1921,
	1921,
	1628,
	1900,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1571,
	1900,
	1935,
	1935,
	1935,
	110,
	1641,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1628,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1427,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1571,
	1935,
	1935,
	1571,
	1571,
	1935,
	1935,
	586,
	1935,
	1935,
	933,
	1610,
	490,
	1610,
	933,
	1610,
	490,
	1610,
	933,
	934,
	301,
	1610,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	3215,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1900,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1972,
	2612,
	2917,
	1935,
	2908,
	3189,
	2908,
	2590,
	2909,
	2908,
	2917,
	2917,
	2915,
	2917,
	2917,
	2917,
	2917,
	2917,
	2912,
	2917,
	2917,
	2908,
	2908,
	2908,
	2908,
	2908,
	2908,
	2917,
	2917,
	2917,
	2917,
	2917,
	2917,
	2917,
	3189,
	2908,
	2908,
	2908,
	2908,
	2917,
	3181,
	3189,
	3189,
	2915,
	2946,
	1935,
	3215,
	1935,
	1935,
	1610,
	1935,
	1571,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1628,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	1935,
	1935,
	763,
	1935,
	1935,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1628,
	1935,
	1935,
	1628,
	1935,
	1935,
	1628,
	1935,
	1935,
	1628,
	1935,
	1935,
	1571,
	1935,
	1597,
	1935,
	1597,
	1935,
	1597,
	1597,
	1935,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1610,
	1935,
	3168,
	3106,
	1935,
	3215,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	3215,
	1935,
	1935,
	1610,
	749,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1610,
	1935,
	1900,
	1900,
	1935,
	1935,
	1935,
	1628,
	588,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1459,
	1935,
	1935,
	1610,
	1610,
	1935,
	2669,
	2946,
	2669,
	2762,
	3032,
	2559,
	2913,
	2677,
	3159,
	2864,
	2678,
	3168,
	2874,
	2674,
	3079,
	2813,
	2670,
	2959,
	2704,
	2673,
	3069,
	2481,
	2673,
	3069,
	2493,
	2673,
	2673,
	2673,
	2673,
	2673,
	2673,
	-1,
	2604,
	2604,
	2604,
	2604,
	2604,
	2604,
	3069,
	2488,
	3069,
	2499,
	3069,
	2503,
	3069,
	2505,
	3069,
	2498,
	3069,
	2483,
	-1,
	2915,
	2915,
	2915,
	2915,
	2915,
	2915,
	3189,
	3231,
	2673,
	2922,
	3106,
	2902,
	3017,
	3189,
	3189,
	1935,
	1935,
	1935,
	1571,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1900,
	1900,
	1900,
	1935,
	1935,
	380,
	3,
	966,
	968,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	3215,
	1935,
	3215,
	3189,
	3189,
	3189,
	3189,
	3189,
	3189,
	1935,
	1610,
	1610,
	1610,
	1610,
	1610,
	1935,
	1610,
	925,
	557,
	1935,
	1935,
	3185,
	3185,
	1935,
	1935,
	1935,
	933,
	1935,
	748,
	1610,
	933,
	1935,
	748,
	1610,
	933,
	1935,
	748,
	1610,
	3215,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	263,
	1935,
	1935,
	1935,
	1935,
	123,
	1935,
	1935,
	1935,
	1935,
	1935,
	1921,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1610,
	1935,
	1628,
	1935,
	1610,
	1610,
	1610,
	1935,
	1900,
	1921,
	1921,
	1935,
	1610,
	1610,
	1571,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1628,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1935,
	1921,
	1921,
	1935,
	1610,
	1610,
	1610,
	1935,
	1935,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1921,
	1866,
	1935,
	1935,
	1935,
	1610,
	1610,
	1610,
	1935,
	1935,
	3215,
	1935,
	1935,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1900,
	1900,
	1900,
	1900,
	1900,
	1921,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1866,
	1628,
	1935,
	1628,
	1866,
	1935,
	1497,
	1886,
	169,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	3215,
	1935,
	1935,
	1610,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1610,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1628,
	1935,
	3215,
	1935,
	1628,
	1935,
	1610,
	1935,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1866,
	1935,
	1866,
	1571,
	1935,
	1935,
	1628,
	1497,
	581,
	1935,
	1935,
	1935,
	1610,
	965,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	3231,
	1935,
	1610,
	1935,
	1610,
	1935,
	934,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1900,
	1900,
	1900,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1597,
	1935,
	1597,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1900,
	1900,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1900,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1610,
	1900,
	1900,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1900,
	1935,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1866,
	1571,
	1866,
	1571,
	1866,
	1571,
	1935,
	1935,
	1935,
	1935,
	1866,
	1935,
	1935,
	1935,
	2526,
	1935,
	1935,
	1935,
	1935,
	1935,
	1628,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1610,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
	1935,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x060002D6, { 0, 2 } },
	{ 0x060002E9, { 2, 1 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[3] = 
{
	{ (Il2CppRGCTXDataType)2, 212 },
	{ (Il2CppRGCTXDataType)3, 517 },
	{ (Il2CppRGCTXDataType)3, 472 },
};
extern const CustomAttributesCacheGenerator g_AssemblyU2DCSharp_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	1338,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	3,
	s_rgctxValues,
	NULL,
	g_AssemblyU2DCSharp_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
