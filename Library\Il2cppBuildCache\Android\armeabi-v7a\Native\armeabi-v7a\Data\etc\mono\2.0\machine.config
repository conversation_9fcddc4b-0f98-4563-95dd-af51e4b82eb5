<?xml version="1.0" encoding="utf-8"?>

<configuration>

	<configSections>
		<section name="configProtectedData" type="System.Configuration.ProtectedConfigurationSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		<section name="appSettings" type="System.Configuration.AppSettingsSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		<section name="connectionStrings" type="System.Configuration.ConnectionStringsSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		<section name="mscorlib" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false"/>
		<section name="runtime" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false"/>
		<section name="assemblyBinding"  type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
		<section name="satelliteassemblies" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
		<section name="startup" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false"/>
		<section name="system.codedom" type="System.CodeDom.Compiler.CodeDomConfigurationHandler, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="system.data" type="System.Data.Common.DbProviderFactoriesConfigurationHandler, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="system.diagnostics" type="System.Diagnostics.DiagnosticsConfigurationHandler, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="system.runtime.remoting" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowLocation="false"/>
		<section name="system.windows.forms" type="System.Windows.Forms.WindowsFormsSection, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="windows" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
		<section name="strongNames" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowLocation="false"/>
		<sectionGroup name="system.web" type="System.Web.Configuration.SystemWebSectionGroup, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
			<section name="anonymousIdentification" type="System.Web.Configuration.AnonymousIdentificationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="authentication" type="System.Web.Configuration.AuthenticationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="authorization" type="System.Web.Configuration.AuthorizationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="browserCaps" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="clientTarget" type="System.Web.Configuration.ClientTargetSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="compilation" type="System.Web.Configuration.CompilationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="customErrors" type="System.Web.Configuration.CustomErrorsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="deployment" type="System.Web.Configuration.DeploymentSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineOnly" />
			<section name="globalization" type="System.Web.Configuration.GlobalizationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="healthMonitoring" type="System.Web.Configuration.HealthMonitoringSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="hostingEnvironment" type="System.Web.Configuration.HostingEnvironmentSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
			<section name="httpCookies" type="System.Web.Configuration.HttpCookiesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="httpHandlers" type="System.Web.Configuration.HttpHandlersSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="httpModules" type="System.Web.Configuration.HttpModulesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="httpRuntime" type="System.Web.Configuration.HttpRuntimeSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="identity" type="System.Web.Configuration.IdentitySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="machineKey" type="System.Web.Configuration.MachineKeySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="membership" type="System.Web.Configuration.MembershipSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="mobileControls" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                        <section name="deviceFilters" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="pages" type="System.Web.Configuration.PagesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="processModel" type="System.Web.Configuration.ProcessModelSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineOnly" allowLocation="false" />
			<section name="profile" type="System.Web.Configuration.ProfileSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="roleManager" type="System.Web.Configuration.RoleManagerSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="securityPolicy" type="System.Web.Configuration.SecurityPolicySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
			<section name="sessionPageState" type="System.Web.Configuration.SessionPageStateSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="sessionState" type="System.Web.Configuration.SessionStateSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="siteMap" type="System.Web.Configuration.SiteMapSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="trace" type="System.Web.Configuration.TraceSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="trust" type="System.Web.Configuration.TrustSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
			<section name="urlMappings" type="System.Web.Configuration.UrlMappingsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			<section name="webControls" type="System.Web.Configuration.WebControlsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="webParts" type="System.Web.Configuration.WebPartsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="webServices" type="System.Web.Services.Configuration.WebServicesSection, System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="xhtmlConformance" type="System.Web.Configuration.XhtmlConformanceSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<sectionGroup name="caching" type="System.Web.Configuration.SystemWebCachingSectionGroup, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
				<section name="cache" type="System.Web.Configuration.CacheSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
				<section name="outputCache" type="System.Web.Configuration.OutputCacheSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
				<section name="outputCacheSettings" type="System.Web.Configuration.OutputCacheSettingsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
				<section name="sqlCacheDependency" type="System.Web.Configuration.OutputCacheSettingsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication"/>
			</sectionGroup>
			<section name="monoSettings" type="System.Web.Configuration.MonoSettingsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		</sectionGroup>
		<sectionGroup name="system.net" type="System.Net.Configuration.NetSectionGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="authenticationModules" type="System.Net.Configuration.AuthenticationModulesSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="connectionManagement" type="System.Net.Configuration.ConnectionManagementSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="defaultProxy" type="System.Net.Configuration.DefaultProxySection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<sectionGroup name="mailSettings" type="System.Net.Configuration.MailSettingsSectionGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
				<section name="smtp" type="System.Net.Configuration.SmtpSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			</sectionGroup>
			<section name="requestCaching" type="System.Net.Configuration.RequestCachingSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="settings" type="System.Net.Configuration.SettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="webRequestModules" type="System.Net.Configuration.WebRequestModulesSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</sectionGroup>
		<section name="system.drawing" type="System.Configuration.NameValueSectionHandler, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<sectionGroup name="system.serviceModel" type="System.ServiceModel.Configuration.ServiceModelSectionGroup, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="behaviors" type="System.ServiceModel.Configuration.BehaviorsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="bindings" type="System.ServiceModel.Configuration.BindingsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="client" type="System.ServiceModel.Configuration.ClientSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="commonBehaviors" type="System.ServiceModel.Configuration.CommonBehaviorsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="diagnostics" type="System.ServiceModel.Configuration.DiagnosticSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="extensions" type="System.ServiceModel.Configuration.ExtensionsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="serviceHostingEnvironment" type="System.ServiceModel.Configuration.ServiceHostingEnvironmentSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="services" type="System.ServiceModel.Configuration.ServicesSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</sectionGroup>
		<sectionGroup name="system.transactions" type="System.Transactions.Configuration.TransactionsSectionGroup, System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, Custom=null">
			<section name="defaultSettings" type="System.Transactions.Configuration.DefaultSettingsSection, System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, Custom=null"/>
			<section name="machineSettings" type="System.Transactions.Configuration.MachineSettingsSection, System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, Custom=null" allowDefinition="MachineOnly" allowExeDefinition="MachineOnly"/>
		</sectionGroup>
		<section name="system.webServer" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		<section name="uri" type="System.Configuration.UriSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
	</configSections>

	<connectionStrings>
		<add name="LocalSqlServer" connectionString="data source=.\SQLEXPRESS;Integrated Security=SSPI;AttachDBFilename=|DataDirectory|aspnetdb.mdf;User Instance=true" providerName="System.Data.SqlClient"/>
		<add name="LocalSqliteServer" connectionString="Data Source=|DataDirectory|/aspnetdb.sqlite;version=3" providerName="Mono.Data.Sqlite"/>
	</connectionStrings>
	
	<configProtectedData defaultProvider="RsaProtectedConfigurationProvider">
		<providers>
			<add name="RsaProtectedConfigurationProvider" type="System.Configuration.RsaProtectedConfigurationProvider, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                             description="" keyContainerName="MonoFrameworkConfigurationKey" cspProviderName="" useMachineContainer="true" useOAEP="false" />
			<add name="DataProtectionConfigurationProvider" type="System.Configuration.DpapiProtectedConfigurationProvider, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                             description="" useMachineProtection="true" keyEntropy="" />
		</providers>
	</configProtectedData>

	<system.net>
		<authenticationModules>
			<add type="System.Net.BasicClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add type="System.Net.DigestClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add type="System.Net.NtlmClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</authenticationModules>
		<webRequestModules>
			<add prefix="http" type="System.Net.HttpRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add prefix="https" type="System.Net.HttpRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add prefix="file" type="System.Net.FileWebRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add prefix="ftp" type="System.Net.FtpRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</webRequestModules>
		<settings>
			<ipv6 enabled="true"/>
		</settings>
	</system.net>
	
	<system.runtime.remoting>
		<application>
			<channels>
				<channel ref="http client" displayName="http client (delay loaded)" delayLoadAsClientChannel="true" />
				<channel ref="tcp client" displayName="tcp client (delay loaded)" delayLoadAsClientChannel="true" />
				<channel ref="ipc client" displayName="ipc client (delay loaded)" delayLoadAsClientChannel="true" />
			</channels>
		</application>
		<channels>
			<channel id="http" type="System.Runtime.Remoting.Channels.Http.HttpChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="http client" type="System.Runtime.Remoting.Channels.Http.HttpClientChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="http server" type="System.Runtime.Remoting.Channels.Http.HttpServerChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="tcp" type="System.Runtime.Remoting.Channels.Tcp.TcpChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="tcp client" type="System.Runtime.Remoting.Channels.Tcp.TcpClientChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="tcp server" type="System.Runtime.Remoting.Channels.Tcp.TcpServerChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="ipc" type="System.Runtime.Remoting.Channels.Ipc.IpcChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="ipc client" type="System.Runtime.Remoting.Channels.Ipc.IpcClientChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="ipc server" type="System.Runtime.Remoting.Channels.Ipc.IpcServerChannel, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</channels>
			<channelSinkProviders>
				<clientProviders>
					<formatter id="soap" type="System.Runtime.Remoting.Channels.SoapClientFormatterSinkProvider, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
					<formatter id="binary" type="System.Runtime.Remoting.Channels.BinaryClientFormatterSinkProvider, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				</clientProviders>
				<serverProviders>
					<formatter id="soap" type="System.Runtime.Remoting.Channels.SoapServerFormatterSinkProvider, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
					<formatter id="binary" type="System.Runtime.Remoting.Channels.BinaryServerFormatterSinkProvider, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
					<provider id="wsdl" type="System.Runtime.Remoting.MetadataServices.SdlChannelSinkProvider, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				</serverProviders>
			</channelSinkProviders>
	</system.runtime.remoting>
	
	<appSettings>
	<!--<add key="yourkey" value="your value" /> -->
	<!--<remove key="a key defined higher in the hierarchy" /> -->
	<!--<clear/> Removes all defined settings -->
	</appSettings>
	<system.diagnostics>
		<trace autoflush="false" indentsize="4" />
	</system.diagnostics>
        <system.drawing>
        </system.drawing>

	<system.data>
	  <DbProviderFactories>
	    <add name="Mono Sqlite Data Provider"  invariant="Mono.Data.SqliteClient" 
		 description="Mono Framework Data Provider for SQLite (old version)" 
		 type="Mono.Data.SqliteClient.SqliteFactory, Mono.Data.SqliteClient, Version=*******, Culture=neutral, PublicKeyToken=0738eb9f132ed756"/>
	    <add name="Mono Sqlite Provider"  invariant="Mono.Data.Sqlite" 
		 description="Mono Framework Data Provider for SQLite (new version)"
		 type="Mono.Data.Sqlite.SqliteFactory, Mono.Data.Sqlite, Version=*******, Culture=neutral, PublicKeyToken=0738eb9f132ed756"/>
	    <add name="Odbc Data Provider"         invariant="System.Data.Odbc"         
		 description=".Net Framework Data Provider for Odbc"      
		 type="System.Data.Odbc.OdbcFactory, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
            <add name="OleDb Data Provider"        invariant="System.Data.OleDb"        
		 description=".Net Framework Data Provider for OleDb"     
		 type="System.Data.OleDb.OleDbFactory, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
            <add name="OracleClient Data Provider" invariant="System.Data.OracleClient" 
		 description=".Net Framework Data Provider for Oracle"    
		 type="System.Data.OracleClient.OracleClientFactory, System.Data.OracleClient, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
            <add name="SqlClient Data Provider"    invariant="System.Data.SqlClient"    
		 description=".Net Framework Data Provider for SqlServer" 
		 type="System.Data.SqlClient.SqlClientFactory, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
            <add name="Sybase Data Provider"    invariant="Mono.Data.SybaseClient"    
		 description=".Net Framework Data Provider for Sybase" 
		 type="Mono.Data.SybaseClient.SybaseClientFactory, Mono.Data.SybaseClient, Version=*******, Culture=neutral, PublicKeyToken=0738eb9f132ed756"/>
	  </DbProviderFactories>
	</system.data>

	<mscorlib>
		<cryptographySettings>
			<cryptoNameMapping>
				<cryptoClasses>
					<cryptoClass monoMD2="Mono.Security.Cryptography.MD2Managed, Mono.Security, Version=*******, Culture=neutral, PublicKeyToken=0738eb9f132ed756" />
					<cryptoClass monoMD4="Mono.Security.Cryptography.MD4Managed, Mono.Security, Version=*******, Culture=neutral, PublicKeyToken=0738eb9f132ed756" />
				</cryptoClasses>
				<nameEntry name="MD2" class="monoMD2" />
				<nameEntry name="MD4" class="monoMD4" />
			</cryptoNameMapping>
			<oidMap>
				<oidEntry OID="1.2.840.113549.2.2" name="MD2" />
				<oidEntry OID="1.2.840.113549.2.2" name="Mono.Security.Cryptography.MD2Managed" />
				<oidEntry OID="1.2.840.113549.2.4" name="MD4" />
				<oidEntry OID="1.2.840.113549.2.4" name="Mono.Security.Cryptography.MD4Managed" />
			</oidMap>
		</cryptographySettings>
	</mscorlib>

	<strongNames>
		<pubTokenMapping>
			<!-- ECMA key -->
			<map Token="b77a5c561934e089" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- Microsoft (final) key -->
			<map Token="b03f5f7f11d50a3a" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- Microsoft (Web Service Enhancement) key -->
			<map Token="31bf3856ad364e35" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- IBM (DB2 Data Provider) key -->
			<map Token="7c307b91aa13d208" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- Silverlight 2.0 key -->
			<map Token="7cec85d7bea7798e" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- XNA Framework key -->                                                            
			<map Token="6d5c3888ef60e27d" PublicKey="0024000004800000940000000602000000240000525341310004000001000100f9a2641bac9847900d92a33d652ccc4e8b529360f908e7af53e57008b2a9a1938c32a160d47f795a23590557608d2c8d0c0e8846a052d070f9298281b8185343dbe5b479bd52de256f73c2a943e1a8a42065b5c918622dc14b1c0151dbd94d9a4543e7cd03e536b1b1d2d6d99af535d227ab9bdac76af9312a21d457bdf817e6" />
		</pubTokenMapping>
	</strongNames>

	<system.web>
		<webServices>
			<protocols>
				<add name="HttpSoap"/>
				<add name="HttpSoap12"/>
				<add name="HttpPost"/>
				<add name="HttpGet"/>
				<add name="Documentation"/>
			</protocols>
			<conformanceWarnings>
				<add name="BasicProfile1_1"/>
			</conformanceWarnings>
			<wsdlHelpGenerator href="DefaultWsdlHelpGenerator.aspx" />
		</webServices>

		<membership>
			<providers>
				<add name="AspNetSqlMembershipProvider" type="System.Web.Security.SqlMembershipProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" connectionStringName="LocalSqlServer" enablePasswordRetrieval="false" enablePasswordReset="true" requiresQuestionAndAnswer="true" applicationName="/" requiresUniqueEmail="false" passwordFormat="Hashed" maxInvalidPasswordAttempts="5" minRequiredPasswordLength="7" minRequiredNonalphanumericCharacters="1" passwordAttemptWindow="10" passwordStrengthRegularExpression=""/>
				<!-- <add name="AspNetSqlMembershipProvider" type="Mainsoft.Web.Security.GenericMembershipProvider, Mainsoft.Web.Security" applicationName="/" connectionStringName="LocalSqlServer" /> -->
			</providers>
		</membership>

		<roleManager>
			<providers>
				<add name="AspNetSqlRoleProvider" type="System.Web.Security.SqlRoleProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" applicationName="/" connectionStringName="LocalSqlServer" />
				<!-- <add name="AspNetSqlRoleProvider" type="Mainsoft.Web.Security.GenericRoleProvider, Mainsoft.Web.Security" applicationName="/" connectionStringName="LocalSqlServer" /> -->
			</providers>
		</roleManager>

		<profile>
        		<providers>
            		    <add name="AspNetSqlProfileProvider" connectionStringName="LocalSqlServer" applicationName="/" type="System.Web.Profile.SqlProfileProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		        </providers>
    		</profile>
	</system.web>

	<system.serviceModel>
		<extensions>
			<behaviorExtensions>
				<add name="enableWebScript" type="System.ServiceModel.Configuration.WebScriptEnablingElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add name="webHttp" type="System.ServiceModel.Configuration.WebHttpElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
			</behaviorExtensions>
			<bindingElementExtensions>
				<add name="webMessageEncoding" type="System.ServiceModel.Configuration.WebMessageEncodingElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
			</bindingElementExtensions>
			<bindingExtensions>
				<add name="webHttpBinding" type="System.ServiceModel.Configuration.WebHttpBindingCollectionElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
			</bindingExtensions>
		</extensions>
	</system.serviceModel>
</configuration>


