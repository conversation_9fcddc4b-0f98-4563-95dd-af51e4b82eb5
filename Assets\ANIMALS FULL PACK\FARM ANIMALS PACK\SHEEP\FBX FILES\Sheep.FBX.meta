fileFormatVersion: 2
guid: caca79ca0adcc5844992495f8c0ed657
ModelImporter:
  serializedVersion: 20200
  internalIDToNameTable:
  - first:
      1: 100000
    second: root
  - first:
      1: 100002
    second: //RootNode
  - first:
      1: 100004
    second: SHEEP_
  - first:
      1: 100006
    second: SHEEP_ Head
  - first:
      1: 100008
    second: SHEEP_ L Calf
  - first:
      1: 100010
    second: SHEEP_ L Clavicle
  - first:
      1: 100012
    second: SHEEP_ L Finger0
  - first:
      1: 100014
    second: SHEEP_ L Foot
  - first:
      1: 100016
    second: SHEEP_ L Forearm
  - first:
      1: 100018
    second: SHEEP_ L Hand
  - first:
      1: 100020
    second: SHEEP_ L HorseLink
  - first:
      1: 100022
    second: SHEEP_ L Thigh
  - first:
      1: 100024
    second: SHEEP_ L UpperArm
  - first:
      1: 100026
    second: SHEEP_ Neck
  - first:
      1: 100028
    second: SHEEP_ Neck1
  - first:
      1: 100030
    second: SHEEP_ Neck2
  - first:
      1: 100032
    second: SHEEP_ Pelvis
  - first:
      1: 100034
    second: SHEEP_ Queue de cheval 1
  - first:
      1: 100036
    second: SHEEP_ R Calf
  - first:
      1: 100038
    second: SHEEP_ R Clavicle
  - first:
      1: 100040
    second: SHEEP_ R Finger0
  - first:
      1: 100042
    second: SHEEP_ R Foot
  - first:
      1: 100044
    second: SHEEP_ R Forearm
  - first:
      1: 100046
    second: SHEEP_ R Hand
  - first:
      1: 100048
    second: SHEEP_ R HorseLink
  - first:
      1: 100050
    second: SHEEP_ R Thigh
  - first:
      1: 100052
    second: SHEEP_ R UpperArm
  - first:
      1: 100054
    second: SHEEP_ Spine
  - first:
      1: 100056
    second: SHEEP_ Spine1
  - first:
      1: 100058
    second: SHEEP_ Tail
  - first:
      1: 100060
    second: SHEEP_ Tail1
  - first:
      1: 100062
    second: SHEEP_ Tail2
  - first:
      1: 100064
    second: SK_Sheep
  - first:
      1: 100066
    second: SK_Sheep_LOD0
  - first:
      1: 100068
    second: SK_Sheep_LOD1
  - first:
      1: 100070
    second: SK_Sheep_LOD2
  - first:
      1: 100072
    second: SK_Sheep_LOD3
  - first:
      4: 400000
    second: root
  - first:
      4: 400002
    second: //RootNode
  - first:
      4: 400004
    second: SHEEP_
  - first:
      4: 400006
    second: SHEEP_ Head
  - first:
      4: 400008
    second: SHEEP_ L Calf
  - first:
      4: 400010
    second: SHEEP_ L Clavicle
  - first:
      4: 400012
    second: SHEEP_ L Finger0
  - first:
      4: 400014
    second: SHEEP_ L Foot
  - first:
      4: 400016
    second: SHEEP_ L Forearm
  - first:
      4: 400018
    second: SHEEP_ L Hand
  - first:
      4: 400020
    second: SHEEP_ L HorseLink
  - first:
      4: 400022
    second: SHEEP_ L Thigh
  - first:
      4: 400024
    second: SHEEP_ L UpperArm
  - first:
      4: 400026
    second: SHEEP_ Neck
  - first:
      4: 400028
    second: SHEEP_ Neck1
  - first:
      4: 400030
    second: SHEEP_ Neck2
  - first:
      4: 400032
    second: SHEEP_ Pelvis
  - first:
      4: 400034
    second: SHEEP_ Queue de cheval 1
  - first:
      4: 400036
    second: SHEEP_ R Calf
  - first:
      4: 400038
    second: SHEEP_ R Clavicle
  - first:
      4: 400040
    second: SHEEP_ R Finger0
  - first:
      4: 400042
    second: SHEEP_ R Foot
  - first:
      4: 400044
    second: SHEEP_ R Forearm
  - first:
      4: 400046
    second: SHEEP_ R Hand
  - first:
      4: 400048
    second: SHEEP_ R HorseLink
  - first:
      4: 400050
    second: SHEEP_ R Thigh
  - first:
      4: 400052
    second: SHEEP_ R UpperArm
  - first:
      4: 400054
    second: SHEEP_ Spine
  - first:
      4: 400056
    second: SHEEP_ Spine1
  - first:
      4: 400058
    second: SHEEP_ Tail
  - first:
      4: 400060
    second: SHEEP_ Tail1
  - first:
      4: 400062
    second: SHEEP_ Tail2
  - first:
      4: 400064
    second: SK_Sheep
  - first:
      4: 400066
    second: SK_Sheep_LOD0
  - first:
      4: 400068
    second: SK_Sheep_LOD1
  - first:
      4: 400070
    second: SK_Sheep_LOD2
  - first:
      4: 400072
    second: SK_Sheep_LOD3
  - first:
      43: 4300000
    second: SK_Sheep
  - first:
      43: 4300002
    second: SK_Sheep_LOD0
  - first:
      43: 4300004
    second: SK_Sheep_LOD1
  - first:
      43: 4300006
    second: SK_Sheep_LOD2
  - first:
      43: 4300008
    second: SK_Sheep_LOD3
  - first:
      74: 7400000
    second: Take 001
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: SK_Sheep
  - first:
      137: 13700002
    second: SK_Sheep_LOD0
  - first:
      137: 13700004
    second: SK_Sheep_LOD1
  - first:
      137: 13700006
    second: SK_Sheep_LOD2
  - first:
      137: 13700008
    second: SK_Sheep_LOD3
  - first:
      205: 20500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.0625
    - 0.01
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 6faa9b7a7f1711948b2583cea1af23c7
  - 9d92cc120dbaa214bbdec63d3e18c1b4
  - 2eb5a28349aa9244f8bc169b12989dc7
  - 96dc5041347c8f04886391b5fcfabac7
  - 50d85ba9004d9f2469177b0f86691128
  - 2b3b5a29a6b7d4943acb2328587a8d19
  - 881c2fd90793e5443b7fabd132f87763
  - 01cf6f19c48d1414ebe6993a5f1ef3b6
  - b7245dd1ab030764b917a9108c8dc689
  - f2f7ff4c942c4b5459add2331905bfb2
  - fbed7497c5a999e4285bc0304e4095eb
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: root
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
