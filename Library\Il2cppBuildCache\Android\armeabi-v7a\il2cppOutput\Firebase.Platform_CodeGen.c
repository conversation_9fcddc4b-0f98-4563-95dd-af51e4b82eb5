﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Collections.Generic.List`1<System.Exception> Firebase.ExceptionAggregator::get_Exceptions()
extern void ExceptionAggregator_get_Exceptions_m421E855AAB77ED0458E6FCBBB9575F0072411DFE (void);
// 0x00000002 System.Exception Firebase.ExceptionAggregator::GetAndClearPendingExceptions()
extern void ExceptionAggregator_GetAndClearPendingExceptions_mE7FDFA220E3ACEB6D56D9884DEEF91540D2B8522 (void);
// 0x00000003 System.Void Firebase.ExceptionAggregator::ThrowAndClearPendingExceptions()
extern void ExceptionAggregator_ThrowAndClearPendingExceptions_mC66983DEDF4880ACD5E96B5E9EBA38E00CC93B91 (void);
// 0x00000004 System.Exception Firebase.ExceptionAggregator::LogException(System.Exception)
extern void ExceptionAggregator_LogException_m68B2EB141A7DDD04EFEE794ED44FCB41673E19F2 (void);
// 0x00000005 System.Void Firebase.ExceptionAggregator::Wrap(System.Action)
extern void ExceptionAggregator_Wrap_m24EBDCC97F8D88B56ADBD17EEF69F25CB3839415 (void);
// 0x00000006 System.Void Firebase.ExceptionAggregator::.cctor()
extern void ExceptionAggregator__cctor_m68C6741A43A5C18AC3DBBA1AC42105FC29CB5B43 (void);
// 0x00000007 System.Void Firebase.Dispatcher::.ctor()
extern void Dispatcher__ctor_mE99B93199AD38F0A53946F925713CC84099C35B3 (void);
// 0x00000008 TResult Firebase.Dispatcher::Run(System.Func`1<TResult>)
// 0x00000009 System.Threading.Tasks.Task`1<TResult> Firebase.Dispatcher::RunAsync(System.Func`1<TResult>)
// 0x0000000A System.Threading.Tasks.Task`1<TResult> Firebase.Dispatcher::RunAsyncNow(System.Func`1<TResult>)
// 0x0000000B System.Boolean Firebase.Dispatcher::ManagesThisThread()
extern void Dispatcher_ManagesThisThread_mDD4799366D040E2A27D9501C6AE8D2C88AAE086A (void);
// 0x0000000C System.Void Firebase.Dispatcher::PollJobs()
extern void Dispatcher_PollJobs_m4DD47E8B63F33D975BC1A7D670A509F597F8E9E0 (void);
// 0x0000000D TResult Firebase.Dispatcher/CallbackStorage`1::get_Result()
// 0x0000000E System.Void Firebase.Dispatcher/CallbackStorage`1::set_Result(TResult)
// 0x0000000F System.Exception Firebase.Dispatcher/CallbackStorage`1::get_Exception()
// 0x00000010 System.Void Firebase.Dispatcher/CallbackStorage`1::set_Exception(System.Exception)
// 0x00000011 System.Void Firebase.Dispatcher/CallbackStorage`1::.ctor()
// 0x00000012 System.Void Firebase.Dispatcher/<>c__DisplayClass4_0`1::.ctor()
// 0x00000013 System.Void Firebase.Dispatcher/<>c__DisplayClass4_0`1::<Run>b__0()
// 0x00000014 System.Void Firebase.Dispatcher/<>c__DisplayClass5_0`1::.ctor()
// 0x00000015 System.Void Firebase.Dispatcher/<>c__DisplayClass5_0`1::<RunAsync>b__0()
// 0x00000016 Firebase.Unity.UnityLoggingService Firebase.Unity.UnityLoggingService::get_Instance()
extern void UnityLoggingService_get_Instance_mCFC4AB732884A36484C59A3B06D70C568DFF9A78 (void);
// 0x00000017 System.Void Firebase.Unity.UnityLoggingService::.ctor()
extern void UnityLoggingService__ctor_m40EA0D43E2C571DD040E6239AFFA67D4A16A258D (void);
// 0x00000018 System.Void Firebase.Unity.UnityLoggingService::.cctor()
extern void UnityLoggingService__cctor_mBE727CBA25AF8C39C91022BD50F0E336F14B2717 (void);
// 0x00000019 System.Void Firebase.Unity.UnityPlatformServices::SetupServices()
extern void UnityPlatformServices_SetupServices_m4B3BAB80E61E32078E9005941D9C9BC69E7CF3D8 (void);
// 0x0000001A System.Void Firebase.Unity.UnitySynchronizationContext::.ctor(UnityEngine.GameObject)
extern void UnitySynchronizationContext__ctor_m16FA79368D74F4CCBF96D397B8C334CCC8A40720 (void);
// 0x0000001B System.Void Firebase.Unity.UnitySynchronizationContext::Create(UnityEngine.GameObject)
extern void UnitySynchronizationContext_Create_mE6147188A48A044C46BE9666824CAA1445B29BD8 (void);
// 0x0000001C System.Void Firebase.Unity.UnitySynchronizationContext::Destroy()
extern void UnitySynchronizationContext_Destroy_m158D9BC9BBF28C2A18D00AF0C8AAE025F9F5D394 (void);
// 0x0000001D System.Threading.ManualResetEvent Firebase.Unity.UnitySynchronizationContext::GetThreadEvent()
extern void UnitySynchronizationContext_GetThreadEvent_mC98D54E3250279ACF4A26392835855D729D729F0 (void);
// 0x0000001E System.Void Firebase.Unity.UnitySynchronizationContext::Post(System.Threading.SendOrPostCallback,System.Object)
extern void UnitySynchronizationContext_Post_mFFC9993A213F2DE9D2AB3003272BD0D62486C6E6 (void);
// 0x0000001F System.Void Firebase.Unity.UnitySynchronizationContext::Send(System.Threading.SendOrPostCallback,System.Object)
extern void UnitySynchronizationContext_Send_m0BDC4069F41293598BA84281FCBBA4C46DAC00A4 (void);
// 0x00000020 System.Void Firebase.Unity.UnitySynchronizationContext::.cctor()
extern void UnitySynchronizationContext__cctor_mD3303025BE218EE1787E91E30323A71276C90224 (void);
// 0x00000021 System.Collections.Generic.Queue`1<System.Tuple`2<System.Threading.SendOrPostCallback,System.Object>> Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir::get_CallbackQueue()
extern void SynchronizationContextBehavoir_get_CallbackQueue_m0E34CE99C7CA3A50D0B5960110C97B35F1464181 (void);
// 0x00000022 System.Collections.IEnumerator Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir::Start()
extern void SynchronizationContextBehavoir_Start_mC0020EEF1D711C56559978F6B05D5980607B1415 (void);
// 0x00000023 System.Void Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir::.ctor()
extern void SynchronizationContextBehavoir__ctor_mB7DAD13667448BFE9813A53C0AEEA01D2ABB23E7 (void);
// 0x00000024 System.Void Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir/<Start>d__3::.ctor(System.Int32)
extern void U3CStartU3Ed__3__ctor_mE286810BB74A14A6CA89DBD541556E73EE6F1873 (void);
// 0x00000025 System.Void Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir/<Start>d__3::System.IDisposable.Dispose()
extern void U3CStartU3Ed__3_System_IDisposable_Dispose_mA3F0338173978F9E1F1C95BCD8A5C65C7DA5464B (void);
// 0x00000026 System.Boolean Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir/<Start>d__3::MoveNext()
extern void U3CStartU3Ed__3_MoveNext_mF7311EB49D00661ED24EBB2AD6A43FC8FB69451E (void);
// 0x00000027 System.Object Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir/<Start>d__3::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CStartU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCB792B39126660A53266DF372A408359EFCE5CCA (void);
// 0x00000028 System.Void Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir/<Start>d__3::System.Collections.IEnumerator.Reset()
extern void U3CStartU3Ed__3_System_Collections_IEnumerator_Reset_m0700DB59F5C1825F093EE05044976C7C512A8026 (void);
// 0x00000029 System.Object Firebase.Unity.UnitySynchronizationContext/SynchronizationContextBehavoir/<Start>d__3::System.Collections.IEnumerator.get_Current()
extern void U3CStartU3Ed__3_System_Collections_IEnumerator_get_Current_mDE0E18D92FEA57AF47CB64A273477B674A114DFD (void);
// 0x0000002A System.Void Firebase.Unity.UnitySynchronizationContext/<>c__DisplayClass14_0::.ctor()
extern void U3CU3Ec__DisplayClass14_0__ctor_mBF75F76D3921A9186C2576BDC0158F2748855D64 (void);
// 0x0000002B System.Void Firebase.Unity.UnitySynchronizationContext/<>c__DisplayClass14_1::.ctor()
extern void U3CU3Ec__DisplayClass14_1__ctor_m7A98D58E7972709AD2351CD86F0C8EF10E75A5D1 (void);
// 0x0000002C System.Void Firebase.Unity.UnitySynchronizationContext/<>c__DisplayClass16_0::.ctor()
extern void U3CU3Ec__DisplayClass16_0__ctor_m864C9D4FA34AF22ECD795F3344D81BEDBA8BAE66 (void);
// 0x0000002D System.Void Firebase.Unity.UnitySynchronizationContext/<>c__DisplayClass16_1::.ctor()
extern void U3CU3Ec__DisplayClass16_1__ctor_mB967C90C84AC0EC6AE041D0ECEF6D1AB66069C80 (void);
// 0x0000002E System.Void Firebase.Unity.UnitySynchronizationContext/<>c__DisplayClass16_1::<Send>b__0(System.Object)
extern void U3CU3Ec__DisplayClass16_1_U3CSendU3Eb__0_mAC9FDB5AD8982ABA072566F9C51D439E1E01674B (void);
// 0x0000002F Firebase.Platform.DebugLogger Firebase.Platform.DebugLogger::get_Instance()
extern void DebugLogger_get_Instance_m0E521CDD954F86EB61792F19F2362373EB10745E (void);
// 0x00000030 System.Void Firebase.Platform.DebugLogger::.ctor()
extern void DebugLogger__ctor_mD6357FC79350285A4D8372BC692899939F6D36C4 (void);
// 0x00000031 System.Void Firebase.Platform.DebugLogger::.cctor()
extern void DebugLogger__cctor_mD2949D665716DF02D7FD7F4C1181AD6525066903 (void);
// 0x00000032 System.Void Firebase.Platform.Services::.cctor()
extern void Services__cctor_m7D026055BA78EEC691B309AA1A6702D54C34F366 (void);
// 0x00000033 System.Void Firebase.Platform.Services::set_AppConfig(Firebase.Platform.IAppConfigExtensions)
extern void Services_set_AppConfig_mE48F0E5E1FA561208AEBA714C1147ECD693AD402 (void);
// 0x00000034 System.Void Firebase.Platform.Services::set_Clock(Firebase.Platform.IClockService)
extern void Services_set_Clock_mBDFC2BAB8F8D2628AF8052A79796026E74DBA370 (void);
// 0x00000035 System.Void Firebase.Platform.Services::set_Logging(Firebase.Platform.ILoggingService)
extern void Services_set_Logging_m53140204CEF56D9BC71B5C5BA7BB9D6B3974FFD5 (void);
// 0x00000036 System.Void Firebase.Platform.IFirebaseAppUtils::TranslateDllNotFoundException(System.Action)
// 0x00000037 System.Void Firebase.Platform.IFirebaseAppUtils::PollCallbacks()
// 0x00000038 Firebase.Platform.PlatformLogLevel Firebase.Platform.IFirebaseAppUtils::GetLogLevel()
// 0x00000039 Firebase.Platform.FirebaseAppUtilsStub Firebase.Platform.FirebaseAppUtilsStub::get_Instance()
extern void FirebaseAppUtilsStub_get_Instance_m980CDC684E1C5AE80F1853386A98DD558AE76ABC (void);
// 0x0000003A System.Void Firebase.Platform.FirebaseAppUtilsStub::TranslateDllNotFoundException(System.Action)
extern void FirebaseAppUtilsStub_TranslateDllNotFoundException_m05B83DDC2DB8B21CEFC55D0AA990CA16A9390C8E (void);
// 0x0000003B System.Void Firebase.Platform.FirebaseAppUtilsStub::PollCallbacks()
extern void FirebaseAppUtilsStub_PollCallbacks_m13A423E04EC1F3EDFD0D7FE63F18E0D3036346E1 (void);
// 0x0000003C Firebase.Platform.PlatformLogLevel Firebase.Platform.FirebaseAppUtilsStub::GetLogLevel()
extern void FirebaseAppUtilsStub_GetLogLevel_m6082A2182D62F889C842C09F4B5A15074B8F4127 (void);
// 0x0000003D System.Void Firebase.Platform.FirebaseAppUtilsStub::.ctor()
extern void FirebaseAppUtilsStub__ctor_m3EC6F08162223655B3A0A2FA44BA7060AE3884AD (void);
// 0x0000003E System.Void Firebase.Platform.FirebaseAppUtilsStub::.cctor()
extern void FirebaseAppUtilsStub__cctor_m5AD579715FDD6408AB8E44DC5F9856684B6B57B8 (void);
// 0x0000003F System.Void Firebase.Platform.MainThreadProperty`1::.ctor(System.Func`1<T>)
// 0x00000040 T Firebase.Platform.MainThreadProperty`1::get_Value()
// 0x00000041 T Firebase.Platform.MainThreadProperty`1::<get_Value>b__5_0()
// 0x00000042 Firebase.Platform.IFirebaseAppUtils Firebase.Platform.FirebaseHandler::get_AppUtils()
extern void FirebaseHandler_get_AppUtils_mC39704DCB7BFD728B88A79C59DC50287679E47B0 (void);
// 0x00000043 System.Void Firebase.Platform.FirebaseHandler::set_AppUtils(Firebase.Platform.IFirebaseAppUtils)
extern void FirebaseHandler_set_AppUtils_mCA47C4E5CB3698D8715B88CB409F5112C38A16D6 (void);
// 0x00000044 System.Int32 Firebase.Platform.FirebaseHandler::get_TickCount()
extern void FirebaseHandler_get_TickCount_m5F47CB784234BDF946837F63C6E93291FA9973ED (void);
// 0x00000045 Firebase.Dispatcher Firebase.Platform.FirebaseHandler::get_ThreadDispatcher()
extern void FirebaseHandler_get_ThreadDispatcher_m8437519751496E4FC4AE6133C7F0C787B8117EAB (void);
// 0x00000046 System.Void Firebase.Platform.FirebaseHandler::set_ThreadDispatcher(Firebase.Dispatcher)
extern void FirebaseHandler_set_ThreadDispatcher_mFF39382EA85A80364D3837E729F973D1602D6E39 (void);
// 0x00000047 System.Boolean Firebase.Platform.FirebaseHandler::get_IsPlayMode()
extern void FirebaseHandler_get_IsPlayMode_m59AFE9F48F2EA1AADCFBD94DBE010730359B3B40 (void);
// 0x00000048 System.Void Firebase.Platform.FirebaseHandler::set_IsPlayMode(System.Boolean)
extern void FirebaseHandler_set_IsPlayMode_m832E6FCD6DA3ACDEA0B59D346EDD8585381C3B15 (void);
// 0x00000049 System.Void Firebase.Platform.FirebaseHandler::.cctor()
extern void FirebaseHandler__cctor_m17F25FC2EE5E6E78B15620FCDE84BEF8625CE0F9 (void);
// 0x0000004A System.Void Firebase.Platform.FirebaseHandler::.ctor()
extern void FirebaseHandler__ctor_mD79B2E4B647013911BE3A8602014EB108DD1D054 (void);
// 0x0000004B System.Void Firebase.Platform.FirebaseHandler::StartMonoBehaviour()
extern void FirebaseHandler_StartMonoBehaviour_m3258056C31C8D32C690329363587720C60B35E0A (void);
// 0x0000004C System.Void Firebase.Platform.FirebaseHandler::StopMonoBehaviour()
extern void FirebaseHandler_StopMonoBehaviour_m1CB49D4E683D48586AEF5F8B515EAC41F2F502BE (void);
// 0x0000004D TResult Firebase.Platform.FirebaseHandler::RunOnMainThread(System.Func`1<TResult>)
// 0x0000004E System.Threading.Tasks.Task`1<TResult> Firebase.Platform.FirebaseHandler::RunOnMainThreadAsync(System.Func`1<TResult>)
// 0x0000004F Firebase.Platform.FirebaseHandler Firebase.Platform.FirebaseHandler::get_DefaultInstance()
extern void FirebaseHandler_get_DefaultInstance_m5FA4E1EC809FA3727799598E906DDFE6BBFC3DC4 (void);
// 0x00000050 System.Void Firebase.Platform.FirebaseHandler::CreatePartialOnMainThread(Firebase.Platform.IFirebaseAppUtils)
extern void FirebaseHandler_CreatePartialOnMainThread_m5F79802A26DF07DAE2DFC2B250CDAB8DDFBC8C4B (void);
// 0x00000051 System.Void Firebase.Platform.FirebaseHandler::Create(Firebase.Platform.IFirebaseAppUtils)
extern void FirebaseHandler_Create_m1B30952F7479043520E3D7023F331D038F6B2125 (void);
// 0x00000052 System.Void Firebase.Platform.FirebaseHandler::Update()
extern void FirebaseHandler_Update_m054C867D529EEC80853232EEAFBCAD18CC287817 (void);
// 0x00000053 System.Void Firebase.Platform.FirebaseHandler::OnApplicationFocus(System.Boolean)
extern void FirebaseHandler_OnApplicationFocus_mC750E0727206EA64FC16F8448D149EBF82C59F41 (void);
// 0x00000054 System.Void Firebase.Platform.FirebaseHandler::OnMonoBehaviourDestroyed(Firebase.Platform.FirebaseMonoBehaviour)
extern void FirebaseHandler_OnMonoBehaviourDestroyed_m327EFE33762C7F1803DDD5C95103007322779A90 (void);
// 0x00000055 System.Void Firebase.Platform.FirebaseHandler::<Update>b__36_0()
extern void FirebaseHandler_U3CUpdateU3Eb__36_0_mDC0BB815941FD6EF6E26F6B31745BBB7E10B5335 (void);
// 0x00000056 System.Void Firebase.Platform.FirebaseHandler/ApplicationFocusChangedEventArgs::set_HasFocus(System.Boolean)
extern void ApplicationFocusChangedEventArgs_set_HasFocus_mDB3B76E13D221D0A0838877B9EE94299135B4CA1 (void);
// 0x00000057 System.Void Firebase.Platform.FirebaseHandler/ApplicationFocusChangedEventArgs::.ctor()
extern void ApplicationFocusChangedEventArgs__ctor_m2B84BC26D7A933044EAA1FBD78A65CB767E1C55C (void);
// 0x00000058 System.Void Firebase.Platform.FirebaseHandler/<>c::.cctor()
extern void U3CU3Ec__cctor_mC3A02F0B920A69CFEAEA3EC52F816DC97213C024 (void);
// 0x00000059 System.Void Firebase.Platform.FirebaseHandler/<>c::.ctor()
extern void U3CU3Ec__ctor_m85629195FA2478A2E4784E8A66606324E6613A78 (void);
// 0x0000005A System.Boolean Firebase.Platform.FirebaseHandler/<>c::<StopMonoBehaviour>b__19_0()
extern void U3CU3Ec_U3CStopMonoBehaviourU3Eb__19_0_mF7FF24CAF2DBC2B8F724320506C358775DF8E7AE (void);
// 0x0000005B System.Void Firebase.Platform.FirebaseHandler/<>c__DisplayClass34_0::.ctor()
extern void U3CU3Ec__DisplayClass34_0__ctor_mC8EDEEF9AF53EC004805D7A0148D08C948F08796 (void);
// 0x0000005C System.Void Firebase.Platform.FirebaseHandler/<>c__DisplayClass34_0::<CreatePartialOnMainThread>b__0()
extern void U3CU3Ec__DisplayClass34_0_U3CCreatePartialOnMainThreadU3Eb__0_m1DDB57163B50F831CB7EC76D9D56CC066D858A1D (void);
// 0x0000005D System.Boolean Firebase.Platform.PlatformInformation::get_IsAndroid()
extern void PlatformInformation_get_IsAndroid_mF6B9AE75F5E1D6A48F5E1754A692FDC167ADE628 (void);
// 0x0000005E System.Boolean Firebase.Platform.PlatformInformation::get_IsIOS()
extern void PlatformInformation_get_IsIOS_m314080A5FF94D29E352F4E3FCA4870CBB67E3840 (void);
// 0x0000005F System.String Firebase.Platform.PlatformInformation::get_DefaultConfigLocation()
extern void PlatformInformation_get_DefaultConfigLocation_mCC41CAB77CCA5F822E67C9B8D86478606777A06C (void);
// 0x00000060 System.Single Firebase.Platform.PlatformInformation::get_RealtimeSinceStartup()
extern void PlatformInformation_get_RealtimeSinceStartup_mC067B0FADDC1B86488739F7C484B231E0E586C55 (void);
// 0x00000061 System.Void Firebase.Platform.PlatformInformation::set_RealtimeSinceStartupSafe(System.Single)
extern void PlatformInformation_set_RealtimeSinceStartupSafe_m2808F45B6549A0E0139BC36B236BC95213B123FC (void);
// 0x00000062 System.String Firebase.Platform.PlatformInformation::get_RuntimeName()
extern void PlatformInformation_get_RuntimeName_m7DC9B4B120826252A1359FBB00EEA9CCA0B9FE81 (void);
// 0x00000063 System.String Firebase.Platform.PlatformInformation::get_RuntimeVersion()
extern void PlatformInformation_get_RuntimeVersion_m449590D737405693CC7C1AF5878FBAED5B49852F (void);
// 0x00000064 System.Void Firebase.Platform.PlatformInformation/<>c::.cctor()
extern void U3CU3Ec__cctor_m33FC037E5010B06E6CDC3951D2E0C5E55F0BF174 (void);
// 0x00000065 System.Void Firebase.Platform.PlatformInformation/<>c::.ctor()
extern void U3CU3Ec__ctor_m100F86B3556423BD4A74464C139ECD9E6EEE7BC7 (void);
// 0x00000066 System.String Firebase.Platform.PlatformInformation/<>c::<get_DefaultConfigLocation>b__6_0()
extern void U3CU3Ec_U3Cget_DefaultConfigLocationU3Eb__6_0_m06C1966148257FDD30A2A8044489552FF4AC2366 (void);
// 0x00000067 System.String Firebase.Platform.PlatformInformation/<>c::<get_RuntimeVersion>b__18_0()
extern void U3CU3Ec_U3Cget_RuntimeVersionU3Eb__18_0_mBEBDF5B525CDCDB0120B5D7BECE6720DBB573738 (void);
// 0x00000068 System.Boolean Firebase.Platform.FirebaseLogger::IsStackTraceLogTypeIncompatibleWithNativeLogs(UnityEngine.StackTraceLogType)
extern void FirebaseLogger_IsStackTraceLogTypeIncompatibleWithNativeLogs_m27CEB41C390C022D632065D0FBF4AF0E176ACD7A (void);
// 0x00000069 System.Boolean Firebase.Platform.FirebaseLogger::CurrentStackTraceLogTypeIsIncompatibleWithNativeLogs()
extern void FirebaseLogger_CurrentStackTraceLogTypeIsIncompatibleWithNativeLogs_m3C672C23E7A9E38397D742A1AA95732883E43C0B (void);
// 0x0000006A System.Boolean Firebase.Platform.FirebaseLogger::get_CanRedirectNativeLogs()
extern void FirebaseLogger_get_CanRedirectNativeLogs_mA3ED5F874C3D698FB2D1656B3B4E1EAFE257BBF4 (void);
// 0x0000006B System.Void Firebase.Platform.FirebaseLogger::LogMessage(Firebase.Platform.PlatformLogLevel,System.String)
extern void FirebaseLogger_LogMessage_mA65FB8012E13AA044A6FF15E9792D2755AF84B86 (void);
// 0x0000006C System.Void Firebase.Platform.FirebaseLogger::.cctor()
extern void FirebaseLogger__cctor_mE7B888BE9D8FD6CC2A7E7FB95B13368CFF5397EC (void);
// 0x0000006D Firebase.Platform.FirebaseHandler Firebase.Platform.FirebaseMonoBehaviour::GetFirebaseHandlerOrDestroyGameObject()
extern void FirebaseMonoBehaviour_GetFirebaseHandlerOrDestroyGameObject_mE01D6AF26DA880E3BF6D8DAF231C270F0C9C002F (void);
// 0x0000006E System.Void Firebase.Platform.FirebaseMonoBehaviour::OnEnable()
extern void FirebaseMonoBehaviour_OnEnable_m18519EA6271E80120F01D31B3EE592127B48B72C (void);
// 0x0000006F System.Void Firebase.Platform.FirebaseMonoBehaviour::Update()
extern void FirebaseMonoBehaviour_Update_m8338C29185F7348D4FC338194CBD3F285645A773 (void);
// 0x00000070 System.Void Firebase.Platform.FirebaseMonoBehaviour::OnApplicationFocus(System.Boolean)
extern void FirebaseMonoBehaviour_OnApplicationFocus_mC5EA1995ED7D3CB5F83902CFA519EC507E05EA8D (void);
// 0x00000071 System.Void Firebase.Platform.FirebaseMonoBehaviour::OnDestroy()
extern void FirebaseMonoBehaviour_OnDestroy_m3FD94734285C9254480EF29135FA354D0E2292B8 (void);
// 0x00000072 System.Void Firebase.Platform.FirebaseMonoBehaviour::.ctor()
extern void FirebaseMonoBehaviour__ctor_m17C407059420FFEA2371846AD4279B80AF15B62E (void);
// 0x00000073 System.Type Firebase.Platform.FirebaseEditorDispatcher::get_EditorApplicationType()
extern void FirebaseEditorDispatcher_get_EditorApplicationType_mD6FD666F69631C72E955CF09AD61929823D0B459 (void);
// 0x00000074 System.Boolean Firebase.Platform.FirebaseEditorDispatcher::get_EditorIsPlaying()
extern void FirebaseEditorDispatcher_get_EditorIsPlaying_m5D0E7984120FE00B82427E13FCF48085918AB1FD (void);
// 0x00000075 System.Boolean Firebase.Platform.FirebaseEditorDispatcher::get_EditorIsPlayingOrWillChangePlaymode()
extern void FirebaseEditorDispatcher_get_EditorIsPlayingOrWillChangePlaymode_mCAC767C009E07776825D231BDFF6C6D1AE119E40 (void);
// 0x00000076 System.Void Firebase.Platform.FirebaseEditorDispatcher::StartEditorUpdate()
extern void FirebaseEditorDispatcher_StartEditorUpdate_mC548F88144B19A23B440492095C0C961FA0D039C (void);
// 0x00000077 System.Void Firebase.Platform.FirebaseEditorDispatcher::StopEditorUpdate()
extern void FirebaseEditorDispatcher_StopEditorUpdate_m02B9EFE5073A720C65958245D6DB2EA60E452374 (void);
// 0x00000078 System.Void Firebase.Platform.FirebaseEditorDispatcher::Update()
extern void FirebaseEditorDispatcher_Update_mE95C0F6CAED8D25B3635F1F7D4A76C5ECE9FAAE9 (void);
// 0x00000079 System.Void Firebase.Platform.FirebaseEditorDispatcher::ListenToPlayState(System.Boolean)
extern void FirebaseEditorDispatcher_ListenToPlayState_m8B3B0DC0A552586D4BE740EFE73A430A2E83D8E3 (void);
// 0x0000007A System.Void Firebase.Platform.FirebaseEditorDispatcher::PlayModeStateChanged()
extern void FirebaseEditorDispatcher_PlayModeStateChanged_mD56F02631552ED79A4B296B857A09E937D1DCE98 (void);
// 0x0000007B System.Void Firebase.Platform.FirebaseEditorDispatcher::PlayModeStateChangedWithArg(T)
// 0x0000007C System.Void Firebase.Platform.FirebaseEditorDispatcher::AddRemoveCallbackToField(System.Reflection.FieldInfo,System.Action,System.Object,System.Boolean,System.String)
extern void FirebaseEditorDispatcher_AddRemoveCallbackToField_mA7E5F78B2F35D05DB49DD041E80BE767FF7834EE (void);
// 0x0000007D Firebase.Platform.IAppConfigExtensions Firebase.Platform.Default.AppConfigExtensions::get_Instance()
extern void AppConfigExtensions_get_Instance_mDA35AED29CF495A2DCCF5FB7B65BBB534192E8AB (void);
// 0x0000007E System.Void Firebase.Platform.Default.AppConfigExtensions::.ctor()
extern void AppConfigExtensions__ctor_m04A8807DCFD232502FB1E0C74B4E29ABF16F091C (void);
// 0x0000007F System.Void Firebase.Platform.Default.AppConfigExtensions::.cctor()
extern void AppConfigExtensions__cctor_m9927D7EDEC092FBB74745A94434790D209057819 (void);
// 0x00000080 System.Void Firebase.Platform.Default.SystemClock::.ctor()
extern void SystemClock__ctor_m99B061010072193DAF3F270722EEA129C960068A (void);
// 0x00000081 System.Void Firebase.Platform.Default.SystemClock::.cctor()
extern void SystemClock__cctor_m95490CBBCD17DDC68771336873D160F866822F5F (void);
// 0x00000082 Firebase.Platform.IAppConfigExtensions Firebase.Platform.Default.UnityConfigExtensions::get_DefaultInstance()
extern void UnityConfigExtensions_get_DefaultInstance_m7CD3CF215F26881AD5B856F2708F15A169D66472 (void);
// 0x00000083 System.Void Firebase.Platform.Default.UnityConfigExtensions::.ctor()
extern void UnityConfigExtensions__ctor_m1068123E08F326815ADFDB2A0104A7D83AD36B2C (void);
// 0x00000084 System.Void Firebase.Platform.Default.UnityConfigExtensions::.cctor()
extern void UnityConfigExtensions__cctor_mDEDBB398B807722C7F826561ACF1C8E215A3ECB6 (void);
static Il2CppMethodPointer s_methodPointers[132] = 
{
	ExceptionAggregator_get_Exceptions_m421E855AAB77ED0458E6FCBBB9575F0072411DFE,
	ExceptionAggregator_GetAndClearPendingExceptions_mE7FDFA220E3ACEB6D56D9884DEEF91540D2B8522,
	ExceptionAggregator_ThrowAndClearPendingExceptions_mC66983DEDF4880ACD5E96B5E9EBA38E00CC93B91,
	ExceptionAggregator_LogException_m68B2EB141A7DDD04EFEE794ED44FCB41673E19F2,
	ExceptionAggregator_Wrap_m24EBDCC97F8D88B56ADBD17EEF69F25CB3839415,
	ExceptionAggregator__cctor_m68C6741A43A5C18AC3DBBA1AC42105FC29CB5B43,
	Dispatcher__ctor_mE99B93199AD38F0A53946F925713CC84099C35B3,
	NULL,
	NULL,
	NULL,
	Dispatcher_ManagesThisThread_mDD4799366D040E2A27D9501C6AE8D2C88AAE086A,
	Dispatcher_PollJobs_m4DD47E8B63F33D975BC1A7D670A509F597F8E9E0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnityLoggingService_get_Instance_mCFC4AB732884A36484C59A3B06D70C568DFF9A78,
	UnityLoggingService__ctor_m40EA0D43E2C571DD040E6239AFFA67D4A16A258D,
	UnityLoggingService__cctor_mBE727CBA25AF8C39C91022BD50F0E336F14B2717,
	UnityPlatformServices_SetupServices_m4B3BAB80E61E32078E9005941D9C9BC69E7CF3D8,
	UnitySynchronizationContext__ctor_m16FA79368D74F4CCBF96D397B8C334CCC8A40720,
	UnitySynchronizationContext_Create_mE6147188A48A044C46BE9666824CAA1445B29BD8,
	UnitySynchronizationContext_Destroy_m158D9BC9BBF28C2A18D00AF0C8AAE025F9F5D394,
	UnitySynchronizationContext_GetThreadEvent_mC98D54E3250279ACF4A26392835855D729D729F0,
	UnitySynchronizationContext_Post_mFFC9993A213F2DE9D2AB3003272BD0D62486C6E6,
	UnitySynchronizationContext_Send_m0BDC4069F41293598BA84281FCBBA4C46DAC00A4,
	UnitySynchronizationContext__cctor_mD3303025BE218EE1787E91E30323A71276C90224,
	SynchronizationContextBehavoir_get_CallbackQueue_m0E34CE99C7CA3A50D0B5960110C97B35F1464181,
	SynchronizationContextBehavoir_Start_mC0020EEF1D711C56559978F6B05D5980607B1415,
	SynchronizationContextBehavoir__ctor_mB7DAD13667448BFE9813A53C0AEEA01D2ABB23E7,
	U3CStartU3Ed__3__ctor_mE286810BB74A14A6CA89DBD541556E73EE6F1873,
	U3CStartU3Ed__3_System_IDisposable_Dispose_mA3F0338173978F9E1F1C95BCD8A5C65C7DA5464B,
	U3CStartU3Ed__3_MoveNext_mF7311EB49D00661ED24EBB2AD6A43FC8FB69451E,
	U3CStartU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCB792B39126660A53266DF372A408359EFCE5CCA,
	U3CStartU3Ed__3_System_Collections_IEnumerator_Reset_m0700DB59F5C1825F093EE05044976C7C512A8026,
	U3CStartU3Ed__3_System_Collections_IEnumerator_get_Current_mDE0E18D92FEA57AF47CB64A273477B674A114DFD,
	U3CU3Ec__DisplayClass14_0__ctor_mBF75F76D3921A9186C2576BDC0158F2748855D64,
	U3CU3Ec__DisplayClass14_1__ctor_m7A98D58E7972709AD2351CD86F0C8EF10E75A5D1,
	U3CU3Ec__DisplayClass16_0__ctor_m864C9D4FA34AF22ECD795F3344D81BEDBA8BAE66,
	U3CU3Ec__DisplayClass16_1__ctor_mB967C90C84AC0EC6AE041D0ECEF6D1AB66069C80,
	U3CU3Ec__DisplayClass16_1_U3CSendU3Eb__0_mAC9FDB5AD8982ABA072566F9C51D439E1E01674B,
	DebugLogger_get_Instance_m0E521CDD954F86EB61792F19F2362373EB10745E,
	DebugLogger__ctor_mD6357FC79350285A4D8372BC692899939F6D36C4,
	DebugLogger__cctor_mD2949D665716DF02D7FD7F4C1181AD6525066903,
	Services__cctor_m7D026055BA78EEC691B309AA1A6702D54C34F366,
	Services_set_AppConfig_mE48F0E5E1FA561208AEBA714C1147ECD693AD402,
	Services_set_Clock_mBDFC2BAB8F8D2628AF8052A79796026E74DBA370,
	Services_set_Logging_m53140204CEF56D9BC71B5C5BA7BB9D6B3974FFD5,
	NULL,
	NULL,
	NULL,
	FirebaseAppUtilsStub_get_Instance_m980CDC684E1C5AE80F1853386A98DD558AE76ABC,
	FirebaseAppUtilsStub_TranslateDllNotFoundException_m05B83DDC2DB8B21CEFC55D0AA990CA16A9390C8E,
	FirebaseAppUtilsStub_PollCallbacks_m13A423E04EC1F3EDFD0D7FE63F18E0D3036346E1,
	FirebaseAppUtilsStub_GetLogLevel_m6082A2182D62F889C842C09F4B5A15074B8F4127,
	FirebaseAppUtilsStub__ctor_m3EC6F08162223655B3A0A2FA44BA7060AE3884AD,
	FirebaseAppUtilsStub__cctor_m5AD579715FDD6408AB8E44DC5F9856684B6B57B8,
	NULL,
	NULL,
	NULL,
	FirebaseHandler_get_AppUtils_mC39704DCB7BFD728B88A79C59DC50287679E47B0,
	FirebaseHandler_set_AppUtils_mCA47C4E5CB3698D8715B88CB409F5112C38A16D6,
	FirebaseHandler_get_TickCount_m5F47CB784234BDF946837F63C6E93291FA9973ED,
	FirebaseHandler_get_ThreadDispatcher_m8437519751496E4FC4AE6133C7F0C787B8117EAB,
	FirebaseHandler_set_ThreadDispatcher_mFF39382EA85A80364D3837E729F973D1602D6E39,
	FirebaseHandler_get_IsPlayMode_m59AFE9F48F2EA1AADCFBD94DBE010730359B3B40,
	FirebaseHandler_set_IsPlayMode_m832E6FCD6DA3ACDEA0B59D346EDD8585381C3B15,
	FirebaseHandler__cctor_m17F25FC2EE5E6E78B15620FCDE84BEF8625CE0F9,
	FirebaseHandler__ctor_mD79B2E4B647013911BE3A8602014EB108DD1D054,
	FirebaseHandler_StartMonoBehaviour_m3258056C31C8D32C690329363587720C60B35E0A,
	FirebaseHandler_StopMonoBehaviour_m1CB49D4E683D48586AEF5F8B515EAC41F2F502BE,
	NULL,
	NULL,
	FirebaseHandler_get_DefaultInstance_m5FA4E1EC809FA3727799598E906DDFE6BBFC3DC4,
	FirebaseHandler_CreatePartialOnMainThread_m5F79802A26DF07DAE2DFC2B250CDAB8DDFBC8C4B,
	FirebaseHandler_Create_m1B30952F7479043520E3D7023F331D038F6B2125,
	FirebaseHandler_Update_m054C867D529EEC80853232EEAFBCAD18CC287817,
	FirebaseHandler_OnApplicationFocus_mC750E0727206EA64FC16F8448D149EBF82C59F41,
	FirebaseHandler_OnMonoBehaviourDestroyed_m327EFE33762C7F1803DDD5C95103007322779A90,
	FirebaseHandler_U3CUpdateU3Eb__36_0_mDC0BB815941FD6EF6E26F6B31745BBB7E10B5335,
	ApplicationFocusChangedEventArgs_set_HasFocus_mDB3B76E13D221D0A0838877B9EE94299135B4CA1,
	ApplicationFocusChangedEventArgs__ctor_m2B84BC26D7A933044EAA1FBD78A65CB767E1C55C,
	U3CU3Ec__cctor_mC3A02F0B920A69CFEAEA3EC52F816DC97213C024,
	U3CU3Ec__ctor_m85629195FA2478A2E4784E8A66606324E6613A78,
	U3CU3Ec_U3CStopMonoBehaviourU3Eb__19_0_mF7FF24CAF2DBC2B8F724320506C358775DF8E7AE,
	U3CU3Ec__DisplayClass34_0__ctor_mC8EDEEF9AF53EC004805D7A0148D08C948F08796,
	U3CU3Ec__DisplayClass34_0_U3CCreatePartialOnMainThreadU3Eb__0_m1DDB57163B50F831CB7EC76D9D56CC066D858A1D,
	PlatformInformation_get_IsAndroid_mF6B9AE75F5E1D6A48F5E1754A692FDC167ADE628,
	PlatformInformation_get_IsIOS_m314080A5FF94D29E352F4E3FCA4870CBB67E3840,
	PlatformInformation_get_DefaultConfigLocation_mCC41CAB77CCA5F822E67C9B8D86478606777A06C,
	PlatformInformation_get_RealtimeSinceStartup_mC067B0FADDC1B86488739F7C484B231E0E586C55,
	PlatformInformation_set_RealtimeSinceStartupSafe_m2808F45B6549A0E0139BC36B236BC95213B123FC,
	PlatformInformation_get_RuntimeName_m7DC9B4B120826252A1359FBB00EEA9CCA0B9FE81,
	PlatformInformation_get_RuntimeVersion_m449590D737405693CC7C1AF5878FBAED5B49852F,
	U3CU3Ec__cctor_m33FC037E5010B06E6CDC3951D2E0C5E55F0BF174,
	U3CU3Ec__ctor_m100F86B3556423BD4A74464C139ECD9E6EEE7BC7,
	U3CU3Ec_U3Cget_DefaultConfigLocationU3Eb__6_0_m06C1966148257FDD30A2A8044489552FF4AC2366,
	U3CU3Ec_U3Cget_RuntimeVersionU3Eb__18_0_mBEBDF5B525CDCDB0120B5D7BECE6720DBB573738,
	FirebaseLogger_IsStackTraceLogTypeIncompatibleWithNativeLogs_m27CEB41C390C022D632065D0FBF4AF0E176ACD7A,
	FirebaseLogger_CurrentStackTraceLogTypeIsIncompatibleWithNativeLogs_m3C672C23E7A9E38397D742A1AA95732883E43C0B,
	FirebaseLogger_get_CanRedirectNativeLogs_mA3ED5F874C3D698FB2D1656B3B4E1EAFE257BBF4,
	FirebaseLogger_LogMessage_mA65FB8012E13AA044A6FF15E9792D2755AF84B86,
	FirebaseLogger__cctor_mE7B888BE9D8FD6CC2A7E7FB95B13368CFF5397EC,
	FirebaseMonoBehaviour_GetFirebaseHandlerOrDestroyGameObject_mE01D6AF26DA880E3BF6D8DAF231C270F0C9C002F,
	FirebaseMonoBehaviour_OnEnable_m18519EA6271E80120F01D31B3EE592127B48B72C,
	FirebaseMonoBehaviour_Update_m8338C29185F7348D4FC338194CBD3F285645A773,
	FirebaseMonoBehaviour_OnApplicationFocus_mC5EA1995ED7D3CB5F83902CFA519EC507E05EA8D,
	FirebaseMonoBehaviour_OnDestroy_m3FD94734285C9254480EF29135FA354D0E2292B8,
	FirebaseMonoBehaviour__ctor_m17C407059420FFEA2371846AD4279B80AF15B62E,
	FirebaseEditorDispatcher_get_EditorApplicationType_mD6FD666F69631C72E955CF09AD61929823D0B459,
	FirebaseEditorDispatcher_get_EditorIsPlaying_m5D0E7984120FE00B82427E13FCF48085918AB1FD,
	FirebaseEditorDispatcher_get_EditorIsPlayingOrWillChangePlaymode_mCAC767C009E07776825D231BDFF6C6D1AE119E40,
	FirebaseEditorDispatcher_StartEditorUpdate_mC548F88144B19A23B440492095C0C961FA0D039C,
	FirebaseEditorDispatcher_StopEditorUpdate_m02B9EFE5073A720C65958245D6DB2EA60E452374,
	FirebaseEditorDispatcher_Update_mE95C0F6CAED8D25B3635F1F7D4A76C5ECE9FAAE9,
	FirebaseEditorDispatcher_ListenToPlayState_m8B3B0DC0A552586D4BE740EFE73A430A2E83D8E3,
	FirebaseEditorDispatcher_PlayModeStateChanged_mD56F02631552ED79A4B296B857A09E937D1DCE98,
	NULL,
	FirebaseEditorDispatcher_AddRemoveCallbackToField_mA7E5F78B2F35D05DB49DD041E80BE767FF7834EE,
	AppConfigExtensions_get_Instance_mDA35AED29CF495A2DCCF5FB7B65BBB534192E8AB,
	AppConfigExtensions__ctor_m04A8807DCFD232502FB1E0C74B4E29ABF16F091C,
	AppConfigExtensions__cctor_m9927D7EDEC092FBB74745A94434790D209057819,
	SystemClock__ctor_m99B061010072193DAF3F270722EEA129C960068A,
	SystemClock__cctor_m95490CBBCD17DDC68771336873D160F866822F5F,
	UnityConfigExtensions_get_DefaultInstance_m7CD3CF215F26881AD5B856F2708F15A169D66472,
	UnityConfigExtensions__ctor_m1068123E08F326815ADFDB2A0104A7D83AD36B2C,
	UnityConfigExtensions__cctor_mDEDBB398B807722C7F826561ACF1C8E215A3ECB6,
};
static const int32_t s_InvokerIndices[132] = 
{
	3215,
	3215,
	3231,
	3069,
	3189,
	3231,
	1935,
	-1,
	-1,
	-1,
	1866,
	1935,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	3215,
	1935,
	3231,
	3231,
	1610,
	3189,
	3231,
	1900,
	934,
	934,
	3231,
	1900,
	1900,
	1935,
	1597,
	1935,
	1866,
	1900,
	1935,
	1900,
	1935,
	1935,
	1935,
	1935,
	1610,
	3215,
	1935,
	3231,
	3231,
	3189,
	3189,
	3189,
	1610,
	1935,
	1886,
	3215,
	1610,
	1935,
	1886,
	1935,
	3231,
	-1,
	-1,
	-1,
	3215,
	3189,
	3209,
	3215,
	3189,
	1866,
	1571,
	3231,
	1935,
	1935,
	1935,
	-1,
	-1,
	3215,
	3189,
	3189,
	1935,
	1571,
	3189,
	1935,
	1571,
	1935,
	3231,
	1935,
	1866,
	1935,
	1935,
	3203,
	3203,
	3215,
	3224,
	3193,
	3215,
	3215,
	3231,
	1935,
	1900,
	1900,
	2943,
	3203,
	3203,
	2902,
	3231,
	1900,
	1935,
	1935,
	1571,
	1935,
	1935,
	3215,
	3203,
	3203,
	3231,
	3231,
	3231,
	3181,
	3231,
	-1,
	2175,
	3215,
	1935,
	3231,
	1935,
	3231,
	3215,
	1935,
	3231,
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x02000005, { 21, 3 } },
	{ 0x02000006, { 24, 3 } },
	{ 0x02000019, { 27, 6 } },
	{ 0x06000008, { 0, 8 } },
	{ 0x06000009, { 8, 7 } },
	{ 0x0600000A, { 15, 6 } },
	{ 0x0600004D, { 33, 2 } },
	{ 0x0600004E, { 35, 2 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[37] = 
{
	{ (Il2CppRGCTXDataType)2, 575 },
	{ (Il2CppRGCTXDataType)3, 48 },
	{ (Il2CppRGCTXDataType)3, 4908 },
	{ (Il2CppRGCTXDataType)2, 711 },
	{ (Il2CppRGCTXDataType)3, 1048 },
	{ (Il2CppRGCTXDataType)3, 49 },
	{ (Il2CppRGCTXDataType)3, 1049 },
	{ (Il2CppRGCTXDataType)3, 1050 },
	{ (Il2CppRGCTXDataType)2, 576 },
	{ (Il2CppRGCTXDataType)3, 54 },
	{ (Il2CppRGCTXDataType)3, 13894 },
	{ (Il2CppRGCTXDataType)2, 2292 },
	{ (Il2CppRGCTXDataType)3, 10616 },
	{ (Il2CppRGCTXDataType)3, 55 },
	{ (Il2CppRGCTXDataType)3, 10617 },
	{ (Il2CppRGCTXDataType)3, 4907 },
	{ (Il2CppRGCTXDataType)3, 14526 },
	{ (Il2CppRGCTXDataType)2, 2293 },
	{ (Il2CppRGCTXDataType)3, 10618 },
	{ (Il2CppRGCTXDataType)3, 10619 },
	{ (Il2CppRGCTXDataType)3, 10620 },
	{ (Il2CppRGCTXDataType)3, 4915 },
	{ (Il2CppRGCTXDataType)3, 1052 },
	{ (Il2CppRGCTXDataType)3, 1051 },
	{ (Il2CppRGCTXDataType)3, 4916 },
	{ (Il2CppRGCTXDataType)3, 10627 },
	{ (Il2CppRGCTXDataType)3, 10626 },
	{ (Il2CppRGCTXDataType)3, 9356 },
	{ (Il2CppRGCTXDataType)2, 1034 },
	{ (Il2CppRGCTXDataType)3, 4911 },
	{ (Il2CppRGCTXDataType)3, 14199 },
	{ (Il2CppRGCTXDataType)3, 10680 },
	{ (Il2CppRGCTXDataType)3, 4912 },
	{ (Il2CppRGCTXDataType)3, 13888 },
	{ (Il2CppRGCTXDataType)3, 4909 },
	{ (Il2CppRGCTXDataType)3, 13891 },
	{ (Il2CppRGCTXDataType)3, 13895 },
};
extern const CustomAttributesCacheGenerator g_Firebase_Platform_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Firebase_Platform_CodeGenModule;
const Il2CppCodeGenModule g_Firebase_Platform_CodeGenModule = 
{
	"Firebase.Platform.dll",
	132,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	8,
	s_rgctxIndices,
	37,
	s_rgctxValues,
	NULL,
	g_Firebase_Platform_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
