﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>



// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Type[]
struct TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755;
// UnityEngine.AddComponentMenu
struct AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100;
// System.Reflection.Binder
struct Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30;
// System.Runtime.CompilerServices.CompilationRelaxationsAttribute
struct CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF;
// System.Runtime.CompilerServices.CompilerGeneratedAttribute
struct CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C;
// System.Diagnostics.DebuggableAttribute
struct DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B;
// System.Diagnostics.DebuggerHiddenAttribute
struct DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88;
// UnityEngine.HeaderAttribute
struct HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB;
// UnityEngine.HideInInspector
struct HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA;
// System.Runtime.CompilerServices.IteratorStateMachineAttribute
struct IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830;
// System.Reflection.MemberFilter
struct MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81;
// System.ObsoleteAttribute
struct ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671;
// UnityEngine.RangeAttribute
struct RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5;
// UnityEngine.RequireComponent
struct RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91;
// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute
struct RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80;
// UnityEngine.SerializeField
struct SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25;
// UnityEngine.SpaceAttribute
struct SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8;
// System.String
struct String_t;
// UnityEngine.TooltipAttribute
struct TooltipAttribute_t503A1598A4E68E91673758F50447D0EDFB95149B;
// System.Type
struct Type_t;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;

IL2CPP_EXTERN_C const RuntimeType* MeshFilter_t763BB2BBF3881176AD25E4570E6DD215BA0AA51A_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* MeshRenderer_tCD983A2F635E12BCB0BAA2E635D96A318757908B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* RCC_DashboardInputs_tD4F52EB04E26C10B5915DE0801BD467A16E57E34_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Rigidbody_t101F2E2F9F16E765A77429B2DE4527D2047A887A_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Text_t6A2339DA6C05AE2646FC1A6C8FCC127391BE7FA1_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* WheelCollider_t57B08104FE16DFC3BF72826F7A3CCB8477C01779_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// System.Object


// System.Attribute
struct Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71  : public RuntimeObject
{
public:

public:
};


// System.Reflection.MemberInfo
struct MemberInfo_t  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// UnityEngine.AddComponentMenu
struct AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String UnityEngine.AddComponentMenu::m_AddComponentMenu
	String_t* ___m_AddComponentMenu_0;
	// System.Int32 UnityEngine.AddComponentMenu::m_Ordering
	int32_t ___m_Ordering_1;

public:
	inline static int32_t get_offset_of_m_AddComponentMenu_0() { return static_cast<int32_t>(offsetof(AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100, ___m_AddComponentMenu_0)); }
	inline String_t* get_m_AddComponentMenu_0() const { return ___m_AddComponentMenu_0; }
	inline String_t** get_address_of_m_AddComponentMenu_0() { return &___m_AddComponentMenu_0; }
	inline void set_m_AddComponentMenu_0(String_t* value)
	{
		___m_AddComponentMenu_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_AddComponentMenu_0), (void*)value);
	}

	inline static int32_t get_offset_of_m_Ordering_1() { return static_cast<int32_t>(offsetof(AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100, ___m_Ordering_1)); }
	inline int32_t get_m_Ordering_1() const { return ___m_Ordering_1; }
	inline int32_t* get_address_of_m_Ordering_1() { return &___m_Ordering_1; }
	inline void set_m_Ordering_1(int32_t value)
	{
		___m_Ordering_1 = value;
	}
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Runtime.CompilerServices.CompilationRelaxationsAttribute
struct CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Int32 System.Runtime.CompilerServices.CompilationRelaxationsAttribute::m_relaxations
	int32_t ___m_relaxations_0;

public:
	inline static int32_t get_offset_of_m_relaxations_0() { return static_cast<int32_t>(offsetof(CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF, ___m_relaxations_0)); }
	inline int32_t get_m_relaxations_0() const { return ___m_relaxations_0; }
	inline int32_t* get_address_of_m_relaxations_0() { return &___m_relaxations_0; }
	inline void set_m_relaxations_0(int32_t value)
	{
		___m_relaxations_0 = value;
	}
};


// System.Runtime.CompilerServices.CompilerGeneratedAttribute
struct CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Diagnostics.DebuggerHiddenAttribute
struct DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// UnityEngine.HideInInspector
struct HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// System.ObsoleteAttribute
struct ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.ObsoleteAttribute::_message
	String_t* ____message_0;
	// System.Boolean System.ObsoleteAttribute::_error
	bool ____error_1;

public:
	inline static int32_t get_offset_of__message_0() { return static_cast<int32_t>(offsetof(ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671, ____message_0)); }
	inline String_t* get__message_0() const { return ____message_0; }
	inline String_t** get_address_of__message_0() { return &____message_0; }
	inline void set__message_0(String_t* value)
	{
		____message_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____message_0), (void*)value);
	}

	inline static int32_t get_offset_of__error_1() { return static_cast<int32_t>(offsetof(ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671, ____error_1)); }
	inline bool get__error_1() const { return ____error_1; }
	inline bool* get_address_of__error_1() { return &____error_1; }
	inline void set__error_1(bool value)
	{
		____error_1 = value;
	}
};


// UnityEngine.PropertyAttribute
struct PropertyAttribute_t4A352471DF625C56C811E27AC86B7E1CE6444052  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// UnityEngine.RequireComponent
struct RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Type UnityEngine.RequireComponent::m_Type0
	Type_t * ___m_Type0_0;
	// System.Type UnityEngine.RequireComponent::m_Type1
	Type_t * ___m_Type1_1;
	// System.Type UnityEngine.RequireComponent::m_Type2
	Type_t * ___m_Type2_2;

public:
	inline static int32_t get_offset_of_m_Type0_0() { return static_cast<int32_t>(offsetof(RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91, ___m_Type0_0)); }
	inline Type_t * get_m_Type0_0() const { return ___m_Type0_0; }
	inline Type_t ** get_address_of_m_Type0_0() { return &___m_Type0_0; }
	inline void set_m_Type0_0(Type_t * value)
	{
		___m_Type0_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_Type0_0), (void*)value);
	}

	inline static int32_t get_offset_of_m_Type1_1() { return static_cast<int32_t>(offsetof(RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91, ___m_Type1_1)); }
	inline Type_t * get_m_Type1_1() const { return ___m_Type1_1; }
	inline Type_t ** get_address_of_m_Type1_1() { return &___m_Type1_1; }
	inline void set_m_Type1_1(Type_t * value)
	{
		___m_Type1_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_Type1_1), (void*)value);
	}

	inline static int32_t get_offset_of_m_Type2_2() { return static_cast<int32_t>(offsetof(RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91, ___m_Type2_2)); }
	inline Type_t * get_m_Type2_2() const { return ___m_Type2_2; }
	inline Type_t ** get_address_of_m_Type2_2() { return &___m_Type2_2; }
	inline void set_m_Type2_2(Type_t * value)
	{
		___m_Type2_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_Type2_2), (void*)value);
	}
};


// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute
struct RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Boolean System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::m_wrapNonExceptionThrows
	bool ___m_wrapNonExceptionThrows_0;

public:
	inline static int32_t get_offset_of_m_wrapNonExceptionThrows_0() { return static_cast<int32_t>(offsetof(RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80, ___m_wrapNonExceptionThrows_0)); }
	inline bool get_m_wrapNonExceptionThrows_0() const { return ___m_wrapNonExceptionThrows_0; }
	inline bool* get_address_of_m_wrapNonExceptionThrows_0() { return &___m_wrapNonExceptionThrows_0; }
	inline void set_m_wrapNonExceptionThrows_0(bool value)
	{
		___m_wrapNonExceptionThrows_0 = value;
	}
};


// UnityEngine.SerializeField
struct SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Runtime.CompilerServices.StateMachineAttribute
struct StateMachineAttribute_tA6E77C77F821508E405473BA1C4C08A69FDA0AC3  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Type System.Runtime.CompilerServices.StateMachineAttribute::<StateMachineType>k__BackingField
	Type_t * ___U3CStateMachineTypeU3Ek__BackingField_0;

public:
	inline static int32_t get_offset_of_U3CStateMachineTypeU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(StateMachineAttribute_tA6E77C77F821508E405473BA1C4C08A69FDA0AC3, ___U3CStateMachineTypeU3Ek__BackingField_0)); }
	inline Type_t * get_U3CStateMachineTypeU3Ek__BackingField_0() const { return ___U3CStateMachineTypeU3Ek__BackingField_0; }
	inline Type_t ** get_address_of_U3CStateMachineTypeU3Ek__BackingField_0() { return &___U3CStateMachineTypeU3Ek__BackingField_0; }
	inline void set_U3CStateMachineTypeU3Ek__BackingField_0(Type_t * value)
	{
		___U3CStateMachineTypeU3Ek__BackingField_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CStateMachineTypeU3Ek__BackingField_0), (void*)value);
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// System.Reflection.BindingFlags
struct BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733 
{
public:
	// System.Int32 System.Reflection.BindingFlags::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// UnityEngine.HeaderAttribute
struct HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB  : public PropertyAttribute_t4A352471DF625C56C811E27AC86B7E1CE6444052
{
public:
	// System.String UnityEngine.HeaderAttribute::header
	String_t* ___header_0;

public:
	inline static int32_t get_offset_of_header_0() { return static_cast<int32_t>(offsetof(HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB, ___header_0)); }
	inline String_t* get_header_0() const { return ___header_0; }
	inline String_t** get_address_of_header_0() { return &___header_0; }
	inline void set_header_0(String_t* value)
	{
		___header_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___header_0), (void*)value);
	}
};


// System.Runtime.CompilerServices.IteratorStateMachineAttribute
struct IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830  : public StateMachineAttribute_tA6E77C77F821508E405473BA1C4C08A69FDA0AC3
{
public:

public:
};


// UnityEngine.RangeAttribute
struct RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5  : public PropertyAttribute_t4A352471DF625C56C811E27AC86B7E1CE6444052
{
public:
	// System.Single UnityEngine.RangeAttribute::min
	float ___min_0;
	// System.Single UnityEngine.RangeAttribute::max
	float ___max_1;

public:
	inline static int32_t get_offset_of_min_0() { return static_cast<int32_t>(offsetof(RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5, ___min_0)); }
	inline float get_min_0() const { return ___min_0; }
	inline float* get_address_of_min_0() { return &___min_0; }
	inline void set_min_0(float value)
	{
		___min_0 = value;
	}

	inline static int32_t get_offset_of_max_1() { return static_cast<int32_t>(offsetof(RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5, ___max_1)); }
	inline float get_max_1() const { return ___max_1; }
	inline float* get_address_of_max_1() { return &___max_1; }
	inline void set_max_1(float value)
	{
		___max_1 = value;
	}
};


// System.RuntimeTypeHandle
struct RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 
{
public:
	// System.IntPtr System.RuntimeTypeHandle::value
	intptr_t ___value_0;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9, ___value_0)); }
	inline intptr_t get_value_0() const { return ___value_0; }
	inline intptr_t* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(intptr_t value)
	{
		___value_0 = value;
	}
};


// UnityEngine.SpaceAttribute
struct SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8  : public PropertyAttribute_t4A352471DF625C56C811E27AC86B7E1CE6444052
{
public:
	// System.Single UnityEngine.SpaceAttribute::height
	float ___height_0;

public:
	inline static int32_t get_offset_of_height_0() { return static_cast<int32_t>(offsetof(SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8, ___height_0)); }
	inline float get_height_0() const { return ___height_0; }
	inline float* get_address_of_height_0() { return &___height_0; }
	inline void set_height_0(float value)
	{
		___height_0 = value;
	}
};


// UnityEngine.TooltipAttribute
struct TooltipAttribute_t503A1598A4E68E91673758F50447D0EDFB95149B  : public PropertyAttribute_t4A352471DF625C56C811E27AC86B7E1CE6444052
{
public:
	// System.String UnityEngine.TooltipAttribute::tooltip
	String_t* ___tooltip_0;

public:
	inline static int32_t get_offset_of_tooltip_0() { return static_cast<int32_t>(offsetof(TooltipAttribute_t503A1598A4E68E91673758F50447D0EDFB95149B, ___tooltip_0)); }
	inline String_t* get_tooltip_0() const { return ___tooltip_0; }
	inline String_t** get_address_of_tooltip_0() { return &___tooltip_0; }
	inline void set_tooltip_0(String_t* value)
	{
		___tooltip_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___tooltip_0), (void*)value);
	}
};


// System.Diagnostics.DebuggableAttribute/DebuggingModes
struct DebuggingModes_t279D5B9C012ABA935887CB73C5A63A1F46AF08A8 
{
public:
	// System.Int32 System.Diagnostics.DebuggableAttribute/DebuggingModes::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(DebuggingModes_t279D5B9C012ABA935887CB73C5A63A1F46AF08A8, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Diagnostics.DebuggableAttribute
struct DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Diagnostics.DebuggableAttribute/DebuggingModes System.Diagnostics.DebuggableAttribute::m_debuggingModes
	int32_t ___m_debuggingModes_0;

public:
	inline static int32_t get_offset_of_m_debuggingModes_0() { return static_cast<int32_t>(offsetof(DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B, ___m_debuggingModes_0)); }
	inline int32_t get_m_debuggingModes_0() const { return ___m_debuggingModes_0; }
	inline int32_t* get_address_of_m_debuggingModes_0() { return &___m_debuggingModes_0; }
	inline void set_m_debuggingModes_0(int32_t value)
	{
		___m_debuggingModes_0 = value;
	}
};


// System.Type
struct Type_t  : public MemberInfo_t
{
public:
	// System.RuntimeTypeHandle System.Type::_impl
	RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  ____impl_9;

public:
	inline static int32_t get_offset_of__impl_9() { return static_cast<int32_t>(offsetof(Type_t, ____impl_9)); }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  get__impl_9() const { return ____impl_9; }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 * get_address_of__impl_9() { return &____impl_9; }
	inline void set__impl_9(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  value)
	{
		____impl_9 = value;
	}
};

struct Type_t_StaticFields
{
public:
	// System.Reflection.MemberFilter System.Type::FilterAttribute
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterAttribute_0;
	// System.Reflection.MemberFilter System.Type::FilterName
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterName_1;
	// System.Reflection.MemberFilter System.Type::FilterNameIgnoreCase
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterNameIgnoreCase_2;
	// System.Object System.Type::Missing
	RuntimeObject * ___Missing_3;
	// System.Char System.Type::Delimiter
	Il2CppChar ___Delimiter_4;
	// System.Type[] System.Type::EmptyTypes
	TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* ___EmptyTypes_5;
	// System.Reflection.Binder System.Type::defaultBinder
	Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * ___defaultBinder_6;

public:
	inline static int32_t get_offset_of_FilterAttribute_0() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterAttribute_0)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterAttribute_0() const { return ___FilterAttribute_0; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterAttribute_0() { return &___FilterAttribute_0; }
	inline void set_FilterAttribute_0(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterAttribute_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterAttribute_0), (void*)value);
	}

	inline static int32_t get_offset_of_FilterName_1() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterName_1)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterName_1() const { return ___FilterName_1; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterName_1() { return &___FilterName_1; }
	inline void set_FilterName_1(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterName_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterName_1), (void*)value);
	}

	inline static int32_t get_offset_of_FilterNameIgnoreCase_2() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterNameIgnoreCase_2)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterNameIgnoreCase_2() const { return ___FilterNameIgnoreCase_2; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterNameIgnoreCase_2() { return &___FilterNameIgnoreCase_2; }
	inline void set_FilterNameIgnoreCase_2(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterNameIgnoreCase_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterNameIgnoreCase_2), (void*)value);
	}

	inline static int32_t get_offset_of_Missing_3() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Missing_3)); }
	inline RuntimeObject * get_Missing_3() const { return ___Missing_3; }
	inline RuntimeObject ** get_address_of_Missing_3() { return &___Missing_3; }
	inline void set_Missing_3(RuntimeObject * value)
	{
		___Missing_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Missing_3), (void*)value);
	}

	inline static int32_t get_offset_of_Delimiter_4() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Delimiter_4)); }
	inline Il2CppChar get_Delimiter_4() const { return ___Delimiter_4; }
	inline Il2CppChar* get_address_of_Delimiter_4() { return &___Delimiter_4; }
	inline void set_Delimiter_4(Il2CppChar value)
	{
		___Delimiter_4 = value;
	}

	inline static int32_t get_offset_of_EmptyTypes_5() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___EmptyTypes_5)); }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* get_EmptyTypes_5() const { return ___EmptyTypes_5; }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755** get_address_of_EmptyTypes_5() { return &___EmptyTypes_5; }
	inline void set_EmptyTypes_5(TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* value)
	{
		___EmptyTypes_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___EmptyTypes_5), (void*)value);
	}

	inline static int32_t get_offset_of_defaultBinder_6() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___defaultBinder_6)); }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * get_defaultBinder_6() const { return ___defaultBinder_6; }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 ** get_address_of_defaultBinder_6() { return &___defaultBinder_6; }
	inline void set_defaultBinder_6(Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * value)
	{
		___defaultBinder_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___defaultBinder_6), (void*)value);
	}
};

#ifdef __clang__
#pragma clang diagnostic pop
#endif



// System.Void System.Runtime.CompilerServices.CompilationRelaxationsAttribute::.ctor(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CompilationRelaxationsAttribute__ctor_mAC3079EBC4EEAB474EED8208EF95DB39C922333B (CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF * __this, int32_t ___relaxations0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute__ctor_m551DDF1438CE97A984571949723F30F44CF7317C (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::set_WrapNonExceptionThrows(System.Boolean)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, bool ___value0, const RuntimeMethod* method);
// System.Void System.Diagnostics.DebuggableAttribute::.ctor(System.Diagnostics.DebuggableAttribute/DebuggingModes)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebuggableAttribute__ctor_m7FF445C8435494A4847123A668D889E692E55550 (DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B * __this, int32_t ___modes0, const RuntimeMethod* method);
// System.Void UnityEngine.HeaderAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * __this, String_t* ___header0, const RuntimeMethod* method);
// System.Void UnityEngine.SerializeField::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3 (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * __this, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.CompilerGeneratedAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35 (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * __this, const RuntimeMethod* method);
// System.Void UnityEngine.RequireComponent::.ctor(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4 (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * __this, Type_t * ___requiredComponent0, const RuntimeMethod* method);
// System.Void UnityEngine.TooltipAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TooltipAttribute__ctor_m1839ACEC1560968A6D0EA55D7EB4535546588042 (TooltipAttribute_t503A1598A4E68E91673758F50447D0EDFB95149B * __this, String_t* ___tooltip0, const RuntimeMethod* method);
// System.Void UnityEngine.AddComponentMenu::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549 (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * __this, String_t* ___menuName0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.IteratorStateMachineAttribute::.ctor(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481 (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * __this, Type_t * ___stateMachineType0, const RuntimeMethod* method);
// System.Void System.Diagnostics.DebuggerHiddenAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3 (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * __this, const RuntimeMethod* method);
// System.Void UnityEngine.RangeAttribute::.ctor(System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000 (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * __this, float ___min0, float ___max1, const RuntimeMethod* method);
// System.Void UnityEngine.HideInInspector::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9 (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * __this, const RuntimeMethod* method);
// System.Void System.ObsoleteAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObsoleteAttribute__ctor_mAC32A5CCD287DA84CDA9F08282C1C8B0DB7B9868 (ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671 * __this, String_t* ___message0, const RuntimeMethod* method);
// System.Void UnityEngine.SpaceAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * __this, const RuntimeMethod* method);
static void AssemblyU2DCSharp_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF * tmp = (CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF *)cache->attributes[0];
		CompilationRelaxationsAttribute__ctor_mAC3079EBC4EEAB474EED8208EF95DB39C922333B(tmp, 8LL, NULL);
	}
	{
		RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * tmp = (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 *)cache->attributes[1];
		RuntimeCompatibilityAttribute__ctor_m551DDF1438CE97A984571949723F30F44CF7317C(tmp, NULL);
		RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline(tmp, true, NULL);
	}
	{
		DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B * tmp = (DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B *)cache->attributes[2];
		DebuggableAttribute__ctor_m7FF445C8435494A4847123A668D889E692E55550(tmp, 2LL, NULL);
	}
}
static void SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_beamLineRendererPrefab(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x50\x72\x65\x66\x61\x62\x73"), NULL);
	}
}
static void SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_beamEndOffset(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x41\x64\x6A\x75\x73\x74\x61\x62\x6C\x65\x20\x56\x61\x72\x69\x61\x62\x6C\x65\x73"), NULL);
	}
}
static void SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_endOffSetSlider(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x50\x75\x74\x20\x53\x6C\x69\x64\x65\x72\x73\x20\x68\x65\x72\x65\x20\x28\x4F\x70\x74\x69\x6F\x6E\x61\x6C\x29"), NULL);
	}
}
static void SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_textBeamName(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x50\x75\x74\x20\x55\x49\x20\x54\x65\x78\x74\x20\x6F\x62\x6A\x65\x63\x74\x20\x68\x65\x72\x65\x20\x74\x6F\x20\x73\x68\x6F\x77\x20\x62\x65\x61\x6D\x20\x6E\x61\x6D\x65"), NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_screenOrientation(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_showAppOpenAd(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobBannerIds(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobInterstitialIds(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobRewardedInterstitialID(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobRewardedVideoID(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_appOpenAdID(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_ShowTestAds(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CInitializeAdmobU3Eb__100_0_mF577590EF5F11DF96E37B33EEA5C71DA2D8AEC40(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom0U3Eb__147_0_mBDEDAF6696EFBC73EF156014A62FDA2F4AD947B7(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom1U3Eb__148_0_m8EEBB36A49F7E395FCE0A0AA027C77C8871B29D9(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom2U3Eb__149_0_m2CCA33817CCD0488526FEE4AD3F2455634A28555(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom3U3Eb__150_0_m0EF8B952E0E063BF6AE3B0B2C0EA691ABFF194EC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom4U3Eb__151_0_m2B74D4185FF4066E99619AC6865D404EC90AF29B(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents0U3Eb__157_0_m714D425E028864A4322427107A7B3665C695FB95(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents0U3Eb__157_1_m27F080C096CD99A962D07567E25302E752537804(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents1U3Eb__158_0_m08D3A39AC12EED321E5528B61E0FEE4A603FC43F(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents1U3Eb__158_1_m41B4A5503A31428083DB809226A94D26751B4FE4(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents2U3Eb__159_0_mBFCF289E0DCDC4004398B12F160BF4BC21AE4049(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents2U3Eb__159_1_m8441521F95ABB7A0ABEBE38658C71A58C02E5200(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents3U3Eb__160_0_m0798E776B41548D1E08C1F73CC605F60D57605EC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents3U3Eb__160_1_m1A3526D78454752D5A8AA0029BA1472D6511EDC5(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents4U3Eb__161_0_m244035B19BFCD05D5CB7C5FDAABF6D427C7D11E5(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents4U3Eb__161_1_m0059419AFA9A891B2C28977DCF1CDC1EBB46DCA1(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadAdmobRewardedInterstitialU3Eb__168_0_m66C00DEC641BC221A256D8DCBCBDD7AA8F11D05B(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_0_m097621CA9ECA00FE09EBD8AE84210F0DE215DE3D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_1_mCBBEEB536D155E2A27845760525C2FC7DB885D93(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CLoadAppOpenAdU3Eb__175_0_m9A72DAD7C216B659D391C69C6615FBDE5EDB5924(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_0_m606895315A0B8258891E783CAEC43339AD0D0C8E(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_1_m2009D1B41B4DD4EBFBE781876AA80D153E8529E9(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_2_m9174B75FA8AC1D6E6F4C39A082F4EC1209677853(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CLoadVideoADU3Eb__180_0_m42A6234A90D26054953322931FDA7918B423852D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_0_m6ED4779AD56F7554C6E523BC2EB21274138EE9A9(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_1_mDB2D1DE644BA6DE800E6E24443218E3E9685965B(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CU3Ec__DisplayClass111_0_t93080340976256D9511F5C9829A579F442BC2A48_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CU3Ec_t64B6A3EC25BDB1B4BFEF24D0D15140329BF99687_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CU3Ec__DisplayClass3_0_t59C2D70F82DD049282C333BD6105D43F5F039728_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MeshFilter_t763BB2BBF3881176AD25E4570E6DD215BA0AA51A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MeshRenderer_tCD983A2F635E12BCB0BAA2E635D96A318757908B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[0];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(MeshRenderer_tCD983A2F635E12BCB0BAA2E635D96A318757908B_0_0_0_var), NULL);
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[1];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(MeshFilter_t763BB2BBF3881176AD25E4570E6DD215BA0AA51A_0_0_0_var), NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_createMultiMaterialMesh(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_combineInactiveChildren(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_deactivateCombinedChildren(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_deactivateCombinedChildrenMeshRenderers(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_generateUVMap(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_destroyCombinedChildren(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_folderPath(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_meshFiltersToSkip(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
	{
		TooltipAttribute_t503A1598A4E68E91673758F50447D0EDFB95149B * tmp = (TooltipAttribute_t503A1598A4E68E91673758F50447D0EDFB95149B *)cache->attributes[1];
		TooltipAttribute__ctor_m1839ACEC1560968A6D0EA55D7EB4535546588042(tmp, il2cpp_codegen_string_new_wrapper("\x4D\x65\x73\x68\x46\x69\x6C\x74\x65\x72\x73\x20\x77\x69\x74\x68\x20\x4D\x65\x73\x68\x65\x73\x20\x77\x68\x69\x63\x68\x20\x77\x65\x20\x64\x6F\x6E\x27\x74\x20\x77\x61\x6E\x74\x20\x74\x6F\x20\x63\x6F\x6D\x62\x69\x6E\x65\x20\x69\x6E\x74\x6F\x20\x6F\x6E\x65\x20\x4D\x65\x73\x68\x2E"), NULL);
	}
}
static void U3CU3Ec__DisplayClass33_0_t625D1FF1495408366D10B041997D39A1E07A4B4C_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CU3Ec__DisplayClass33_1_t47917A8CA5B35A825FA0802DA1F60C2FB0051AE3_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CU3Ec_tC607834D420DED2B2B095D41BAB8005F418C3722_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AIBrakeZone_tA4CB5237B9A2722D09DC8C10F794D33FB3412252_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x41\x49\x2F\x52\x43\x43\x20\x41\x49\x20\x42\x72\x61\x6B\x65\x20\x5A\x6F\x6E\x65"), NULL);
	}
}
static void RCC_AIBrakeZonesContainer_t49DA4ABE34EB66A9F12E55C439A8B83CD06F144A_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x41\x49\x2F\x52\x43\x43\x20\x41\x49\x20\x42\x72\x61\x6B\x65\x20\x5A\x6F\x6E\x65\x73\x20\x43\x6F\x6E\x74\x61\x69\x6E\x65\x72"), NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x41\x49\x2F\x52\x43\x43\x20\x41\x49\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72"), NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_OnRCCAISpawned(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_OnRCCAIDestroyed(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_add_OnRCCAISpawned_m3BBD0EA7218909E9EC80375B0554ED8B038802B4(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_remove_OnRCCAISpawned_m12038CFCBA20EF03A2964B286F80A3B7E6482E36(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_add_OnRCCAIDestroyed_mB643B14ED1DF88403006531517DB79B66D111908(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_remove_OnRCCAIDestroyed_m560E810569BF799FDE187E14A5191DC49EADA4C2(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_AIWaypointsContainer_t316BD7A22115F53AB44A6A8C5F267E1347103CB8_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x41\x49\x2F\x52\x43\x43\x20\x41\x49\x20\x57\x61\x79\x70\x6F\x69\x6E\x74\x73\x20\x43\x6F\x6E\x74\x61\x69\x6E\x65\x72"), NULL);
	}
}
static void RCC_Caliper_tB450B6F91CFA111A6750E30770B221E570FA83F9_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x56\x69\x73\x75\x61\x6C\x20\x42\x72\x61\x6B\x65\x20\x43\x61\x6C\x69\x70\x65\x72"), NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x43\x61\x6D\x65\x72\x61\x2F\x52\x43\x43\x20\x43\x61\x6D\x65\x72\x61"), NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_OnBCGCameraSpawned(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_add_OnBCGCameraSpawned_m549CD035B0CD04B8480C8180120D4DB55F875854(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_remove_OnBCGCameraSpawned_m48CF21668CA531B403CBA86D6255D1B0E61A14B9(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_m81900DC1EFC048D1FEB5DDC99C0EE145AEB2C2FD(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_0_0_0_var), NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_mF615890DD4E3C817537453620BA1C4541FACA563(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_0_0_0_var), NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_m6605134E41B5EA121A4E35112EA369D026476F3C(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_0_0_0_var), NULL);
	}
}
static void RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_mC500C30540E4A110FFA05675A12DA784F4BB0B44(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_0_0_0_var), NULL);
	}
}
static void U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103__ctor_mB86DAE1538A1817A4DE2965F70A7554F43963218(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_IDisposable_Dispose_m5BB60050071E0246912AB3BDEA18A1C6D632BC82(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m994C4DFCB355539E7E93873A49246D0A88AFE3B0(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_Reset_m9829DB058D07F760EBBFC42C7178D3C8A7CCB4F0(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_get_Current_m94AE930C9BC639C3E657621CA82377820CDAB375(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104__ctor_mFEFEB8210262FB261D9927ACB4922D120C7BE39F(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_IDisposable_Dispose_m1E79DE15E4F22EAA324B8B6907B6DAFFFD02CBD7(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3B2A71C4778AFB90E1632278C7A4BDF8AA1E33BA(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_Reset_m019D201542FE2E5E4FF8C0ECD1E4CF7F113208C5(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_get_Current_mA3C32942E6C8F8367E80DFA28F7F43687BBEC68C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105__ctor_m857814C291E57FD0CE48F0CC72F0A75B8677C5A7(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_IDisposable_Dispose_m5F30716C2F6EA674FE1D07F890F36ECEC8835154(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43C55302F19223A3F9E6A753C1E33495E15D5334(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_Reset_m37567EB21D0C21FBAF45612A28B6D990F8473682(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_get_Current_m1303A4AFE8A3BEA65829C1A2ECB0A431CBC3B22D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106__ctor_mD1116B9E48DA8B41D2403A93A8D7AA21FCD0D0AD(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_IDisposable_Dispose_mC71F8B2F7180442670680DD6C0FA2638D844B9D6(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB71E03BE2D95E1613CDB315DC1D0F856FD2B0CDE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_Reset_m72A38018ED33FC3DAB06663FA585036AB746828E(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_get_Current_m356DEFF51672783BA4A8008CCF6B9B8F7FB829FC(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody_t101F2E2F9F16E765A77429B2DE4527D2047A887A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x61\x69\x6E\x2F\x52\x43\x43\x20\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x20\x56\x33"), NULL);
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[1];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(Rigidbody_t101F2E2F9F16E765A77429B2DE4527D2047A887A_0_0_0_var), NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_biasedWheelTorque(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 100.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_engineInertia(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.75f, 2.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steeringSensitivity(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 1.0f, 15.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_gearShiftingDelay(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 0.5f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_gearShiftingThreshold(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.25f, 0.800000012f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_clutchInertia(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.100000001f, 0.899999976f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_minEngineSoundPitch(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_maxEngineSoundPitch(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 1.0f, 2.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_minEngineSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_maxEngineSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_idleEngineSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_gasInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_brakeInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steerInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_clutchInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_handbrakeInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_boostInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_idleInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_fuelInput(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_cutGas(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_ABSThreshold(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0500000007f, 0.5f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_TCSThreshold(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0500000007f, 0.5f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_TCSStrength(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0500000007f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_ESPThreshold(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0500000007f, 0.5f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_ESPStrength(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0500000007f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steerHelperLinearVelStrength(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steerHelperAngularVelStrength(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_tractionHelperStrength(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_angularDragHelperStrength(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_counterSteeringFactor(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_OnRCCPlayerSpawned(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_OnRCCPlayerDestroyed(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_OnRCCPlayerCollision(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_add_OnRCCPlayerSpawned_mFDE1F6DAD5D918C29976EE4998677077769E45A7(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_remove_OnRCCPlayerSpawned_m59CF492DA2E14D35290ECA32A6506719A0A9CCD8(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_add_OnRCCPlayerDestroyed_m1DE2E27CCB341E870B1A3112B65F6706C3B0F03A(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_remove_OnRCCPlayerDestroyed_m638B2F4306EDFC49B3E1B44E943E79547EEF0B04(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_add_OnRCCPlayerCollision_m4DC0247EA3B605AFE3088CB97148583553DB95A7(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_remove_OnRCCPlayerCollision_m5FC92A8B329061A31DD477FCE63991D12E893771(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_RCCPlayerSpawned_mEFB68FDC59E48F6F7B81FD56D502D3EAF80B3EA7(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_0_0_0_var), NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_StartEngineDelayed_m700F24D67534B52A4C3D899D4F4C0F3DE321893A(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_0_0_0_var), NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_ChangeGear_m4C80019C3A3C73BC9A4B97A683246C2241563FAB(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_0_0_0_var), NULL);
	}
}
static void RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB____AIController_PropertyInfo(CustomAttributesCache* cache)
{
	{
		ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671 * tmp = (ObsoleteAttribute_t14BAC1669C0409EB9F28D72D664FFA6764ACD671 *)cache->attributes[0];
		ObsoleteAttribute__ctor_mAC32A5CCD287DA84CDA9F08282C1C8B0DB7B9868(tmp, il2cpp_codegen_string_new_wrapper("\x57\x61\x72\x6E\x69\x6E\x67\x20\x27\x41\x49\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x27\x20\x69\x73\x20\x6F\x62\x73\x6F\x6C\x65\x74\x65\x3A\x20\x27\x50\x6C\x65\x61\x73\x65\x20\x75\x73\x65\x20\x65\x78\x74\x65\x72\x6E\x61\x6C\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2E"), NULL);
	}
}
static void U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245__ctor_m84F6C98EF2E1E6AB823D57CCD4F336C37681C2F3(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_IDisposable_Dispose_m0D02C0D246B87634EE283883DF60CC2ADEC78D47(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0953BFC21C7566C1EA4C20B5F2FAFDB84CABECCC(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_Reset_m9BB8908191673DFD4029B0EFD2576BAF9F4F55AD(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_get_Current_m30EE7661ACAFF1FF3B5242188997BEC07415401E(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254__ctor_m01C19BCD75D3AD447DDB684436968DFD8D5CF58D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_IDisposable_Dispose_m1C21109B2BD3F928F6808B8E168E9FD976B97E7F(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mAADA60BDC6AFDB619885F62973196BA3CA9183AE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_Reset_mE1CE9FCC838DAB413B5F31FDC43D874722C29EA8(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_get_Current_m77C2F1F6912869036D311A198CDD6F30CBA657E0(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274__ctor_m42019740291409B8CA24D09A7ED3645BDE11DF87(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_IDisposable_Dispose_mD457816E6E4533B0AE7AFF0E6933D83B455898D2(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0A82C933A082D8DA6B5144E20C9B5778342B4544(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_Collections_IEnumerator_Reset_m6632FDBB744826C15C1B065954F1523339070A79(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_Collections_IEnumerator_get_Current_mA431DD0D991872FDA3AF4F7449666D51F3057CC7(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_CharacterController_tF95DD1DCD4775FD03CFA4B37B82573B779930C2E_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x41\x6E\x69\x6D\x61\x74\x6F\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72"), NULL);
	}
}
static void RCC_Chassis_tA0F6A482E38EEE438DD7A6F5150AE9F6269F4229_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x43\x68\x61\x73\x73\x69\x73"), NULL);
	}
}
static void RCC_Chassis_tA0F6A482E38EEE438DD7A6F5150AE9F6269F4229_CustomAttributesCacheGenerator_RCC_Chassis_ReEnable_mDC685A708AA2048130E56BAEF2A9564648B41D8B(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_0_0_0_var), NULL);
	}
}
static void U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11__ctor_mECB7864140A80DFBD981DD0D1E32A9C657A9EE46(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_IDisposable_Dispose_mACA2471CAD921534B9E0F254D841DED400E29576(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFA07227D312084BF51EB4BA81ECE8C173C0F0C4C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_Collections_IEnumerator_Reset_m848803CEDDC57D5DC87E5F9B21710A13560AEABE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_Collections_IEnumerator_get_Current_m239E706369E3C28CF8DD3218CC0A6BFFFE82D697(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_CinematicCamera_t1AD76F879C5B66AEB260127EFF15191031BA9C37_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x43\x61\x6D\x65\x72\x61\x2F\x52\x43\x43\x20\x43\x69\x6E\x65\x6D\x61\x74\x69\x63\x20\x43\x61\x6D\x65\x72\x61"), NULL);
	}
}
static void RCC_ColorPickerBySliders_t36A18A4D19E929A1F16FDBF5E3F587829CABED80_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x43\x6F\x6C\x6F\x72\x20\x50\x69\x63\x6B\x65\x72\x20\x42\x79\x20\x55\x49\x20\x53\x6C\x69\x64\x65\x72\x73"), NULL);
	}
}
static void RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x43\x75\x73\x74\x6F\x6D\x69\x7A\x65\x72\x20\x45\x78\x61\x6D\x70\x6C\x65"), NULL);
	}
}
static void RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_wheelsMenu(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x55\x49\x20\x4D\x65\x6E\x75\x73"), NULL);
	}
}
static void RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_frontCamber(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x55\x49\x20\x53\x6C\x69\x64\x65\x72\x73"), NULL);
	}
}
static void RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_TCS(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x55\x49\x20\x54\x6F\x67\x67\x6C\x65\x73"), NULL);
	}
}
static void RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_maxSpeed(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x55\x49\x20\x49\x6E\x70\x75\x74\x46\x69\x65\x6C\x64\x73"), NULL);
	}
}
static void RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_drivetrainMode(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x55\x49\x20\x44\x72\x6F\x70\x64\x6F\x77\x6E\x20\x4D\x65\x6E\x75\x73"), NULL);
	}
}
static void RCC_DashboardColors_tCBE68324FC94E074074360571FB98CFFDAA59BC2_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x55\x49\x20\x44\x61\x73\x68\x62\x6F\x61\x72\x64\x20\x43\x6F\x6C\x6F\x72\x73"), NULL);
	}
}
static void RCC_DashboardInputs_tD4F52EB04E26C10B5915DE0801BD467A16E57E34_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x55\x49\x20\x44\x61\x73\x68\x62\x6F\x61\x72\x64\x20\x49\x6E\x70\x75\x74\x73"), NULL);
	}
}
static void RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x56\x69\x73\x75\x61\x6C\x20\x44\x61\x73\x68\x62\x6F\x61\x72\x64\x20\x4F\x62\x6A\x65\x63\x74\x73"), NULL);
	}
}
static void RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_rPMDial(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_speedDial(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_fuelDial(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_heatDial(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_interiorLights(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void RCC_Demo_t2E6DB6845E44CFB1FB1189F4E70FE5C7246B61A6_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x44\x65\x6D\x6F\x20\x4D\x61\x6E\x61\x67\x65\x72"), NULL);
	}
}
static void RCC_Demo_t2E6DB6845E44CFB1FB1189F4E70FE5C7246B61A6_CustomAttributesCacheGenerator_selectableVehicles(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x53\x70\x61\x77\x6E\x61\x62\x6C\x65\x20\x56\x65\x68\x69\x63\x6C\x65\x73"), NULL);
	}
}
static void RCC_Exhaust_t312041844622521D4098B5D8F8DFD086375938C5_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x45\x78\x68\x61\x75\x73\x74"), NULL);
	}
}
static void RCC_FixedCamera_tE79ACA4DDA21344137A0D195172D72F6CEF9EE2E_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x43\x61\x6D\x65\x72\x61\x2F\x52\x43\x43\x20\x46\x69\x78\x65\x64\x20\x43\x61\x6D\x65\x72\x61"), NULL);
	}
}
static void GroundMaterialFrictions_t7651DDF614C275421B32CFE9FF0E3C5A5D776994_CustomAttributesCacheGenerator_volume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_HoodCamera_tD5E7DA2C38265724870BA409EA86E9742E1880B2_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x43\x61\x6D\x65\x72\x61\x2F\x52\x43\x43\x20\x48\x6F\x6F\x64\x20\x43\x61\x6D\x65\x72\x61"), NULL);
	}
}
static void RCC_HoodCamera_tD5E7DA2C38265724870BA409EA86E9742E1880B2_CustomAttributesCacheGenerator_RCC_HoodCamera_FixShakeDelayed_mD689A3D2A58A116F11925616EE76776FE4E90659(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_0_0_0_var), NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1__ctor_mAE1EA1789C6C9E6B76669634CF665CC58511FAF5(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m059CF06692AD6C920357FDF367C809C9B6621731(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC7026D71FC9F1BF49D5C0A39EFE43CFBBFAF5ABA(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m7675E5E8EE65BB02E4CE3266F68749E5AD3EC311(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m6465D6EB3B8B010CF242BC740AE221BF2F76BE8D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_InfoLabel_t6A19CE611168F5435C1F981218D38F32B3BE1626_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Text_t6A2339DA6C05AE2646FC1A6C8FCC127391BE7FA1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x55\x49\x20\x49\x6E\x66\x6F\x20\x44\x69\x73\x70\x6C\x61\x79\x65\x72"), NULL);
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[1];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(Text_t6A2339DA6C05AE2646FC1A6C8FCC127391BE7FA1_0_0_0_var), NULL);
	}
}
static void RCC_InfoLabel_t6A19CE611168F5435C1F981218D38F32B3BE1626_CustomAttributesCacheGenerator_RCC_InfoLabel_ShowInfoCo_m66C62CE2BCD816FD11A849C7150D82225C8EBABB(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_0_0_0_var), NULL);
	}
}
static void U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8__ctor_m754A108198109E44982129332FE0725F84334DBF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_IDisposable_Dispose_m26676BBDB19173609B45E7F970C02E28E6259337(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7B0CEAF5DD5A83C6EC8337121F9CA17390640609(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_Reset_m4AF8C4E672A789A3B4A328D5FDEC475DB50B9611(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_get_Current_m1ADE673386CC8EADC5576419114A9AF413936423(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_Light_tC82A4EC473CAEE4A2FF7A2E46B8BE313F6D58E91_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4C\x69\x67\x68\x74\x2F\x52\x43\x43\x20\x4C\x69\x67\x68\x74"), NULL);
	}
}
static void RCC_LightEmission_t0565CFA014750B6CB3742874C60C39E6226A5F51_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4C\x69\x67\x68\x74\x2F\x52\x43\x43\x20\x4C\x69\x67\x68\x74\x20\x45\x6D\x69\x73\x73\x69\x6F\x6E"), NULL);
	}
}
static void RCC_Mirror_t08CF543D7B45C73DEE3876A206E1A90DF1D315BA_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x4D\x69\x72\x72\x6F\x72"), NULL);
	}
}
static void RCC_Mirror_t08CF543D7B45C73DEE3876A206E1A90DF1D315BA_CustomAttributesCacheGenerator_RCC_Mirror_FixDepth_m6D176AE51609272DAA74B49F80D521584B9E2ADC(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_0_0_0_var), NULL);
	}
}
static void U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4__ctor_mFD7A770C3B171A8A9EAE242A7BDAB7756F588309(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_IDisposable_Dispose_mF0F38EF8288B4076B7D0573E37B2B46DD1D4025F(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m359B793B68B60ED3881A48997B33E23942CA5184(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_Collections_IEnumerator_Reset_m389126C8BBAE3FFD380EE405A68318CC88493E15(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_Collections_IEnumerator_get_Current_mE23AD3527CBF07E20752D481DFD3DB2D0EEC3A47(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_MobileButtons_t696DEEE1E2B18E07B8506BAA7D6F18EA34FDB67B_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x4D\x6F\x62\x69\x6C\x65\x2F\x52\x43\x43\x20\x55\x49\x20\x4D\x6F\x62\x69\x6C\x65\x20\x42\x75\x74\x74\x6F\x6E\x73"), NULL);
	}
}
static void RCC_MobileUIDrag_tC079FEEC88381E161AE683BBA380CC907B61F70C_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x4D\x6F\x62\x69\x6C\x65\x2F\x52\x43\x43\x20\x55\x49\x20\x44\x72\x61\x67"), NULL);
	}
}
static void RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x52\x65\x63\x6F\x72\x64\x65\x72"), NULL);
	}
}
static void RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator_RCC_Recorder_Replay_mCB8F543BB938360131A26B17032F2D9E4E84D13B(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_0_0_0_var), NULL);
	}
}
static void RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator_RCC_Recorder_Repos_mACEA28AD16398978903BF022E8792B9E61156A1B(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_0_0_0_var), NULL);
	}
}
static void RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator_RCC_Recorder_Revel_mD605F0CBA5107BFBD8A1F9AD191ECCF9B1DF95F2(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_0_0_0_var), NULL);
	}
}
static void Recorded_t626684EBAC57FEEF50CE5EAFEDA7224D2D91D0B7_CustomAttributesCacheGenerator_inputs(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void Recorded_t626684EBAC57FEEF50CE5EAFEDA7224D2D91D0B7_CustomAttributesCacheGenerator_transforms(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void Recorded_t626684EBAC57FEEF50CE5EAFEDA7224D2D91D0B7_CustomAttributesCacheGenerator_rigids(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16__ctor_mB9F8BF6A7961FC0BCDE24595914C6CDD4185DA2D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_IDisposable_Dispose_mA568CC957DDCF7AEA1EC83C6014EA817142AC99D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB652DB22113A751F5E62C0118A89C0A7852A9DCA(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_Collections_IEnumerator_Reset_mBD22EE0F1A4885817E0B124AD841CD0722581351(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_Collections_IEnumerator_get_Current_mA572D3B3D2BD541645BBAE9D72A8AAF66B6869B1(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17__ctor_m0321A3A30C4717AAEB9D7837F58F67D03513E459(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_IDisposable_Dispose_mDA410B5BB748060FCF2F80273D2E9D13E2309201(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEB158A68D5082D6CF6388D0B2B8BDB5435AFD296(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_Collections_IEnumerator_Reset_m451D855D4F5F9A10588987393698DDC05DA5C61C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_Collections_IEnumerator_get_Current_mD260B401F286E2C92C928D9F687AF973A052C378(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18__ctor_mA59F93C7334102341D5DB01F68CE2B32648C2B41(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_IDisposable_Dispose_m972AAB5504AC3EDBE56D21DB91C6679BE1899018(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB3545751513147303B9DC59D51AB7493A296931B(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_Collections_IEnumerator_Reset_m4BA9ED55300401BD6803495E21E63F5C89EBF25B(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_Collections_IEnumerator_get_Current_m3117313D81F41949A5A3DE9640715A179B974331(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x61\x69\x6E\x2F\x52\x43\x43\x20\x53\x63\x65\x6E\x65\x20\x4D\x61\x6E\x61\x67\x65\x72"), NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_OnMainControllerChanged(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_OnBehaviorChanged(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_OnVehicleChanged(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_add_OnMainControllerChanged_m89A15548A2B46FBBF43DA08C9DE5AE0DED73475C(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_remove_OnMainControllerChanged_mED16E813B18487627B068BC21ED9245A171DED93(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_add_OnBehaviorChanged_mAEC81E82068FEF375971E4CDA90E7804D1EA9D64(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_remove_OnBehaviorChanged_mC9EC4327421E5E6DB88D947DFBEF9B11F7786AF1(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_add_OnVehicleChanged_m5A750372D5BAE35948ABD7F5C464D4A4DE8E667E(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_remove_OnVehicleChanged_m05DCEFCDC113B7D0F6FFDD6D231AE5D31FA76F37(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_fixedTimeStep(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.00499999989f, 0.0599999987f, NULL);
	}
}
static void RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxAngularVelocity(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.5f, 20.0f, NULL);
	}
}
static void RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxGearShiftingSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxCrashSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxWindSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxBrakeSoundVolume(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steeringHelper(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x53\x74\x65\x65\x72\x69\x6E\x67\x20\x48\x65\x6C\x70\x65\x72\x73"), NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperAngularVelStrengthMinimum(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[1];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperAngularVelStrengthMaximum(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperLinearVelStrengthMinimum(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperLinearVelStrengthMaximum(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_tractionHelperStrengthMinimum(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_tractionHelperStrengthMaximum(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_antiRollFrontHorizontalMinimum(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_gearShiftingDelayMaximum(CustomAttributesCache* cache)
{
	{
		SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 * tmp = (SpaceAttribute_t041FADA1DC4DD39BBDEBC47F445290D7EE4BBCC8 *)cache->attributes[0];
		SpaceAttribute__ctor_m9C74D8BD18B12F12D81F733115FF9A0BFE581D1D(tmp, NULL);
	}
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[1];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 1.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_angularDrag(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, 0.0f, 10.0f, NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_forwardExtremumSlip(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x57\x68\x65\x65\x6C\x20\x46\x72\x69\x63\x74\x69\x6F\x6E\x73\x20\x46\x6F\x72\x77\x61\x72\x64"), NULL);
	}
}
static void BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_sidewaysExtremumSlip(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x57\x68\x65\x65\x6C\x20\x46\x72\x69\x63\x74\x69\x6F\x6E\x73\x20\x53\x69\x64\x65\x77\x61\x79\x73"), NULL);
	}
}
static void RCC_ShadowRotConst_tA324371CE75FAEEA5E9B4CCC1EF03AFBC75BFAF3_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x53\x68\x61\x64\x6F\x77"), NULL);
	}
}
static void RCC_Skidmarks_t26D3E65EA5B74778D7FB59F52638A35DBD8514A2_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x53\x6B\x69\x64\x6D\x61\x72\x6B\x73"), NULL);
	}
}
static void RCC_SuspensionArm_t265F697579B5C7C8854275646F2943EF71F5338C_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x56\x69\x73\x75\x61\x6C\x20\x41\x78\x6C\x65\x20\x28\x53\x75\x73\x70\x65\x6E\x73\x69\x6F\x6E\x20\x44\x69\x73\x74\x61\x6E\x63\x65\x20\x42\x61\x73\x65\x64\x29"), NULL);
	}
}
static void RCC_TruckTrailer_t5DF67D3913DF57506718BD4BDA2463AF459CD731_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody_t101F2E2F9F16E765A77429B2DE4527D2047A887A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[0];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(Rigidbody_t101F2E2F9F16E765A77429B2DE4527D2047A887A_0_0_0_var), NULL);
	}
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[1];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x69\x73\x63\x2F\x52\x43\x43\x20\x54\x72\x75\x63\x6B\x20\x54\x72\x61\x69\x6C\x65\x72"), NULL);
	}
}
static void RCC_UIController_t6CFFE2C879501B333B2B684628629B6FF5E541DC_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x4D\x6F\x62\x69\x6C\x65\x2F\x52\x43\x43\x20\x55\x49\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x20\x42\x75\x74\x74\x6F\x6E"), NULL);
	}
}
static void RCC_UIDashboardButton_tA7D9D7E6074D18315F9C8AD590CFEAF955888F27_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x55\x49\x20\x44\x61\x73\x68\x62\x6F\x61\x72\x64\x20\x42\x75\x74\x74\x6F\x6E"), NULL);
	}
}
static void RCC_UIDashboardButton_tA7D9D7E6074D18315F9C8AD590CFEAF955888F27_CustomAttributesCacheGenerator_RCC_UIDashboardButton_U3CStartU3Eb__4_0_m8B583736442F9A204A460DF737E434AF0436CA98(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_UIDashboardDisplay_tDE9F8CF16F5A81A98EE3A9040512A4E210A28B1B_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RCC_DashboardInputs_tD4F52EB04E26C10B5915DE0801BD467A16E57E34_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x55\x49\x20\x44\x61\x73\x68\x62\x6F\x61\x72\x64\x20\x44\x69\x73\x70\x6C\x61\x79\x65\x72"), NULL);
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[1];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(RCC_DashboardInputs_tD4F52EB04E26C10B5915DE0801BD467A16E57E34_0_0_0_var), NULL);
	}
}
static void RCC_UIJoystick_t0ADDC928336850161B068F0E9939452816993AA9_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x4D\x6F\x62\x69\x6C\x65\x2F\x52\x43\x43\x20\x55\x49\x20\x4A\x6F\x79\x73\x74\x69\x63\x6B"), NULL);
	}
}
static void RCC_UISliderTextReader_t44D9FAB2C1C0FF7949AE9B33FC4BC55AEC6DF2EF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x52\x43\x43\x20\x55\x49\x20\x53\x6C\x69\x64\x65\x72\x20\x54\x65\x78\x74\x20\x52\x65\x61\x64\x65\x72"), NULL);
	}
}
static void RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x55\x49\x2F\x4D\x6F\x62\x69\x6C\x65\x2F\x52\x43\x43\x20\x55\x49\x20\x53\x74\x65\x65\x72\x69\x6E\x67\x20\x57\x68\x65\x65\x6C"), NULL);
	}
}
static void RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator_RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_0_m22BF9241C6050BDF05C4CE723689FA865A361402(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator_RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_1_mC3C4DFB94E509F77DADC020AD49074B13DE338B2(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator_RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_2_mBC078C2B056DF8D07E00F130A48B601A989E7701(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RCC_WheelCamera_tA621AE65173676A56140193E15CBA23982ADB953_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[0];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x43\x61\x6D\x65\x72\x61\x2F\x52\x43\x43\x20\x57\x68\x65\x65\x6C\x20\x43\x61\x6D\x65\x72\x61"), NULL);
	}
}
static void RCC_WheelCamera_tA621AE65173676A56140193E15CBA23982ADB953_CustomAttributesCacheGenerator_RCC_WheelCamera_FixShakeDelayed_m2E0A03ECD03FE70C28057C91BFF5646F2C4C4F28(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_0_0_0_var), NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1__ctor_m204A4662380E106CFB28EB14B004A2E9DB6BD546(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m897889779F5BADDE9EF6DFF5CC32935906FF3DB2(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB0CA96DCB23FB13704FA6C9A44E4F1AFF97388C0(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m5430D52BA502C196D76FC653209F18AE9C630819(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m27B86E74B26471D822D64FE4AEFCA6D397A37C45(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void RCC_WheelCollider_tC190621AA9D7640733117BB1349E024306AD4B1A_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_t57B08104FE16DFC3BF72826F7A3CCB8477C01779_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 * tmp = (RequireComponent_tEDA546F9722B8874DA9658BDAB821BA49647FC91 *)cache->attributes[0];
		RequireComponent__ctor_m5EC89D3D22D7D880E1B88A5C9FADF1FBDC713EE4(tmp, il2cpp_codegen_type_get_object(WheelCollider_t57B08104FE16DFC3BF72826F7A3CCB8477C01779_0_0_0_var), NULL);
	}
	{
		AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 * tmp = (AddComponentMenu_t3477A931DC56E9A4F67FFA5745D657ADD2931100 *)cache->attributes[1];
		AddComponentMenu__ctor_m34CE7BDF93FA607429964AEF1D23436963EE8549(tmp, il2cpp_codegen_string_new_wrapper("\x42\x6F\x6E\x65\x43\x72\x61\x63\x6B\x65\x72\x20\x47\x61\x6D\x65\x73\x2F\x52\x65\x61\x6C\x69\x73\x74\x69\x63\x20\x43\x61\x72\x20\x43\x6F\x6E\x74\x72\x6F\x6C\x6C\x65\x72\x2F\x4D\x61\x69\x6E\x2F\x52\x43\x43\x20\x57\x68\x65\x65\x6C\x20\x43\x6F\x6C\x6C\x69\x64\x65\x72"), NULL);
	}
}
static void RCC_WheelCollider_tC190621AA9D7640733117BB1349E024306AD4B1A_CustomAttributesCacheGenerator_steeringMultiplier(CustomAttributesCache* cache)
{
	{
		RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 * tmp = (RangeAttribute_t14A6532D68168764C15E7CF1FDABCD99CB32D0C5 *)cache->attributes[0];
		RangeAttribute__ctor_mC74D39A9F20DD2A0D4174F05785ABE4F0DAEF000(tmp, -1.0f, 1.0f, NULL);
	}
}
static void Fail_t7945FC81C74A165C02D6BEEA889DB452ECE7A7F7_CustomAttributesCacheGenerator_Fail_OTHER_m9302EB2FA190E6331828330607495633CC78098A(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_0_0_0_var), NULL);
	}
}
static void U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11__ctor_m0B6E55F410EF7E22AD3A9754F608909D926D5FFF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_IDisposable_Dispose_m0B9B07668BD86422B5AB242B5C0F897B301D587C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1B623C693AAEF9EF516DE1D821E354D497C399D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_Collections_IEnumerator_Reset_m771865FB69A0E526862906D154CBD9C36231DD17(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_Collections_IEnumerator_get_Current_m0EE232A7B5409AB7194EC5DFE2E38D2CF303240A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void SliderRelease_t47BA1274862EBE6AA2F2A475504780424EACCD10_CustomAttributesCacheGenerator_SliderRelease_ReleaseSlider_m4736AD6D8FDB819574EB0412E32DA2F6FE0D598C(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_0_0_0_var), NULL);
	}
}
static void U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13__ctor_mE4197199E78602577ED2A3A635EB216C77F39FEF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_IDisposable_Dispose_m34F9BA03563BAF7CD102DF712A39ECFE541B0418(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m46AD7346655EE2AC283D6C40CD1F9D1E27B296CA(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_Reset_mDF5695C2AB546ADE270505C73425F4B3797CA92E(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_get_Current_mEEAC84F67676031DDAF637E24168B12AAC567883(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void playermain_t013413CD227FE50FF4BBE765B89E4118DBA3AEB9_CustomAttributesCacheGenerator_playermain_OTHER_mE22E734AA6FEB4168A845F06FE53D5B77EC2460A(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_0_0_0_var), NULL);
	}
}
static void U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48__ctor_m5614BDC61EE0311FE73BDF2E8CD344AE041045BD(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_IDisposable_Dispose_mFA8C120D991A2CD9F00B084ED447859CC63AB39A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDFF4827EC256709E77F32DC3C36DE0390C28EA80(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_Collections_IEnumerator_Reset_m4674F834B072F810A67AE4BF0618E260D5865472(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_Collections_IEnumerator_get_Current_mF3D78D6C443475337EF24152BDF6F16C6D63B190(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void AutoTypeText_tC2ED291D692CB9F3BC9CC7831E058C315F669CF2_CustomAttributesCacheGenerator_AutoTypeText_abc_m046B8D1B8B3FF5F4EAF9AE9DBC4328EFF526DCF6(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_0_0_0_var), NULL);
	}
}
static void AutoTypeText_tC2ED291D692CB9F3BC9CC7831E058C315F669CF2_CustomAttributesCacheGenerator_AutoTypeText_TypeText_m9848A0BF62B3A385570F90DFD4D488DAED70D0B5(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_0_0_0_var), NULL);
	}
}
static void U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6__ctor_m43A18AC38E38869EC38704D231C3192BD88B34FF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_IDisposable_Dispose_m334377B0DD65CB59F9A08190AA84717DFCDE6FA4(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E6B56B3DC93F0BC5F924495777906F176C7113D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_Collections_IEnumerator_Reset_mECD8B6EB875F70004883F06C7A3BDFE201021FF4(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_Collections_IEnumerator_get_Current_mD9320F71B108229AB2F622829C492F69E5AAE9C8(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7__ctor_mE690467BF2D539923CA7D99FDC7E59A01CAF1771(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_IDisposable_Dispose_mD8CC279851A8912BF3A06D31485098AFA39E7B15(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBDB53438EAEEBD2F8D9349B79FE49779359ED9D3(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_Collections_IEnumerator_Reset_m0AED2DACDF8C6C8E405ABC72555E846E51193C9C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_Collections_IEnumerator_get_Current_mFF4349924EBC96992A6D7061D898499D0A02A58C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CU3Ec_t0ADF0160E3050B8CBB65CAD3D5DA2BF486D78DC0_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl1_m222961C305B83C3AB7B11B8A3B26EA4633BFD51C(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_0_0_0_var), NULL);
	}
}
static void gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl2_mF5C4185CDD7BE11833ECDB4F9A609821A637BC7F(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_0_0_0_var), NULL);
	}
}
static void gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl5_mE465A99D7A5FDA173F615C95A56582FAA3553193(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_0_0_0_var), NULL);
	}
}
static void gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl7_mD2457D7BEC6A0AE46E473CC4E2CDCF3EE8DBF7F0(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_0_0_0_var), NULL);
	}
}
static void gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl0_mEEB025DEF93243F8959C9CF6E309A0582C316691(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_0_0_0_var), NULL);
	}
}
static void U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56__ctor_mE4E1A42457E3124976DD73E8A9A59F196E027090(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_IDisposable_Dispose_mD644E8350E522FC488F9A31B7F82D5B0400F3A7D(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEA76BFC814DD367A3B382F1EE6399BE317112A7E(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_Collections_IEnumerator_Reset_mE9C4D92ACE2895DEDF2B1FE7F2E3B8B434AF9A9A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_Collections_IEnumerator_get_Current_mF653D72D087211FC9AE7331E286474EE4EC350F8(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57__ctor_m2EFFDF87AA2BFC5D3DA40264E825A3C26362C0CE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_IDisposable_Dispose_m4D1C8CFB2CDE3D156A311E59B92937267663962A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m502500F20DE9F50F19AC1A274D7864CFF008708F(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_Collections_IEnumerator_Reset_mBDD3D39B888218936D37127AF3FC42C46452EFB1(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_Collections_IEnumerator_get_Current_m7F1B9220F2F4A62551EA065C1A917ACB67DE5F45(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58__ctor_m5F5BC85F8D246E3C0D35E5FCF5650E48438DEE75(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_IDisposable_Dispose_mBAEEC551D6A403B453B735AADF2DEF366AC952DE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m21C3F4676E11B20E8C9DD88CC8483F6644AF9B18(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_Collections_IEnumerator_Reset_m58488997363FEC377CD7CD8938796727DEACD2E4(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_Collections_IEnumerator_get_Current_mEFACCB740AA3DF190553CB5471E81B414FBAC7D6(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59__ctor_mEF40FF7E99BD7112CA3029749A5F8EDF590A9591(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_IDisposable_Dispose_m1DD570AFC814E1D59B9622652298FD8D398DA3A6(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4EE7A5B50DA55D5482C2183630F78A16A2800F15(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_Collections_IEnumerator_Reset_m5D77977BFF43716E13D5C93FA3B833D219CED75A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_Collections_IEnumerator_get_Current_m9AC0E3A9E524228C023189A55B8D0C946175E6F7(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60__ctor_m9DCD1CE8813F87353DE64E94CB456BDC65C8AADD(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_IDisposable_Dispose_m0978BEAAFB3BAAD4F4D90C39CF341A1C41058A59(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mABE72F3BB290F9274B298E22045957414E685854(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_Collections_IEnumerator_Reset_m2249E8A74EB47EAF45BB4E180FE7D0C7C0B14C2B(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_Collections_IEnumerator_get_Current_mA7B9E52589C82967F50793D3AEC50D52CB0083F6(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_loading_m974C5533A63E1BCCD69F13414389E7A4894AA90B(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_0_0_0_var), NULL);
	}
}
static void mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_loding_m3F53A9567827B070999CFDD722D504533DF87C9E(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_0_0_0_var), NULL);
	}
}
static void mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_load_m2C09862F74DAA82291C77B68A06CABE82B9CCA8C(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_0_0_0_var), NULL);
	}
}
static void mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_load1_mFBDBDF9E120DF76AAA9786952432BCA6A89B0C66(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_0_0_0_var), NULL);
	}
}
static void mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_lod2_m3C32CA7424199D1DF6BCAC484E1A712B1BE51FA8(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_0_0_0_var), NULL);
	}
}
static void U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39__ctor_m6E3FC03533E32165DBA82BC30E8E728E245D1DB0(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_IDisposable_Dispose_m340482F2B6E36928BC7A77E7843026EDE08DEBD1(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D74B40166DEA5F08B1626BE899B7BD3E4EC784B(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_Collections_IEnumerator_Reset_m7EC41A340C0613CDB99CD1EE5E5A95E952AD47EE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_Collections_IEnumerator_get_Current_m4D01BCCC9E254743B500D32E295079258EFC34BA(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45__ctor_mA348507D99FF90AEB48918426A1CA4F39937C41A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_IDisposable_Dispose_mBF0A271F3AB8B910EC9D49AE9D2110162FBAE4A4(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCBD6CD24E193E34EBC9C494309CF7AECBB8074CF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_Collections_IEnumerator_Reset_m8757C7459472A549127A813CB0C53400F5957B64(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_Collections_IEnumerator_get_Current_m85E0209029F5CEAD7E6370A74C02ABB2911F0349(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46__ctor_m3F8833D5E42271B150929700613389E90A753EA9(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_IDisposable_Dispose_m64A9A989D0F422E2A10DBA8312AB0A4D55C9C566(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF7D814B5290AFD4675C4A1ACA51D91854381876C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_Collections_IEnumerator_Reset_m6F7822F745718F8D484D30E140AB8537A53468E9(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_Collections_IEnumerator_get_Current_m7DE394C9A1824F61F51EA0DC310A4BD59494C796(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47__ctor_mEBA0E592445B2C566D58411EB5D4C82078FBBEF6(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_IDisposable_Dispose_m4BEB4DB183B0F6F71E186A3C18BB359254514962(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCF06F55E47D6EB22E57CD961BC3C6DF6768AFAAF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_Collections_IEnumerator_Reset_m08878A97DB160EE3BC983A626AB66D00CBCCAD3A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_Collections_IEnumerator_get_Current_m7F6A1C3904D02C53B01621FC9D3AFFB6E9960748(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48__ctor_mD1723DAC9616E26A433A7E11CEB236671FB5B476(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_IDisposable_Dispose_m8BA719AA6E68CA80163FDC5EB3844D066E72244A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m32C1CBC447D7A1F0933C26FB4009744C8B998E23(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_Collections_IEnumerator_Reset_m6119113503F3F7DD48CD9298A57E31C5CC2DD099(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_Collections_IEnumerator_get_Current_m31F6464795D1660A336D499BC584E47E8E211BAF(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void manger2_t6D3650AF285836D8C01E8E89ECA0ADE70A0DF2E7_CustomAttributesCacheGenerator_manger2_start_mF7905CD41FC4F9C5B6E99A3B72144A1C09860052(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_0_0_0_var), NULL);
	}
}
static void manger2_t6D3650AF285836D8C01E8E89ECA0ADE70A0DF2E7_CustomAttributesCacheGenerator_manger2_obect_mE9A71697220BFB6C64C7B84A184C7EC578E071EA(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_0_0_0_var), NULL);
	}
}
static void manger2_t6D3650AF285836D8C01E8E89ECA0ADE70A0DF2E7_CustomAttributesCacheGenerator_manger2_strt_m039C3A25F67681C51A80999F2376E17ABDFDD1E6(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_0_0_0_var), NULL);
	}
}
static void U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24__ctor_m5DEE7DDB19BF271D5A33A477561A6E315342189A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_IDisposable_Dispose_mFACAD12B5AD1C8EEC0441BA4AA524B261C8FDB77(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD8FC10AF493EFB7391EEB109EE8D9028C3D552A8(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_Collections_IEnumerator_Reset_mF6780D537B9EBF1E49349F59FC3D0B8ED362428A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_Collections_IEnumerator_get_Current_m4C1BA7A30A06294A299D337756AE0AF96A99572C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25__ctor_mE5DC237276F154F4CC97B951EBEBD42410A4B0DE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_IDisposable_Dispose_m42743905108FA175D898410D377F0696B456B0E9(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m92463448801F7F98AEFE06D2D1F5F6E3B148BA22(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_Collections_IEnumerator_Reset_mC27CAB5EED358B0E5F7AE37AB0E4BB481C66F505(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_Collections_IEnumerator_get_Current_mBA65AE75917D2F878E8A835886C7A4D6DFDB4AB2(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26__ctor_mCCDCD27B5FD203C4A6FAD5DFBBD5E7452B315620(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_IDisposable_Dispose_m6C707852E0BE32F4725F21A6042848C63FA3DD0B(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1E7F837CAD35AC52B569095A33494BA6747F2B0F(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_Collections_IEnumerator_Reset_mFAD7C77823E3FE573B35E48CA2AE175F79A346BE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_Collections_IEnumerator_get_Current_mCB29ECBEA6C4DCFD31BB26971E2E6993390CC739(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void player_tA80E0022C279849EE513BB6A4017ED62943ADC71_CustomAttributesCacheGenerator_player_end_mE3A73A581F4D2AE9D208F1B10F6C62E4AB189E31(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_0_0_0_var), NULL);
	}
}
static void player_tA80E0022C279849EE513BB6A4017ED62943ADC71_CustomAttributesCacheGenerator_player_end1_m570774867FE7BA7D3DBCEB2E4E75AA766332DE00(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_0_0_0_var), NULL);
	}
}
static void player_tA80E0022C279849EE513BB6A4017ED62943ADC71_CustomAttributesCacheGenerator_player_lvl6_m33375427C70655644B18D709087A612DC8FA5A5E(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_0_0_0_var), NULL);
	}
}
static void U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17__ctor_m54D9016ECDB432ED712D6F06007B82FCF226CAFC(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_IDisposable_Dispose_m87B1BAD34325D7644464870E5F275AD5AB6F24DB(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A3DBD6F5B7F6E46C4FB207F9540541464F2B53A(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_Collections_IEnumerator_Reset_mAC65AD5E5A699707CA188140BA46B67B990C35D0(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_Collections_IEnumerator_get_Current_mFCB8DA308C1E2FB13319B250288E37A9625CDFCB(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18__ctor_mCA0B5DC2E2851ABDD7A79BD4D598C1257DCD25BE(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_IDisposable_Dispose_m0CA518025250D42F03A0892B90BC9ED04D6292B5(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7A720503E941B74F8E5D33A81BD6697774CE4B8C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_Collections_IEnumerator_Reset_m07DA0736EF0D66CAB0F90F42C5A5A49835DE169E(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_Collections_IEnumerator_get_Current_m5DE3F0165B0786FA5F6FA1581D13F939182EBF8E(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19__ctor_mDB18D4251B4F1069B69F9B95D57511CEDB1ECBB4(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_IDisposable_Dispose_m4E1D48EA935AB6220B5C7EB5417B3E828BF7C6C3(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4056B2F8F96C4274CABDD0D14C45F05481003ABB(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_Collections_IEnumerator_Reset_mC12E146F0D6D1CB27AFB7C7BA03DA91F10D7E4E2(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_Collections_IEnumerator_get_Current_mA46297F7242F0AF07D15F6FC61D55D8DEA4164D1(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void startscene_t2CCBF105CBE570083DB724FFA05E2CD33283149A_CustomAttributesCacheGenerator_startscene_load_mD919890EC64F9A4913422ED0A59861A6DE185C3B(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_0_0_0_var), NULL);
	}
}
static void U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4__ctor_mDDEB6A99F7CD51746E3F63C14596586EBE32CD8C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_IDisposable_Dispose_mF0978BF8E970E01288786C36C522FFF679326B42(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A7B178E9FC4BF7AA0E8E01F4149B530309C422C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_Collections_IEnumerator_Reset_m19068CB4D993EBF7D6154E6C7C183B0F09053E90(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_Collections_IEnumerator_get_Current_mB92520266F090E771D54FE565B244BE04ABEED44(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void PowerUpAnimation_tE771ADAEC06ED91C57E91E9B2B0C02BF12B55F2B_CustomAttributesCacheGenerator__animateRotation(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void PowerUpAnimation_tE771ADAEC06ED91C57E91E9B2B0C02BF12B55F2B_CustomAttributesCacheGenerator__animateScale(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void PowerUpAnimation_tE771ADAEC06ED91C57E91E9B2B0C02BF12B55F2B_CustomAttributesCacheGenerator__animateYOffset(CustomAttributesCache* cache)
{
	{
		SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 * tmp = (SerializeField_t6B23EE6CC99B21C3EBD946352112832A70E67E25 *)cache->attributes[0];
		SerializeField__ctor_mDE6A7673BA2C1FAD03CFEC65C6D473CC37889DD3(tmp, NULL);
	}
}
static void SciFiFireProjectile_tF59DEBF0C0D1E7435808A9EE2356D1D97544E7A8_CustomAttributesCacheGenerator_currentProjectile(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void SciFiLoopScript_t5A0D6FB48CDFFBD8FFFEA178BC2E196E6436CDCB_CustomAttributesCacheGenerator_SciFiLoopScript_EffectLoop_m73BC3A0884B7A62594CA5F184D431DB81E2A6ED7(CustomAttributesCache* cache)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 * tmp = (IteratorStateMachineAttribute_t6C72F3EC15FB34D08D47727AA7A86AB7FEA27830 *)cache->attributes[0];
		IteratorStateMachineAttribute__ctor_m019CD62C4E5301F55EDF4723107B608AE8F12481(tmp, il2cpp_codegen_type_get_object(U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_0_0_0_var), NULL);
	}
}
static void U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4__ctor_m9FDDAD0DE8BB6450657E8E7B7B83BC85838F6B6C(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_IDisposable_Dispose_m4B490EBA83EF4F6E7753CA05239EFB051DFDBCA1(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5C6B23587D0C866DFF63356BDAEEB61CA603BB3B(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m992C579A5BA9707614B0E1C087C94BED47BC8C11(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_get_Current_m79C803F68611B6834219B1F249C098DAF2E52BC7(CustomAttributesCache* cache)
{
	{
		DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 * tmp = (DebuggerHiddenAttribute_tD84728997C009D6F540FB29D88F032350E046A88 *)cache->attributes[0];
		DebuggerHiddenAttribute__ctor_mB40799BB5DAFE439BEFE895836CF792B8DBEA7F3(tmp, NULL);
	}
}
static void SciFiProjectileScript_t3AF2A8A8251BE61CA3187BE163786A8B7FA845BC_CustomAttributesCacheGenerator_impactNormal(CustomAttributesCache* cache)
{
	{
		HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA * tmp = (HideInInspector_tDD5B9D3AD8D48C93E23FE6CA3ECDA5589D60CCDA *)cache->attributes[0];
		HideInInspector__ctor_mE2B7FB1D206A74BA583C7812CDB4EBDD83EB66F9(tmp, NULL);
	}
}
static void SciFiLightFade_tB52BA448FE7ACF6EA41AC1E94657EF2EA5314630_CustomAttributesCacheGenerator_life(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x53\x65\x63\x6F\x6E\x64\x73\x20\x74\x6F\x20\x64\x69\x6D\x20\x74\x68\x65\x20\x6C\x69\x67\x68\x74"), NULL);
	}
}
static void SciFiRotation_t7E26C10B0A17339C829D6D14DBD4D526307A18D9_CustomAttributesCacheGenerator_rotateVector(CustomAttributesCache* cache)
{
	{
		HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB * tmp = (HeaderAttribute_t9B431E6BA0524D46406D9C413D6A71CB5F2DD1AB *)cache->attributes[0];
		HeaderAttribute__ctor_m601319E0BCE8C44A9E79B2C0ABAAD0FEF46A9F1E(tmp, il2cpp_codegen_string_new_wrapper("\x52\x6F\x74\x61\x74\x65\x20\x61\x78\x69\x73\x65\x73\x20\x62\x79\x20\x64\x65\x67\x72\x65\x65\x73\x20\x70\x65\x72\x20\x73\x65\x63\x6F\x6E\x64"), NULL);
	}
}
static void U3CPrivateImplementationDetailsU3E_t6BC7664D9CD46304D39A7D175BB8FFBE0B9F4528_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
IL2CPP_EXTERN_C const CustomAttributesCacheGenerator g_AssemblyU2DCSharp_AttributeGenerators[];
const CustomAttributesCacheGenerator g_AssemblyU2DCSharp_AttributeGenerators[464] = 
{
	U3CU3Ec__DisplayClass111_0_t93080340976256D9511F5C9829A579F442BC2A48_CustomAttributesCacheGenerator,
	U3CU3Ec_t64B6A3EC25BDB1B4BFEF24D0D15140329BF99687_CustomAttributesCacheGenerator,
	U3CU3Ec__DisplayClass3_0_t59C2D70F82DD049282C333BD6105D43F5F039728_CustomAttributesCacheGenerator,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator,
	U3CU3Ec__DisplayClass33_0_t625D1FF1495408366D10B041997D39A1E07A4B4C_CustomAttributesCacheGenerator,
	U3CU3Ec__DisplayClass33_1_t47917A8CA5B35A825FA0802DA1F60C2FB0051AE3_CustomAttributesCacheGenerator,
	U3CU3Ec_tC607834D420DED2B2B095D41BAB8005F418C3722_CustomAttributesCacheGenerator,
	RCC_AIBrakeZone_tA4CB5237B9A2722D09DC8C10F794D33FB3412252_CustomAttributesCacheGenerator,
	RCC_AIBrakeZonesContainer_t49DA4ABE34EB66A9F12E55C439A8B83CD06F144A_CustomAttributesCacheGenerator,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator,
	RCC_AIWaypointsContainer_t316BD7A22115F53AB44A6A8C5F267E1347103CB8_CustomAttributesCacheGenerator,
	RCC_Caliper_tB450B6F91CFA111A6750E30770B221E570FA83F9_CustomAttributesCacheGenerator,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator,
	U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator,
	U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator,
	U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator,
	U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator,
	U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator,
	U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator,
	U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator,
	RCC_CharacterController_tF95DD1DCD4775FD03CFA4B37B82573B779930C2E_CustomAttributesCacheGenerator,
	RCC_Chassis_tA0F6A482E38EEE438DD7A6F5150AE9F6269F4229_CustomAttributesCacheGenerator,
	U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator,
	RCC_CinematicCamera_t1AD76F879C5B66AEB260127EFF15191031BA9C37_CustomAttributesCacheGenerator,
	RCC_ColorPickerBySliders_t36A18A4D19E929A1F16FDBF5E3F587829CABED80_CustomAttributesCacheGenerator,
	RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator,
	RCC_DashboardColors_tCBE68324FC94E074074360571FB98CFFDAA59BC2_CustomAttributesCacheGenerator,
	RCC_DashboardInputs_tD4F52EB04E26C10B5915DE0801BD467A16E57E34_CustomAttributesCacheGenerator,
	RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator,
	RCC_Demo_t2E6DB6845E44CFB1FB1189F4E70FE5C7246B61A6_CustomAttributesCacheGenerator,
	RCC_Exhaust_t312041844622521D4098B5D8F8DFD086375938C5_CustomAttributesCacheGenerator,
	RCC_FixedCamera_tE79ACA4DDA21344137A0D195172D72F6CEF9EE2E_CustomAttributesCacheGenerator,
	RCC_HoodCamera_tD5E7DA2C38265724870BA409EA86E9742E1880B2_CustomAttributesCacheGenerator,
	U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator,
	RCC_InfoLabel_t6A19CE611168F5435C1F981218D38F32B3BE1626_CustomAttributesCacheGenerator,
	U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator,
	RCC_Light_tC82A4EC473CAEE4A2FF7A2E46B8BE313F6D58E91_CustomAttributesCacheGenerator,
	RCC_LightEmission_t0565CFA014750B6CB3742874C60C39E6226A5F51_CustomAttributesCacheGenerator,
	RCC_Mirror_t08CF543D7B45C73DEE3876A206E1A90DF1D315BA_CustomAttributesCacheGenerator,
	U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator,
	RCC_MobileButtons_t696DEEE1E2B18E07B8506BAA7D6F18EA34FDB67B_CustomAttributesCacheGenerator,
	RCC_MobileUIDrag_tC079FEEC88381E161AE683BBA380CC907B61F70C_CustomAttributesCacheGenerator,
	RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator,
	U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator,
	U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator,
	U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator,
	RCC_ShadowRotConst_tA324371CE75FAEEA5E9B4CCC1EF03AFBC75BFAF3_CustomAttributesCacheGenerator,
	RCC_Skidmarks_t26D3E65EA5B74778D7FB59F52638A35DBD8514A2_CustomAttributesCacheGenerator,
	RCC_SuspensionArm_t265F697579B5C7C8854275646F2943EF71F5338C_CustomAttributesCacheGenerator,
	RCC_TruckTrailer_t5DF67D3913DF57506718BD4BDA2463AF459CD731_CustomAttributesCacheGenerator,
	RCC_UIController_t6CFFE2C879501B333B2B684628629B6FF5E541DC_CustomAttributesCacheGenerator,
	RCC_UIDashboardButton_tA7D9D7E6074D18315F9C8AD590CFEAF955888F27_CustomAttributesCacheGenerator,
	RCC_UIDashboardDisplay_tDE9F8CF16F5A81A98EE3A9040512A4E210A28B1B_CustomAttributesCacheGenerator,
	RCC_UIJoystick_t0ADDC928336850161B068F0E9939452816993AA9_CustomAttributesCacheGenerator,
	RCC_UISliderTextReader_t44D9FAB2C1C0FF7949AE9B33FC4BC55AEC6DF2EF_CustomAttributesCacheGenerator,
	RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator,
	RCC_WheelCamera_tA621AE65173676A56140193E15CBA23982ADB953_CustomAttributesCacheGenerator,
	U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator,
	RCC_WheelCollider_tC190621AA9D7640733117BB1349E024306AD4B1A_CustomAttributesCacheGenerator,
	U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator,
	U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator,
	U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator,
	U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator,
	U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator,
	U3CU3Ec_t0ADF0160E3050B8CBB65CAD3D5DA2BF486D78DC0_CustomAttributesCacheGenerator,
	U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator,
	U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator,
	U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator,
	U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator,
	U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator,
	U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator,
	U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator,
	U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator,
	U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator,
	U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator,
	U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator,
	U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator,
	U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator,
	U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator,
	U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator,
	U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator,
	U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator,
	U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator,
	U3CPrivateImplementationDetailsU3E_t6BC7664D9CD46304D39A7D175BB8FFBE0B9F4528_CustomAttributesCacheGenerator,
	SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_beamLineRendererPrefab,
	SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_beamEndOffset,
	SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_endOffSetSlider,
	SciFiBeamScript_tAB3054BB1510F7F99BF046B844C07E2D01200A81_CustomAttributesCacheGenerator_textBeamName,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_screenOrientation,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_showAppOpenAd,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobBannerIds,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobInterstitialIds,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobRewardedInterstitialID,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_admobRewardedVideoID,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_appOpenAdID,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_ShowTestAds,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_createMultiMaterialMesh,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_combineInactiveChildren,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_deactivateCombinedChildren,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_deactivateCombinedChildrenMeshRenderers,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_generateUVMap,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_destroyCombinedChildren,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_folderPath,
	MeshCombiner_t2CD8003020EE4332F6A85F814B7876EE79DE1C05_CustomAttributesCacheGenerator_meshFiltersToSkip,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_OnRCCAISpawned,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_OnRCCAIDestroyed,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_OnBCGCameraSpawned,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_biasedWheelTorque,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_engineInertia,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steeringSensitivity,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_gearShiftingDelay,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_gearShiftingThreshold,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_clutchInertia,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_minEngineSoundPitch,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_maxEngineSoundPitch,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_minEngineSoundVolume,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_maxEngineSoundVolume,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_idleEngineSoundVolume,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_gasInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_brakeInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steerInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_clutchInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_handbrakeInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_boostInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_idleInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_fuelInput,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_cutGas,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_ABSThreshold,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_TCSThreshold,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_TCSStrength,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_ESPThreshold,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_ESPStrength,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steerHelperLinearVelStrength,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_steerHelperAngularVelStrength,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_tractionHelperStrength,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_angularDragHelperStrength,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_counterSteeringFactor,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_OnRCCPlayerSpawned,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_OnRCCPlayerDestroyed,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_OnRCCPlayerCollision,
	RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_wheelsMenu,
	RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_frontCamber,
	RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_TCS,
	RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_maxSpeed,
	RCC_CustomizerExample_tA9725A8076805B5776250F7F9441D77B2CFB6C51_CustomAttributesCacheGenerator_drivetrainMode,
	RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_rPMDial,
	RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_speedDial,
	RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_fuelDial,
	RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_heatDial,
	RCC_DashboardObjects_tB8B9CC6799DD7CB6EF5FDCCFAA3CB96123A467CB_CustomAttributesCacheGenerator_interiorLights,
	RCC_Demo_t2E6DB6845E44CFB1FB1189F4E70FE5C7246B61A6_CustomAttributesCacheGenerator_selectableVehicles,
	GroundMaterialFrictions_t7651DDF614C275421B32CFE9FF0E3C5A5D776994_CustomAttributesCacheGenerator_volume,
	Recorded_t626684EBAC57FEEF50CE5EAFEDA7224D2D91D0B7_CustomAttributesCacheGenerator_inputs,
	Recorded_t626684EBAC57FEEF50CE5EAFEDA7224D2D91D0B7_CustomAttributesCacheGenerator_transforms,
	Recorded_t626684EBAC57FEEF50CE5EAFEDA7224D2D91D0B7_CustomAttributesCacheGenerator_rigids,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_OnMainControllerChanged,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_OnBehaviorChanged,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_OnVehicleChanged,
	RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_fixedTimeStep,
	RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxAngularVelocity,
	RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxGearShiftingSoundVolume,
	RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxCrashSoundVolume,
	RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxWindSoundVolume,
	RCC_Settings_t1A306B1DE069889F6EF5A26EEFA4724F42E69BCB_CustomAttributesCacheGenerator_maxBrakeSoundVolume,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steeringHelper,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperAngularVelStrengthMinimum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperAngularVelStrengthMaximum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperLinearVelStrengthMinimum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_steerHelperLinearVelStrengthMaximum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_tractionHelperStrengthMinimum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_tractionHelperStrengthMaximum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_antiRollFrontHorizontalMinimum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_gearShiftingDelayMaximum,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_angularDrag,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_forwardExtremumSlip,
	BehaviorType_tF079087BBBCB33E599758270F1F3E59F05AFBBCE_CustomAttributesCacheGenerator_sidewaysExtremumSlip,
	RCC_WheelCollider_tC190621AA9D7640733117BB1349E024306AD4B1A_CustomAttributesCacheGenerator_steeringMultiplier,
	PowerUpAnimation_tE771ADAEC06ED91C57E91E9B2B0C02BF12B55F2B_CustomAttributesCacheGenerator__animateRotation,
	PowerUpAnimation_tE771ADAEC06ED91C57E91E9B2B0C02BF12B55F2B_CustomAttributesCacheGenerator__animateScale,
	PowerUpAnimation_tE771ADAEC06ED91C57E91E9B2B0C02BF12B55F2B_CustomAttributesCacheGenerator__animateYOffset,
	SciFiFireProjectile_tF59DEBF0C0D1E7435808A9EE2356D1D97544E7A8_CustomAttributesCacheGenerator_currentProjectile,
	SciFiProjectileScript_t3AF2A8A8251BE61CA3187BE163786A8B7FA845BC_CustomAttributesCacheGenerator_impactNormal,
	SciFiLightFade_tB52BA448FE7ACF6EA41AC1E94657EF2EA5314630_CustomAttributesCacheGenerator_life,
	SciFiRotation_t7E26C10B0A17339C829D6D14DBD4D526307A18D9_CustomAttributesCacheGenerator_rotateVector,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CInitializeAdmobU3Eb__100_0_mF577590EF5F11DF96E37B33EEA5C71DA2D8AEC40,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom0U3Eb__147_0_mBDEDAF6696EFBC73EF156014A62FDA2F4AD947B7,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom1U3Eb__148_0_m8EEBB36A49F7E395FCE0A0AA027C77C8871B29D9,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom2U3Eb__149_0_m2CCA33817CCD0488526FEE4AD3F2455634A28555,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom3U3Eb__150_0_m0EF8B952E0E063BF6AE3B0B2C0EA691ABFF194EC,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadInterstitialAdCustom4U3Eb__151_0_m2B74D4185FF4066E99619AC6865D404EC90AF29B,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents0U3Eb__157_0_m714D425E028864A4322427107A7B3665C695FB95,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents0U3Eb__157_1_m27F080C096CD99A962D07567E25302E752537804,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents1U3Eb__158_0_m08D3A39AC12EED321E5528B61E0FEE4A603FC43F,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents1U3Eb__158_1_m41B4A5503A31428083DB809226A94D26751B4FE4,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents2U3Eb__159_0_mBFCF289E0DCDC4004398B12F160BF4BC21AE4049,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents2U3Eb__159_1_m8441521F95ABB7A0ABEBE38658C71A58C02E5200,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents3U3Eb__160_0_m0798E776B41548D1E08C1F73CC605F60D57605EC,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents3U3Eb__160_1_m1A3526D78454752D5A8AA0029BA1472D6511EDC5,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents4U3Eb__161_0_m244035B19BFCD05D5CB7C5FDAABF6D427C7D11E5,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterInterEvents4U3Eb__161_1_m0059419AFA9A891B2C28977DCF1CDC1EBB46DCA1,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CloadAdmobRewardedInterstitialU3Eb__168_0_m66C00DEC641BC221A256D8DCBCBDD7AA8F11D05B,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_0_m097621CA9ECA00FE09EBD8AE84210F0DE215DE3D,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedInterEventHandlersU3Eb__169_1_mCBBEEB536D155E2A27845760525C2FC7DB885D93,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CLoadAppOpenAdU3Eb__175_0_m9A72DAD7C216B659D391C69C6615FBDE5EDB5924,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_0_m606895315A0B8258891E783CAEC43339AD0D0C8E,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_1_m2009D1B41B4DD4EBFBE781876AA80D153E8529E9,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterAppOpenEventHandlersU3Eb__176_2_m9174B75FA8AC1D6E6F4C39A082F4EC1209677853,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CLoadVideoADU3Eb__180_0_m42A6234A90D26054953322931FDA7918B423852D,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_0_m6ED4779AD56F7554C6E523BC2EB21274138EE9A9,
	AdsManager_t7C47F9BD4DF345D849754CC5D99CD0E6B7751842_CustomAttributesCacheGenerator_AdsManager_U3CRegisterRewardedVideoEventHandlersU3Eb__181_1_mDB2D1DE644BA6DE800E6E24443218E3E9685965B,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_add_OnRCCAISpawned_m3BBD0EA7218909E9EC80375B0554ED8B038802B4,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_remove_OnRCCAISpawned_m12038CFCBA20EF03A2964B286F80A3B7E6482E36,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_add_OnRCCAIDestroyed_mB643B14ED1DF88403006531517DB79B66D111908,
	RCC_AICarController_t39D68CC363BABB30E6870BDCE68AD81E45F8E58A_CustomAttributesCacheGenerator_RCC_AICarController_remove_OnRCCAIDestroyed_m560E810569BF799FDE187E14A5191DC49EADA4C2,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_add_OnBCGCameraSpawned_m549CD035B0CD04B8480C8180120D4DB55F875854,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_remove_OnBCGCameraSpawned_m48CF21668CA531B403CBA86D6255D1B0E61A14B9,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_m81900DC1EFC048D1FEB5DDC99C0EE145AEB2C2FD,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_mF615890DD4E3C817537453620BA1C4541FACA563,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_m6605134E41B5EA121A4E35112EA369D026476F3C,
	RCC_Camera_tF78455FAEC428924688688C438E039A399B5637D_CustomAttributesCacheGenerator_RCC_Camera_AutoFocus_mC500C30540E4A110FFA05675A12DA784F4BB0B44,
	U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103__ctor_mB86DAE1538A1817A4DE2965F70A7554F43963218,
	U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_IDisposable_Dispose_m5BB60050071E0246912AB3BDEA18A1C6D632BC82,
	U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m994C4DFCB355539E7E93873A49246D0A88AFE3B0,
	U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_Reset_m9829DB058D07F760EBBFC42C7178D3C8A7CCB4F0,
	U3CAutoFocusU3Ed__103_tC4816F1E5F3D92BAA2A2562C30F7C84F85040BE6_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__103_System_Collections_IEnumerator_get_Current_m94AE930C9BC639C3E657621CA82377820CDAB375,
	U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104__ctor_mFEFEB8210262FB261D9927ACB4922D120C7BE39F,
	U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_IDisposable_Dispose_m1E79DE15E4F22EAA324B8B6907B6DAFFFD02CBD7,
	U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3B2A71C4778AFB90E1632278C7A4BDF8AA1E33BA,
	U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_Reset_m019D201542FE2E5E4FF8C0ECD1E4CF7F113208C5,
	U3CAutoFocusU3Ed__104_t411A59829EC8B387E32DC8000E0F842C6D24CD41_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__104_System_Collections_IEnumerator_get_Current_mA3C32942E6C8F8367E80DFA28F7F43687BBEC68C,
	U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105__ctor_m857814C291E57FD0CE48F0CC72F0A75B8677C5A7,
	U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_IDisposable_Dispose_m5F30716C2F6EA674FE1D07F890F36ECEC8835154,
	U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43C55302F19223A3F9E6A753C1E33495E15D5334,
	U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_Reset_m37567EB21D0C21FBAF45612A28B6D990F8473682,
	U3CAutoFocusU3Ed__105_tF68C3A336CAAB9BC0325E556B32D78F1C57C5250_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__105_System_Collections_IEnumerator_get_Current_m1303A4AFE8A3BEA65829C1A2ECB0A431CBC3B22D,
	U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106__ctor_mD1116B9E48DA8B41D2403A93A8D7AA21FCD0D0AD,
	U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_IDisposable_Dispose_mC71F8B2F7180442670680DD6C0FA2638D844B9D6,
	U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB71E03BE2D95E1613CDB315DC1D0F856FD2B0CDE,
	U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_Reset_m72A38018ED33FC3DAB06663FA585036AB746828E,
	U3CAutoFocusU3Ed__106_t753FDDA7760663497432C9AAA08691ADE01C64F5_CustomAttributesCacheGenerator_U3CAutoFocusU3Ed__106_System_Collections_IEnumerator_get_Current_m356DEFF51672783BA4A8008CCF6B9B8F7FB829FC,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_add_OnRCCPlayerSpawned_mFDE1F6DAD5D918C29976EE4998677077769E45A7,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_remove_OnRCCPlayerSpawned_m59CF492DA2E14D35290ECA32A6506719A0A9CCD8,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_add_OnRCCPlayerDestroyed_m1DE2E27CCB341E870B1A3112B65F6706C3B0F03A,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_remove_OnRCCPlayerDestroyed_m638B2F4306EDFC49B3E1B44E943E79547EEF0B04,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_add_OnRCCPlayerCollision_m4DC0247EA3B605AFE3088CB97148583553DB95A7,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_remove_OnRCCPlayerCollision_m5FC92A8B329061A31DD477FCE63991D12E893771,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_RCCPlayerSpawned_mEFB68FDC59E48F6F7B81FD56D502D3EAF80B3EA7,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_StartEngineDelayed_m700F24D67534B52A4C3D899D4F4C0F3DE321893A,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_ChangeGear_m4C80019C3A3C73BC9A4B97A683246C2241563FAB,
	U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245__ctor_m84F6C98EF2E1E6AB823D57CCD4F336C37681C2F3,
	U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_IDisposable_Dispose_m0D02C0D246B87634EE283883DF60CC2ADEC78D47,
	U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0953BFC21C7566C1EA4C20B5F2FAFDB84CABECCC,
	U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_Reset_m9BB8908191673DFD4029B0EFD2576BAF9F4F55AD,
	U3CRCCPlayerSpawnedU3Ed__245_tAF76634C97DCAF7043CA0EAE868E6352D37A3F90_CustomAttributesCacheGenerator_U3CRCCPlayerSpawnedU3Ed__245_System_Collections_IEnumerator_get_Current_m30EE7661ACAFF1FF3B5242188997BEC07415401E,
	U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254__ctor_m01C19BCD75D3AD447DDB684436968DFD8D5CF58D,
	U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_IDisposable_Dispose_m1C21109B2BD3F928F6808B8E168E9FD976B97E7F,
	U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mAADA60BDC6AFDB619885F62973196BA3CA9183AE,
	U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_Reset_mE1CE9FCC838DAB413B5F31FDC43D874722C29EA8,
	U3CStartEngineDelayedU3Ed__254_t5E360A1A262ABBD903F82DF2A7D1DBFC5E70799B_CustomAttributesCacheGenerator_U3CStartEngineDelayedU3Ed__254_System_Collections_IEnumerator_get_Current_m77C2F1F6912869036D311A198CDD6F30CBA657E0,
	U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274__ctor_m42019740291409B8CA24D09A7ED3645BDE11DF87,
	U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_IDisposable_Dispose_mD457816E6E4533B0AE7AFF0E6933D83B455898D2,
	U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0A82C933A082D8DA6B5144E20C9B5778342B4544,
	U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_Collections_IEnumerator_Reset_m6632FDBB744826C15C1B065954F1523339070A79,
	U3CChangeGearU3Ed__274_tD2114733DBA206FBE48160BE93E4C87824D8C784_CustomAttributesCacheGenerator_U3CChangeGearU3Ed__274_System_Collections_IEnumerator_get_Current_mA431DD0D991872FDA3AF4F7449666D51F3057CC7,
	RCC_Chassis_tA0F6A482E38EEE438DD7A6F5150AE9F6269F4229_CustomAttributesCacheGenerator_RCC_Chassis_ReEnable_mDC685A708AA2048130E56BAEF2A9564648B41D8B,
	U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11__ctor_mECB7864140A80DFBD981DD0D1E32A9C657A9EE46,
	U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_IDisposable_Dispose_mACA2471CAD921534B9E0F254D841DED400E29576,
	U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFA07227D312084BF51EB4BA81ECE8C173C0F0C4C,
	U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_Collections_IEnumerator_Reset_m848803CEDDC57D5DC87E5F9B21710A13560AEABE,
	U3CReEnableU3Ed__11_t43DC9CBF3F09D92D2AC86C709EDFA727DA345F57_CustomAttributesCacheGenerator_U3CReEnableU3Ed__11_System_Collections_IEnumerator_get_Current_m239E706369E3C28CF8DD3218CC0A6BFFFE82D697,
	RCC_HoodCamera_tD5E7DA2C38265724870BA409EA86E9742E1880B2_CustomAttributesCacheGenerator_RCC_HoodCamera_FixShakeDelayed_mD689A3D2A58A116F11925616EE76776FE4E90659,
	U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1__ctor_mAE1EA1789C6C9E6B76669634CF665CC58511FAF5,
	U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m059CF06692AD6C920357FDF367C809C9B6621731,
	U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC7026D71FC9F1BF49D5C0A39EFE43CFBBFAF5ABA,
	U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m7675E5E8EE65BB02E4CE3266F68749E5AD3EC311,
	U3CFixShakeDelayedU3Ed__1_tD3047BAF115AEDBB69C60C11F7AE92DB5DCD8FF1_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m6465D6EB3B8B010CF242BC740AE221BF2F76BE8D,
	RCC_InfoLabel_t6A19CE611168F5435C1F981218D38F32B3BE1626_CustomAttributesCacheGenerator_RCC_InfoLabel_ShowInfoCo_m66C62CE2BCD816FD11A849C7150D82225C8EBABB,
	U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8__ctor_m754A108198109E44982129332FE0725F84334DBF,
	U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_IDisposable_Dispose_m26676BBDB19173609B45E7F970C02E28E6259337,
	U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7B0CEAF5DD5A83C6EC8337121F9CA17390640609,
	U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_Reset_m4AF8C4E672A789A3B4A328D5FDEC475DB50B9611,
	U3CShowInfoCoU3Ed__8_t2C08754EAC646528C78F6B31A7E1A43AD0D1723F_CustomAttributesCacheGenerator_U3CShowInfoCoU3Ed__8_System_Collections_IEnumerator_get_Current_m1ADE673386CC8EADC5576419114A9AF413936423,
	RCC_Mirror_t08CF543D7B45C73DEE3876A206E1A90DF1D315BA_CustomAttributesCacheGenerator_RCC_Mirror_FixDepth_m6D176AE51609272DAA74B49F80D521584B9E2ADC,
	U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4__ctor_mFD7A770C3B171A8A9EAE242A7BDAB7756F588309,
	U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_IDisposable_Dispose_mF0F38EF8288B4076B7D0573E37B2B46DD1D4025F,
	U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m359B793B68B60ED3881A48997B33E23942CA5184,
	U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_Collections_IEnumerator_Reset_m389126C8BBAE3FFD380EE405A68318CC88493E15,
	U3CFixDepthU3Ed__4_tA9FA0E48D12A3225DEEAFD62473EA8892FACE506_CustomAttributesCacheGenerator_U3CFixDepthU3Ed__4_System_Collections_IEnumerator_get_Current_mE23AD3527CBF07E20752D481DFD3DB2D0EEC3A47,
	RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator_RCC_Recorder_Replay_mCB8F543BB938360131A26B17032F2D9E4E84D13B,
	RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator_RCC_Recorder_Repos_mACEA28AD16398978903BF022E8792B9E61156A1B,
	RCC_Recorder_tBC5EE793E6854F6CF71D7E020ED0B0E5068EB2CE_CustomAttributesCacheGenerator_RCC_Recorder_Revel_mD605F0CBA5107BFBD8A1F9AD191ECCF9B1DF95F2,
	U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16__ctor_mB9F8BF6A7961FC0BCDE24595914C6CDD4185DA2D,
	U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_IDisposable_Dispose_mA568CC957DDCF7AEA1EC83C6014EA817142AC99D,
	U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB652DB22113A751F5E62C0118A89C0A7852A9DCA,
	U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_Collections_IEnumerator_Reset_mBD22EE0F1A4885817E0B124AD841CD0722581351,
	U3CReplayU3Ed__16_tE15CEB2F2576180BF81D65316E658E66480F8AB0_CustomAttributesCacheGenerator_U3CReplayU3Ed__16_System_Collections_IEnumerator_get_Current_mA572D3B3D2BD541645BBAE9D72A8AAF66B6869B1,
	U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17__ctor_m0321A3A30C4717AAEB9D7837F58F67D03513E459,
	U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_IDisposable_Dispose_mDA410B5BB748060FCF2F80273D2E9D13E2309201,
	U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEB158A68D5082D6CF6388D0B2B8BDB5435AFD296,
	U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_Collections_IEnumerator_Reset_m451D855D4F5F9A10588987393698DDC05DA5C61C,
	U3CReposU3Ed__17_t954D5EFE620D22A308D4A9DF03BB7CBCAC5F8886_CustomAttributesCacheGenerator_U3CReposU3Ed__17_System_Collections_IEnumerator_get_Current_mD260B401F286E2C92C928D9F687AF973A052C378,
	U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18__ctor_mA59F93C7334102341D5DB01F68CE2B32648C2B41,
	U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_IDisposable_Dispose_m972AAB5504AC3EDBE56D21DB91C6679BE1899018,
	U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB3545751513147303B9DC59D51AB7493A296931B,
	U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_Collections_IEnumerator_Reset_m4BA9ED55300401BD6803495E21E63F5C89EBF25B,
	U3CRevelU3Ed__18_t3F2DC9A0656ACBD0E74C0E1ACABBFDAEB3AC42CF_CustomAttributesCacheGenerator_U3CRevelU3Ed__18_System_Collections_IEnumerator_get_Current_m3117313D81F41949A5A3DE9640715A179B974331,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_add_OnMainControllerChanged_m89A15548A2B46FBBF43DA08C9DE5AE0DED73475C,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_remove_OnMainControllerChanged_mED16E813B18487627B068BC21ED9245A171DED93,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_add_OnBehaviorChanged_mAEC81E82068FEF375971E4CDA90E7804D1EA9D64,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_remove_OnBehaviorChanged_mC9EC4327421E5E6DB88D947DFBEF9B11F7786AF1,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_add_OnVehicleChanged_m5A750372D5BAE35948ABD7F5C464D4A4DE8E667E,
	RCC_SceneManager_t5AA54A0F89D0F05CB7F00192E3F4CF374F4FA656_CustomAttributesCacheGenerator_RCC_SceneManager_remove_OnVehicleChanged_m05DCEFCDC113B7D0F6FFDD6D231AE5D31FA76F37,
	RCC_UIDashboardButton_tA7D9D7E6074D18315F9C8AD590CFEAF955888F27_CustomAttributesCacheGenerator_RCC_UIDashboardButton_U3CStartU3Eb__4_0_m8B583736442F9A204A460DF737E434AF0436CA98,
	RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator_RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_0_m22BF9241C6050BDF05C4CE723689FA865A361402,
	RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator_RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_1_mC3C4DFB94E509F77DADC020AD49074B13DE338B2,
	RCC_UISteeringWheelController_tBB0050C0742681BFD18C6ECF30BD6B3BA0E1F7CA_CustomAttributesCacheGenerator_RCC_UISteeringWheelController_U3CSteeringWheelEventsInitU3Eb__21_2_mBC078C2B056DF8D07E00F130A48B601A989E7701,
	RCC_WheelCamera_tA621AE65173676A56140193E15CBA23982ADB953_CustomAttributesCacheGenerator_RCC_WheelCamera_FixShakeDelayed_m2E0A03ECD03FE70C28057C91BFF5646F2C4C4F28,
	U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1__ctor_m204A4662380E106CFB28EB14B004A2E9DB6BD546,
	U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_IDisposable_Dispose_m897889779F5BADDE9EF6DFF5CC32935906FF3DB2,
	U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB0CA96DCB23FB13704FA6C9A44E4F1AFF97388C0,
	U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_Reset_m5430D52BA502C196D76FC653209F18AE9C630819,
	U3CFixShakeDelayedU3Ed__1_t53854BCBA46B7B8CAB756FEB3CCDA685C95E27CF_CustomAttributesCacheGenerator_U3CFixShakeDelayedU3Ed__1_System_Collections_IEnumerator_get_Current_m27B86E74B26471D822D64FE4AEFCA6D397A37C45,
	Fail_t7945FC81C74A165C02D6BEEA889DB452ECE7A7F7_CustomAttributesCacheGenerator_Fail_OTHER_m9302EB2FA190E6331828330607495633CC78098A,
	U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11__ctor_m0B6E55F410EF7E22AD3A9754F608909D926D5FFF,
	U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_IDisposable_Dispose_m0B9B07668BD86422B5AB242B5C0F897B301D587C,
	U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1B623C693AAEF9EF516DE1D821E354D497C399D,
	U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_Collections_IEnumerator_Reset_m771865FB69A0E526862906D154CBD9C36231DD17,
	U3COTHERU3Ed__11_t624139F67C824272D064BE294BDA66BBCAEB0D3D_CustomAttributesCacheGenerator_U3COTHERU3Ed__11_System_Collections_IEnumerator_get_Current_m0EE232A7B5409AB7194EC5DFE2E38D2CF303240A,
	SliderRelease_t47BA1274862EBE6AA2F2A475504780424EACCD10_CustomAttributesCacheGenerator_SliderRelease_ReleaseSlider_m4736AD6D8FDB819574EB0412E32DA2F6FE0D598C,
	U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13__ctor_mE4197199E78602577ED2A3A635EB216C77F39FEF,
	U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_IDisposable_Dispose_m34F9BA03563BAF7CD102DF712A39ECFE541B0418,
	U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m46AD7346655EE2AC283D6C40CD1F9D1E27B296CA,
	U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_Reset_mDF5695C2AB546ADE270505C73425F4B3797CA92E,
	U3CReleaseSliderU3Ed__13_t9E965E09221F8F35CC42A5B22CD368C982638A95_CustomAttributesCacheGenerator_U3CReleaseSliderU3Ed__13_System_Collections_IEnumerator_get_Current_mEEAC84F67676031DDAF637E24168B12AAC567883,
	playermain_t013413CD227FE50FF4BBE765B89E4118DBA3AEB9_CustomAttributesCacheGenerator_playermain_OTHER_mE22E734AA6FEB4168A845F06FE53D5B77EC2460A,
	U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48__ctor_m5614BDC61EE0311FE73BDF2E8CD344AE041045BD,
	U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_IDisposable_Dispose_mFA8C120D991A2CD9F00B084ED447859CC63AB39A,
	U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDFF4827EC256709E77F32DC3C36DE0390C28EA80,
	U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_Collections_IEnumerator_Reset_m4674F834B072F810A67AE4BF0618E260D5865472,
	U3COTHERU3Ed__48_t179A14C8A2043D7DD76037FB7A84F85BDA534AAE_CustomAttributesCacheGenerator_U3COTHERU3Ed__48_System_Collections_IEnumerator_get_Current_mF3D78D6C443475337EF24152BDF6F16C6D63B190,
	AutoTypeText_tC2ED291D692CB9F3BC9CC7831E058C315F669CF2_CustomAttributesCacheGenerator_AutoTypeText_abc_m046B8D1B8B3FF5F4EAF9AE9DBC4328EFF526DCF6,
	AutoTypeText_tC2ED291D692CB9F3BC9CC7831E058C315F669CF2_CustomAttributesCacheGenerator_AutoTypeText_TypeText_m9848A0BF62B3A385570F90DFD4D488DAED70D0B5,
	U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6__ctor_m43A18AC38E38869EC38704D231C3192BD88B34FF,
	U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_IDisposable_Dispose_m334377B0DD65CB59F9A08190AA84717DFCDE6FA4,
	U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E6B56B3DC93F0BC5F924495777906F176C7113D,
	U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_Collections_IEnumerator_Reset_mECD8B6EB875F70004883F06C7A3BDFE201021FF4,
	U3CabcU3Ed__6_t9443D60A489B19EF3D817F295A0145375687F4D9_CustomAttributesCacheGenerator_U3CabcU3Ed__6_System_Collections_IEnumerator_get_Current_mD9320F71B108229AB2F622829C492F69E5AAE9C8,
	U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7__ctor_mE690467BF2D539923CA7D99FDC7E59A01CAF1771,
	U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_IDisposable_Dispose_mD8CC279851A8912BF3A06D31485098AFA39E7B15,
	U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBDB53438EAEEBD2F8D9349B79FE49779359ED9D3,
	U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_Collections_IEnumerator_Reset_m0AED2DACDF8C6C8E405ABC72555E846E51193C9C,
	U3CTypeTextU3Ed__7_tB0A2CFF7ABD4675C9158B19D908DDEB679827D05_CustomAttributesCacheGenerator_U3CTypeTextU3Ed__7_System_Collections_IEnumerator_get_Current_mFF4349924EBC96992A6D7061D898499D0A02A58C,
	gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl1_m222961C305B83C3AB7B11B8A3B26EA4633BFD51C,
	gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl2_mF5C4185CDD7BE11833ECDB4F9A609821A637BC7F,
	gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl5_mE465A99D7A5FDA173F615C95A56582FAA3553193,
	gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl7_mD2457D7BEC6A0AE46E473CC4E2CDCF3EE8DBF7F0,
	gameplay_tD448BE8FE6744E948A65562DA623286FC2CC261F_CustomAttributesCacheGenerator_gameplay_lvl0_mEEB025DEF93243F8959C9CF6E309A0582C316691,
	U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56__ctor_mE4E1A42457E3124976DD73E8A9A59F196E027090,
	U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_IDisposable_Dispose_mD644E8350E522FC488F9A31B7F82D5B0400F3A7D,
	U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEA76BFC814DD367A3B382F1EE6399BE317112A7E,
	U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_Collections_IEnumerator_Reset_mE9C4D92ACE2895DEDF2B1FE7F2E3B8B434AF9A9A,
	U3Clvl1U3Ed__56_t2C0427959B68245BCE373FBAF12031152E5F82D4_CustomAttributesCacheGenerator_U3Clvl1U3Ed__56_System_Collections_IEnumerator_get_Current_mF653D72D087211FC9AE7331E286474EE4EC350F8,
	U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57__ctor_m2EFFDF87AA2BFC5D3DA40264E825A3C26362C0CE,
	U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_IDisposable_Dispose_m4D1C8CFB2CDE3D156A311E59B92937267663962A,
	U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m502500F20DE9F50F19AC1A274D7864CFF008708F,
	U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_Collections_IEnumerator_Reset_mBDD3D39B888218936D37127AF3FC42C46452EFB1,
	U3Clvl2U3Ed__57_tAFBD151EAF5BE63AE38D3DED2E0A223019187CBA_CustomAttributesCacheGenerator_U3Clvl2U3Ed__57_System_Collections_IEnumerator_get_Current_m7F1B9220F2F4A62551EA065C1A917ACB67DE5F45,
	U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58__ctor_m5F5BC85F8D246E3C0D35E5FCF5650E48438DEE75,
	U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_IDisposable_Dispose_mBAEEC551D6A403B453B735AADF2DEF366AC952DE,
	U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m21C3F4676E11B20E8C9DD88CC8483F6644AF9B18,
	U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_Collections_IEnumerator_Reset_m58488997363FEC377CD7CD8938796727DEACD2E4,
	U3Clvl5U3Ed__58_t73903F38534AB751D773742C87EE9AD5292DADCE_CustomAttributesCacheGenerator_U3Clvl5U3Ed__58_System_Collections_IEnumerator_get_Current_mEFACCB740AA3DF190553CB5471E81B414FBAC7D6,
	U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59__ctor_mEF40FF7E99BD7112CA3029749A5F8EDF590A9591,
	U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_IDisposable_Dispose_m1DD570AFC814E1D59B9622652298FD8D398DA3A6,
	U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4EE7A5B50DA55D5482C2183630F78A16A2800F15,
	U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_Collections_IEnumerator_Reset_m5D77977BFF43716E13D5C93FA3B833D219CED75A,
	U3Clvl7U3Ed__59_tC27C398653B698E4176D332366226916C5728F53_CustomAttributesCacheGenerator_U3Clvl7U3Ed__59_System_Collections_IEnumerator_get_Current_m9AC0E3A9E524228C023189A55B8D0C946175E6F7,
	U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60__ctor_m9DCD1CE8813F87353DE64E94CB456BDC65C8AADD,
	U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_IDisposable_Dispose_m0978BEAAFB3BAAD4F4D90C39CF341A1C41058A59,
	U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mABE72F3BB290F9274B298E22045957414E685854,
	U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_Collections_IEnumerator_Reset_m2249E8A74EB47EAF45BB4E180FE7D0C7C0B14C2B,
	U3Clvl0U3Ed__60_t833539681DF0604A9DDD29F65A799BE32462B428_CustomAttributesCacheGenerator_U3Clvl0U3Ed__60_System_Collections_IEnumerator_get_Current_mA7B9E52589C82967F50793D3AEC50D52CB0083F6,
	mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_loading_m974C5533A63E1BCCD69F13414389E7A4894AA90B,
	mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_loding_m3F53A9567827B070999CFDD722D504533DF87C9E,
	mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_load_m2C09862F74DAA82291C77B68A06CABE82B9CCA8C,
	mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_load1_mFBDBDF9E120DF76AAA9786952432BCA6A89B0C66,
	mainmenu_t8BE55F02D8C30BFD4C43C41B470220AF6F50BC02_CustomAttributesCacheGenerator_mainmenu_lod2_m3C32CA7424199D1DF6BCAC484E1A712B1BE51FA8,
	U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39__ctor_m6E3FC03533E32165DBA82BC30E8E728E245D1DB0,
	U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_IDisposable_Dispose_m340482F2B6E36928BC7A77E7843026EDE08DEBD1,
	U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D74B40166DEA5F08B1626BE899B7BD3E4EC784B,
	U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_Collections_IEnumerator_Reset_m7EC41A340C0613CDB99CD1EE5E5A95E952AD47EE,
	U3CloadingU3Ed__39_t3BEFB41E1228A0485972AB58FA7C7922E7BEDFDF_CustomAttributesCacheGenerator_U3CloadingU3Ed__39_System_Collections_IEnumerator_get_Current_m4D01BCCC9E254743B500D32E295079258EFC34BA,
	U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45__ctor_mA348507D99FF90AEB48918426A1CA4F39937C41A,
	U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_IDisposable_Dispose_mBF0A271F3AB8B910EC9D49AE9D2110162FBAE4A4,
	U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCBD6CD24E193E34EBC9C494309CF7AECBB8074CF,
	U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_Collections_IEnumerator_Reset_m8757C7459472A549127A813CB0C53400F5957B64,
	U3ClodingU3Ed__45_t095D84A25CE83CF23327BC49A989DB2E6C4F5379_CustomAttributesCacheGenerator_U3ClodingU3Ed__45_System_Collections_IEnumerator_get_Current_m85E0209029F5CEAD7E6370A74C02ABB2911F0349,
	U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46__ctor_m3F8833D5E42271B150929700613389E90A753EA9,
	U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_IDisposable_Dispose_m64A9A989D0F422E2A10DBA8312AB0A4D55C9C566,
	U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF7D814B5290AFD4675C4A1ACA51D91854381876C,
	U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_Collections_IEnumerator_Reset_m6F7822F745718F8D484D30E140AB8537A53468E9,
	U3CloadU3Ed__46_tB01D3926173CD667E8D40C6C4130B7BF1308EEBF_CustomAttributesCacheGenerator_U3CloadU3Ed__46_System_Collections_IEnumerator_get_Current_m7DE394C9A1824F61F51EA0DC310A4BD59494C796,
	U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47__ctor_mEBA0E592445B2C566D58411EB5D4C82078FBBEF6,
	U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_IDisposable_Dispose_m4BEB4DB183B0F6F71E186A3C18BB359254514962,
	U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCF06F55E47D6EB22E57CD961BC3C6DF6768AFAAF,
	U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_Collections_IEnumerator_Reset_m08878A97DB160EE3BC983A626AB66D00CBCCAD3A,
	U3Cload1U3Ed__47_tF02CB3E30DBD252CF51AE3E6675301CABBB523AB_CustomAttributesCacheGenerator_U3Cload1U3Ed__47_System_Collections_IEnumerator_get_Current_m7F6A1C3904D02C53B01621FC9D3AFFB6E9960748,
	U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48__ctor_mD1723DAC9616E26A433A7E11CEB236671FB5B476,
	U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_IDisposable_Dispose_m8BA719AA6E68CA80163FDC5EB3844D066E72244A,
	U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m32C1CBC447D7A1F0933C26FB4009744C8B998E23,
	U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_Collections_IEnumerator_Reset_m6119113503F3F7DD48CD9298A57E31C5CC2DD099,
	U3Clod2U3Ed__48_tA9708ED9F7710E96267366F99FEDBF7043D85824_CustomAttributesCacheGenerator_U3Clod2U3Ed__48_System_Collections_IEnumerator_get_Current_m31F6464795D1660A336D499BC584E47E8E211BAF,
	manger2_t6D3650AF285836D8C01E8E89ECA0ADE70A0DF2E7_CustomAttributesCacheGenerator_manger2_start_mF7905CD41FC4F9C5B6E99A3B72144A1C09860052,
	manger2_t6D3650AF285836D8C01E8E89ECA0ADE70A0DF2E7_CustomAttributesCacheGenerator_manger2_obect_mE9A71697220BFB6C64C7B84A184C7EC578E071EA,
	manger2_t6D3650AF285836D8C01E8E89ECA0ADE70A0DF2E7_CustomAttributesCacheGenerator_manger2_strt_m039C3A25F67681C51A80999F2376E17ABDFDD1E6,
	U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24__ctor_m5DEE7DDB19BF271D5A33A477561A6E315342189A,
	U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_IDisposable_Dispose_mFACAD12B5AD1C8EEC0441BA4AA524B261C8FDB77,
	U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD8FC10AF493EFB7391EEB109EE8D9028C3D552A8,
	U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_Collections_IEnumerator_Reset_mF6780D537B9EBF1E49349F59FC3D0B8ED362428A,
	U3CstartU3Ed__24_tEA8879B7A66D434D2FA8CDA6597CD0D7A447F2E6_CustomAttributesCacheGenerator_U3CstartU3Ed__24_System_Collections_IEnumerator_get_Current_m4C1BA7A30A06294A299D337756AE0AF96A99572C,
	U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25__ctor_mE5DC237276F154F4CC97B951EBEBD42410A4B0DE,
	U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_IDisposable_Dispose_m42743905108FA175D898410D377F0696B456B0E9,
	U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m92463448801F7F98AEFE06D2D1F5F6E3B148BA22,
	U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_Collections_IEnumerator_Reset_mC27CAB5EED358B0E5F7AE37AB0E4BB481C66F505,
	U3CobectU3Ed__25_t21054EF8DCC9754C61E24FEDABAB3C3E4D2B009B_CustomAttributesCacheGenerator_U3CobectU3Ed__25_System_Collections_IEnumerator_get_Current_mBA65AE75917D2F878E8A835886C7A4D6DFDB4AB2,
	U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26__ctor_mCCDCD27B5FD203C4A6FAD5DFBBD5E7452B315620,
	U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_IDisposable_Dispose_m6C707852E0BE32F4725F21A6042848C63FA3DD0B,
	U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1E7F837CAD35AC52B569095A33494BA6747F2B0F,
	U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_Collections_IEnumerator_Reset_mFAD7C77823E3FE573B35E48CA2AE175F79A346BE,
	U3CstrtU3Ed__26_tBD133BC10A3C82556ADD6F701AFF96F5910B98CA_CustomAttributesCacheGenerator_U3CstrtU3Ed__26_System_Collections_IEnumerator_get_Current_mCB29ECBEA6C4DCFD31BB26971E2E6993390CC739,
	player_tA80E0022C279849EE513BB6A4017ED62943ADC71_CustomAttributesCacheGenerator_player_end_mE3A73A581F4D2AE9D208F1B10F6C62E4AB189E31,
	player_tA80E0022C279849EE513BB6A4017ED62943ADC71_CustomAttributesCacheGenerator_player_end1_m570774867FE7BA7D3DBCEB2E4E75AA766332DE00,
	player_tA80E0022C279849EE513BB6A4017ED62943ADC71_CustomAttributesCacheGenerator_player_lvl6_m33375427C70655644B18D709087A612DC8FA5A5E,
	U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17__ctor_m54D9016ECDB432ED712D6F06007B82FCF226CAFC,
	U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_IDisposable_Dispose_m87B1BAD34325D7644464870E5F275AD5AB6F24DB,
	U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A3DBD6F5B7F6E46C4FB207F9540541464F2B53A,
	U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_Collections_IEnumerator_Reset_mAC65AD5E5A699707CA188140BA46B67B990C35D0,
	U3CendU3Ed__17_tDFF0B6607F19A09EDF3B143AB959BAE5E4CF4135_CustomAttributesCacheGenerator_U3CendU3Ed__17_System_Collections_IEnumerator_get_Current_mFCB8DA308C1E2FB13319B250288E37A9625CDFCB,
	U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18__ctor_mCA0B5DC2E2851ABDD7A79BD4D598C1257DCD25BE,
	U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_IDisposable_Dispose_m0CA518025250D42F03A0892B90BC9ED04D6292B5,
	U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7A720503E941B74F8E5D33A81BD6697774CE4B8C,
	U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_Collections_IEnumerator_Reset_m07DA0736EF0D66CAB0F90F42C5A5A49835DE169E,
	U3Cend1U3Ed__18_t91B8DA66E2C6365089C78758E65E83FD7B44BCDF_CustomAttributesCacheGenerator_U3Cend1U3Ed__18_System_Collections_IEnumerator_get_Current_m5DE3F0165B0786FA5F6FA1581D13F939182EBF8E,
	U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19__ctor_mDB18D4251B4F1069B69F9B95D57511CEDB1ECBB4,
	U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_IDisposable_Dispose_m4E1D48EA935AB6220B5C7EB5417B3E828BF7C6C3,
	U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4056B2F8F96C4274CABDD0D14C45F05481003ABB,
	U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_Collections_IEnumerator_Reset_mC12E146F0D6D1CB27AFB7C7BA03DA91F10D7E4E2,
	U3Clvl6U3Ed__19_t769526BE60EC97675C720CF82E1E60DC3049297C_CustomAttributesCacheGenerator_U3Clvl6U3Ed__19_System_Collections_IEnumerator_get_Current_mA46297F7242F0AF07D15F6FC61D55D8DEA4164D1,
	startscene_t2CCBF105CBE570083DB724FFA05E2CD33283149A_CustomAttributesCacheGenerator_startscene_load_mD919890EC64F9A4913422ED0A59861A6DE185C3B,
	U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4__ctor_mDDEB6A99F7CD51746E3F63C14596586EBE32CD8C,
	U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_IDisposable_Dispose_mF0978BF8E970E01288786C36C522FFF679326B42,
	U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3A7B178E9FC4BF7AA0E8E01F4149B530309C422C,
	U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_Collections_IEnumerator_Reset_m19068CB4D993EBF7D6154E6C7C183B0F09053E90,
	U3CloadU3Ed__4_t10DFD693BFE8800C8BBAAFEC402BD991A8A83713_CustomAttributesCacheGenerator_U3CloadU3Ed__4_System_Collections_IEnumerator_get_Current_mB92520266F090E771D54FE565B244BE04ABEED44,
	SciFiLoopScript_t5A0D6FB48CDFFBD8FFFEA178BC2E196E6436CDCB_CustomAttributesCacheGenerator_SciFiLoopScript_EffectLoop_m73BC3A0884B7A62594CA5F184D431DB81E2A6ED7,
	U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4__ctor_m9FDDAD0DE8BB6450657E8E7B7B83BC85838F6B6C,
	U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_IDisposable_Dispose_m4B490EBA83EF4F6E7753CA05239EFB051DFDBCA1,
	U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5C6B23587D0C866DFF63356BDAEEB61CA603BB3B,
	U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m992C579A5BA9707614B0E1C087C94BED47BC8C11,
	U3CEffectLoopU3Ed__4_t41595263BA8E4548889C4F9F4925BA108FC89733_CustomAttributesCacheGenerator_U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_get_Current_m79C803F68611B6834219B1F249C098DAF2E52BC7,
	RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB_CustomAttributesCacheGenerator_RCC_CarControllerV3_tC8674C9DC8A9AD14EE9EFF8B590C7D2D186FD6DB____AIController_PropertyInfo,
	AssemblyU2DCSharp_CustomAttributesCacheGenerator,
};
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, bool ___value0, const RuntimeMethod* method)
{
	{
		bool L_0 = ___value0;
		__this->set_m_wrapNonExceptionThrows_0(L_0);
		return;
	}
}
