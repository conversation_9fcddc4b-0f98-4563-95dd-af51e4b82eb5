
=== Tue Apr 11 11:00:41 2023

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.collab-proxy@1.15.13
  com.unity.ide.rider@2.0.7
  com.unity.ide.visualstudio@2.0.14
  com.unity.ide.vscode@1.2.5
  com.unity.modules.ai@1.0.0
  com.unity.modules.androidjni@1.0.0
  com.unity.modules.animation@1.0.0
  com.unity.modules.assetbundle@1.0.0
  com.unity.modules.audio@1.0.0
  com.unity.modules.cloth@1.0.0
  com.unity.modules.director@1.0.0
  com.unity.modules.imageconversion@1.0.0
  com.unity.modules.imgui@1.0.0
  com.unity.modules.jsonserialize@1.0.0
  com.unity.modules.particlesystem@1.0.0
  com.unity.modules.physics@1.0.0
  com.unity.modules.physics2d@1.0.0
  com.unity.modules.screencapture@1.0.0
  com.unity.modules.terrain@1.0.0
  com.unity.modules.terrainphysics@1.0.0
  com.unity.modules.tilemap@1.0.0
  com.unity.modules.ui@1.0.0
  com.unity.modules.uielements@1.0.0
  com.unity.modules.umbra@1.0.0
  com.unity.modules.unityanalytics@1.0.0
  com.unity.modules.unitywebrequest@1.0.0
  com.unity.modules.unitywebrequestassetbundle@1.0.0
  com.unity.modules.unitywebrequestaudio@1.0.0
  com.unity.modules.unitywebrequesttexture@1.0.0
  com.unity.modules.unitywebrequestwww@1.0.0
  com.unity.modules.vehicles@1.0.0
  com.unity.modules.video@1.0.0
  com.unity.modules.vr@1.0.0
  com.unity.modules.wind@1.0.0
  com.unity.modules.xr@1.0.0
  com.unity.test-framework@1.1.31
  com.unity.textmeshpro@3.0.6
  com.unity.timeline@1.4.8
  com.unity.ugui@1.0.0

=== Wed Jul 19 11:26:29 2023

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.collab-proxy from version 1.15.13 to 2.0.5
  com.unity.ide.rider from version 2.0.7 to 3.0.24
  com.unity.ide.visualstudio from version 2.0.14 to 2.0.18
  com.unity.test-framework from version 1.1.31 to 1.1.33
  com.unity.timeline from version 1.4.8 to 1.6.5

=== Mon Dec 16 15:32:35 2024

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.collab-proxy from version 2.0.5 to 2.5.2
  com.unity.ide.rider from version 3.0.24 to 3.0.31
  com.unity.ide.visualstudio from version 2.0.18 to 2.0.22
  com.unity.probuilder from version 4.5.2 to 5.2.3

=== Wed Jul 16 12:37:23 2025

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.inputsystem from version 1.3.0 to 1.7.0
  com.unity.shadergraph from version 7.7.1 to 12.1.15
  com.unity.textmeshpro from version 2.1.6 to 3.0.6

=== Wed Jul 16 12:37:23 2025

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.inputsystem@1.7.0
  com.unity.shadergraph@12.1.15

=== Wed Jul 16 12:41:54 2025

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.inputsystem from version 1.3.0 to 1.7.0
  com.unity.shadergraph from version 7.7.1 to 12.1.15
  com.unity.textmeshpro from version 2.1.6 to 3.0.6

=== Wed Jul 16 12:55:21 2025

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.inputsystem from version 1.3.0 to 1.7.0
  com.unity.shadergraph from version 7.7.1 to 12.1.15
  com.unity.textmeshpro from version 2.1.6 to 3.0.6
