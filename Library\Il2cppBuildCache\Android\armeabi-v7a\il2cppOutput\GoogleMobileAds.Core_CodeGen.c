﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Api.AdManager.AdManagerAdRequest::.ctor()
extern void AdManagerAdRequest__ctor_m34E1A34DBDE358F53E6B6535773D61C2C2E99EA6 (void);
// 0x00000002 System.Void GoogleMobileAds.Api.AdRequest::.cctor()
extern void AdRequest__cctor_mB567C9044ECBDFE38B504F9C4960B6E6995510D0 (void);
// 0x00000003 System.Void GoogleMobileAds.Api.AdRequest::.ctor()
extern void AdRequest__ctor_mFA778383D6EE57A62A19BE2F446F898CB178B7E8 (void);
// 0x00000004 System.String GoogleMobileAds.Api.AdRequest::get_Version()
extern void AdRequest_get_Version_m0140F2D9E5FE5EB8A076CD376660C34DB55C10F9 (void);
// 0x00000005 System.Void GoogleMobileAds.Api.AdRequest::set_Version(System.String)
extern void AdRequest_set_Version_m938C94CD619E6DAB2EAA5860BD8EE98A802DF82F (void);
// 0x00000006 System.String GoogleMobileAds.Api.AdRequest::BuildVersionString(System.String)
extern void AdRequest_BuildVersionString_m23168963AC9A833D3CD7F43AEBB20D638DDEDD09 (void);
// 0x00000007 System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32)
extern void AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929 (void);
// 0x00000008 System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32,GoogleMobileAds.Api.AdSize/Type)
extern void AdSize__ctor_m0D17F8059B3EF0FC19B2E9AE90E06857D2880792 (void);
// 0x00000009 GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::CreateAnchoredAdaptiveAdSize(System.Int32,GoogleMobileAds.Api.Orientation)
extern void AdSize_CreateAnchoredAdaptiveAdSize_mB4718A1E214DA7FBF8806046D159F60C876868C6 (void);
// 0x0000000A GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth(System.Int32)
extern void AdSize_GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth_m397FAC78A9C9E87A5D8808145567068138BF3FFE (void);
// 0x0000000B System.Int32 GoogleMobileAds.Api.AdSize::get_Width()
extern void AdSize_get_Width_m3BEB8032410BE3D663C957BFCF01E6A59F208B31 (void);
// 0x0000000C System.Int32 GoogleMobileAds.Api.AdSize::get_Height()
extern void AdSize_get_Height_m5D9A50F40AE5D1ADE624BC788141DB38BD999455 (void);
// 0x0000000D GoogleMobileAds.Api.AdSize/Type GoogleMobileAds.Api.AdSize::get_AdType()
extern void AdSize_get_AdType_m1B1093C98B765BA1EB2E238CD241366915D33967 (void);
// 0x0000000E GoogleMobileAds.Api.Orientation GoogleMobileAds.Api.AdSize::get_Orientation()
extern void AdSize_get_Orientation_m128E54BB23D60EC047E3ABCCF14039599A901A18 (void);
// 0x0000000F System.Boolean GoogleMobileAds.Api.AdSize::Equals(System.Object)
extern void AdSize_Equals_mEDEC9BFF9CB8BAA2BFD25A9A8151894B9E34AC51 (void);
// 0x00000010 System.Int32 GoogleMobileAds.Api.AdSize::GetHashCode()
extern void AdSize_GetHashCode_m69600B06087C9871F8B2596DA604B592BCB1283E (void);
// 0x00000011 System.Void GoogleMobileAds.Api.AdSize::.cctor()
extern void AdSize__cctor_mCF933C938E0761B4033174E15EB117B05D813C3C (void);
// 0x00000012 System.Void GoogleMobileAds.Api.AdValue::.ctor()
extern void AdValue__ctor_m37CBA3EBBF99BEBC2ADFD28630D1EE6A3071395B (void);
// 0x00000013 System.Void GoogleMobileAds.Api.AdValue::set_Precision(GoogleMobileAds.Api.AdValue/PrecisionType)
extern void AdValue_set_Precision_m41801AC0C15990FC28445C401A818279D6734FEC (void);
// 0x00000014 System.Void GoogleMobileAds.Api.AdValue::set_Value(System.Int64)
extern void AdValue_set_Value_m253FBF7AB640E038912EDE5A26E097B6503713C1 (void);
// 0x00000015 System.Void GoogleMobileAds.Api.AdValue::set_CurrencyCode(System.String)
extern void AdValue_set_CurrencyCode_m0B1E6921005D7C14D7DEDB5716B62884773560B0 (void);
// 0x00000016 System.Void GoogleMobileAds.Api.AdValueEventArgs::.ctor()
extern void AdValueEventArgs__ctor_mF6CEC05465A21DFA6AC1305763E27E4989E11F32 (void);
// 0x00000017 GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.AdValueEventArgs::get_AdValue()
extern void AdValueEventArgs_get_AdValue_m47E07F15E141D9E7E4599AD72935E6CCCBFE047C (void);
// 0x00000018 System.Void GoogleMobileAds.Api.AdValueEventArgs::set_AdValue(GoogleMobileAds.Api.AdValue)
extern void AdValueEventArgs_set_AdValue_m68EFEA232807581D77002A5E00A88CBB2D287591 (void);
// 0x00000019 System.Void GoogleMobileAds.Api.AdapterStatus::.ctor(GoogleMobileAds.Api.AdapterState,System.String,System.Int32)
extern void AdapterStatus__ctor_m464B6C58733F1FC6FC8171A3AC4B27C97B5F81CA (void);
// 0x0000001A System.Void GoogleMobileAds.Api.AdapterStatus::set_InitializationState(GoogleMobileAds.Api.AdapterState)
extern void AdapterStatus_set_InitializationState_m8D23EB6FAA5DC25A033006B82E4640E5E0FCF4DE (void);
// 0x0000001B System.Void GoogleMobileAds.Api.AdapterStatus::set_Description(System.String)
extern void AdapterStatus_set_Description_m12EECD98F5BE85D58D7C1632F6D80FEF1A3BA733 (void);
// 0x0000001C System.Void GoogleMobileAds.Api.AdapterStatus::set_Latency(System.Int32)
extern void AdapterStatus_set_Latency_m6AFDC90CA78BE3A71352878AD5B16B86C9290A2B (void);
// 0x0000001D System.Void GoogleMobileAds.Api.AdManager.AppEvent::.ctor()
extern void AppEvent__ctor_m01D295C1FC52F4EDE3A6456CC5071FC3EAE34ECF (void);
// 0x0000001E System.Void GoogleMobileAds.Api.AdManager.AppEvent::set_Name(System.String)
extern void AppEvent_set_Name_m607FC3BF917696813C1CF88E0B1355A717E22115 (void);
// 0x0000001F System.Void GoogleMobileAds.Api.AdManager.AppEvent::set_Data(System.String)
extern void AppEvent_set_Data_m1B6BDC68337061F10E7C61AAD9672E7C9E22652E (void);
// 0x00000020 System.Void GoogleMobileAds.Api.MaxAdContentRating::.ctor(System.String)
extern void MaxAdContentRating__ctor_m61B9B7976E688768408525833B8F0416837E72BB (void);
// 0x00000021 GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.MaxAdContentRating::ToMaxAdContentRating(System.String)
extern void MaxAdContentRating_ToMaxAdContentRating_mD30E1DF2AB94EE009F7CB1EA0D02A3A7976F6887 (void);
// 0x00000022 System.String GoogleMobileAds.Api.MaxAdContentRating::get_Value()
extern void MaxAdContentRating_get_Value_m7BFF3E65CC7A4C015DC31B26AF20C6C332FC85CA (void);
// 0x00000023 System.Void GoogleMobileAds.Api.MaxAdContentRating::set_Value(System.String)
extern void MaxAdContentRating_set_Value_m87B495BE0B1BB9D04CE8B144A4D99A2A5ADD9646 (void);
// 0x00000024 System.Void GoogleMobileAds.Api.RequestConfiguration::.ctor()
extern void RequestConfiguration__ctor_mB474168D61CAEE374CA2497A21DD2FF1FB337E99 (void);
// 0x00000025 System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::.ctor()
extern void Builder__ctor_m421D9BBC603B8E15090C742C5B714B8260609ABB (void);
// 0x00000026 GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.RequestConfiguration/Builder::get_MaxAdContentRating()
extern void Builder_get_MaxAdContentRating_m9A3592507C10CC11286B697201CC6465ABC79671 (void);
// 0x00000027 System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_MaxAdContentRating(GoogleMobileAds.Api.MaxAdContentRating)
extern void Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C (void);
// 0x00000028 System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TagForChildDirectedTreatment()
extern void Builder_get_TagForChildDirectedTreatment_mCD38E6703E8325EB13CE37474C0A347AC206FDCF (void);
// 0x00000029 System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TagForChildDirectedTreatment(System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>)
extern void Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA (void);
// 0x0000002A System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TagForUnderAgeOfConsent()
extern void Builder_get_TagForUnderAgeOfConsent_m44813E5169846F8DB5D7C2D840C828DB225B60CF (void);
// 0x0000002B System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TagForUnderAgeOfConsent(System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>)
extern void Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF (void);
// 0x0000002C System.Collections.Generic.List`1<System.String> GoogleMobileAds.Api.RequestConfiguration/Builder::get_TestDeviceIds()
extern void Builder_get_TestDeviceIds_m4963727F9F2E889F7E0B5C8CFCDB5256C1F456AE (void);
// 0x0000002D System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_TestDeviceIds(System.Collections.Generic.List`1<System.String>)
extern void Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F (void);
// 0x0000002E System.Nullable`1<System.Boolean> GoogleMobileAds.Api.RequestConfiguration/Builder::get_SameAppKeyEnabled()
extern void Builder_get_SameAppKeyEnabled_m6B799BA968F08CA2F07E3B1F6375EFFD2C7F17C1 (void);
// 0x0000002F System.Void GoogleMobileAds.Api.RequestConfiguration/Builder::set_SameAppKeyEnabled(System.Nullable`1<System.Boolean>)
extern void Builder_set_SameAppKeyEnabled_m4F22919DC10A3690CB48160BF601A27DEE973C1A (void);
// 0x00000030 GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetMaxAdContentRating(GoogleMobileAds.Api.MaxAdContentRating)
extern void Builder_SetMaxAdContentRating_mC248A6EA37CD3C767C58A6C544A1D716A51AB4D0 (void);
// 0x00000031 GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetTagForChildDirectedTreatment(System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>)
extern void Builder_SetTagForChildDirectedTreatment_m9D68DD5CD57DDA251CF26C4C62BB939C3725A09C (void);
// 0x00000032 GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetTagForUnderAgeOfConsent(System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>)
extern void Builder_SetTagForUnderAgeOfConsent_m596B502201DDB50C6D52BA1DB44CE36A6D99320D (void);
// 0x00000033 GoogleMobileAds.Api.RequestConfiguration/Builder GoogleMobileAds.Api.RequestConfiguration/Builder::SetTestDeviceIds(System.Collections.Generic.List`1<System.String>)
extern void Builder_SetTestDeviceIds_mC86FC178F26606663E63411D30FBB4956024B98E (void);
// 0x00000034 GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Api.RequestConfiguration/Builder::build()
extern void Builder_build_m167E7DDB110F1F7DD752424FA257F5802E77E4DA (void);
// 0x00000035 System.Void GoogleMobileAds.Api.Reward::.ctor()
extern void Reward__ctor_mE06181CF7816ECBE92B2B7B000C75F12721C2DFA (void);
// 0x00000036 System.Void GoogleMobileAds.Api.ServerSideVerificationOptions::.ctor()
extern void ServerSideVerificationOptions__ctor_m7F3CA9D05B947139B90C70BBDFC312CBF2A3F9E2 (void);
// 0x00000037 System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.Mediation.MediationExtras::get_Extras()
extern void MediationExtras_get_Extras_m053815293202950C6CF255BE08D4FD7F783C1BEB (void);
// 0x00000038 System.String GoogleMobileAds.Api.Mediation.MediationExtras::get_AndroidMediationExtraBuilderClassName()
static Il2CppMethodPointer s_methodPointers[56] = 
{
	AdManagerAdRequest__ctor_m34E1A34DBDE358F53E6B6535773D61C2C2E99EA6,
	AdRequest__cctor_mB567C9044ECBDFE38B504F9C4960B6E6995510D0,
	AdRequest__ctor_mFA778383D6EE57A62A19BE2F446F898CB178B7E8,
	AdRequest_get_Version_m0140F2D9E5FE5EB8A076CD376660C34DB55C10F9,
	AdRequest_set_Version_m938C94CD619E6DAB2EAA5860BD8EE98A802DF82F,
	AdRequest_BuildVersionString_m23168963AC9A833D3CD7F43AEBB20D638DDEDD09,
	AdSize__ctor_mEB6363D6EABAD51D48C38D42169170DD20AD2929,
	AdSize__ctor_m0D17F8059B3EF0FC19B2E9AE90E06857D2880792,
	AdSize_CreateAnchoredAdaptiveAdSize_mB4718A1E214DA7FBF8806046D159F60C876868C6,
	AdSize_GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth_m397FAC78A9C9E87A5D8808145567068138BF3FFE,
	AdSize_get_Width_m3BEB8032410BE3D663C957BFCF01E6A59F208B31,
	AdSize_get_Height_m5D9A50F40AE5D1ADE624BC788141DB38BD999455,
	AdSize_get_AdType_m1B1093C98B765BA1EB2E238CD241366915D33967,
	AdSize_get_Orientation_m128E54BB23D60EC047E3ABCCF14039599A901A18,
	AdSize_Equals_mEDEC9BFF9CB8BAA2BFD25A9A8151894B9E34AC51,
	AdSize_GetHashCode_m69600B06087C9871F8B2596DA604B592BCB1283E,
	AdSize__cctor_mCF933C938E0761B4033174E15EB117B05D813C3C,
	AdValue__ctor_m37CBA3EBBF99BEBC2ADFD28630D1EE6A3071395B,
	AdValue_set_Precision_m41801AC0C15990FC28445C401A818279D6734FEC,
	AdValue_set_Value_m253FBF7AB640E038912EDE5A26E097B6503713C1,
	AdValue_set_CurrencyCode_m0B1E6921005D7C14D7DEDB5716B62884773560B0,
	AdValueEventArgs__ctor_mF6CEC05465A21DFA6AC1305763E27E4989E11F32,
	AdValueEventArgs_get_AdValue_m47E07F15E141D9E7E4599AD72935E6CCCBFE047C,
	AdValueEventArgs_set_AdValue_m68EFEA232807581D77002A5E00A88CBB2D287591,
	AdapterStatus__ctor_m464B6C58733F1FC6FC8171A3AC4B27C97B5F81CA,
	AdapterStatus_set_InitializationState_m8D23EB6FAA5DC25A033006B82E4640E5E0FCF4DE,
	AdapterStatus_set_Description_m12EECD98F5BE85D58D7C1632F6D80FEF1A3BA733,
	AdapterStatus_set_Latency_m6AFDC90CA78BE3A71352878AD5B16B86C9290A2B,
	AppEvent__ctor_m01D295C1FC52F4EDE3A6456CC5071FC3EAE34ECF,
	AppEvent_set_Name_m607FC3BF917696813C1CF88E0B1355A717E22115,
	AppEvent_set_Data_m1B6BDC68337061F10E7C61AAD9672E7C9E22652E,
	MaxAdContentRating__ctor_m61B9B7976E688768408525833B8F0416837E72BB,
	MaxAdContentRating_ToMaxAdContentRating_mD30E1DF2AB94EE009F7CB1EA0D02A3A7976F6887,
	MaxAdContentRating_get_Value_m7BFF3E65CC7A4C015DC31B26AF20C6C332FC85CA,
	MaxAdContentRating_set_Value_m87B495BE0B1BB9D04CE8B144A4D99A2A5ADD9646,
	RequestConfiguration__ctor_mB474168D61CAEE374CA2497A21DD2FF1FB337E99,
	Builder__ctor_m421D9BBC603B8E15090C742C5B714B8260609ABB,
	Builder_get_MaxAdContentRating_m9A3592507C10CC11286B697201CC6465ABC79671,
	Builder_set_MaxAdContentRating_m7BA9784198E3F9E39EB9F4C854CE615E78594B9C,
	Builder_get_TagForChildDirectedTreatment_mCD38E6703E8325EB13CE37474C0A347AC206FDCF,
	Builder_set_TagForChildDirectedTreatment_mC0A9E652562C63ACDED1E629794534B714B38DAA,
	Builder_get_TagForUnderAgeOfConsent_m44813E5169846F8DB5D7C2D840C828DB225B60CF,
	Builder_set_TagForUnderAgeOfConsent_m10C8AF106278D4731B0DA929A3D2F5BA989225AF,
	Builder_get_TestDeviceIds_m4963727F9F2E889F7E0B5C8CFCDB5256C1F456AE,
	Builder_set_TestDeviceIds_m43C3C09E57363CCD0B2E019564BAD5E0A3FEDF0F,
	Builder_get_SameAppKeyEnabled_m6B799BA968F08CA2F07E3B1F6375EFFD2C7F17C1,
	Builder_set_SameAppKeyEnabled_m4F22919DC10A3690CB48160BF601A27DEE973C1A,
	Builder_SetMaxAdContentRating_mC248A6EA37CD3C767C58A6C544A1D716A51AB4D0,
	Builder_SetTagForChildDirectedTreatment_m9D68DD5CD57DDA251CF26C4C62BB939C3725A09C,
	Builder_SetTagForUnderAgeOfConsent_m596B502201DDB50C6D52BA1DB44CE36A6D99320D,
	Builder_SetTestDeviceIds_mC86FC178F26606663E63411D30FBB4956024B98E,
	Builder_build_m167E7DDB110F1F7DD752424FA257F5802E77E4DA,
	Reward__ctor_mE06181CF7816ECBE92B2B7B000C75F12721C2DFA,
	ServerSideVerificationOptions__ctor_m7F3CA9D05B947139B90C70BBDFC312CBF2A3F9E2,
	MediationExtras_get_Extras_m053815293202950C6CF255BE08D4FD7F783C1BEB,
	NULL,
};
static const int32_t s_InvokerIndices[56] = 
{
	1935,
	3231,
	1935,
	3215,
	3189,
	3069,
	871,
	538,
	2782,
	3066,
	1886,
	1886,
	1886,
	1886,
	1182,
	1886,
	3231,
	1935,
	1597,
	1598,
	1610,
	1935,
	1900,
	1610,
	544,
	1597,
	1610,
	1597,
	1935,
	1610,
	1610,
	1610,
	3069,
	1900,
	1610,
	1935,
	1935,
	1900,
	1610,
	1839,
	1549,
	1840,
	1550,
	1900,
	1610,
	1837,
	1548,
	1431,
	1416,
	1417,
	1431,
	1900,
	1935,
	1935,
	1900,
	1900,
};
extern const CustomAttributesCacheGenerator g_GoogleMobileAds_Core_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Core_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Core_CodeGenModule = 
{
	"GoogleMobileAds.Core.dll",
	56,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_GoogleMobileAds_Core_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
