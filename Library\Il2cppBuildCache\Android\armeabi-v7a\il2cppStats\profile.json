{"instructions_readme": "1) Open Chrome, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "traceEvents": [{"pid": 1, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 1, "ts": 835.3000283241272, "dur": 33389128.90625, "ph": "X", "name": "il2cpp.exe", "args": {"durationMS": 33389.12890625, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 37671.00143432617, "dur": 33350035.15625, "ph": "X", "name": "Build", "args": {"durationMS": 33350.03515625, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 37927.398681640625, "dur": 120868.5073852539, "ph": "X", "name": "FindFilesToCompile", "args": {"durationMS": 120.8685073852539, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 158927.2918701172, "dur": 142255.2032470703, "ph": "X", "name": "Calculate header hashes", "args": {"durationMS": 142.2552032470703, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 301182.80029296875, "dur": 21377.901077270508, "ph": "X", "name": "Calculate lumped hashes", "args": {"durationMS": 21.377901077270508, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 322680.6945800781, "dur": 5935458.49609375, "ph": "X", "name": "Compile All Files", "args": {"durationMS": 5935.45849609375, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 6258155.76171875, "dur": 674.4999885559082, "ph": "X", "name": "OnBeforeLink Build", "args": {"durationMS": 0.6744999885559082, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 6258846.6796875, "dur": 27058406.25, "ph": "X", "name": "Link", "args": {"durationMS": 27058.40625, "detail": "libil2cpp.so"}}, {"pid": 1, "tid": 1, "ts": 6275729.98046875, "dur": 7019.400119781494, "ph": "X", "name": "hash linker invocation", "args": {"durationMS": 7.019400119781494, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 6317286.1328125, "dur": 23497843.75, "ph": "X", "name": "ActualLinkerInvocation", "args": {"durationMS": 23497.84375, "detail": "libil2cpp.so"}}, {"pid": 1, "tid": 1, "ts": 29815353.515625, "dur": 2482865.234375, "ph": "X", "name": "ToolChain OnAfterLink Build", "args": {"durationMS": 2482.865234375, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 29818945.3125, "dur": 75557.40356445312, "ph": "X", "name": "strip", "args": {"durationMS": 75.55740356445312, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 29894550.78125, "dur": 1970484.86328125, "ph": "X", "name": "strip", "args": {"durationMS": 1970.48486328125, "detail": "debug"}}, {"pid": 1, "tid": 1, "ts": 31865273.4375, "dur": 432943.603515625, "ph": "X", "name": "strip", "args": {"durationMS": 432.943603515625, "detail": "debug link"}}, {"pid": 1, "tid": 1, "ts": 32298220.703125, "dur": 1018960.9985351562, "ph": "X", "name": "Copy from cache to output directory", "args": {"durationMS": 1018.96**********2, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 33317253.90625, "dur": 14877.20012664795, "ph": "X", "name": "ProgramDescription Finalize Build", "args": {"durationMS": 14.87720012664795, "detail": ""}}, {"pid": 1, "tid": 1, "ts": 33332128.90625, "dur": 55574.00131225586, "ph": "X", "name": "Clean IL2CPP Cache", "args": {"durationMS": 55.57400131225586, "detail": ""}}, {"pid": 1, "tid": 12, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 12, "ts": 261307.**********, "dur": 11794.699668884277, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 11.794699668884277, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\pch"}}, {"pid": 1, "tid": 12, "ts": 262602.**********, "dur": 9870.400428771973, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 9.870400428771973, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\pch"}}, {"pid": 1, "tid": 12, "ts": 272480.89599609375, "dur": 619.5999979972839, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.6195999979972839, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\pch"}}, {"pid": 1, "tid": 5, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 5, "ts": 261317.90161132812, "dur": 5650.300025939941, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 5.650300025939941, "detail": "."}}, {"pid": 1, "tid": 5, "ts": 262602.**********, "dur": 3345.7999229431152, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 3.3457999229431152, "detail": ".h ."}}, {"pid": 1, "tid": 5, "ts": 265957.**********, "dur": 1008.4999799728394, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.0084999799728394, "detail": ".inc ."}}, {"pid": 1, "tid": 6, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 6, "ts": 261318.29833984375, "dur": 18878.799438476562, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 18.878799438476562, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include"}}, {"pid": 1, "tid": 6, "ts": 262606.**********, "dur": 16425.399780273438, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 16.425399780273438, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include"}}, {"pid": 1, "tid": 6, "ts": 279045.**********, "dur": 1142.699956893921, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.142699956893921, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include"}}, {"pid": 1, "tid": 7, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 7, "ts": 261324.70703125, "dur": 12327.699661254883, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 12.327699661254883, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Platforms\\Android\\Include"}}, {"pid": 1, "tid": 7, "ts": 262612.**********, "dur": 10378.899574279785, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 10.378899574279785, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Platforms\\Android\\Include"}}, {"pid": 1, "tid": 7, "ts": 272996.**********, "dur": 654.2999744415283, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.6542999744415283, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Platforms\\Android\\Include"}}, {"pid": 1, "tid": 8, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 8, "ts": 261334.80834960938, "dur": 14828.099250793457, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 14.828099250793457, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include"}}, {"pid": 1, "tid": 8, "ts": 262613.09814453125, "dur": 12979.700088500977, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 12.979700088500977, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include"}}, {"pid": 1, "tid": 8, "ts": 275596.**********, "dur": 561.5000128746033, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.5615000128746033, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include"}}, {"pid": 1, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 9, "ts": 261341.40014648438, "dur": 27900.89988708496, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 27.90089988708496, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src"}}, {"pid": 1, "tid": 9, "ts": 262626.**********, "dur": 25701.000213623047, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 25.701000213623047, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src"}}, {"pid": 1, "tid": 9, "ts": 288332.**********, "dur": 901.199996471405, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.901199996471405, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src"}}, {"pid": 1, "tid": 10, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 10, "ts": 261343.90258789062, "dur": 11875.200271606445, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 11.875200271606445, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\xxHash"}}, {"pid": 1, "tid": 10, "ts": 262632.**********, "dur": 9842.599868774414, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 9.842599868774414, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\xxHash"}}, {"pid": 1, "tid": 10, "ts": 272480.**********, "dur": 730.3000092506409, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.7303000092506409, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\xxHash"}}, {"pid": 1, "tid": 11, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 11, "ts": 261350.61645507812, "dur": 39765.39993286133, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 39.76539993286133, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp"}}, {"pid": 1, "tid": 11, "ts": 262641.**********, "dur": 34100.89874267578, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 34.10089874267578, "detail": ".h C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp"}}, {"pid": 1, "tid": 11, "ts": 296747.802734375, "dur": 4332.200050354004, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 4.332200050354004, "detail": ".inc C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp"}}, {"pid": 1, "tid": 13, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 13, "ts": 261404.20532226562, "dur": 5654.600143432617, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 5.654600143432617, "detail": "D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Library\\Il2cppBuildCache\\Android\\armeabi-v7a\\il2cppOutput"}}, {"pid": 1, "tid": 13, "ts": 262651.123046875, "dur": 3297.8999614715576, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 3.2978999614715576, "detail": ".h D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Library\\Il2cppBuildCache\\Android\\armeabi-v7a\\il2cppOutput"}}, {"pid": 1, "tid": 13, "ts": 265959.89990234375, "dur": 1097.6999998092651, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.0976999998092651, "detail": ".inc D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Library\\Il2cppBuildCache\\Android\\armeabi-v7a\\il2cppOutput"}}, {"pid": 1, "tid": 17, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 17, "ts": 266116.39404296875, "dur": 6115.200042724609, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.115200042724609, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\pch\\pch-c.h"}}, {"pid": 1, "tid": 22, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 22, "ts": 266122.**********, "dur": 6108.699798583984, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.108699798583984, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include\\cord.h"}}, {"pid": 1, "tid": 22, "ts": 272828.**********, "dur": 571.7999935150146, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.5717999935150146, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include\\gc.h"}}, {"pid": 1, "tid": 22, "ts": 274582.**********, "dur": 404.70001101493835, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.40470001101493835, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include\\private\\gc_priv.h"}}, {"pid": 1, "tid": 25, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 25, "ts": 266130.9814453125, "dur": 6123.899936676025, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.123899936676025, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src\\atomic_ops.h"}}, {"pid": 1, "tid": 25, "ts": 272264.1906738281, "dur": 576.69997215271, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.57669997215271, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src\\atomic_ops\\generalize-arithm.h"}}, {"pid": 1, "tid": 25, "ts": 273894.1955566406, "dur": 14286.999702453613, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 14.286999702453613, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src\\atomic_ops\\sysdeps\\gcc\\generic.h"}}, {"pid": 1, "tid": 19, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 19, "ts": 266136.0778808594, "dur": 6109.300136566162, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.109300136566162, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\xxHash\\xxh3.h"}}, {"pid": 1, "tid": 28, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 28, "ts": 266147.3083496094, "dur": 6084.3000411987305, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.0843000411987305, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\xxHash\\xxhash.h"}}, {"pid": 1, "tid": 27, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 27, "ts": 266149.4140625, "dur": 6148.900032043457, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.148900032043457, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src\\atomic_ops\\ao_version.h"}}, {"pid": 1, "tid": 27, "ts": 272308.41064453125, "dur": 444.0999925136566, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.4440999925136566, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\libatomic_ops\\src\\atomic_ops\\generalize-small.h"}}, {"pid": 1, "tid": 29, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 29, "ts": 266154.1748046875, "dur": 6136.300086975098, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.136300086975098, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Platforms\\Android\\Include\\BaselibPlatformSpecificEnvironment.h"}}, {"pid": 1, "tid": 31, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 31, "ts": 266158.99658203125, "dur": 6123.499870300293, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.123499870300293, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\Baselib.h"}}, {"pid": 1, "tid": 23, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 23, "ts": 266159.7900390625, "dur": 6105.100154876709, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.105100154876709, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include\\cord_pos.h"}}, {"pid": 1, "tid": 23, "ts": 274672.30224609375, "dur": 535.0000262260437, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.5350000262260437, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include\\private\\gcconfig.h"}}, {"pid": 1, "tid": 30, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 30, "ts": 266164.1845703125, "dur": 6073.699951171875, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.073699951171875, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Platforms\\Android\\Include\\C\\Baselib_ErrorState.inl.h"}}, {"pid": 1, "tid": 32, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 32, "ts": 266172.4853515625, "dur": 6101.29976272583, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 6.10129976272583, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_Alignment.h"}}, {"pid": 1, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 44, "ts": 279582.8857421875, "dur": 1598.0000495910645, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 1.5980000495910645, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Globalization\\Generated\\CultureInfoTablesNet_4_0.h"}}, {"pid": 1, "tid": 44, "ts": 285179.01611328125, "dur": 704.9000263214111, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.7049000263214111, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\il2cpp-normalization-tables.h"}}, {"pid": 1, "tid": 53, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 53, "ts": 302109.**********, "dur": 14392.899513244629, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 14.392899513244629, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc"}}, {"pid": 1, "tid": 53, "ts": 302118.**********, "dur": 3631.7999362945557, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 3.6317999362945557, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc"}}, {"pid": 1, "tid": 53, "ts": 305755.**********, "dur": 10734.49993133545, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 10.73449993133545, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc"}}, {"pid": 1, "tid": 54, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 54, "ts": 302209.41162109375, "dur": 2032.599925994873, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 2.032599925994873, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\codegen"}}, {"pid": 1, "tid": 54, "ts": 302214.**********, "dur": 1018.8000202178955, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.0188000202178955, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\codegen"}}, {"pid": 1, "tid": 54, "ts": 303237.97607421875, "dur": 1002.6999711990356, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.0026999711990356, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\codegen"}}, {"pid": 1, "tid": 54, "ts": 304243.**********, "dur": 3054.500102996826, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 3.054500102996826, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm-utils"}}, {"pid": 1, "tid": 54, "ts": 304248.**********, "dur": 1963.7000560760498, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.9637000560760498, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm-utils"}}, {"pid": 1, "tid": 54, "ts": 306225.89111328125, "dur": 1071.2000131607056, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.0712000131607056, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm-utils"}}, {"pid": 1, "tid": 55, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 55, "ts": 302316.**********, "dur": 2561.800003051758, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 2.561800003051758, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\debugger"}}, {"pid": 1, "tid": 55, "ts": 302321.**********, "dur": 828.8999795913696, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.8288999795913696, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\debugger"}}, {"pid": 1, "tid": 55, "ts": 303161.**********, "dur": 1715.1999473571777, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.7151999473571777, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\debugger"}}, {"pid": 1, "tid": 56, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 56, "ts": 302427.**********, "dur": 2831.5000534057617, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 2.8315000534057617, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\gc"}}, {"pid": 1, "tid": 56, "ts": 302435.**********, "dur": 1034.7000360488892, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.0347000360488892, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\gc"}}, {"pid": 1, "tid": 56, "ts": 303473.69384765625, "dur": 1783.5999727249146, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.7835999727249146, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\gc"}}, {"pid": 1, "tid": 57, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 57, "ts": 302575.98876953125, "dur": 19347.10121154785, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 19.34710121154785, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls"}}, {"pid": 1, "tid": 57, "ts": 302584.**********, "dur": 16831.501007080078, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 16.831501007080078, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls"}}, {"pid": 1, "tid": 57, "ts": 319424.31640625, "dur": 2482.1999073028564, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 2.4821999073028564, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls"}}, {"pid": 1, "tid": 67, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 67, "ts": 302684.**********, "dur": 365.9999966621399, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.3659999966621399, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\debugger\\il2cpp-stubs.cpp"}}, {"pid": 1, "tid": 58, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 58, "ts": 302772.**********, "dur": 3962.3000621795654, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 3.9623000621795654, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\metadata"}}, {"pid": 1, "tid": 58, "ts": 302778.50341796875, "dur": 2456.899881362915, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 2.456899881362915, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\metadata"}}, {"pid": 1, "tid": 58, "ts": 305245.78857421875, "dur": 1487.2000217437744, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.4872000217437744, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\metadata"}}, {"pid": 1, "tid": 59, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 59, "ts": 303054.**********, "dur": 4080.2998542785645, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 4.0802998542785645, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\mono"}}, {"pid": 1, "tid": 59, "ts": 303061.**********, "dur": 1977.3999452590942, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 1.9773999452590942, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\mono"}}, {"pid": 1, "tid": 59, "ts": 305042.29736328125, "dur": 2088.200092315674, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 2.088200092315674, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\mono"}}, {"pid": 1, "tid": 60, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 60, "ts": 303241.**********, "dur": 19243.999481201172, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 19.243999481201172, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\os"}}, {"pid": 1, "tid": 60, "ts": 303251.**********, "dur": 18296.300888061523, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 18.296300888061523, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\os"}}, {"pid": 1, "tid": 60, "ts": 321552.**********, "dur": 913.9000177383423, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.9139000177383423, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\os"}}, {"pid": 1, "tid": 61, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 61, "ts": 303468.81103515625, "dur": 3703.0999660491943, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 3.7030999660491943, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils"}}, {"pid": 1, "tid": 61, "ts": 303474.30419921875, "dur": 2740.7000064849854, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 2.7407000064849854, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils"}}, {"pid": 1, "tid": 61, "ts": 306218.994140625, "dur": 939.1000270843506, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.9391000270843506, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils"}}, {"pid": 1, "tid": 62, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 62, "ts": 303803.**********, "dur": 6751.800060272217, "ph": "X", "name": "FileHashProvider.Initialize", "args": {"durationMS": 6.751800060272217, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm"}}, {"pid": 1, "tid": 62, "ts": 303809.**********, "dur": 6118.899822235107, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 6.118899822235107, "detail": ".cpp C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm"}}, {"pid": 1, "tid": 62, "ts": 309933.**********, "dur": 611.4000082015991, "ph": "X", "name": "HashOfAllFilesWithProviderExtensionInDirectory", "args": {"durationMS": 0.6114000082015991, "detail": ".c C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm"}}, {"pid": 1, "tid": 78, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 78, "ts": 304613.**********, "dur": 352.49999165534973, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.35249999165534973, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\mono\\ThreadPool\\threadpool-ms.cpp"}}, {"pid": 1, "tid": 82, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 82, "ts": 305177.91748046875, "dur": 428.4999966621399, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.4284999966621399, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils\\Il2CppHStringReference.cpp"}}, {"pid": 1, "tid": 86, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 86, "ts": 305944.0002441406, "dur": 370.49999833106995, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.37049999833106995, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\Class.cpp"}}, {"pid": 1, "tid": 86, "ts": 306746.7041015625, "dur": 437.20000982284546, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.43720000982284546, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\GlobalMetadata.cpp"}}, {"pid": 1, "tid": 86, "ts": 307622.61962890625, "dur": 342.5000011920929, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.3425000011920929, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\MetadataCache.cpp"}}, {"pid": 1, "tid": 97, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 97, "ts": 312494.384765625, "dur": 417.69999265670776, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.41769999265670776, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\mark.c"}}, {"pid": 1, "tid": 97, "ts": 313170.6237792969, "dur": 698.5999941825867, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.6985999941825867, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\os_dep.c"}}, {"pid": 1, "tid": 98, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 98, "ts": 312853.515625, "dur": 414.6000146865845, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.4146000146865845, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\misc.c"}}, {"pid": 1, "tid": 98, "ts": 313614.990234375, "dur": 367.000013589859, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.367000013589859, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\pthread_support.c"}}, {"pid": 1, "tid": 98, "ts": 315880.0964355469, "dur": 514.19997215271, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.51419997215271, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\win32_threads.c"}}, {"pid": 1, "tid": 99, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 99, "ts": 311752.50244140625, "dur": 379.7999918460846, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.3797999918460846, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Mono.Interop\\ComInteropProxy.cpp"}}, {"pid": 1, "tid": 100, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 100, "ts": 311270.99609375, "dur": 341.8999910354614, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.3418999910354614, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.Net.Sockets\\Socket.cpp"}}, {"pid": 1, "tid": 100, "ts": 315403.2897949219, "dur": 415.10000824928284, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.41510000824928284, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.InteropServices\\Marshal.cpp"}}, {"pid": 1, "tid": 100, "ts": 317693.115234375, "dur": 388.9999985694885, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.3889999985694885, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System\\Decimal.cpp"}}, {"pid": 1, "tid": 103, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 103, "ts": 313154.4189453125, "dur": 524.8000025749207, "ph": "X", "name": "HashOfFile", "args": {"durationMS": 0.5248000025749207, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Messages.cpp"}}, {"pid": 1, "tid": 112, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 112, "ts": 336723.20556640625, "dur": 27420.09925842285, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 27.42009925842285, "detail": "Il2CppGenericMethodPointerTable.cpp"}}, {"pid": 1, "tid": 112, "ts": 364194.6105957031, "dur": 11600.3999710083, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.6003999710083, "detail": "mscorlib10.cpp"}}, {"pid": 1, "tid": 112, "ts": 375830.2917480469, "dur": 11834.600448608398, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.834600448608398, "detail": "mscorlib4.cpp"}}, {"pid": 1, "tid": 112, "ts": 387706.298828125, "dur": 12356.900215148926, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.356900215148926, "detail": "GenericMethods.cpp"}}, {"pid": 1, "tid": 112, "ts": 400106.01806640625, "dur": 13990.301132202148, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.990301132202148, "detail": "Generics10.cpp"}}, {"pid": 1, "tid": 112, "ts": 414143.49365234375, "dur": 5741.8999671936035, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 5.7418999671936035, "detail": "Firebase.App.cpp"}}, {"pid": 1, "tid": 112, "ts": 419928.5888671875, "dur": 4471.700191497803, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.471700191497803, "detail": "UnityEngine.UI_CodeGen.c"}}, {"pid": 1, "tid": 112, "ts": 424443.115234375, "dur": 10821.700096130371, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.821700096130371, "detail": "Unity.ProBuilder_Attr.cpp"}}, {"pid": 1, "tid": 112, "ts": 435309.326171875, "dur": 2958.8000774383545, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.9588000774383545, "detail": "UnityEngine.UIModule.cpp"}}, {"pid": 1, "tid": 112, "ts": 438306.9152832031, "dur": 13932.80029296875, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.93280029296875, "detail": "UnityEngine.AnimationModule_Attr.cpp"}}, {"pid": 1, "tid": 112, "ts": 452320.00732421875, "dur": 4645.599842071533, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.645599842071533, "detail": "GoogleMobileAds.Ump.cpp"}}, {"pid": 1, "tid": 112, "ts": 457000.48828125, "dur": 2102.799892425537, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.102799892425537, "detail": "UnityEngine.GridModule_Attr.cpp"}}, {"pid": 1, "tid": 112, "ts": 459138.2141113281, "dur": 2625.9000301361084, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6259000301361084, "detail": "UnityEngine.TerrainPhysicsModule_Attr.cpp"}}, {"pid": 1, "tid": 112, "ts": 461805.9**********, "dur": 2607.599973678589, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.607599973678589, "detail": "UnityEngine.InputModule.cpp"}}, {"pid": 1, "tid": 112, "ts": 464447.4792480469, "dur": 4232.500076293945, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.232500076293945, "detail": "il2cpp-api.cpp"}}, {"pid": 1, "tid": 112, "ts": 468712.70751953125, "dur": 6732.399940490723, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.732399940490723, "detail": "infback.c"}}, {"pid": 1, "tid": 112, "ts": 475495.5749511719, "dur": 2148.900032043457, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.148900032043457, "detail": "gzlib.c"}}, {"pid": 1, "tid": 112, "ts": 477694.4274902344, "dur": 3283.6999893188477, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.2836999893188477, "detail": "GoogleMobileAds.Ump_CodeGen.c"}}, {"pid": 1, "tid": 112, "ts": 481016.2048339844, "dur": 2601.2001037597656, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6012001037597656, "detail": "Mono.Security_CodeGen.c"}}, {"pid": 1, "tid": 112, "ts": 483661.5295410156, "dur": 2501.8999576568604, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.5018999576568604, "detail": "UnityEngine.VRModule_CodeGen.c"}}, {"pid": 1, "tid": 112, "ts": 486200.4089355469, "dur": 2217.099905014038, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.217099905014038, "detail": "il2cpp-benchmark-support.cpp"}}, {"pid": 1, "tid": 112, "ts": 488451.2634277344, "dur": 15452.59952545166, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.45259952545166, "detail": "Il2CppGenericComDefinitions1.cpp"}}, {"pid": 1, "tid": 112, "ts": 504073.8830566406, "dur": 3125.699996948242, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.125699996948242, "detail": "Il2CppGenericComDefinitions24.cpp"}}, {"pid": 1, "tid": 112, "ts": 507246.7041015625, "dur": 2889.4999027252197, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8894999027252197, "detail": "Il2CppGenericComDefinitions38.cpp"}}, {"pid": 1, "tid": 116, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 116, "ts": 336724.0905761719, "dur": 27599.90119934082, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 27.59990119934082, "detail": "Generics9.cpp"}}, {"pid": 1, "tid": 116, "ts": 364360.3820800781, "dur": 17696.701049804688, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 17.696701049804688, "detail": "Generics8.cpp"}}, {"pid": 1, "tid": 116, "ts": 382094.6044921875, "dur": 13791.799545288086, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.791799545288086, "detail": "mscorlib5.cpp"}}, {"pid": 1, "tid": 116, "ts": 395913.1774902344, "dur": 11677.800178527832, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.677800178527832, "detail": "mscorlib15.cpp"}}, {"pid": 1, "tid": 116, "ts": 407629.21142578125, "dur": 6788.79976272583, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.78879976272583, "detail": "UnityEngine.UI3.cpp"}}, {"pid": 1, "tid": 116, "ts": 414459.89990234375, "dur": 4831.299781799316, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.831299781799316, "detail": "UnityEngine.AndroidJNIModule.cpp"}}, {"pid": 1, "tid": 116, "ts": 419332.82470703125, "dur": 3975.1999378204346, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.9751999378204346, "detail": "UnityEngine.CoreModule_CodeGen.c"}}, {"pid": 1, "tid": 116, "ts": 423342.1936035156, "dur": 11178.199768066406, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.178199768066406, "detail": "char-conversions.cpp"}}, {"pid": 1, "tid": 116, "ts": 434554.**********, "dur": 4475.399971008301, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.475399971008301, "detail": "Il2CppGenericClassTable.c"}}, {"pid": 1, "tid": 116, "ts": 439063.29345703125, "dur": 12789.69955444336, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.78969955444336, "detail": "Il2CppCCFieldValuesTable.cpp"}}, {"pid": 1, "tid": 116, "ts": 451895.3857421875, "dur": 4756.29997253418, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.75629997253418, "detail": "UnityEngine.UIModule_Attr.cpp"}}, {"pid": 1, "tid": 116, "ts": 456709.3200683594, "dur": 3066.499948501587, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.066499948501587, "detail": "UnityEngine.AIModule_Attr.cpp"}}, {"pid": 1, "tid": 116, "ts": 459811.6760253906, "dur": 3233.4001064300537, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.2334001064300537, "detail": "GoogleMobileAds.Core_Attr.cpp"}}, {"pid": 1, "tid": 116, "ts": 463076.5075683594, "dur": 3017.2998905181885, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.0172998905181885, "detail": "UnityEngine.AIModule.cpp"}}, {"pid": 1, "tid": 116, "ts": 466134.8876953125, "dur": 3857.300043106079, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.857300043106079, "detail": "Unity.ProBuilder.KdTree.cpp"}}, {"pid": 1, "tid": 116, "ts": 470029.96826171875, "dur": 9985.300064086914, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.985300064086914, "detail": "Lump_libil2cpp_os.cpp"}}, {"pid": 1, "tid": 116, "ts": 480051.69677734375, "dur": 4348.599910736084, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.348599910736084, "detail": "Lump_libil2cpp_vm.cpp"}}, {"pid": 1, "tid": 116, "ts": 484458.7097167969, "dur": 17034.000396728516, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 17.034000396728516, "detail": "krait_signal_handler.c"}}, {"pid": 1, "tid": 116, "ts": 501529.3884277344, "dur": 1988.6000156402588, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.9886000156402588, "detail": "Il2CppGenericComDefinitions14.cpp"}}, {"pid": 1, "tid": 116, "ts": 503558.5021972656, "dur": 2647.7999687194824, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6477999687194824, "detail": "Il2CppGenericComDefinitions20.cpp"}}, {"pid": 1, "tid": 116, "ts": 506236.7248535156, "dur": 1757.4000358581543, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.7574000358581543, "detail": "Il2CppGenericComDefinitions32.cpp"}}, {"pid": 1, "tid": 116, "ts": 508045.9899902344, "dur": 2701.200008392334, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.701200008392334, "detail": "Il2CppGenericComDefinitions8.cpp"}}, {"pid": 1, "tid": 110, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 110, "ts": 336727.**********, "dur": 42913.60092163086, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 42.91360092163086, "detail": "Il2CppCCalculateTypeValues.cpp"}}, {"pid": 1, "tid": 110, "ts": 379696.4111328125, "dur": 16933.500289916992, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 16.933500289916992, "detail": "Il2CppMetadataUsage.c"}}, {"pid": 1, "tid": 110, "ts": 396666.50390625, "dur": 11594.200134277344, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.594200134277344, "detail": "mscorlib3.cpp"}}, {"pid": 1, "tid": 110, "ts": 408288.9099121094, "dur": 6486.899852752686, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.4868998527526855, "detail": "System.cpp"}}, {"pid": 1, "tid": 110, "ts": 414816.8029785156, "dur": 9290.499687194824, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.290499687194824, "detail": "Il2CppGenericInstDefinitions.c"}}, {"pid": 1, "tid": 110, "ts": 424141.**********, "dur": 12212.699890136719, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.212699890136719, "detail": "UnityEngine.UI_Attr.cpp"}}, {"pid": 1, "tid": 110, "ts": 436387.1154785156, "dur": 2850.600004196167, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.850600004196167, "detail": "UnityEngine.PhysicsModule_Attr.cpp"}}, {"pid": 1, "tid": 110, "ts": 439269.9890136719, "dur": 15019.500732421875, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.019500732421875, "detail": "UnityEngine.InputLegacyModule.cpp"}}, {"pid": 1, "tid": 110, "ts": 454323.2116699219, "dur": 2884.9000930786133, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8849000930786133, "detail": "UnityEngine.TerrainModule_Attr.cpp"}}, {"pid": 1, "tid": 110, "ts": 457236.6943359375, "dur": 4069.6001052856445, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.0696001052856445, "detail": "UnityEngine.VRModule_Attr.cpp"}}, {"pid": 1, "tid": 110, "ts": 461337.9211425781, "dur": 2335.9999656677246, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.3359999656677246, "detail": "UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 1, "tid": 110, "ts": 463708.4045410156, "dur": 3795.2001094818115, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.7952001094818115, "detail": "GoogleMobileAds.Common_Attr.cpp"}}, {"pid": 1, "tid": 110, "ts": 467536.8957519531, "dur": 2654.2000770568848, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6542000770568848, "detail": "xamarin_getifaddrs.c"}}, {"pid": 1, "tid": 110, "ts": 470224.8840332031, "dur": 6532.899856567383, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.532899856567383, "detail": "Firebase.Analytics_CodeGen.c"}}, {"pid": 1, "tid": 110, "ts": 476786.80419921875, "dur": 2792.29998588562, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.79229998588562, "detail": "Unity.ProBuilder.KdTree_CodeGen.c"}}, {"pid": 1, "tid": 110, "ts": 479618.8049316406, "dur": 2645.4999446868896, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6454999446868896, "detail": "zutil.c"}}, {"pid": 1, "tid": 110, "ts": 482304.931640625, "dur": 2463.900089263916, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.463900089263916, "detail": "UnityEngine.Physics2DModule_CodeGen.c"}}, {"pid": 1, "tid": 110, "ts": 484802.2155761719, "dur": 3282.399892807007, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.282399892807007, "detail": "Lump_libil2cpp_vm-utils.cpp"}}, {"pid": 1, "tid": 110, "ts": 488112.8845214844, "dur": 14216.09878540039, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.21609878540039, "detail": "Il2CppGenericComDefinitions.cpp"}}, {"pid": 1, "tid": 110, "ts": 502362.5183105469, "dur": 2785.900115966797, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.785900115966797, "detail": "Il2CppGenericComDefinitions17.cpp"}}, {"pid": 1, "tid": 110, "ts": 505195.4040527344, "dur": 1927.299976348877, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.927299976348877, "detail": "Il2CppGenericComDefinitions28.cpp"}}, {"pid": 1, "tid": 110, "ts": 507156.494140625, "dur": 2148.0000019073486, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.1480000019073486, "detail": "Il2CppGenericComDefinitions37.cpp"}}, {"pid": 1, "tid": 115, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 115, "ts": 336733.21533203125, "dur": 26913.999557495117, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 26.913999557495117, "detail": "GenericMethods3.cpp"}}, {"pid": 1, "tid": 115, "ts": 364008.9111328125, "dur": 14663.299560546875, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.663299560546875, "detail": "UnityEngine.UI.cpp"}}, {"pid": 1, "tid": 115, "ts": 378720.52001953125, "dur": 10945.899963378906, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.945899963378906, "detail": "Assembly-CSharp1.cpp"}}, {"pid": 1, "tid": 115, "ts": 389699.6154785156, "dur": 12930.700302124023, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.930700302124023, "detail": "GenericMethods2.cpp"}}, {"pid": 1, "tid": 115, "ts": 402667.6025390625, "dur": 13644.30046081543, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.64430046081543, "detail": "Generics7.cpp"}}, {"pid": 1, "tid": 115, "ts": 416348.1140136719, "dur": 7270.400047302246, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 7.270400047302246, "detail": "System2.cpp"}}, {"pid": 1, "tid": 115, "ts": 423654.5104980469, "dur": 11901.100158691406, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.901100158691406, "detail": "Il2CppGenericMethodTable.c"}}, {"pid": 1, "tid": 115, "ts": 435608.88671875, "dur": 3753.4000873565674, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.7534000873565674, "detail": "Firebase.Analytics.cpp"}}, {"pid": 1, "tid": 115, "ts": 439400.390625, "dur": 14960.599899291992, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.960599899291992, "detail": "UnityEngine.XRModule_Attr.cpp"}}, {"pid": 1, "tid": 115, "ts": 454394.10400390625, "dur": 3036.400079727173, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.036400079727173, "detail": "UnityEngine.Physics2DModule_Attr.cpp"}}, {"pid": 1, "tid": 115, "ts": 457479.43115234375, "dur": 2700.2999782562256, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.7002999782562256, "detail": "System_Attr.cpp"}}, {"pid": 1, "tid": 115, "ts": 460212.7990722656, "dur": 3615.799903869629, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.615799903869629, "detail": "UnityEngine.VehiclesModule.cpp"}}, {"pid": 1, "tid": 115, "ts": 463880.615234375, "dur": 3176.0001182556152, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.1760001182556152, "detail": "Firebase.Analytics_Attr.cpp"}}, {"pid": 1, "tid": 115, "ts": 467090.1184082031, "dur": 3591.599941253662, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.591599941253662, "detail": "Mono.Security_Attr.cpp"}}, {"pid": 1, "tid": 115, "ts": 470716.73583984375, "dur": 6687.20006942749, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.68720006942749, "detail": "UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 1, "tid": 115, "ts": 477435.302734375, "dur": 2497.299909591675, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.497299909591675, "detail": "GoogleMobileAds.Ump.Android_CodeGen.c"}}, {"pid": 1, "tid": 115, "ts": 479961.7004394531, "dur": 2213.599920272827, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.213599920272827, "detail": "UnityEngine.TilemapModule.cpp"}}, {"pid": 1, "tid": 115, "ts": 482204.77294921875, "dur": 19357.59925842285, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 19.35759925842285, "detail": "gc.c"}}, {"pid": 1, "tid": 115, "ts": 501585.1135253906, "dur": 2122.9000091552734, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.1229000091552734, "detail": "Il2CppGenericComDefinitions15.cpp"}}, {"pid": 1, "tid": 115, "ts": 503758.6975097656, "dur": 3044.300079345703, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.044300079345703, "detail": "Il2CppGenericComDefinitions23.cpp"}}, {"pid": 1, "tid": 115, "ts": 506842.22412109375, "dur": 1980.7000160217285, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.9807000160217285, "detail": "Il2CppGenericComDefinitions34.cpp"}}, {"pid": 1, "tid": 115, "ts": 508858.1237792969, "dur": 1997.499942779541, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.997499942779541, "detail": "il2cpp-runtime-stats.cpp"}}, {"pid": 1, "tid": 109, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 109, "ts": 336741.3024902344, "dur": 41404.300689697266, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 41.404300689697266, "detail": "Il2CppCCalculateFieldValues.cpp"}}, {"pid": 1, "tid": 109, "ts": 378178.1921386719, "dur": 11422.900199890137, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.422900199890137, "detail": "Unity.ProBuilder4.cpp"}}, {"pid": 1, "tid": 109, "ts": 389634.3994140625, "dur": 8966.60041809082, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 8.96660041809082, "detail": "UnityEngine.UI2.cpp"}}, {"pid": 1, "tid": 109, "ts": 398637.6037597656, "dur": 9446.800231933594, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.446800231933594, "detail": "Generics1.cpp"}}, {"pid": 1, "tid": 109, "ts": 408119.81201171875, "dur": 7317.100048065186, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 7.3171000480651855, "detail": "GenericMethods4.cpp"}}, {"pid": 1, "tid": 109, "ts": 415472.3205566406, "dur": 8328.300476074219, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 8.328300476074219, "detail": "Generics16.cpp"}}, {"pid": 1, "tid": 109, "ts": 423838.1042480469, "dur": 11555.299758911133, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.555299758911133, "detail": "UnresolvedVirtualCallStubs.cpp"}}, {"pid": 1, "tid": 109, "ts": 435431.7932128906, "dur": 3382.8001022338867, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.3828001022338867, "detail": "UnityEngine.TextRenderingModule.cpp"}}, {"pid": 1, "tid": 109, "ts": 438845.703125, "dur": 12877.699851989746, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.877699851989746, "detail": "UnityEngine.SharedInternalsModule.cpp"}}, {"pid": 1, "tid": 109, "ts": 451785.888671875, "dur": 3625.699996948242, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.625699996948242, "detail": "UnityEngine.SubsystemsModule.cpp"}}, {"pid": 1, "tid": 109, "ts": 455469.2077636719, "dur": 3308.7000846862793, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.3087000846862793, "detail": "UnityEngine.InputLegacyModule_Attr.cpp"}}, {"pid": 1, "tid": 109, "ts": 458813.0798339844, "dur": 2478.4998893737793, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4784998893737793, "detail": "UnityEngine.InputModule_Attr.cpp"}}, {"pid": 1, "tid": 109, "ts": 461322.509765625, "dur": 4058.000087738037, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.058000087738037, "detail": "inflate.c"}}, {"pid": 1, "tid": 109, "ts": 465420.6848144531, "dur": 2795.300006866455, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.795300006866455, "detail": "Firebase.Platform_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 468254.5166015625, "dur": 5357.200145721436, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 5.3572001457214355, "detail": "GoogleMobileAds.Common_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 473642.39501953125, "dur": 3316.4000511169434, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.3164000511169434, "detail": "UnityEngine.TerrainModule_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 476988.09814453125, "dur": 2572.200059890747, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.572200059890747, "detail": "UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 479610.6262207031, "dur": 2444.2999362945557, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4442999362945557, "detail": "UnityEngine.SubsystemsModule_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 482084.**********, "dur": 3045.9001064300537, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.0459001064300537, "detail": "UnityEngine.InputModule_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 485160.3088378906, "dur": 2373.1000423431396, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.3731000423431396, "detail": "UnityEngine.cpp"}}, {"pid": 1, "tid": 109, "ts": 487580.2001953125, "dur": 14790.899276733398, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.790899276733398, "detail": "UnityEngine.GridModule_CodeGen.c"}}, {"pid": 1, "tid": 109, "ts": 502406.7077636719, "dur": 2784.10005569458, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.78410005569458, "detail": "Il2CppGenericComDefinitions19.cpp"}}, {"pid": 1, "tid": 109, "ts": 505225.0061035156, "dur": 2186.000108718872, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.186000108718872, "detail": "Il2CppGenericComDefinitions29.cpp"}}, {"pid": 1, "tid": 109, "ts": 507437.4084472656, "dur": 2875.0998973846436, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8750998973846436, "detail": "Il2CppGenericComDefinitions5.cpp"}}, {"pid": 1, "tid": 111, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 111, "ts": 336742.98095703125, "dur": 29773.099899291992, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 29.773099899291992, "detail": "Il2CppInvokerTable.cpp"}}, {"pid": 1, "tid": 111, "ts": 366551.9104003906, "dur": 10814.200401306152, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.814200401306152, "detail": "mscorlib2.cpp"}}, {"pid": 1, "tid": 111, "ts": 377411.19384765625, "dur": 11140.899658203125, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.140899658203125, "detail": "mscorlib.cpp"}}, {"pid": 1, "tid": 111, "ts": 388588.68408203125, "dur": 10177.000045776367, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.177000045776367, "detail": "Unity.ProBuilder2.cpp"}}, {"pid": 1, "tid": 111, "ts": 398803.1005859375, "dur": 7655.399799346924, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 7.655399799346924, "detail": "mscorlib14.cpp"}}, {"pid": 1, "tid": 111, "ts": 406497.1008300781, "dur": 9659.700393676758, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.659700393676758, "detail": "UnityEngine.UI1.cpp"}}, {"pid": 1, "tid": 111, "ts": 416210.7849121094, "dur": 8118.000030517578, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 8.118000030517578, "detail": "Unity.ProBuilder.Poly2Tri.cpp"}}, {"pid": 1, "tid": 111, "ts": 424359.2834472656, "dur": 10892.000198364258, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.892000198364258, "detail": "Assembly-CSharp_CodeGen.c"}}, {"pid": 1, "tid": 111, "ts": 435280.3955078125, "dur": 4026.400089263916, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.026400089263916, "detail": "GoogleMobileAds.Ump.Android.cpp"}}, {"pid": 1, "tid": 111, "ts": 439349.18212890625, "dur": 15115.900993347168, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.115900993347168, "detail": "UnityEngine.AudioModule_Attr.cpp"}}, {"pid": 1, "tid": 111, "ts": 454522.1252441406, "dur": 4135.700225830078, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.135700225830078, "detail": "UnityEngine.AndroidJNIModule_CodeGen.c"}}, {"pid": 1, "tid": 111, "ts": 458715.4846191406, "dur": 2603.8999557495117, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6038999557495117, "detail": "Firebase.Platform_Attr.cpp"}}, {"pid": 1, "tid": 111, "ts": 461354.2785644531, "dur": 2728.3999919891357, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.7283999919891357, "detail": "System.Xml_Attr.cpp"}}, {"pid": 1, "tid": 111, "ts": 464118.22509765625, "dur": 3233.299970626831, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.233299970626831, "detail": "Unity.ProBuilder.KdTree_Attr.cpp"}}, {"pid": 1, "tid": 111, "ts": 467384.3078613281, "dur": 2892.6000595092773, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8926000595092773, "detail": "UnityEngine.Physics2DModule.cpp"}}, {"pid": 1, "tid": 111, "ts": 470308.10546875, "dur": 9354.999542236328, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.354999542236328, "detail": "Lump_libil2cpp_icalls.cpp"}}, {"pid": 1, "tid": 111, "ts": 479702.0263671875, "dur": 2689.69988822937, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.68969988822937, "detail": "System.Xml.cpp"}}, {"pid": 1, "tid": 111, "ts": 482418.88427734375, "dur": 3831.5000534057617, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.8315000534057617, "detail": "Lump_libil2cpp_metadata.cpp"}}, {"pid": 1, "tid": 111, "ts": 486279.6936035156, "dur": 2836.1001014709473, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8361001014709473, "detail": "UnityEngine.TerrainPhysicsModule_CodeGen.c"}}, {"pid": 1, "tid": 111, "ts": 489202.0263671875, "dur": 15281.399726867676, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.281399726867676, "detail": "Il2CppGenericComDefinitions12.cpp"}}, {"pid": 1, "tid": 111, "ts": 504515.0146484375, "dur": 2388.7999057769775, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.3887999057769775, "detail": "Il2CppGenericComDefinitions26.cpp"}}, {"pid": 1, "tid": 111, "ts": 506945.068359375, "dur": 3209.199905395508, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.209199905395508, "detail": "Il2CppGenericComDefinitions35.cpp"}}, {"pid": 1, "tid": 117, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 117, "ts": 336748.**********, "dur": 26544.099807739258, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 26.544099807739258, "detail": "Unity.ProBuilder.cpp"}}, {"pid": 1, "tid": 117, "ts": 364009.1857910156, "dur": 11437.700271606445, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.437700271606445, "detail": "mscorlib9.cpp"}}, {"pid": 1, "tid": 117, "ts": 375493.1945800781, "dur": 10939.200401306152, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.939200401306152, "detail": "mscorlib7.cpp"}}, {"pid": 1, "tid": 117, "ts": 386485.595703125, "dur": 12596.699714660645, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.596699714660645, "detail": "Assembly-CSharp2.cpp"}}, {"pid": 1, "tid": 117, "ts": 399117.5842285156, "dur": 10258.399963378906, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.258399963378906, "detail": "System1.cpp"}}, {"pid": 1, "tid": 117, "ts": 409408.69140625, "dur": 11292.699813842773, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.292699813842773, "detail": "GoogleMobileAds.Android.cpp"}}, {"pid": 1, "tid": 117, "ts": 420736.9079589844, "dur": 3751.699924468994, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.751699924468994, "detail": "Unity.ProBuilder_CodeGen.c"}}, {"pid": 1, "tid": 117, "ts": 424526.8859863281, "dur": 11060.199737548828, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.060199737548828, "detail": "Il2CppCCTypeValuesTable.cpp"}}, {"pid": 1, "tid": 117, "ts": 435615.78369140625, "dur": 4791.999816894531, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.791999816894531, "detail": "mscorlib17.cpp"}}, {"pid": 1, "tid": 117, "ts": 440437.2863769531, "dur": 14465.399742126465, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.465399742126465, "detail": "UnityEngine.SharedInternalsModule_Attr.cpp"}}, {"pid": 1, "tid": 117, "ts": 454939.1784667969, "dur": 3998.800039291382, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.998800039291382, "detail": "System.Core_Attr.cpp"}}, {"pid": 1, "tid": 117, "ts": 458972.0153808594, "dur": 2597.599983215332, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.597599983215332, "detail": "deflate.c"}}, {"pid": 1, "tid": 117, "ts": 461702.9113769531, "dur": 3640.3000354766846, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.6403000354766846, "detail": "GoogleMobileAds_CodeGen.c"}}, {"pid": 1, "tid": 117, "ts": 465377.5939941406, "dur": 4696.300029754639, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.696300029754639, "detail": "GoogleMobileAds.Ump_Attr.cpp"}}, {"pid": 1, "tid": 117, "ts": 470106.689453125, "dur": 6869.29988861084, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.86929988861084, "detail": "gzread.c"}}, {"pid": 1, "tid": 117, "ts": 477011.1999511719, "dur": 3036.900043487549, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.036900043487549, "detail": "inftrees.c"}}, {"pid": 1, "tid": 117, "ts": 480079.9865722656, "dur": 2948.4000205993652, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.9484000205993652, "detail": "adler32.c"}}, {"pid": 1, "tid": 117, "ts": 483064.208984375, "dur": 2832.70001411438, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.83270001411438, "detail": "UnityAdsStubs.cpp"}}, {"pid": 1, "tid": 117, "ts": 485929.2297363281, "dur": 2582.0999145507812, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.5820999145507812, "detail": "Lump_libil2cpp_gc.cpp"}}, {"pid": 1, "tid": 117, "ts": 488545.2880859375, "dur": 14321.000099182129, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.321000099182129, "detail": "Il2CppGenericComDefinitions10.cpp"}}, {"pid": 1, "tid": 117, "ts": 502907.28759765625, "dur": 2515.199899673462, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.515199899673462, "detail": "Il2CppGenericComDefinitions2.cpp"}}, {"pid": 1, "tid": 117, "ts": 505452.3010253906, "dur": 2078.0999660491943, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.0780999660491943, "detail": "Il2CppGenericComDefinitions3.cpp"}}, {"pid": 1, "tid": 117, "ts": 507559.87548828125, "dur": 2893.5999870300293, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8935999870300293, "detail": "Il2CppGenericComDefinitions6.cpp"}}, {"pid": 1, "tid": 114, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 114, "ts": 336748.**********, "dur": 28810.800552368164, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 28.810800552368164, "detail": "Generics5.cpp"}}, {"pid": 1, "tid": 114, "ts": 365596.00830078125, "dur": 11597.100257873535, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.597100257873535, "detail": "Unity.ProBuilder3.cpp"}}, {"pid": 1, "tid": 114, "ts": 377230.89599609375, "dur": 11073.399543762207, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.073399543762207, "detail": "UnityEngine.CoreModule.cpp"}}, {"pid": 1, "tid": 114, "ts": 388341.30859375, "dur": 10350.99983215332, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.35099983215332, "detail": "Generics15.cpp"}}, {"pid": 1, "tid": 114, "ts": 398726.1047363281, "dur": 8277.600288391113, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 8.277600288391113, "detail": "Assembly-CSharp3.cpp"}}, {"pid": 1, "tid": 114, "ts": 407038.4216308594, "dur": 7684.999942779541, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 7.684999942779541, "detail": "Generics3.cpp"}}, {"pid": 1, "tid": 114, "ts": 414752.01416015625, "dur": 5945.700168609619, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 5.945700168609619, "detail": "mscorlib_Attr.cpp"}}, {"pid": 1, "tid": 114, "ts": 420751.89208984375, "dur": 5154.099941253662, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 5.154099941253662, "detail": "Firebase.Platform.cpp"}}, {"pid": 1, "tid": 114, "ts": 425938.6901855469, "dur": 14401.299476623535, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.401299476623535, "detail": "UnityEngine.PhysicsModule.cpp"}}, {"pid": 1, "tid": 114, "ts": 440378.0212402344, "dur": 14246.899604797363, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.246899604797363, "detail": "UnityEngine.ParticleSystemModule_Attr.cpp"}}, {"pid": 1, "tid": 114, "ts": 454654.2053222656, "dur": 4123.700141906738, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.123700141906738, "detail": "GoogleMobileAds.Common.cpp"}}, {"pid": 1, "tid": 114, "ts": 458813.0798339844, "dur": 3349.400043487549, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.349400043487549, "detail": "UnityEngine.VehiclesModule_Attr.cpp"}}, {"pid": 1, "tid": 114, "ts": 462212.3107910156, "dur": 2871.500015258789, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.871500015258789, "detail": "System.Configuration.cpp"}}, {"pid": 1, "tid": 114, "ts": 465120.8190917969, "dur": 4508.999824523926, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.508999824523926, "detail": "System.Core_CodeGen.c"}}, {"pid": 1, "tid": 114, "ts": 469671.56982421875, "dur": 6071.499824523926, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.071499824523926, "detail": "UnityEngine.XRModule_CodeGen.c"}}, {"pid": 1, "tid": 114, "ts": 475795.71533203125, "dur": 3266.5998935699463, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.2665998935699463, "detail": "GoogleMobileAds.Core_CodeGen.c"}}, {"pid": 1, "tid": 114, "ts": 479100.40283203125, "dur": 2134.500026702881, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.134500026702881, "detail": "UnityEngine.VehiclesModule_CodeGen.c"}}, {"pid": 1, "tid": 114, "ts": 481268.49365234375, "dur": 2063.2998943328857, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.0632998943328857, "detail": "UnityEngine.SpriteShapeModule.cpp"}}, {"pid": 1, "tid": 114, "ts": 483365.5700683594, "dur": 4329.899787902832, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.329899787902832, "detail": "Lump_libil2cpp_utils.cpp"}}, {"pid": 1, "tid": 114, "ts": 487750.0915527344, "dur": 14570.499420166016, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.570499420166016, "detail": "UnityEngine_CodeGen.c"}}, {"pid": 1, "tid": 114, "ts": 502351.62353515625, "dur": 2387.5999450683594, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.3875999450683594, "detail": "Il2CppGenericComDefinitions16.cpp"}}, {"pid": 1, "tid": 114, "ts": 504770.9045410156, "dur": 2445.3999996185303, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4453999996185303, "detail": "Il2CppGenericComDefinitions27.cpp"}}, {"pid": 1, "tid": 114, "ts": 507259.4909667969, "dur": 2612.6999855041504, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6126999855041504, "detail": "Il2CppGenericComDefinitions4.cpp"}}, {"pid": 1, "tid": 113, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 113, "ts": 336759.9182128906, "dur": 30078.201293945312, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 30.078201293945312, "detail": "Il2CppTypeDefinitions.c"}}, {"pid": 1, "tid": 113, "ts": 366891.6015625, "dur": 10287.400245666504, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.287400245666504, "detail": "Generics.cpp"}}, {"pid": 1, "tid": 113, "ts": 377220.0012207031, "dur": 11269.200325012207, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.269200325012207, "detail": "mscorlib16.cpp"}}, {"pid": 1, "tid": 113, "ts": 388523.8037109375, "dur": 11645.000457763672, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 11.645000457763672, "detail": "Generics6.cpp"}}, {"pid": 1, "tid": 113, "ts": 400209.6862792969, "dur": 9407.600402832031, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.407600402832031, "detail": "mscorlib12.cpp"}}, {"pid": 1, "tid": 113, "ts": 409658.1115722656, "dur": 9799.099922180176, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.799099922180176, "detail": "UnityEngine.CoreModule_Attr.cpp"}}, {"pid": 1, "tid": 113, "ts": 419493.59130859375, "dur": 4330.8000564575195, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.3308000564575195, "detail": "GoogleMobileAds.cpp"}}, {"pid": 1, "tid": 113, "ts": 423855.1025390625, "dur": 10717.399597167969, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.717399597167969, "detail": "Assembly-CSharp_Attr.cpp"}}, {"pid": 1, "tid": 113, "ts": 434607.2998046875, "dur": 3500.0998973846436, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.5000998973846436, "detail": "UnityEngine.XRModule.cpp"}}, {"pid": 1, "tid": 113, "ts": 438207.275390625, "dur": 3043.600082397461, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.043600082397461, "detail": "GoogleMobileAds.Core.cpp"}}, {"pid": 1, "tid": 113, "ts": 452597.59521484375, "dur": 4077.4002075195312, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.077400207519531, "detail": "UnityEngine.AudioModule.cpp"}}, {"pid": 1, "tid": 113, "ts": 456710.8154296875, "dur": 2567.6000118255615, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.5676000118255615, "detail": "GoogleMobileAds.Android_CodeGen.c"}}, {"pid": 1, "tid": 113, "ts": 459389.70947265625, "dur": 3109.800100326538, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.109800100326538, "detail": "GoogleMobileAds_Attr.cpp"}}, {"pid": 1, "tid": 113, "ts": 462539.7644042969, "dur": 3322.700023651123, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.322700023651123, "detail": "System.Configuration_Attr.cpp"}}, {"pid": 1, "tid": 113, "ts": 466645.5993652344, "dur": 4360.7001304626465, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.3607001304626465, "detail": "System.Core.cpp"}}, {"pid": 1, "tid": 113, "ts": 473536.7126464844, "dur": 3379.300117492676, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.379300117492676, "detail": "UnityEngine.ParticleSystemModule_CodeGen.c"}}, {"pid": 1, "tid": 113, "ts": 476947.9064941406, "dur": 2487.0998859405518, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4870998859405518, "detail": "Il2CppReversePInvokeWrapperTable.cpp"}}, {"pid": 1, "tid": 113, "ts": 479467.22412109375, "dur": 2002.8998851776123, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.0028998851776123, "detail": "UnityEngine_Attr.cpp"}}, {"pid": 1, "tid": 113, "ts": 481500.732421875, "dur": 3089.099884033203, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.089099884033203, "detail": "UnityEngine.TerrainPhysicsModule.cpp"}}, {"pid": 1, "tid": 113, "ts": 484622.1923828125, "dur": 2874.500036239624, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.874500036239624, "detail": "System.Xml_CodeGen.c"}}, {"pid": 1, "tid": 113, "ts": 487530.3955078125, "dur": 14840.70110321045, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.84070110321045, "detail": "UnityEngine.TilemapModule_CodeGen.c"}}, {"pid": 1, "tid": 113, "ts": 502406.7077636719, "dur": 3069.7999000549316, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.0697999000549316, "detail": "Il2CppGenericComDefinitions18.cpp"}}, {"pid": 1, "tid": 113, "ts": 505509.6740722656, "dur": 2312.299966812134, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.312299966812134, "detail": "Il2CppGenericComDefinitions30.cpp"}}, {"pid": 1, "tid": 113, "ts": 507856.9030761719, "dur": 2864.3999099731445, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.8643999099731445, "detail": "Il2CppGenericComDefinitions7.cpp"}}, {"pid": 1, "tid": 118, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 118, "ts": 336764.22119140625, "dur": 26505.699157714844, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 26.505699157714844, "detail": "Unity.ProBuilder1.cpp"}}, {"pid": 1, "tid": 118, "ts": 364009.09423828125, "dur": 13180.6001663208, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.1806001663208, "detail": "GenericMethods1.cpp"}}, {"pid": 1, "tid": 118, "ts": 377227.2033691406, "dur": 12448.399543762207, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.448399543762207, "detail": "mscorlib6.cpp"}}, {"pid": 1, "tid": 118, "ts": 389718.505859375, "dur": 10657.400131225586, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.657400131225586, "detail": "mscorlib8.cpp"}}, {"pid": 1, "tid": 118, "ts": 400411.376953125, "dur": 8375.900268554688, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 8.375900268554688, "detail": "Generics13.cpp"}}, {"pid": 1, "tid": 118, "ts": 408832.70263671875, "dur": 6323.800086975098, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.323800086975098, "detail": "Generics12.cpp"}}, {"pid": 1, "tid": 118, "ts": 415187.1**********, "dur": 9257.30037689209, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 9.25730037689209, "detail": "UnityEngine.IMGUIModule.cpp"}}, {"pid": 1, "tid": 118, "ts": 424480.89599609375, "dur": 10666.000366210938, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.666000366210938, "detail": "UnityEngine.TerrainModule.cpp"}}, {"pid": 1, "tid": 118, "ts": 435183.1970214844, "dur": 2220.2000617980957, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.2202000617980957, "detail": "UnityEngine.AnimationModule.cpp"}}, {"pid": 1, "tid": 118, "ts": 437431.7932128906, "dur": 3472.3000526428223, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.4723000526428223, "detail": "System_CodeGen.c"}}, {"pid": 1, "tid": 118, "ts": 440940.49072265625, "dur": 15472.30052947998, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.47230052947998, "detail": "Mono.Security.cpp"}}, {"pid": 1, "tid": 118, "ts": 456444.0002441406, "dur": 3062.2000694274902, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.0622000694274902, "detail": "UnityEngine.TilemapModule_Attr.cpp"}}, {"pid": 1, "tid": 118, "ts": 459619.7814941406, "dur": 3861.500024795532, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.8615000247955322, "detail": "Firebase.App_CodeGen.c"}}, {"pid": 1, "tid": 118, "ts": 463517.9138183594, "dur": 4076.000213623047, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.076000213623047, "detail": "trees.c"}}, {"pid": 1, "tid": 118, "ts": 467623.779296875, "dur": 2685.6000423431396, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6856000423431396, "detail": "UnityEngine.AnimationModule_CodeGen.c"}}, {"pid": 1, "tid": 118, "ts": 470334.9304199219, "dur": 6584.400177001953, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.584400177001953, "detail": "gzwrite.c"}}, {"pid": 1, "tid": 118, "ts": 476947.9064941406, "dur": 3218.400001525879, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.218400001525879, "detail": "crc32.c"}}, {"pid": 1, "tid": 118, "ts": 480206.60400390625, "dur": 2038.8000011444092, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.038800001144409, "detail": "UnityEngine.AIModule_CodeGen.c"}}, {"pid": 1, "tid": 118, "ts": 482276.30615234375, "dur": 3173.8998889923096, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.1738998889923096, "detail": "uncompr.c"}}, {"pid": 1, "tid": 118, "ts": 485497.7111816406, "dur": 2334.1000080108643, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.3341000080108643, "detail": "gzclose.c"}}, {"pid": 1, "tid": 118, "ts": 487863.9221191406, "dur": 15784.600257873535, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.784600257873535, "detail": "Lump_libil2cpp_codegen.cpp"}}, {"pid": 1, "tid": 118, "ts": 503682.**********, "dur": 1980.7000160217285, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.9807000160217285, "detail": "Il2CppGenericComDefinitions22.cpp"}}, {"pid": 1, "tid": 118, "ts": 505792.5109863281, "dur": 2470.7999229431152, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4707999229431152, "detail": "Il2CppGenericComDefinitions31.cpp"}}, {"pid": 1, "tid": 118, "ts": 508298.583984375, "dur": 2457.2999477386475, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4572999477386475, "detail": "Il2CppGenericComDefinitions9.cpp"}}, {"pid": 1, "tid": 120, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 120, "ts": 336768.2189941406, "dur": 27889.101028442383, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 27.889101028442383, "detail": "mscorlib_CodeGen.c"}}, {"pid": 1, "tid": 120, "ts": 364691.89453125, "dur": 12534.500122070312, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.534500122070312, "detail": "mscorlib13.cpp"}}, {"pid": 1, "tid": 120, "ts": 377263.0920410156, "dur": 13191.800117492676, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.191800117492676, "detail": "mscorlib1.cpp"}}, {"pid": 1, "tid": 120, "ts": 390488.9221191406, "dur": 13748.498916625977, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 13.748498916625977, "detail": "Generics4.cpp"}}, {"pid": 1, "tid": 120, "ts": 404286.68212890625, "dur": 12072.999954223633, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.072999954223633, "detail": "mscorlib11.cpp"}}, {"pid": 1, "tid": 120, "ts": 416402.8015136719, "dur": 8232.600212097168, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 8.232600212097168, "detail": "Unity.ProBuilder5.cpp"}}, {"pid": 1, "tid": 120, "ts": 424670.**********, "dur": 10513.699531555176, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 10.513699531555176, "detail": "Il2CppGenericMethodDefinitions.c"}}, {"pid": 1, "tid": 120, "ts": 435215.9729003906, "dur": 4074.2998123168945, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 4.0742998123168945, "detail": "UnityEngine.AndroidJNIModule_Attr.cpp"}}, {"pid": 1, "tid": 120, "ts": 439319.7021484375, "dur": 15255.000114440918, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.255000114440918, "detail": "Il2CppInteropDataTable.cpp"}}, {"pid": 1, "tid": 120, "ts": 454605.8349609375, "dur": 3247.4000453948975, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.2474000453948975, "detail": "UnityEngine.TextRenderingModule_Attr.cpp"}}, {"pid": 1, "tid": 120, "ts": 457888.61083984375, "dur": 2752.8998851776123, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.7528998851776123, "detail": "UnityEngine.SpriteShapeModule_Attr.cpp"}}, {"pid": 1, "tid": 120, "ts": 460677.3681640625, "dur": 2488.1999492645264, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.4881999492645264, "detail": "Unity.ProBuilder.Poly2Tri_Attr.cpp"}}, {"pid": 1, "tid": 120, "ts": 463201.59912109375, "dur": 3881.700038909912, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.881700038909912, "detail": "UnityEngine.PhysicsModule_CodeGen.c"}}, {"pid": 1, "tid": 120, "ts": 467125.18310546875, "dur": 2944.000005722046, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.944000005722046, "detail": "GoogleMobileAds.Ump.Android_Attr.cpp"}}, {"pid": 1, "tid": 120, "ts": 470104.1259765625, "dur": 6405.900001525879, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.405900001525879, "detail": "UnityEngine.VRModule.cpp"}}, {"pid": 1, "tid": 120, "ts": 476547.0275878906, "dur": 3110.8999252319336, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.1108999252319336, "detail": "UnityEngine.TextRenderingModule_CodeGen.c"}}, {"pid": 1, "tid": 120, "ts": 479691.10107421875, "dur": 2600.399971008301, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.600399971008301, "detail": "Il2CppCodeRegistration.cpp"}}, {"pid": 1, "tid": 120, "ts": 482321.8078613281, "dur": 2919.3999767303467, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.9193999767303467, "detail": "compress.c"}}, {"pid": 1, "tid": 120, "ts": 485305.60302734375, "dur": 3321.899890899658, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.321899890899658, "detail": "Lump_libil2cpp_mono.cpp"}}, {"pid": 1, "tid": 120, "ts": 488654.78515625, "dur": 14975.10051727295, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.97510051727295, "detail": "Il2CppGenericComDefinitions11.cpp"}}, {"pid": 1, "tid": 120, "ts": 503664.0625, "dur": 2780.4999351501465, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.7804999351501465, "detail": "Il2CppGenericComDefinitions21.cpp"}}, {"pid": 1, "tid": 120, "ts": 506505.43212890625, "dur": 2175.299882888794, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.175299882888794, "detail": "Il2CppGenericComDefinitions33.cpp"}}, {"pid": 1, "tid": 120, "ts": 508714.90478515625, "dur": 2552.7000427246094, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.5527000427246094, "detail": "Lump_libil2cpp_debugger.cpp"}}, {"pid": 1, "tid": 119, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 119, "ts": 336772.39990234375, "dur": 26961.7977142334, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 26.9617977142334, "detail": "Assembly-CSharp.cpp"}}, {"pid": 1, "tid": 119, "ts": 364148.62060546875, "dur": 16573.79913330078, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 16.57379913330078, "detail": "UnityEngine.CoreModule1.cpp"}}, {"pid": 1, "tid": 119, "ts": 380759.6130371094, "dur": 15051.600456237793, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.051600456237793, "detail": "Generics14.cpp"}}, {"pid": 1, "tid": 119, "ts": 395854.7058105469, "dur": 12984.700202941895, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.984700202941895, "detail": "Generics2.cpp"}}, {"pid": 1, "tid": 119, "ts": 408872.**********, "dur": 12364.60018157959, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 12.36460018157959, "detail": "Generics11.cpp"}}, {"pid": 1, "tid": 119, "ts": 421269.1955566406, "dur": 15966.79973602295, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 15.96679973602295, "detail": "Il2CppGenericAdjustorThunkTable.cpp"}}, {"pid": 1, "tid": 119, "ts": 437269.8059082031, "dur": 3960.200071334839, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.960200071334839, "detail": "UnityEngine.IMGUIModule_Attr.cpp"}}, {"pid": 1, "tid": 119, "ts": 441264.892578125, "dur": 14236.000061035156, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.236000061035156, "detail": "UnityEngine.ParticleSystemModule.cpp"}}, {"pid": 1, "tid": 119, "ts": 455537.9333496094, "dur": 2694.9000358581543, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6949000358581543, "detail": "UnityEngine.SubsystemsModule_Attr.cpp"}}, {"pid": 1, "tid": 119, "ts": 458271.484375, "dur": 2646.5001106262207, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6465001106262207, "detail": "Firebase.App_Attr.cpp"}}, {"pid": 1, "tid": 119, "ts": 460951.6906738281, "dur": 3101.3998985290527, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.1013998985290527, "detail": "Unity.ProBuilder.Poly2Tri_CodeGen.c"}}, {"pid": 1, "tid": 119, "ts": 464109.619140625, "dur": 3625.2999305725098, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.6252999305725098, "detail": "GoogleMobileAds.Android_Attr.cpp"}}, {"pid": 1, "tid": 119, "ts": 467774.8107910156, "dur": 2714.200019836426, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.714200019836426, "detail": "UnityEngine.UIModule_CodeGen.c"}}, {"pid": 1, "tid": 119, "ts": 470524.9938964844, "dur": 6646.299839019775, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 6.646299839019775, "detail": "UnityEngine.AudioModule_CodeGen.c"}}, {"pid": 1, "tid": 119, "ts": 477201.2939453125, "dur": 2610.1999282836914, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6101999282836914, "detail": "inffast.c"}}, {"pid": 1, "tid": 119, "ts": 479848.08349609375, "dur": 1914.199948310852, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 1.914199948310852, "detail": "System.Configuration_CodeGen.c"}}, {"pid": 1, "tid": 119, "ts": 481871.6125488281, "dur": 2636.1000537872314, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.6361000537872314, "detail": "UnityEngine.GridModule.cpp"}}, {"pid": 1, "tid": 119, "ts": 484529.72412109375, "dur": 2261.399984359741, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.261399984359741, "detail": "Il2CppMetadataRegistration.c"}}, {"pid": 1, "tid": 119, "ts": 486839.6911621094, "dur": 14251.100540161133, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 14.251100540161133, "detail": "UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 1, "tid": 119, "ts": 501158.2946777344, "dur": 3093.100070953369, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 3.093100070953369, "detail": "Il2CppGenericComDefinitions13.cpp"}}, {"pid": 1, "tid": 119, "ts": 504297.42431640625, "dur": 2631.00004196167, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.63100004196167, "detail": "Il2CppGenericComDefinitions25.cpp"}}, {"pid": 1, "tid": 119, "ts": 506958.49609375, "dur": 2111.**********, "ph": "X", "name": "HashCompilerInvocation", "args": {"durationMS": 2.111**********, "detail": "Il2CppGenericComDefinitions36.cpp"}}, {"pid": 1, "tid": 318, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 318, "ts": 489274.4140625, "dur": 11449.09954071045, "ph": "X", "name": "HeaderFileHashProvider.HashForAllHeaderFilesReachableByFilesIn", "args": {"durationMS": 11.44909954071045, "detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\include"}}, {"pid": 1, "tid": 212, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 212, "ts": 516002.38037109375, "dur": 3011825.68359375, "ph": "X", "name": "Compile File", "args": {"durationMS": 3011.82568359375, "detail": "Il2CppCCalculateTypeValues.cpp"}}, {"pid": 1, "tid": 213, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 213, "ts": 516008.72802734375, "dur": 4071529.296875, "ph": "X", "name": "Compile File", "args": {"durationMS": 4071.529296875, "detail": "Il2CppCCalculateFieldValues.cpp"}}, {"pid": 1, "tid": 208, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 208, "ts": 516014.404296875, "dur": 1514746.**********, "ph": "X", "name": "Compile File", "args": {"durationMS": 1514.746**********, "detail": "Il2CppMetadataUsage.c"}}, {"pid": 1, "tid": 208, "ts": 2030770.3857421875, "dur": 958519.1040039062, "ph": "X", "name": "Compile File", "args": {"durationMS": 958.5191040039062, "detail": "Il2CppMetadataRegistration.c"}}, {"pid": 1, "tid": 209, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 209, "ts": 516014.404296875, "dur": 5738532.2265625, "ph": "X", "name": "Compile File", "args": {"durationMS": 5738.5322265625, "detail": "UnityEngine.CoreModule1.cpp"}}, {"pid": 1, "tid": 207, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 207, "ts": 516019.22607421875, "dur": 3245636.474609375, "ph": "X", "name": "Compile File", "args": {"durationMS": 3245.636474609375, "detail": "UnityEngine.CoreModule_Attr.cpp"}}, {"pid": 1, "tid": 211, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 211, "ts": 516026.18408203125, "dur": 1739175.1708984375, "ph": "X", "name": "Compile File", "args": {"durationMS": 1739.1751708984375, "detail": "Il2CppTypeDefinitions.c"}}, {"pid": 1, "tid": 206, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 206, "ts": 516028.13720703125, "dur": 1513102.1728515625, "ph": "X", "name": "Compile File", "args": {"durationMS": 1513.1021728515625, "detail": "UnityEngine.CoreModule_CodeGen.c"}}, {"pid": 1, "tid": 206, "ts": 2029134.3994140625, "dur": 958843.9331054688, "ph": "X", "name": "Compile File", "args": {"durationMS": 958.8439331054688, "detail": "Il2CppCodeRegistration.cpp"}}, {"pid": 1, "tid": 205, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 205, "ts": 516036.376953125, "dur": 2588004.8828125, "ph": "X", "name": "Compile File", "args": {"durationMS": 2588.0048828125, "detail": "GoogleMobileAds.cpp"}}, {"pid": 1, "tid": 204, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 204, "ts": 516037.353515625, "dur": 1512638.916015625, "ph": "X", "name": "Compile File", "args": {"durationMS": 1512.638916015625, "detail": "Il2CppGenericAdjustorThunkTable.cpp"}}, {"pid": 1, "tid": 204, "ts": 2028682.**********, "dur": 959849.2431640625, "ph": "X", "name": "Compile File", "args": {"durationMS": 959.8492431640625, "detail": "GoogleMobileAds.Common_CodeGen.c"}}, {"pid": 1, "tid": 201, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 201, "ts": 516038.51318359375, "dur": 551278.8696289062, "ph": "X", "name": "Compile File", "args": {"durationMS": 551.2788696289062, "detail": "Il2CppGenericMethodDefinitions.c"}}, {"pid": 1, "tid": 201, "ts": 1067600.341796875, "dur": 1179653.80859375, "ph": "X", "name": "Compile File", "args": {"durationMS": 1179.65380859375, "detail": "Il2CppCCFieldValuesTable.cpp"}}, {"pid": 1, "tid": 203, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 203, "ts": 516054.50439453125, "dur": 590629.7607421875, "ph": "X", "name": "Compile File", "args": {"durationMS": 590.6297607421875, "detail": "Il2CppGenericMethodTable.c"}}, {"pid": 1, "tid": 203, "ts": 1106694.2138671875, "dur": 1230469.1162109375, "ph": "X", "name": "Compile File", "args": {"durationMS": 1230.4691162109375, "detail": "GoogleMobileAds.Common.cpp"}}, {"pid": 1, "tid": 202, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1, "tid": 202, "ts": 516056.884765625, "dur": 1512080.6884765625, "ph": "X", "name": "Compile File", "args": {"durationMS": 1512.0**********25, "detail": "Il2CppCCTypeValuesTable.cpp"}}, {"pid": 1, "tid": 202, "ts": 2028148.681640625, "dur": 963341.1254882812, "ph": "X", "name": "Compile File", "args": {"durationMS": 963.3411254882812, "detail": "GoogleMobileAds.Common_Attr.cpp"}}], "meta_datetime": "07/01/2024 11:59:22", "meta_command_line": "\"C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\build\\deploy\\netcoreapp3.1\\il2cpp.dll\" --compile-cpp --libil2cpp-static --platform=Android --architecture=ARM64 --configuration=Release \"--outputpath=D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Library\\Il2cppBuildCache\\Android\\arm64-v8a\\Native\\arm64-v8a\\libil2cpp.so\" \"--cachedirectory=D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Assets\\..\\Library\\il2cpp_android_arm64-v8a/il2cpp_cache\" \"--additional-include-directories=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\bdwgc/include\" \"--additional-include-directories=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\libil2cpp/include\" \"--baselib-directory=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Variations\\il2cpp\\Release\\StaticLibs\\arm64-v8a\" --avoid-dynamic-library-copy \"--tool-chain-path=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK\" --incremental-g-c-time-slice=3 --profiler-report \"--map-file-parser=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/MapFileParser/MapFileParser.exe\" \"--generatedcppdir=D:/cargo truck/Tractor Simulator Cargo Games (V8)smg/Library/Il2cppBuildCache/Android/armeabi-v7a/il2cppOutput\" \"--stats-output-dir=D:/cargo truck/Tractor Simulator Cargo Games (V8)smg/Library/Il2cppBuildCache/Android/armeabi-v7a/il2cppStats\" --dotnetprofile=unityaot", "meta_command_line_args": "C:\\Program Files\\Unity\\Hub\\Editor\\2020.3.32f1\\Editor\\Data\\il2cpp\\build\\deploy\\netcoreapp3.1\\il2cpp.dll --compile-cpp --libil2cpp-static --platform=Android --architecture=ARM64 --configuration=Release --outputpath=D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Library\\Il2cppBuildCache\\Android\\arm64-v8a\\Native\\arm64-v8a\\libil2cpp.so --cachedirectory=D:\\cargo truck\\Tractor Simulator Cargo Games (V8)smg\\Assets\\..\\Library\\il2cpp_android_arm64-v8a/il2cpp_cache --additional-include-directories=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\bdwgc/include --additional-include-directories=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\libil2cpp/include --baselib-directory=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Variations\\il2cpp\\Release\\StaticLibs\\arm64-v8a --avoid-dynamic-library-copy --tool-chain-path=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK --incremental-g-c-time-slice=3 --profiler-report --map-file-parser=C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/MapFileParser/MapFileParser.exe --generatedcppdir=D:/cargo truck/Tractor Simulator Cargo Games (V8)smg/Library/Il2cppBuildCache/Android/armeabi-v7a/il2cppOutput --stats-output-dir=D:/cargo truck/Tractor Simulator Cargo Games (V8)smg/Library/Il2cppBuildCache/Android/armeabi-v7a/il2cppStats --dotnetprofile=unityaot", "meta_user_name": "Laptop Land", "meta_os_version": "Microsoft Windows NT 6.2.9200.0", "meta_cpu_count": "12"}