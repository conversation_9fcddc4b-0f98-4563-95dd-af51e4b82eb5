apply plugin: 'android-library'

dependencies {
    implementation fileTree(dir: 'bin', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}

android {
    namespace "com.google.unity.ads"
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            //java.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }
    }

    compileSdkVersion 36
    buildToolsVersion '34.0.0'
    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 9
    }

    lintOptions {
        abortOnError false
    }
}
