﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void Mono.Security.ASN1::.ctor(System.Byte)
extern void ASN1__ctor_mC8594B7A2376B58F26F1D0457B0F9F5880D87142 (void);
// 0x00000002 System.Void Mono.Security.ASN1::.ctor(System.Byte,System.Byte[])
extern void ASN1__ctor_mB8A19279E6079D30BB6A594ADAC7FEE89E822CDC (void);
// 0x00000003 System.Void Mono.Security.ASN1::.ctor(System.Byte[])
extern void ASN1__ctor_mE534D499DABEAAA35E0F30572CD295A9FCFA1C7E (void);
// 0x00000004 System.Int32 Mono.Security.ASN1::get_Count()
extern void ASN1_get_Count_mBF134B153CFA218C251FB692A25AA392DCF9F583 (void);
// 0x00000005 System.Byte Mono.Security.ASN1::get_Tag()
extern void ASN1_get_Tag_mA82F15B6EB97BF0F3EBAA69C21765909D7A675D3 (void);
// 0x00000006 System.Byte[] Mono.Security.ASN1::get_Value()
extern void ASN1_get_Value_m95545A82635424B999816713F09A224ED01DF0C2 (void);
// 0x00000007 System.Void Mono.Security.ASN1::set_Value(System.Byte[])
extern void ASN1_set_Value_mCA987F3A4CA629E76A62EB82CC0C9D448A66C13D (void);
// 0x00000008 Mono.Security.ASN1 Mono.Security.ASN1::Add(Mono.Security.ASN1)
extern void ASN1_Add_m35AB44F469BE9C185A91D2E265A7DA6B27311F7B (void);
// 0x00000009 System.Byte[] Mono.Security.ASN1::GetBytes()
extern void ASN1_GetBytes_mDE8FEDD6687DE65668CA9C987B5A50063AAEDC02 (void);
// 0x0000000A System.Void Mono.Security.ASN1::Decode(System.Byte[],System.Int32&,System.Int32)
extern void ASN1_Decode_mFB5F309ED41074BEB2D4FCC6C49E6901EDCF3D87 (void);
// 0x0000000B System.Void Mono.Security.ASN1::DecodeTLV(System.Byte[],System.Int32&,System.Byte&,System.Int32&,System.Byte[]&)
extern void ASN1_DecodeTLV_m469B49047A31E0DE6D8E37D3153C284D4945EB03 (void);
// 0x0000000C Mono.Security.ASN1 Mono.Security.ASN1::get_Item(System.Int32)
extern void ASN1_get_Item_mBA4AF2346A0847038957881A98202AF8DAF09B50 (void);
// 0x0000000D System.String Mono.Security.ASN1::ToString()
extern void ASN1_ToString_m5BCBD4583786543E88D243E197C218C748ADC356 (void);
// 0x0000000E Mono.Security.ASN1 Mono.Security.ASN1Convert::FromInt32(System.Int32)
extern void ASN1Convert_FromInt32_m2EB0E4A8D3D06D4EE1BEFD4F50E9021FF6B82FA2 (void);
// 0x0000000F System.Int32 Mono.Security.ASN1Convert::ToInt32(Mono.Security.ASN1)
extern void ASN1Convert_ToInt32_m381CC48A18572F6F58C4332C3E07906562034A77 (void);
// 0x00000010 System.String Mono.Security.ASN1Convert::ToOid(Mono.Security.ASN1)
extern void ASN1Convert_ToOid_m6F617C7AC370CC5D6EAC2F813D8F7B73A3D8F61F (void);
// 0x00000011 System.Byte[] Mono.Security.BitConverterLE::GetUIntBytes(System.Byte*)
extern void BitConverterLE_GetUIntBytes_m3771CE625EE76BB13B8C60BCBB038E412514E939 (void);
// 0x00000012 System.Byte[] Mono.Security.BitConverterLE::GetBytes(System.Int32)
extern void BitConverterLE_GetBytes_mD4BDBCF2894D9C49D53420C8CAD0933372E8B698 (void);
// 0x00000013 System.String Mono.Security.Cryptography.CryptoConvert::ToHex(System.Byte[])
extern void CryptoConvert_ToHex_m567E8BF67E972F8A8AC9DC37BEE4F06521082EF4 (void);
static Il2CppMethodPointer s_methodPointers[19] = 
{
	ASN1__ctor_mC8594B7A2376B58F26F1D0457B0F9F5880D87142,
	ASN1__ctor_mB8A19279E6079D30BB6A594ADAC7FEE89E822CDC,
	ASN1__ctor_mE534D499DABEAAA35E0F30572CD295A9FCFA1C7E,
	ASN1_get_Count_mBF134B153CFA218C251FB692A25AA392DCF9F583,
	ASN1_get_Tag_mA82F15B6EB97BF0F3EBAA69C21765909D7A675D3,
	ASN1_get_Value_m95545A82635424B999816713F09A224ED01DF0C2,
	ASN1_set_Value_mCA987F3A4CA629E76A62EB82CC0C9D448A66C13D,
	ASN1_Add_m35AB44F469BE9C185A91D2E265A7DA6B27311F7B,
	ASN1_GetBytes_mDE8FEDD6687DE65668CA9C987B5A50063AAEDC02,
	ASN1_Decode_mFB5F309ED41074BEB2D4FCC6C49E6901EDCF3D87,
	ASN1_DecodeTLV_m469B49047A31E0DE6D8E37D3153C284D4945EB03,
	ASN1_get_Item_mBA4AF2346A0847038957881A98202AF8DAF09B50,
	ASN1_ToString_m5BCBD4583786543E88D243E197C218C748ADC356,
	ASN1Convert_FromInt32_m2EB0E4A8D3D06D4EE1BEFD4F50E9021FF6B82FA2,
	ASN1Convert_ToInt32_m381CC48A18572F6F58C4332C3E07906562034A77,
	ASN1Convert_ToOid_m6F617C7AC370CC5D6EAC2F813D8F7B73A3D8F61F,
	BitConverterLE_GetUIntBytes_m3771CE625EE76BB13B8C60BCBB038E412514E939,
	BitConverterLE_GetBytes_mD4BDBCF2894D9C49D53420C8CAD0933372E8B698,
	CryptoConvert_ToHex_m567E8BF67E972F8A8AC9DC37BEE4F06521082EF4,
};
static const int32_t s_InvokerIndices[19] = 
{
	1571,
	782,
	1610,
	1886,
	1866,
	1900,
	1610,
	1431,
	1900,
	554,
	151,
	1427,
	1900,
	3066,
	3017,
	3069,
	3056,
	3066,
	3069,
};
extern const CustomAttributesCacheGenerator g_Mono_Security_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Mono_Security_CodeGenModule;
const Il2CppCodeGenModule g_Mono_Security_CodeGenModule = 
{
	"Mono.Security.dll",
	19,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_Mono_Security_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
