﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



// UnityEngine.Collider
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76;
// System.String
struct String_t;
// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
// UnityEngine.WheelCollider
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481;

IL2CPP_EXTERN_C RuntimeField* WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeType* WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_t3E8E0BA57A4D4D0EF43301B668B802ED48E39035 
{
};

// System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;
};

// System.IntPtr
struct IntPtr_t 
{
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;
};

// UnityEngine.JointSpring
struct JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 
{
	// System.Single UnityEngine.JointSpring::spring
	float ___spring_0;
	// System.Single UnityEngine.JointSpring::damper
	float ___damper_1;
	// System.Single UnityEngine.JointSpring::targetPosition
	float ___targetPosition_2;
};

// UnityEngine.Quaternion
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	// System.Single UnityEngine.Quaternion::x
	float ___x_0;
	// System.Single UnityEngine.Quaternion::y
	float ___y_1;
	// System.Single UnityEngine.Quaternion::z
	float ___z_2;
	// System.Single UnityEngine.Quaternion::w
	float ___w_3;
};

// System.Single
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	// System.Single System.Single::m_value
	float ___m_value_0;
};

// UnityEngine.Vector3
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	// System.Single UnityEngine.Vector3::x
	float ___x_2;
	// System.Single UnityEngine.Vector3::y
	float ___y_3;
	// System.Single UnityEngine.Vector3::z
	float ___z_4;
};

// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};

// UnityEngine.WheelFrictionCurve
struct WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 
{
	// System.Single UnityEngine.WheelFrictionCurve::m_ExtremumSlip
	float ___m_ExtremumSlip_0;
	// System.Single UnityEngine.WheelFrictionCurve::m_ExtremumValue
	float ___m_ExtremumValue_1;
	// System.Single UnityEngine.WheelFrictionCurve::m_AsymptoteSlip
	float ___m_AsymptoteSlip_2;
	// System.Single UnityEngine.WheelFrictionCurve::m_AsymptoteValue
	float ___m_AsymptoteValue_3;
	// System.Single UnityEngine.WheelFrictionCurve::m_Stiffness
	float ___m_Stiffness_4;
};

// UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	// System.IntPtr UnityEngine.Object::m_CachedPtr
	intptr_t ___m_CachedPtr_0;
};
// Native definition for P/Invoke marshalling of UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr_0;
};
// Native definition for COM marshalling of UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr_0;
};

// UnityEngine.WheelHit
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455 
{
	// UnityEngine.Vector3 UnityEngine.WheelHit::m_Point
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point_0;
	// UnityEngine.Vector3 UnityEngine.WheelHit::m_Normal
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal_1;
	// UnityEngine.Vector3 UnityEngine.WheelHit::m_ForwardDir
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir_2;
	// UnityEngine.Vector3 UnityEngine.WheelHit::m_SidewaysDir
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir_3;
	// System.Single UnityEngine.WheelHit::m_Force
	float ___m_Force_4;
	// System.Single UnityEngine.WheelHit::m_ForwardSlip
	float ___m_ForwardSlip_5;
	// System.Single UnityEngine.WheelHit::m_SidewaysSlip
	float ___m_SidewaysSlip_6;
	// UnityEngine.Collider UnityEngine.WheelHit::m_Collider
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider_7;
};
// Native definition for P/Invoke marshalling of UnityEngine.WheelHit
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point_0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal_1;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir_2;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir_3;
	float ___m_Force_4;
	float ___m_ForwardSlip_5;
	float ___m_SidewaysSlip_6;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider_7;
};
// Native definition for COM marshalling of UnityEngine.WheelHit
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point_0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal_1;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir_2;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir_3;
	float ___m_Force_4;
	float ___m_ForwardSlip_5;
	float ___m_SidewaysSlip_6;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider_7;
};

// UnityEngine.Component
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};

// UnityEngine.Collider
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};

// UnityEngine.WheelCollider
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};

// <Module>

// <Module>

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;
};

// System.Boolean

// UnityEngine.JointSpring

// UnityEngine.JointSpring

// UnityEngine.Quaternion
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	// UnityEngine.Quaternion UnityEngine.Quaternion::identityQuaternion
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion_4;
};

// UnityEngine.Quaternion

// System.Single

// System.Single

// UnityEngine.Vector3
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	// UnityEngine.Vector3 UnityEngine.Vector3::zeroVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector_5;
	// UnityEngine.Vector3 UnityEngine.Vector3::oneVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector_6;
	// UnityEngine.Vector3 UnityEngine.Vector3::upVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector_7;
	// UnityEngine.Vector3 UnityEngine.Vector3::downVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector_8;
	// UnityEngine.Vector3 UnityEngine.Vector3::leftVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector_9;
	// UnityEngine.Vector3 UnityEngine.Vector3::rightVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector_10;
	// UnityEngine.Vector3 UnityEngine.Vector3::forwardVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector_11;
	// UnityEngine.Vector3 UnityEngine.Vector3::backVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector_12;
	// UnityEngine.Vector3 UnityEngine.Vector3::positiveInfinityVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector_13;
	// UnityEngine.Vector3 UnityEngine.Vector3::negativeInfinityVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector_14;
};

// UnityEngine.Vector3

// System.Void

// System.Void

// UnityEngine.WheelFrictionCurve

// UnityEngine.WheelFrictionCurve

// UnityEngine.WheelHit

// UnityEngine.WheelHit

// UnityEngine.Collider

// UnityEngine.Collider

// UnityEngine.WheelCollider

// UnityEngine.WheelCollider
#ifdef __clang__
#pragma clang diagnostic pop
#endif



// UnityEngine.Collider UnityEngine.WheelHit::get_collider()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.WheelHit::get_point()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.WheelHit::get_normal()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
// System.Single UnityEngine.WheelHit::get_force()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
// System.Single UnityEngine.WheelHit::get_forwardSlip()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
// System.Single UnityEngine.WheelHit::get_sidewaysSlip()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.WheelCollider::get_suspensionSpring_Injected(UnityEngine.JointSpring&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_ret, const RuntimeMethod* method) ;
// System.Void UnityEngine.WheelCollider::set_suspensionSpring_Injected(UnityEngine.JointSpring&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.WheelCollider::get_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) ;
// System.Void UnityEngine.WheelCollider::set_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.WheelCollider::get_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) ;
// System.Void UnityEngine.WheelCollider::set_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.WheelHit
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_Collider_7Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Collider_7Exception, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke_back(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_Collider_7Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Collider_7Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.WheelHit
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke_cleanup(WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.WheelHit
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_Collider_7Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Collider_7Exception, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com_back(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_Collider_7Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_7_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Collider_7Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.WheelHit
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com_cleanup(WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled)
{
}
// UnityEngine.Collider UnityEngine.WheelHit::get_collider()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* V_0 = NULL;
	{
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_0 = __this->___m_Collider_7;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* _returnValue;
	_returnValue = WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D(_thisAdjusted, method);
	return _returnValue;
}
// UnityEngine.Vector3 UnityEngine.WheelHit::get_point()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_Point_0;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C(_thisAdjusted, method);
	return _returnValue;
}
// UnityEngine.Vector3 UnityEngine.WheelHit::get_normal()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_Normal_1;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162(_thisAdjusted, method);
	return _returnValue;
}
// System.Single UnityEngine.WheelHit::get_force()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_Force_4;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E(_thisAdjusted, method);
	return _returnValue;
}
// System.Single UnityEngine.WheelHit::get_forwardSlip()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_ForwardSlip_5;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE(_thisAdjusted, method);
	return _returnValue;
}
// System.Single UnityEngine.WheelHit::get_sidewaysSlip()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_SidewaysSlip_6;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Single UnityEngine.WheelCollider::get_radius()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_radius()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::set_radius(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_radius(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Single UnityEngine.WheelCollider::get_suspensionDistance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionDistance()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::set_suspensionDistance(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionDistance(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// UnityEngine.JointSpring UnityEngine.WheelCollider::get_suspensionSpring()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB(__this, (&V_0), NULL);
		JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 L_0 = V_0;
		return L_0;
	}
}
// System.Void UnityEngine.WheelCollider::set_suspensionSpring(UnityEngine.JointSpring)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 ___0_value, const RuntimeMethod* method) 
{
	{
		WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F(__this, (&___0_value), NULL);
		return;
	}
}
// System.Void UnityEngine.WheelCollider::set_forceAppPointDistance(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_forceAppPointDistance(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Void UnityEngine.WheelCollider::set_mass(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_mass(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Void UnityEngine.WheelCollider::set_wheelDampingRate(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_wheelDampingRate(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// UnityEngine.WheelFrictionCurve UnityEngine.WheelCollider::get_forwardFriction()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2(__this, (&V_0), NULL);
		WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 L_0 = V_0;
		return L_0;
	}
}
// System.Void UnityEngine.WheelCollider::set_forwardFriction(UnityEngine.WheelFrictionCurve)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 ___0_value, const RuntimeMethod* method) 
{
	{
		WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205(__this, (&___0_value), NULL);
		return;
	}
}
// UnityEngine.WheelFrictionCurve UnityEngine.WheelCollider::get_sidewaysFriction()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1(__this, (&V_0), NULL);
		WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 L_0 = V_0;
		return L_0;
	}
}
// System.Void UnityEngine.WheelCollider::set_sidewaysFriction(UnityEngine.WheelFrictionCurve)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 ___0_value, const RuntimeMethod* method) 
{
	{
		WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9(__this, (&___0_value), NULL);
		return;
	}
}
// System.Single UnityEngine.WheelCollider::get_motorTorque()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_motorTorque()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::set_motorTorque(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_motorTorque(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Single UnityEngine.WheelCollider::get_brakeTorque()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_brakeTorque()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::set_brakeTorque(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_brakeTorque(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Single UnityEngine.WheelCollider::get_steerAngle()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_steerAngle()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::set_steerAngle(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_steerAngle(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Boolean UnityEngine.WheelCollider::get_isGrounded()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_isGrounded()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Single UnityEngine.WheelCollider::get_rpm()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_rpm()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::GetWorldPose(UnityEngine.Vector3&,UnityEngine.Quaternion&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_pos, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* ___1_quat, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*);
	static WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::GetWorldPose(UnityEngine.Vector3&,UnityEngine.Quaternion&)");
	_il2cpp_icall_func(__this, ___0_pos, ___1_quat);
}
// System.Boolean UnityEngine.WheelCollider::GetGroundHit(UnityEngine.WheelHit&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* ___0_hit, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*);
	static WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::GetGroundHit(UnityEngine.WheelHit&)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___0_hit);
	return icallRetVal;
}
// System.Void UnityEngine.WheelCollider::get_suspensionSpring_Injected(UnityEngine.JointSpring&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5*);
	static WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionSpring_Injected(UnityEngine.JointSpring&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
// System.Void UnityEngine.WheelCollider::set_suspensionSpring_Injected(UnityEngine.JointSpring&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5*);
	static WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionSpring_Injected(UnityEngine.JointSpring&)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Void UnityEngine.WheelCollider::get_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
// System.Void UnityEngine.WheelCollider::set_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_value);
}
// System.Void UnityEngine.WheelCollider::get_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
// System.Void UnityEngine.WheelCollider::set_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_value);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
