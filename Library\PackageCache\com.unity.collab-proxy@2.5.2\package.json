{"name": "com.unity.collab-proxy", "displayName": "Version Control", "version": "2.5.2", "unity": "2020.3", "unityRelease": "48f1", "description": "The package gives you the ability to use Unity Version Control in the Unity editor. To use Unity Version Control, a subscription is required. Learn more about how you can get started for free by visiting https://unity.com/solutions/version-control", "keywords": ["backup", "cloud", "collab", "collaborate", "collaboration", "control", "devops", "plastic", "plasticscm", "source", "team", "teams", "version", "vcs"], "category": "Editor", "relatedPackages": {"com.unity.collab-proxy.tests": "2.5.2"}, "_upm": {"changelog": "### Fixed\n\n- Fix token renewal issue: Can't obtain a new token (Message: Invalid Refresh Token., Code: 132.104)"}, "upmCi": {"footprint": "e3c8c550b11acbc6cd4ff90b9c272d14a5d8b0e8"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.5/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git", "type": "git", "revision": "c402e6396d3ea20ecb2d59eac908e7e0a30cd7e6"}}