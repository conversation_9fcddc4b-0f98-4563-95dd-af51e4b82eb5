fileFormatVersion: 2
guid: 8a5f71c28d4354c4e86e87bd7b83a7cb
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: GOAT_
  - first:
      1: 100004
    second: GOAT_ Head
  - first:
      1: 100006
    second: GOAT_ L Calf
  - first:
      1: 100008
    second: GOAT_ L Clavicle
  - first:
      1: 100010
    second: GOAT_ L Finger0
  - first:
      1: 100012
    second: GOAT_ L Foot
  - first:
      1: 100014
    second: GOAT_ L Forearm
  - first:
      1: 100016
    second: GOAT_ L Hand
  - first:
      1: 100018
    second: GOAT_ L HorseLink
  - first:
      1: 100020
    second: GOAT_ L Thigh
  - first:
      1: 100022
    second: GOAT_ L UpperArm
  - first:
      1: 100024
    second: GOAT_ Neck
  - first:
      1: 100026
    second: GOAT_ Neck1
  - first:
      1: 100028
    second: GOAT_ Neck2
  - first:
      1: 100030
    second: GOAT_ Pelvis
  - first:
      1: 100032
    second: GOAT_ Queue de cheval 1
  - first:
      1: 100034
    second: GOAT_ R Calf
  - first:
      1: 100036
    second: GOAT_ R Clavicle
  - first:
      1: 100038
    second: GOAT_ R Finger0
  - first:
      1: 100040
    second: GOAT_ R Forearm
  - first:
      1: 100042
    second: GOAT_ R Hand
  - first:
      1: 100044
    second: GOAT_ R HorseLink
  - first:
      1: 100046
    second: GOAT_ R Thigh
  - first:
      1: 100048
    second: GOAT_ R UpperArm
  - first:
      1: 100050
    second: GOAT_ Spine
  - first:
      1: 100052
    second: GOAT_ Spine1
  - first:
      1: 100054
    second: GOAT_ Tail
  - first:
      1: 100056
    second: GOAT_ Tail1
  - first:
      1: 100058
    second: GOAT_ Tail2
  - first:
      1: 100060
    second: goaty_1
  - first:
      1: 100062
    second: goaty_2
  - first:
      1: 100064
    second: goaty_3
  - first:
      1: 100066
    second: root
  - first:
      1: 100068
    second: WattleL
  - first:
      1: 100070
    second: WattleR
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: GOAT_
  - first:
      4: 400004
    second: GOAT_ Head
  - first:
      4: 400006
    second: GOAT_ L Calf
  - first:
      4: 400008
    second: GOAT_ L Clavicle
  - first:
      4: 400010
    second: GOAT_ L Finger0
  - first:
      4: 400012
    second: GOAT_ L Foot
  - first:
      4: 400014
    second: GOAT_ L Forearm
  - first:
      4: 400016
    second: GOAT_ L Hand
  - first:
      4: 400018
    second: GOAT_ L HorseLink
  - first:
      4: 400020
    second: GOAT_ L Thigh
  - first:
      4: 400022
    second: GOAT_ L UpperArm
  - first:
      4: 400024
    second: GOAT_ Neck
  - first:
      4: 400026
    second: GOAT_ Neck1
  - first:
      4: 400028
    second: GOAT_ Neck2
  - first:
      4: 400030
    second: GOAT_ Pelvis
  - first:
      4: 400032
    second: GOAT_ Queue de cheval 1
  - first:
      4: 400034
    second: GOAT_ R Calf
  - first:
      4: 400036
    second: GOAT_ R Clavicle
  - first:
      4: 400038
    second: GOAT_ R Finger0
  - first:
      4: 400040
    second: GOAT_ R Forearm
  - first:
      4: 400042
    second: GOAT_ R Hand
  - first:
      4: 400044
    second: GOAT_ R HorseLink
  - first:
      4: 400046
    second: GOAT_ R Thigh
  - first:
      4: 400048
    second: GOAT_ R UpperArm
  - first:
      4: 400050
    second: GOAT_ Spine
  - first:
      4: 400052
    second: GOAT_ Spine1
  - first:
      4: 400054
    second: GOAT_ Tail
  - first:
      4: 400056
    second: GOAT_ Tail1
  - first:
      4: 400058
    second: GOAT_ Tail2
  - first:
      4: 400060
    second: goaty_1
  - first:
      4: 400062
    second: goaty_2
  - first:
      4: 400064
    second: goaty_3
  - first:
      4: 400066
    second: root
  - first:
      4: 400068
    second: WattleL
  - first:
      4: 400070
    second: WattleR
  - first:
      74: 7400000
    second: IdleChew
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: IdleChew
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: root
        weight: 1
      - path: root/GOAT_
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ L Thigh
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ L Thigh/GOAT_ L Calf
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ L Thigh/GOAT_ L Calf/GOAT_
          L HorseLink
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ L Thigh/GOAT_ L Calf/GOAT_
          L HorseLink/GOAT_ L Foot
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ R Thigh
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ R Thigh/GOAT_ R Calf
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ R Thigh/GOAT_ R Calf/GOAT_
          R HorseLink
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ L Clavicle
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ L Clavicle/GOAT_
          L UpperArm
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ L Clavicle/GOAT_
          L UpperArm/GOAT_ L Forearm
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ L Clavicle/GOAT_
          L UpperArm/GOAT_ L Forearm/GOAT_ L Hand
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ L Clavicle/GOAT_
          L UpperArm/GOAT_ L Forearm/GOAT_ L Hand/GOAT_ L Finger0
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/GOAT_ Head
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/GOAT_ Head/GOAT_ Queue de cheval 1
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/GOAT_ Head/GOAT_ Queue de cheval 1/goaty_1
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/GOAT_ Head/GOAT_ Queue de cheval 1/goaty_1/goaty_2
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/GOAT_ Head/GOAT_ Queue de cheval 1/goaty_1/goaty_2/goaty_3
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/WattleL
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ Neck/GOAT_ Neck1/GOAT_
          Neck2/WattleR
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ R Clavicle
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ R Clavicle/GOAT_
          R UpperArm
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ R Clavicle/GOAT_
          R UpperArm/GOAT_ R Forearm
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ R Clavicle/GOAT_
          R UpperArm/GOAT_ R Forearm/GOAT_ R Hand
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Spine/GOAT_ Spine1/GOAT_ R Clavicle/GOAT_
          R UpperArm/GOAT_ R Forearm/GOAT_ R Hand/GOAT_ R Finger0
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Tail
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Tail/GOAT_ Tail1
        weight: 1
      - path: root/GOAT_/GOAT_ Pelvis/GOAT_ Tail/GOAT_ Tail1/GOAT_ Tail2
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: root
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: a5c0a31cdcff2af44bc6a503931e12ba, type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
