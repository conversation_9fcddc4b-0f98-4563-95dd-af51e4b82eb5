<variant
    name="release"
    package="com.smg.tractor.trolly.games.farming.game"
    minSdkVersion="23"
    targetSdkVersion="36"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-7.4.2"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\26436efe58187f47fc7a11f38c2f7795\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin"
        resDirectories="src\main\res"
        assetsDirectories="src\main\assets"/>
    <sourceProvider
        manifest="src\release\AndroidManifest.xml"
        javaDirectories="src\release\java;src\release\kotlin"
        resDirectories="src\release\res"
        assetsDirectories="src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <mainArtifact
      classOutputs="build\intermediates\javac\release\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.smg.tractor.trolly.games.farming.game"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\aidl_source_output_dir\release\out;build\generated\source\buildConfig\release;build\generated\renderscript_source_output_dir\release\out"
      generatedResourceFolders="build\generated\res\rs\release;build\generated\res\resValues\release">
  </mainArtifact>
</variant>
