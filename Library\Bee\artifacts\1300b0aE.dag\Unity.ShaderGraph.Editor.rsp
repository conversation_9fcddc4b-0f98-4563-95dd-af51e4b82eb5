-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-define:UNITY_2021_3_45
-define:UNITY_2021_3
-define:UNITY_2021
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_RUNTIME_PERMISSIONS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_ANDROID
-define:TEXTCORE_1_0_OR_NEWER
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:BCG_RCC
-define:Admob_Simple_Rizwan
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.IOSResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.JarResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.PackageManagerResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.185/Google.VersionHandlerImpl.dll"
-r:"Assets/ExternalDependencyManager/Editor/Google.VersionHandler.dll"
-r:"Assets/Firebase/Editor/Firebase.Editor.dll"
-r:"Assets/Firebase/Plugins/Firebase.Analytics.dll"
-r:"Assets/Firebase/Plugins/Firebase.App.dll"
-r:"Assets/Firebase/Plugins/Firebase.Platform.dll"
-r:"Assets/Firebase/Plugins/Firebase.TaskExtension.dll"
-r:"Assets/Firebase/Plugins/Google.MiniJson.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Common.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Core.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.Unity.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Unity.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/AssetCallbacks/CreateShaderGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/AssetCallbacks/CreateShaderSubGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/AssetCallbacks/CreateVFXShaderGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Actions/GraphViewActions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/BuiltinKeywordAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/ContextFilterableAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/InspectableAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/NeverAllowedByTargetAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/SGPropertyDrawerAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/SRPFilterAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Attributes/SubTargetFilterAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/ContextData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Enumerations/Precision.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/AbstractMaterialGraphAsset.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/AbstractShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/BitangentMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/BooleanMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/BooleanShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/CategoryData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ColorMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ColorRGBMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ColorShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/CubemapInputMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/CubemapMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/CubemapShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/DataStore.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/DynamicMatrixMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/DynamicValueMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/DynamicVectorMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GradientInputMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GradientMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GradientShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GraphConcretization.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GraphData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GraphDataReadOnly.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GraphDataUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GraphSetup.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GraphValidation.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/GroupData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/IMaterialGraphAsset.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/IMaterialSlotHasValue.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/LightmappingShaderProperties.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Matrix2MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Matrix2ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Matrix3MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Matrix3ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Matrix4MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Matrix4ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/MatrixShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/MinimalGraphData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/NormalMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ParentGroupChange.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/PositionMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/PreviewMode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/PreviewProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/PropertyConnectionStateMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SamplerStateMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SamplerStateShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ScreenPositionMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SerializableCubemap.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SerializableGuid.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SerializableMesh.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SerializableTexture.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SerializableTextureArray.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SerializableVirtualTexture.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ShaderDropdown.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ShaderGraphRequirements.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ShaderInput.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ShaderKeyword.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/SpaceMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/StickyNoteData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/TangentMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture2DArrayInputMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture2DArrayMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture2DArrayShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture2DInputMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture2DMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture2DShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture3DInputMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture3DMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Texture3DShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/TextureSamplerState.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/UVMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector1MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector1ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector2MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector2ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector3MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector3ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector4MaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/Vector4ShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/VectorShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/VertexColorMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/ViewDirectionMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/VirtualTextureInputMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/VirtualTextureMaterialSlot.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Graphs/VirtualTextureShaderProperty.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Implementation/Edge.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Implementation/GraphObject.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Implementation/HasDependenciesAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Implementation/IHasDependencies.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Implementation/NodeUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Implementation/SlotType.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/GenerationMode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/Graph/DrawState.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/Graph/GraphDrawingData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/Graph/IEdge.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/Graph/INode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/Graph/IOnAssetEnabled.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/Graph/SlotReference.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/ICanChangeShaderGUI.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IGeneratesBodyCode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IGeneratesFunction.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IGraphDataAction.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IGroupItem.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IHasCustomDeprecationMessage.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IInspectable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireBitangent.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireCameraOpaqueTexture.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireDepthTexture.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireFaceSign.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireMeshUV.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireNormal.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequirePosition.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequirePositionPredisplacement.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireScreenPosition.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireTangent.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireTime.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireTransform.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireVertexColor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireVertexID.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireVertexSkinning.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMayRequireViewDirection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IMaySupportVFX.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/IPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/NeededCoordinateSpace.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Interfaces/PositionSource.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/AbstractMaterialNode0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/Edge0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/GraphData0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/GroupData0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/ILegacyTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/IMasterNode1.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/PBRMasterNode1.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/SerializableGuid.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/ShaderInput0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/SlotReference0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/SpriteLitMasterNode1.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/SpriteUnlitMasterNode1.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/StickyNoteData0.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/UnlitMasterNode1.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Legacy/VisualEffectMasterNode1.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/AbstractMaterialNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/ChannelMixerNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/ContrastNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/HueNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/InvertColorsNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/ReplaceColorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/SaturationNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Adjustment/WhiteBalanceNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Blend/BlendMode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Blend/BlendNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Filter/DitherNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Mask/ChannelMaskNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Mask/ColorMaskNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Normal/NormalBlendNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Normal/NormalFromHeightNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Normal/NormalFromTextureNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Normal/NormalReconstructZNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Normal/NormalStrengthNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Normal/NormalUnpackNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Artistic/Utility/ColorspaceConversion.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/BlockNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Channel/CombineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Channel/FlipNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Channel/SplitNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Channel/SwizzleNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/CodeFunctionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/FormerNameAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/FunctionMultiInput.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/GeometryNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/GuidEncoder.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/BooleanNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/ColorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/ConstantNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/IntegerNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/SliderNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/TimeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/Vector1Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/Vector2Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/Vector3Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Basic/Vector4Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/CustomInterpolatorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/BitangentVectorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/InstanceIDNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/NormalVectorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/PositionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/ScreenPositionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/TangentVectorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/UVNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/VertexColorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/VertexIDNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/ViewDirectionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Geometry/ViewVectorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Gradient/BlackbodyNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Gradient/GradientNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Gradient/SampleGradientNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Lighting/AmbientNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Lighting/BakedGINode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Lighting/ReflectionProbeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Matrix/Matrix2Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Matrix/Matrix3Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Matrix/Matrix4Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Matrix/TransformationMatrixNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/PBR/DielectricSpecularNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/PBR/MetalReflectanceNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/PropertyNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/CameraNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/EyeIndexNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/FogNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/ObjectNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/SceneColorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/SceneDepthNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Scene/ScreenNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/CalculateLevelOfDetailTexture2DNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/CubemapAssetNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/GatherTexture2DNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/ProceduralVirtualTextureNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleCubemapNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleRawCubemapNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SamplerStateNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleTexture2DArrayNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleTexture2DLODNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleTexture2DNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleTexture3DNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/SampleVirtualTextureNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/TexelSizeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/Texture2DArrayAssetNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/Texture2DAssetNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/Texture3DAssetNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Input/Texture/TextureStackNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/IPropertyFromNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/LegacyUnknownTypeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/AbsoluteNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/ExponentialNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/LengthNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/LogNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/ModuloNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/NegateNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/NormalizeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/PosterizeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/ReciprocalNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Advanced/ReciprocalSquareRootNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Basic/AddNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Basic/DivideNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Basic/MultiplyNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Basic/PowerNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Basic/SquareRootNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Basic/SubtractNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Derivative/DDXNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Derivative/DDXYNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Derivative/DDYNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Interpolation/InverseLerpNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Interpolation/LerpNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Interpolation/SmoothstepNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Matrix/MatrixConstructionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Matrix/MatrixDeterminantNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Matrix/MatrixSplitNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Matrix/MatrixTransposeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/ClampNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/FractionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/MaximumNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/MinimumNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/OneMinusNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/RandomRangeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/RemapNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Range/SaturateNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Round/CeilingNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Round/FloorNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Round/RoundNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Round/SignNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Round/StepNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Round/TruncateNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/ArccosineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/ArcsineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/Arctangent2Node.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/ArctangentNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/CosineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/DegreesToRadiansNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/HyperbolicCosineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/HyperbolicSineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/HyperbolicTangentNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/RadiansToDegreesNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/SineNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Trigonometry/TangentNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/CrossProductNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/DistanceNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/DotProductNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/FresnelEffectNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/ProjectionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/ReflectionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/RejectionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/RotateAboutAxisNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/SphereMaskNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Vector/TransformNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Wave/NoiseSineWaveNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Wave/SawtoothWaveNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Wave/SquareWaveNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Math/Wave/TriangleWaveNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/MeshDeformation/ComputeDeformNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/MeshDeformation/LinearBlendSkinningNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/NodeClassCache.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/NormalMapSpace.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/CheckerboardNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Noise/GradientNoiseNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Noise/SimpleNoiseNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Noise/VoronoiNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Shape/EllipseNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Shape/PolygonNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Shape/RectangleNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Shape/RoundedPolygonNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Procedural/Shape/RoundedRectangleNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/RedirectNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/ShaderStage.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/SlotValue.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/TitleAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/CustomFunctionNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/DropdownNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/KeywordNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/AllNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/AndNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/AnyNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/BranchNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/BranchOnInputConnection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/ComparisonNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/IsFrontFaceNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/IsInfiniteNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/IsNanNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/NandNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/NotNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/Logic/OrNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/PreviewNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/RedirectNodeData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/RedirectNodeView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/SplitTextureTransformNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/Utility/SubGraphNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/FlipbookNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/ParallaxMappingNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/ParallaxOcclusionMappingNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/PolarCoordinatesNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/RadialShearNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/RotateNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/SpherizeNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/TilingAndOffsetNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/TriplanarNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Nodes/UV/TwirlNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/SubGraph/SubGraphAsset.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/SubGraph/SubGraphOutputNode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/FunctionRegistry.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/GradientUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/GraphUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/Identifier.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/KeywordCollector.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/KeywordDependentCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/KeywordUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/Logging.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/PooledHashSet.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/PooledList.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/PrecisionUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/PropertyUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/ScreenSpaceType.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/SerializationHelper.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/ShaderGraphRequirementsPerKeyword.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/SlotValueTypeUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/StackPool.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/TextUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Data/Util/UvChannel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/DefaultShaderIncludes.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Blackboard/BlackboardInputInfo.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Blackboard/BlackboardUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Blackboard/SGBlackboard.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Blackboard/SGBlackboardCategory.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Blackboard/SGBlackboardField.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Blackboard/SGBlackboardRow.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/CategoryColors.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/ColorManager.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/CustomColorData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/IColorProvider.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/NoColors.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/PrecisionColors.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Colors/UserColors.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controllers/BlackboardCategoryController.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controllers/BlackboardController.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controllers/SGController.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controllers/ShaderInputViewController.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ButtonControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ChannelEnumControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ChannelEnumMaskControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ChannelMixerControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ColorControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/CubemapControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/DefaultControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/DielectricSpecularControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/EnumControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/EnumConversionControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/GradientControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/IControlAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/IdentifierControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/IntegerControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ObjectControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/PopupControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/SliderControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/TextControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/Texture3DControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/TextureArrayControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/TextureControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/ToggleControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Controls/VectorControl.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/EdgeConnectorListener.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/INodeModificationListener.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/InspectorView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/MasterPreviewView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/AbstractMaterialNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/BoolPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/ColorPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/CubemapPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/CustomFunctionNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/DropdownPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/EnumPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/FloatPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/GradientPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/GraphDataPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/IntegerPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/IShaderPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/MatrixPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/PositionNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/ProceduralVirtualTextureNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/SamplerStateNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/SampleTexture2DArrayNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/SampleTexture2DNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/SampleVirtualTextureNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/ShaderInputPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/SubGraphOutputNodePropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/TextPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/Texture2DArrayPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/Texture2DPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/Texture3DPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/ToggleDataPropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/Vector2PropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/Vector3PropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawers/Vector4PropertyDrawer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/PropertyDrawerUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/TabbedView/TabbedView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/TabbedView/TabButton.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Inspector/WindowDockingLayout.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Interfaces/IRectInterface.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Interfaces/IResizable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Interfaces/ISGControlledElement.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Interfaces/ISGViewModel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Manipulators/Draggable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Manipulators/ElementResizer.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Manipulators/ResizeBorderFrame.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Manipulators/ResizeSideHandle.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Manipulators/Scrollable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Manipulators/WindowDraggable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/MaterialEditor/ShaderGraphPropertyDrawers.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/MaterialGraphEditWindow.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/MaterialGraphPreviewGenerator.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/PreviewManager.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/SearchWindowAdapter.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/SearchWindowProvider.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/ViewModels/BlackboardCategoryViewModel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/ViewModels/BlackboardViewModel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/ViewModels/InspectorViewModel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/ViewModels/ShaderInputViewModel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/ContextView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/FloatField.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/GradientEdge.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/GraphEditorView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/GraphSubWindow.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/HelpBoxRow.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/HlslFunctionView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/IdentifierField.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/IShaderNodeView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/MaterialGraphView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/MaterialNodeView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/NodeSettingsView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/PortInputView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/PreviewSceneResources.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/PropertyNodeView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/PropertyRow.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/PropertySheet.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/ReorderableSlotListView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/ReorderableTextListView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/ResizableElement.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/ShaderGroup.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/ShaderPort.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/BooleanSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/ColorRGBSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/ColorSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/CubemapSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/GradientSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/LabelSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/MultiFloatSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/MultiIntegerSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/PropertyConnectionStateSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/ScreenPositionSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/Texture3DSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/TextureArraySlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/TextureSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/Slots/UVSlotControlView.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Drawing/Views/StickyNote.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Extensions/FieldExtensions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Extensions/IConditionalExtensions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Extensions/StencilExtensions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Attributes/GenerateBlocksAttribute.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/AdditionalCommandCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/AssetCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/DefineCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/DependencyCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/FieldCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/IncludeCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/KeywordCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/PassCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/PragmaCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/RenderStateCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Collections/StructCollection.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Contexts/TargetActiveBlockContext.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Contexts/TargetFieldContext.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Contexts/TargetPropertyGUIContext.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Contexts/TargetSetupContext.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Controls.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Data/ConditionalField.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Data/DropdownEntry.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Data/FieldCondition.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Data/FieldDependency.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Data/KeywordEntry.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/AdditionalCommandDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/BlockFieldDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/FieldDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/IncludeDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/KeywordDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/PassDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/PragmaDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/RenderStateDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/StencilDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/StructDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Descriptors/SubShaderDescriptor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/Blend.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/BlendOp.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/Cull.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/IncludeLocation.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/InstancingOptions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/KeywordDefinition.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/KeywordScope.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/KeywordShaderStage.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/KeywordType.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/NormalDropOffSpace.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/Platform.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/PropertyType.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/RenderQueue.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/RenderType.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/ShaderModel.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/ShaderValueType.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/StructFieldOptions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/ZTest.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Enumerations/ZWrite.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/GraphCode.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/IHasMetaData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/OutputMetadata.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/ActiveFields.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/CustomInterpolatorUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/GenerationUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/Generator.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/GraphCompilationResult.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/MatrixNames.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/PropertyCollector.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/ShaderGeneratorNames.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/ShaderSpliceUtil.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Processors/ShaderStringBuilder.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/ShaderGraphVfxAsset.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/SubTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Target.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/TargetResources/BlockFields.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/TargetResources/FieldDependencies.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/TargetResources/Fields.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/TargetResources/StructFields.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/TargetResources/Structs.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/AssetPostProcessors/MaterialPostprocessor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/AssetPostProcessors/ShaderGraphMaterialsUpdater.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/AssetVersion.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/AssetCallbacks/CreateLitShaderGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/AssetCallbacks/CreateUnlitShaderGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInFields.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInMetadata.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInProperties.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInStructFields.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInStructs.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/Targets/BuiltInLitSubTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/Targets/BuiltInSubTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/Targets/BuiltInTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/Targets/BuiltInUnlitSubTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGUI/BaseShaderGUI.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGUI/BuiltInLitGUI.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderGUI/BuiltInUnlitGUI.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderPreprocessor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/BuiltIn/Editor/ShaderUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/PreviewTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Targets/VFXTarget.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Generation/Utils/TargetUtils.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderGraphAssetPostProcessor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderGraphImporter.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderGraphImporterEditor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderGraphMetadata.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderSubGraphImporter.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderSubGraphImporterEditor.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Importers/ShaderSubGraphMetadata.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Interface/IConditional.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/FakeJsonObject.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/JsonData.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/JsonObject.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/JsonRef.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/MultiJson.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/MultiJsonEntry.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/MultiJsonInternal.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/RefDataEnumerable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/RefValueEnumerable.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Serialization/SerializationExtensions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/ShaderGraphAnalytics.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/ShaderGraphPreferences.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/ShaderGraphProjectSettings.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/ShaderGUI/GenericShaderGraphMaterialGUI.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/AssertHelpers.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/CompatibilityExtensions.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/CopyPasteGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/CreateSerializableGraph.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/Documentation.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/FileUtilities.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/IndexSet.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/ListUtilities.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/MessageManager.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/TypeMapper.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/TypeMapping.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/UIUtilities.cs"
"Library/PackageCache/com.unity.shadergraph@12.1.15/Editor/Util/ValueUtilities.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.AdditionalFile.txt"