# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.2.0"
  }
  digests {
    sha256: "=!1\245Za\247w2.!&\340\001\200\021\357\2463\236S\264AS\353e\033\026\002\f\312p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.21"
  }
  digests {
    sha256: "\004*\034\321\254\227l\334\376^\266?\035\216\v\v\211,\222H\341Zi\310\317\272I]Tn\245*"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.21"
  }
  digests {
    sha256: "jD\311\354\311\327uM\236\224?\261\343X\214t\324\243\361x[\345\020t\364\235lW#h*s"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.8.0"
  }
  digests {
    sha256: "H\306J\025\354>\261\033\37333\236\\\353p\354\177\202\033\322\337\242\353\206u\353\32553\027\347\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.20"
  }
  digests {
    sha256: "\343\230\266ywb\'\030\277\030\377\231\2679\307\331\332\006\0173\373E\212.% 2!\301j\360\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.20"
  }
  digests {
    sha256: "\257\036\304\f;\225\032\375\314\f*\001s\307\270\027c\305(\034-[\257\277\n\205D\242L]\314\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.6.1"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.1"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.1"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.1"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.1"
  }
  digests {
    sha256: "\310/\211\"\032\333\341\235\367\307\255\272\266?N\314\205\177\307F\343\311IBV\253\217\245\302\004\221\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\027\220\273\233\v>\376j\'\373\033\243\243S\v\v\232\246eNX\277\212\236\367\203\367g\345\r1\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.1"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.1"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.0"
  }
  digests {
    sha256: "\352\255V\210t\316\247\341s\213\356\2772\230g\n\207C\342[\264\223EFvK\356b\366\322\177&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.5.1"
  }
  digests {
    sha256: "K\004\264-,\037\201\300/\257\017{n\234\311\376\336\020\375\356\217f\023l\324\271\237\210\350\364\214\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.2.0"
  }
  digests {
    sha256: "\304p)|\003\377=\341\303\321]\254\360\276\f\256c\253\301\vR\360!\335\a\256(\332\243\020\017\345"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "24.2.0"
  }
  digests {
    sha256: "\035\340\3706\200\256R\302\245\315\354\222\250iI\025\325\373}\250\266`\367\331\314d\260\272\323R!\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.11.0"
  }
  digests {
    sha256: "r\034\271\030B\264o\240V\204}\020MR%\310\270\341\350\266\"c\271\223\005\036\036Z\0017\267\354"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.11.0-alpha02"
  }
  digests {
    sha256: ";\037^#n\221\231\2518\375\vP{\306\316q\034F\315\002\206\275@\246\330r\336@\232\267\331N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-api"
    version: "24.2.0"
  }
  digests {
    sha256: "q\3062\024\2308\by\377m\332\315\310\213\307@\211\232\376\177\253\331\363\354o\254y\362\001\352<\266"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\210\202>\000\272\232\256m\306Cj\355Y(\300p\326\212b\302X\035\351ne\\\250o\003\016r\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "21.3.0"
  }
  digests {
    sha256: "\213c\314\315\326(E\270\253\206,\264\273\261z;X\264\004\004L\"\264\354s\257I\354\266\2271\265"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "21.3.0"
  }
  digests {
    sha256: "\312V>R\362\324\314\003c`\305\213\276_E\033[(W2O\314jD~t\354\352\r\241\356\367"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\262\303\364Th\313\2057\246d\353x8\\\006\221\027\212\2534\020,\246\350U\220(\037!&&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.2.0"
  }
  digests {
    sha256: "\272\036\376A\213~&\226\207\214\306\004\302}\307\2572\242\307\024b\337Uc#\a\311\t\252\234.\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "21.3.0"
  }
  digests {
    sha256: "f(?\375Ut\021\267\033\274[m:\347\315\215\342\3443\316\241\226\334I\314\032\230\311\371\274\303f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "21.3.0"
  }
  digests {
    sha256: "\364\343\311\271\314xW\202\323\245\030\202Qc\243\340\327!\237\200\266Xw\324\375\376v\027z\256\215\246"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "21.3.0"
  }
  digests {
    sha256: "\023m~\027\324\212U\000\017\006\247r]\262\227\264\355\237F\230g1\324\204{\224\223\323E>\200\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "21.3.0"
  }
  digests {
    sha256: "\221\215\217\215V*J]\264\233\310\336\317af\001\214\333mB\037\021\372A1\366\000\034\b\260\321|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "20.3.3"
  }
  digests {
    sha256: ")\036\206|\227)\221\267A\254\020\r9\364$\2127rx\005\246\303|\345\223v3O\261N\376\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "17.1.0"
  }
  digests {
    sha256: "5,q\370\231\224\313D\201\332\353E\35560\273\361x\233\337\033\344V9\210\342\301\206\030\257O`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.0.1"
  }
  digests {
    sha256: "-\202>SS\374\334\277\356\201L\2725\2777\203\253\004#\201D\273\315\273\024\236q\215V\303\273\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.0.1"
  }
  digests {
    sha256: "B\301\247N\231\211:\227zS\225\321R\312\245G@\026e\341\274N\2632\351\264:\241inG\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "21.3.0"
  }
  digests {
    sha256: ">\374B\205\355:\374\273~\025\3731\226\277\271\324\353]Mq\337W}o\356\356\r\225%x\336n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics-unity"
    version: "11.6.0"
  }
  digests {
    sha256: "[\2319\372DE\367Q\200\362\212OJ\347\334\262\027.\241\257\177p]\351\021\357\002p?]^I"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-app-unity"
    version: "11.6.0"
  }
  digests {
    sha256: "\006E\277\b\215\002\274\3357M\031\211\b\036\301qv\313\214\364\356\306\024Xd\0037\212<_\321\356"
  }
}
library {
  maven_library {
    artifactId: "googlemobileads-unity"
  }
  digests {
    sha256: "\317\243X\035\246\227>?`t\230?6B\325\006\261\264\n\224\336Pj\306\274fe\200\334\177\322\270"
  }
}
library {
  digests {
    sha256: "S\337\346\265\317\244\375\367\266\263v .UJ|l\272\233\221\350\227c\261\346a\036\211\321\025\257*"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 7
  library_dep_index: 46
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 9
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 7
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 34
}
library_dependencies {
  library_index: 8
  library_dep_index: 4
}
library_dependencies {
  library_index: 9
  library_dep_index: 2
}
library_dependencies {
  library_index: 10
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 12
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 33
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
}
library_dependencies {
  library_index: 14
  library_dep_index: 2
  library_dep_index: 13
}
library_dependencies {
  library_index: 15
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 31
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 32
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 5
  library_dep_index: 20
}
library_dependencies {
  library_index: 19
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 17
}
library_dependencies {
  library_index: 20
  library_dep_index: 4
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 4
}
library_dependencies {
  library_index: 22
  library_dep_index: 2
  library_dep_index: 15
  library_dep_index: 15
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 23
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 25
  library_dep_index: 2
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 29
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 4
}
library_dependencies {
  library_index: 30
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 4
}
library_dependencies {
  library_index: 31
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 23
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 12
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 31
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 25
  library_dep_index: 11
}
library_dependencies {
  library_index: 34
  library_dep_index: 2
  library_dep_index: 9
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 23
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 38
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 4
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 26
  library_dep_index: 4
}
library_dependencies {
  library_index: 38
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 31
  library_dep_index: 27
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 2
  library_dep_index: 7
}
library_dependencies {
  library_index: 41
  library_dep_index: 9
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 42
  library_dep_index: 43
}
library_dependencies {
  library_index: 42
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 9
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 9
}
library_dependencies {
  library_index: 44
  library_dep_index: 2
}
library_dependencies {
  library_index: 45
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 40
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 11
}
library_dependencies {
  library_index: 48
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 7
  library_dep_index: 44
  library_dep_index: 11
}
library_dependencies {
  library_index: 49
  library_dep_index: 2
  library_dep_index: 29
  library_dep_index: 4
  library_dep_index: 17
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 11
  library_dep_index: 4
  library_dep_index: 17
  library_dep_index: 49
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
  library_dep_index: 11
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 57
  library_dep_index: 2
  library_dep_index: 7
}
library_dependencies {
  library_index: 58
  library_dep_index: 48
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 67
}
library_dependencies {
  library_index: 59
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 31
  library_dep_index: 25
  library_dep_index: 7
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 62
  library_dep_index: 7
  library_dep_index: 32
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 14
}
library_dependencies {
  library_index: 61
  library_dep_index: 2
}
library_dependencies {
  library_index: 62
  library_dep_index: 2
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 2
}
library_dependencies {
  library_index: 64
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 36
}
library_dependencies {
  library_index: 65
  library_dep_index: 64
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 64
}
library_dependencies {
  library_index: 67
  library_dep_index: 2
  library_dep_index: 64
}
library_dependencies {
  library_index: 68
  library_dep_index: 64
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
  library_dep_index: 64
  library_dep_index: 71
}
library_dependencies {
  library_index: 70
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 36
  library_dep_index: 64
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 64
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
  library_dep_index: 80
  library_dep_index: 88
}
library_dependencies {
  library_index: 73
  library_dep_index: 9
  library_dep_index: 74
  library_dep_index: 68
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 74
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 75
  library_dep_index: 38
  library_dep_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 75
  library_dep_index: 2
}
library_dependencies {
  library_index: 76
  library_dep_index: 2
}
library_dependencies {
  library_index: 77
  library_dep_index: 2
}
library_dependencies {
  library_index: 78
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 68
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 74
  library_dep_index: 64
}
library_dependencies {
  library_index: 80
  library_dep_index: 68
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 65
  library_dep_index: 71
  library_dep_index: 81
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 84
  library_dep_index: 10
  library_dep_index: 64
  library_dep_index: 71
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
}
library_dependencies {
  library_index: 84
  library_dep_index: 2
  library_dep_index: 55
  library_dep_index: 82
}
library_dependencies {
  library_index: 85
  library_dep_index: 71
  library_dep_index: 81
  library_dep_index: 84
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 71
  library_dep_index: 82
}
library_dependencies {
  library_index: 87
  library_dep_index: 64
  library_dep_index: 82
}
library_dependencies {
  library_index: 88
  library_dep_index: 9
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 78
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 36
  dependency_index: 22
  dependency_index: 24
  dependency_index: 47
  dependency_index: 70
  dependency_index: 67
  dependency_index: 72
  dependency_index: 89
  dependency_index: 90
  dependency_index: 81
  dependency_index: 91
  dependency_index: 92
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://maven.google.com/"
  }
}
