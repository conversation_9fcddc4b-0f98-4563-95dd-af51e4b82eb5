﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>



// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Reflection.AssemblyCompanyAttribute
struct AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4;
// System.Reflection.AssemblyConfigurationAttribute
struct AssemblyConfigurationAttribute_t071B324A83314FBA14A43F37BE7206C420218B7C;
// System.Reflection.AssemblyCopyrightAttribute
struct AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC;
// System.Reflection.AssemblyDescriptionAttribute
struct AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3;
// System.Reflection.AssemblyFileVersionAttribute
struct AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F;
// System.Reflection.AssemblyProductAttribute
struct AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA;
// System.Reflection.AssemblyTitleAttribute
struct AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7;
// System.Reflection.AssemblyTrademarkAttribute
struct AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2;
// System.Runtime.CompilerServices.CompilerGeneratedAttribute
struct CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C;
// System.Diagnostics.DebuggableAttribute
struct DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B;
// System.Diagnostics.DebuggerBrowsableAttribute
struct DebuggerBrowsableAttribute_t2FA4793AD1982F5150E07D26822ED5953CD90F53;
// System.Runtime.CompilerServices.InternalsVisibleToAttribute
struct InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C;
// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute
struct RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80;
// System.String
struct String_t;



IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// System.Object


// System.Attribute
struct Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// System.Reflection.AssemblyCompanyAttribute
struct AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyCompanyAttribute::m_company
	String_t* ___m_company_0;

public:
	inline static int32_t get_offset_of_m_company_0() { return static_cast<int32_t>(offsetof(AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4, ___m_company_0)); }
	inline String_t* get_m_company_0() const { return ___m_company_0; }
	inline String_t** get_address_of_m_company_0() { return &___m_company_0; }
	inline void set_m_company_0(String_t* value)
	{
		___m_company_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_company_0), (void*)value);
	}
};


// System.Reflection.AssemblyConfigurationAttribute
struct AssemblyConfigurationAttribute_t071B324A83314FBA14A43F37BE7206C420218B7C  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyConfigurationAttribute::m_configuration
	String_t* ___m_configuration_0;

public:
	inline static int32_t get_offset_of_m_configuration_0() { return static_cast<int32_t>(offsetof(AssemblyConfigurationAttribute_t071B324A83314FBA14A43F37BE7206C420218B7C, ___m_configuration_0)); }
	inline String_t* get_m_configuration_0() const { return ___m_configuration_0; }
	inline String_t** get_address_of_m_configuration_0() { return &___m_configuration_0; }
	inline void set_m_configuration_0(String_t* value)
	{
		___m_configuration_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_configuration_0), (void*)value);
	}
};


// System.Reflection.AssemblyCopyrightAttribute
struct AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyCopyrightAttribute::m_copyright
	String_t* ___m_copyright_0;

public:
	inline static int32_t get_offset_of_m_copyright_0() { return static_cast<int32_t>(offsetof(AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC, ___m_copyright_0)); }
	inline String_t* get_m_copyright_0() const { return ___m_copyright_0; }
	inline String_t** get_address_of_m_copyright_0() { return &___m_copyright_0; }
	inline void set_m_copyright_0(String_t* value)
	{
		___m_copyright_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_copyright_0), (void*)value);
	}
};


// System.Reflection.AssemblyDescriptionAttribute
struct AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyDescriptionAttribute::m_description
	String_t* ___m_description_0;

public:
	inline static int32_t get_offset_of_m_description_0() { return static_cast<int32_t>(offsetof(AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3, ___m_description_0)); }
	inline String_t* get_m_description_0() const { return ___m_description_0; }
	inline String_t** get_address_of_m_description_0() { return &___m_description_0; }
	inline void set_m_description_0(String_t* value)
	{
		___m_description_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_description_0), (void*)value);
	}
};


// System.Reflection.AssemblyFileVersionAttribute
struct AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyFileVersionAttribute::_version
	String_t* ____version_0;

public:
	inline static int32_t get_offset_of__version_0() { return static_cast<int32_t>(offsetof(AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F, ____version_0)); }
	inline String_t* get__version_0() const { return ____version_0; }
	inline String_t** get_address_of__version_0() { return &____version_0; }
	inline void set__version_0(String_t* value)
	{
		____version_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____version_0), (void*)value);
	}
};


// System.Reflection.AssemblyProductAttribute
struct AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyProductAttribute::m_product
	String_t* ___m_product_0;

public:
	inline static int32_t get_offset_of_m_product_0() { return static_cast<int32_t>(offsetof(AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA, ___m_product_0)); }
	inline String_t* get_m_product_0() const { return ___m_product_0; }
	inline String_t** get_address_of_m_product_0() { return &___m_product_0; }
	inline void set_m_product_0(String_t* value)
	{
		___m_product_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_product_0), (void*)value);
	}
};


// System.Reflection.AssemblyTitleAttribute
struct AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyTitleAttribute::m_title
	String_t* ___m_title_0;

public:
	inline static int32_t get_offset_of_m_title_0() { return static_cast<int32_t>(offsetof(AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7, ___m_title_0)); }
	inline String_t* get_m_title_0() const { return ___m_title_0; }
	inline String_t** get_address_of_m_title_0() { return &___m_title_0; }
	inline void set_m_title_0(String_t* value)
	{
		___m_title_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_title_0), (void*)value);
	}
};


// System.Reflection.AssemblyTrademarkAttribute
struct AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyTrademarkAttribute::m_trademark
	String_t* ___m_trademark_0;

public:
	inline static int32_t get_offset_of_m_trademark_0() { return static_cast<int32_t>(offsetof(AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2, ___m_trademark_0)); }
	inline String_t* get_m_trademark_0() const { return ___m_trademark_0; }
	inline String_t** get_address_of_m_trademark_0() { return &___m_trademark_0; }
	inline void set_m_trademark_0(String_t* value)
	{
		___m_trademark_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_trademark_0), (void*)value);
	}
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Runtime.CompilerServices.CompilerGeneratedAttribute
struct CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// System.Runtime.CompilerServices.InternalsVisibleToAttribute
struct InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Runtime.CompilerServices.InternalsVisibleToAttribute::_assemblyName
	String_t* ____assemblyName_0;
	// System.Boolean System.Runtime.CompilerServices.InternalsVisibleToAttribute::_allInternalsVisible
	bool ____allInternalsVisible_1;

public:
	inline static int32_t get_offset_of__assemblyName_0() { return static_cast<int32_t>(offsetof(InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C, ____assemblyName_0)); }
	inline String_t* get__assemblyName_0() const { return ____assemblyName_0; }
	inline String_t** get_address_of__assemblyName_0() { return &____assemblyName_0; }
	inline void set__assemblyName_0(String_t* value)
	{
		____assemblyName_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____assemblyName_0), (void*)value);
	}

	inline static int32_t get_offset_of__allInternalsVisible_1() { return static_cast<int32_t>(offsetof(InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C, ____allInternalsVisible_1)); }
	inline bool get__allInternalsVisible_1() const { return ____allInternalsVisible_1; }
	inline bool* get_address_of__allInternalsVisible_1() { return &____allInternalsVisible_1; }
	inline void set__allInternalsVisible_1(bool value)
	{
		____allInternalsVisible_1 = value;
	}
};


// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute
struct RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Boolean System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::m_wrapNonExceptionThrows
	bool ___m_wrapNonExceptionThrows_0;

public:
	inline static int32_t get_offset_of_m_wrapNonExceptionThrows_0() { return static_cast<int32_t>(offsetof(RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80, ___m_wrapNonExceptionThrows_0)); }
	inline bool get_m_wrapNonExceptionThrows_0() const { return ___m_wrapNonExceptionThrows_0; }
	inline bool* get_address_of_m_wrapNonExceptionThrows_0() { return &___m_wrapNonExceptionThrows_0; }
	inline void set_m_wrapNonExceptionThrows_0(bool value)
	{
		___m_wrapNonExceptionThrows_0 = value;
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// System.Diagnostics.DebuggerBrowsableState
struct DebuggerBrowsableState_t2A824ECEB650CFABB239FD0918FCC88A09B45091 
{
public:
	// System.Int32 System.Diagnostics.DebuggerBrowsableState::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(DebuggerBrowsableState_t2A824ECEB650CFABB239FD0918FCC88A09B45091, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Diagnostics.DebuggableAttribute/DebuggingModes
struct DebuggingModes_t279D5B9C012ABA935887CB73C5A63A1F46AF08A8 
{
public:
	// System.Int32 System.Diagnostics.DebuggableAttribute/DebuggingModes::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(DebuggingModes_t279D5B9C012ABA935887CB73C5A63A1F46AF08A8, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Diagnostics.DebuggableAttribute
struct DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Diagnostics.DebuggableAttribute/DebuggingModes System.Diagnostics.DebuggableAttribute::m_debuggingModes
	int32_t ___m_debuggingModes_0;

public:
	inline static int32_t get_offset_of_m_debuggingModes_0() { return static_cast<int32_t>(offsetof(DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B, ___m_debuggingModes_0)); }
	inline int32_t get_m_debuggingModes_0() const { return ___m_debuggingModes_0; }
	inline int32_t* get_address_of_m_debuggingModes_0() { return &___m_debuggingModes_0; }
	inline void set_m_debuggingModes_0(int32_t value)
	{
		___m_debuggingModes_0 = value;
	}
};


// System.Diagnostics.DebuggerBrowsableAttribute
struct DebuggerBrowsableAttribute_t2FA4793AD1982F5150E07D26822ED5953CD90F53  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Diagnostics.DebuggerBrowsableState System.Diagnostics.DebuggerBrowsableAttribute::state
	int32_t ___state_0;

public:
	inline static int32_t get_offset_of_state_0() { return static_cast<int32_t>(offsetof(DebuggerBrowsableAttribute_t2FA4793AD1982F5150E07D26822ED5953CD90F53, ___state_0)); }
	inline int32_t get_state_0() const { return ___state_0; }
	inline int32_t* get_address_of_state_0() { return &___state_0; }
	inline void set_state_0(int32_t value)
	{
		___state_0 = value;
	}
};

#ifdef __clang__
#pragma clang diagnostic pop
#endif



// System.Void System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute__ctor_m551DDF1438CE97A984571949723F30F44CF7317C (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::set_WrapNonExceptionThrows(System.Boolean)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, bool ___value0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.InternalsVisibleToAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9 (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * __this, String_t* ___assemblyName0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyFileVersionAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyFileVersionAttribute__ctor_mF855AEBC51CB72F4FF913499256741AE57B0F13D (AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F * __this, String_t* ___version0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyTrademarkAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyTrademarkAttribute__ctor_m6FBD5AAE48F00120043AD8BECF2586896CFB6C02 (AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2 * __this, String_t* ___trademark0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyCopyrightAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyCopyrightAttribute__ctor_mB0B5F5C1A7A8B172289CC694E2711F07A37CE3F3 (AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC * __this, String_t* ___copyright0, const RuntimeMethod* method);
// System.Void System.Diagnostics.DebuggableAttribute::.ctor(System.Diagnostics.DebuggableAttribute/DebuggingModes)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebuggableAttribute__ctor_m7FF445C8435494A4847123A668D889E692E55550 (DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B * __this, int32_t ___modes0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyTitleAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyTitleAttribute__ctor_mE239F206B3B369C48AE1F3B4211688778FE99E8D (AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7 * __this, String_t* ___title0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyDescriptionAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyDescriptionAttribute__ctor_m3A0BD500FF352A67235FBA499FBA58EFF15B1F25 (AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3 * __this, String_t* ___description0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyProductAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyProductAttribute__ctor_m26DF1EBC1C86E7DA4786C66B44123899BE8DBCB8 (AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA * __this, String_t* ___product0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyCompanyAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyCompanyAttribute__ctor_m435C9FEC405646617645636E67860598A0C46FF0 (AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4 * __this, String_t* ___company0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyConfigurationAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyConfigurationAttribute__ctor_m6EE76F5A155EDEA71967A32F78D777038ADD0757 (AssemblyConfigurationAttribute_t071B324A83314FBA14A43F37BE7206C420218B7C * __this, String_t* ___configuration0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.CompilerGeneratedAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35 (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * __this, const RuntimeMethod* method);
// System.Void System.Diagnostics.DebuggerBrowsableAttribute::.ctor(System.Diagnostics.DebuggerBrowsableState)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebuggerBrowsableAttribute__ctor_mAA8BCC1E418754685F320B14A08AC226E76346E5 (DebuggerBrowsableAttribute_t2FA4793AD1982F5150E07D26822ED5953CD90F53 * __this, int32_t ___state0, const RuntimeMethod* method);
static void GoogleMobileAds_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * tmp = (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 *)cache->attributes[0];
		RuntimeCompatibilityAttribute__ctor_m551DDF1438CE97A984571949723F30F44CF7317C(tmp, NULL);
		RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline(tmp, true, NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[1];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x4D\x6F\x62\x69\x6C\x65\x41\x64\x73\x2E\x55\x6D\x70"), NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[2];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x55\x6E\x69\x74\x54\x65\x73\x74\x73"), NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[3];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x62\x61\x73\x69\x63\x5F\x75\x6E\x69\x74\x74\x65\x73\x74\x73\x5F\x74\x65\x73\x74\x5F\x6C\x69\x62\x72\x61\x72\x79"), NULL);
	}
	{
		AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F * tmp = (AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F *)cache->attributes[4];
		AssemblyFileVersionAttribute__ctor_mF855AEBC51CB72F4FF913499256741AE57B0F13D(tmp, il2cpp_codegen_string_new_wrapper("\x38\x2E\x35\x2E\x33"), NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[5];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x4D\x6F\x62\x69\x6C\x65\x41\x64\x73\x2E\x50\x6C\x61\x63\x65\x6D\x65\x6E\x74"), NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[6];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x4D\x6F\x62\x69\x6C\x65\x41\x64\x73\x4E\x61\x74\x69\x76\x65"), NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[7];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x4D\x6F\x62\x69\x6C\x65\x41\x64\x73\x4E\x61\x74\x69\x76\x65\x2E\x75\x6E\x6F\x62\x66\x75\x73\x63\x61\x74\x65"), NULL);
	}
	{
		AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2 * tmp = (AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2 *)cache->attributes[8];
		AssemblyTrademarkAttribute__ctor_m6FBD5AAE48F00120043AD8BECF2586896CFB6C02(tmp, il2cpp_codegen_string_new_wrapper(""), NULL);
	}
	{
		AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC * tmp = (AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC *)cache->attributes[9];
		AssemblyCopyrightAttribute__ctor_mB0B5F5C1A7A8B172289CC694E2711F07A37CE3F3(tmp, il2cpp_codegen_string_new_wrapper("\x43\x6F\x70\x79\x72\x69\x67\x68\x74\x20\x32\x30\x32\x33\x20\x47\x6F\x6F\x67\x6C\x65\x20\x4C\x4C\x43\x2E\x20\x41\x6C\x6C\x20\x52\x69\x67\x68\x74\x73\x20\x52\x65\x73\x65\x72\x76\x65\x64\x2E"), NULL);
	}
	{
		DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B * tmp = (DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B *)cache->attributes[10];
		DebuggableAttribute__ctor_m7FF445C8435494A4847123A668D889E692E55550(tmp, 2LL, NULL);
	}
	{
		AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7 * tmp = (AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7 *)cache->attributes[11];
		AssemblyTitleAttribute__ctor_mE239F206B3B369C48AE1F3B4211688778FE99E8D(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x4D\x6F\x62\x69\x6C\x65\x41\x64\x73"), NULL);
	}
	{
		AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3 * tmp = (AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3 *)cache->attributes[12];
		AssemblyDescriptionAttribute__ctor_m3A0BD500FF352A67235FBA499FBA58EFF15B1F25(tmp, il2cpp_codegen_string_new_wrapper(""), NULL);
	}
	{
		AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA * tmp = (AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA *)cache->attributes[13];
		AssemblyProductAttribute__ctor_m26DF1EBC1C86E7DA4786C66B44123899BE8DBCB8(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x4D\x6F\x62\x69\x6C\x65\x41\x64\x73"), NULL);
	}
	{
		AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4 * tmp = (AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4 *)cache->attributes[14];
		AssemblyCompanyAttribute__ctor_m435C9FEC405646617645636E67860598A0C46FF0(tmp, il2cpp_codegen_string_new_wrapper("\x47\x6F\x6F\x67\x6C\x65\x20\x4C\x4C\x43"), NULL);
	}
	{
		AssemblyConfigurationAttribute_t071B324A83314FBA14A43F37BE7206C420218B7C * tmp = (AssemblyConfigurationAttribute_t071B324A83314FBA14A43F37BE7206C420218B7C *)cache->attributes[15];
		AssemblyConfigurationAttribute__ctor_m6EE76F5A155EDEA71967A32F78D777038ADD0757(tmp, il2cpp_codegen_string_new_wrapper(""), NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__0_m6EB81ECE17183A949FE0D9A1E1161982BF11103C(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__1_mDD77C10B8C79F36145F039C5E46E2A5F4BF98CD8(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__2_m0745E7DCB5926AEC7A942C72CFA54A04533BE2B3(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__3_m9E010CC5392B712457E641BF5548AAD5F900DB66(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__4_m9E97077F81575A81B9AB836AEB1AB284F72C7DA4(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__5_m1A2128B5A04CDD0DD741FF0FA7E614914662D349(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__6_mDA2F15595A3B1DE9A59987F766BDBDE62DA02B3B(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__7_mC2C2932C98115FB731FEBCEB7D14BCECDBF18E04(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__8_mEBBF84209D60D055AA2108C55F9C5B256A111786(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__9_mFAF62BAD52DBBA208A350C8287396F93ADE86D40(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CLoadU3Ec__AnonStorey0_t18F05EC16F465CDA73BB2F2A4E445B0951DAA0B3_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey4_tDD386DCEDA5EC2299327149FEF7DC8BFA2474003_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey5_t16E862463AA91A1B059587F593E52C8FECCC8AF3_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__0_mAB59CDA05FF9FAC2CBE5CB5815C46895F641CB54(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__1_mCC40AAD739C5097E87710765BD6E616290EB9A8F(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__2_m45BDA7B13065BB9326A56CB29357CC5BE655AEFB(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__3_mC978D558480BB77CB457D63268531A4B42F05DB1(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__4_mF3FFB125AB04A05BCAD1AD9895549454FCDC22CC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__5_mC876D2C02AE75C3F88460A7B1117A6A8E83BBFDA(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__6_m5CF638B97328668C57B6ADD8F894F8A896E5CE55(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__7_m2EF5ACBA7BC2ED7757DE3168313B510254D40360(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__8_m45637795C8771179168190F2416C12E00F7F5FD4(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__9_mBAFF6B7A8D1EBEC7EAA84045F33688A758D5E350(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__A_m7E6F8FC51B0D9116AD0FCDD8BA911ACDEEF81B6B(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__B_m3F0E58BBAF524995BE9191214C7A00866D239EE7(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CConfigureBannerEventsU3Ec__AnonStorey0_t8D7A4E07534568D47D5CAC4DD4048CFA0B96954D_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CConfigureBannerEventsU3Ec__AnonStorey1_tE04C8662CA6BB59526E972D0BF19F67D3ADBD713_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__0_m5C25BCCFC3BF2A06F96EC0835D47171031D421BB(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__1_mC7A3521C70DAAE2ABA2EE6D9C991B2F67D8F8958(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__2_m750551D2E4E46E2F6B45F8034DC66D11021D21CC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__3_mA5A87376CBE52F0B1B619BC1D93E80583FD9FCB3(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__4_m88E8DE4728A66B7F5D639F15DAD0CDD62DD45710(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__5_mBF6B8452E7AD80EE2856817AFA2D559125B00A1D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__6_m5704D40E2891F4DA8D22775889CC7ACF18477910(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__7_mBFF8EE45A4089CD23352D93FA5343038A6568AC0(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__8_m562F35643C919BED83DDA088AB0744F9436B8365(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__9_m49563EB6919CD19798232B4B5B7B067111F4DD5F(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CLoadU3Ec__AnonStorey0_tF56A7165D07857276C0520C54C1725C96BAD1394_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey3_t9C60D10F3D9FB66A0C49964353DC40849E34405B_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey4_t4A470A0ECB17E1CF95CA364906A6C26B0B80E28F_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_CustomAttributesCacheGenerator_U3CRaiseAdEventsOnUnityMainThreadU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
	{
		DebuggerBrowsableAttribute_t2FA4793AD1982F5150E07D26822ED5953CD90F53 * tmp = (DebuggerBrowsableAttribute_t2FA4793AD1982F5150E07D26822ED5953CD90F53 *)cache->attributes[1];
		DebuggerBrowsableAttribute__ctor_mAA8BCC1E418754685F320B14A08AC226E76346E5(tmp, 0LL, NULL);
	}
}
static void MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_CustomAttributesCacheGenerator_MobileAds_get_RaiseAdEventsOnUnityMainThread_m666A82842C0649D1082F7093EEA4339F5040F86D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CInitializeU3Ec__AnonStorey0_tDE5E51D76D3E60BFD962C0DE8C6965C11B06FDD4_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRaiseActionU3Ec__AnonStorey4_t19EBDC2629FB63E7C3847E892D22A896A0D32673_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__0_mAEB8B50B32D272E4C7619E7DCC33B523F94BE5EC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__1_m141DE54DA1D602B3EB716134CE0FBADCD2144744(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__2_m5EEEB3616932EC86B7C5F2F182C77890C1AFD5BA(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__3_mF6E35C54409557EB2C616968A93848D0C76274E0(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__4_m2A8445A99D60F80091ACF715A44698A34FF64C2F(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__5_m1964F8BF3D5D2B871688F810CCC93C0B1988CC3C(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__6_m02BAE62EBD229AAE3E055A27950B6BDA2348DDEF(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__7_m3363873E5ED93C748889CBBA82F729836EEEF341(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__8_m07B5B43AAD0578F38B75424B547CCF128B4993BF(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__9_m31BBA6C3DE03F61AA0AD2F9BAD77099FE7299902(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__A_mA16151D456016AA5B967CA38D0E6C26D3138F9CC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CLoadU3Ec__AnonStorey0_t6041C919D2F462D1B29F9B1F6836C104707CBC69_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey2_tFF2B0B50DD39E1ECC7C160590C316F491E644CB0_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey3_tDAF748BBA63689110FA9BD0785EB994FDF2EA93F_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey4_tD2D2B6288AD1D8E7E51B08E43914DBED2AA2ADCF_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m4280C1BED635E8CE98831B28294A9449DAB06A25(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m94CF1788E4D4B09025A96C2E0B0B2F8E787BAD61(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_mCAF40DE07FAF857BB7C1D2C912D787AA7AB5BA49(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m49F90F797705E782795E7CE3549809389CBF9556(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m70A2ADA6428F0F6A5F28126640D1991D29EEA750(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m9C805E78125CE3E185F7F62351B8EAC3391C77F3(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_m932A05721D7C43A3AEF86AB3694B98DE14F7C824(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_m634C748D4119E9D2AC7F0887C838B6A2B125DB03(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mC29EE6EF37D9872E422D28750BABD3465BD2EF74(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m33BB80707918E085C311394254E35D827FEBEA70(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m27A124481E82CF9ECF20BD94A3C9396C94CCD948(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CLoadU3Ec__AnonStorey0_t21DAEA2B05F2558CE28F92FD68BFD5DA3F248E83_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey2_tA51179490B0D050E7291CB16C8973465E8222261_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey3_t793B456CB63101BBA8D83917B59B2D2B6C85E84A_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void U3CRegisterAdEventsU3Ec__AnonStorey4_t797673F3D9EF71FC86206B3D194AAD52FB46C37D_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
IL2CPP_EXTERN_C const CustomAttributesCacheGenerator g_GoogleMobileAds_AttributeGenerators[];
const CustomAttributesCacheGenerator g_GoogleMobileAds_AttributeGenerators[75] = 
{
	U3CLoadU3Ec__AnonStorey0_t18F05EC16F465CDA73BB2F2A4E445B0951DAA0B3_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey4_tDD386DCEDA5EC2299327149FEF7DC8BFA2474003_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey5_t16E862463AA91A1B059587F593E52C8FECCC8AF3_CustomAttributesCacheGenerator,
	U3CConfigureBannerEventsU3Ec__AnonStorey0_t8D7A4E07534568D47D5CAC4DD4048CFA0B96954D_CustomAttributesCacheGenerator,
	U3CConfigureBannerEventsU3Ec__AnonStorey1_tE04C8662CA6BB59526E972D0BF19F67D3ADBD713_CustomAttributesCacheGenerator,
	U3CLoadU3Ec__AnonStorey0_tF56A7165D07857276C0520C54C1725C96BAD1394_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey3_t9C60D10F3D9FB66A0C49964353DC40849E34405B_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey4_t4A470A0ECB17E1CF95CA364906A6C26B0B80E28F_CustomAttributesCacheGenerator,
	U3CInitializeU3Ec__AnonStorey0_tDE5E51D76D3E60BFD962C0DE8C6965C11B06FDD4_CustomAttributesCacheGenerator,
	U3CRaiseActionU3Ec__AnonStorey4_t19EBDC2629FB63E7C3847E892D22A896A0D32673_CustomAttributesCacheGenerator,
	U3CLoadU3Ec__AnonStorey0_t6041C919D2F462D1B29F9B1F6836C104707CBC69_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey2_tFF2B0B50DD39E1ECC7C160590C316F491E644CB0_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey3_tDAF748BBA63689110FA9BD0785EB994FDF2EA93F_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey4_tD2D2B6288AD1D8E7E51B08E43914DBED2AA2ADCF_CustomAttributesCacheGenerator,
	U3CLoadU3Ec__AnonStorey0_t21DAEA2B05F2558CE28F92FD68BFD5DA3F248E83_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey2_tA51179490B0D050E7291CB16C8973465E8222261_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey3_t793B456CB63101BBA8D83917B59B2D2B6C85E84A_CustomAttributesCacheGenerator,
	U3CRegisterAdEventsU3Ec__AnonStorey4_t797673F3D9EF71FC86206B3D194AAD52FB46C37D_CustomAttributesCacheGenerator,
	MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_CustomAttributesCacheGenerator_U3CRaiseAdEventsOnUnityMainThreadU3Ek__BackingField,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__0_m6EB81ECE17183A949FE0D9A1E1161982BF11103C,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__1_mDD77C10B8C79F36145F039C5E46E2A5F4BF98CD8,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__2_m0745E7DCB5926AEC7A942C72CFA54A04533BE2B3,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__3_m9E010CC5392B712457E641BF5548AAD5F900DB66,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__4_m9E97077F81575A81B9AB836AEB1AB284F72C7DA4,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__5_m1A2128B5A04CDD0DD741FF0FA7E614914662D349,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__6_mDA2F15595A3B1DE9A59987F766BDBDE62DA02B3B,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__7_mC2C2932C98115FB731FEBCEB7D14BCECDBF18E04,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__8_mEBBF84209D60D055AA2108C55F9C5B256A111786,
	AppOpenAd_t638A7955FBB06BDEEC55D2949D27B83A45D74E0B_CustomAttributesCacheGenerator_AppOpenAd_U3CRegisterAdEventsU3Em__9_mFAF62BAD52DBBA208A350C8287396F93ADE86D40,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__0_mAB59CDA05FF9FAC2CBE5CB5815C46895F641CB54,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__1_mCC40AAD739C5097E87710765BD6E616290EB9A8F,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__2_m45BDA7B13065BB9326A56CB29357CC5BE655AEFB,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__3_mC978D558480BB77CB457D63268531A4B42F05DB1,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__4_mF3FFB125AB04A05BCAD1AD9895549454FCDC22CC,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__5_mC876D2C02AE75C3F88460A7B1117A6A8E83BBFDA,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__6_m5CF638B97328668C57B6ADD8F894F8A896E5CE55,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__7_m2EF5ACBA7BC2ED7757DE3168313B510254D40360,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__8_m45637795C8771179168190F2416C12E00F7F5FD4,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__9_mBAFF6B7A8D1EBEC7EAA84045F33688A758D5E350,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__A_m7E6F8FC51B0D9116AD0FCDD8BA911ACDEEF81B6B,
	BannerView_tCE6FD894697CDF14C3870309DEE9F9B8FB317CC0_CustomAttributesCacheGenerator_BannerView_U3CConfigureBannerEventsU3Em__B_m3F0E58BBAF524995BE9191214C7A00866D239EE7,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__0_m5C25BCCFC3BF2A06F96EC0835D47171031D421BB,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__1_mC7A3521C70DAAE2ABA2EE6D9C991B2F67D8F8958,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__2_m750551D2E4E46E2F6B45F8034DC66D11021D21CC,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__3_mA5A87376CBE52F0B1B619BC1D93E80583FD9FCB3,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__4_m88E8DE4728A66B7F5D639F15DAD0CDD62DD45710,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__5_mBF6B8452E7AD80EE2856817AFA2D559125B00A1D,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__6_m5704D40E2891F4DA8D22775889CC7ACF18477910,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__7_mBFF8EE45A4089CD23352D93FA5343038A6568AC0,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__8_m562F35643C919BED83DDA088AB0744F9436B8365,
	InterstitialAd_tDBF075997F8CDE79B2E223E0B117FCC5796A473C_CustomAttributesCacheGenerator_InterstitialAd_U3CRegisterAdEventsU3Em__9_m49563EB6919CD19798232B4B5B7B067111F4DD5F,
	MobileAds_t94FBD442C710E5C35536E85C517CE33B9333EA4D_CustomAttributesCacheGenerator_MobileAds_get_RaiseAdEventsOnUnityMainThread_m666A82842C0649D1082F7093EEA4339F5040F86D,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__0_mAEB8B50B32D272E4C7619E7DCC33B523F94BE5EC,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__1_m141DE54DA1D602B3EB716134CE0FBADCD2144744,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__2_m5EEEB3616932EC86B7C5F2F182C77890C1AFD5BA,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__3_mF6E35C54409557EB2C616968A93848D0C76274E0,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__4_m2A8445A99D60F80091ACF715A44698A34FF64C2F,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__5_m1964F8BF3D5D2B871688F810CCC93C0B1988CC3C,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__6_m02BAE62EBD229AAE3E055A27950B6BDA2348DDEF,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__7_m3363873E5ED93C748889CBBA82F729836EEEF341,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__8_m07B5B43AAD0578F38B75424B547CCF128B4993BF,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__9_m31BBA6C3DE03F61AA0AD2F9BAD77099FE7299902,
	RewardedAd_tE35A13E627AB27659F9C7DA24CF5BB4CA855ABB2_CustomAttributesCacheGenerator_RewardedAd_U3CRegisterAdEventsU3Em__A_mA16151D456016AA5B967CA38D0E6C26D3138F9CC,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m4280C1BED635E8CE98831B28294A9449DAB06A25,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m94CF1788E4D4B09025A96C2E0B0B2F8E787BAD61,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_mCAF40DE07FAF857BB7C1D2C912D787AA7AB5BA49,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m49F90F797705E782795E7CE3549809389CBF9556,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m70A2ADA6428F0F6A5F28126640D1991D29EEA750,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m9C805E78125CE3E185F7F62351B8EAC3391C77F3,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_m932A05721D7C43A3AEF86AB3694B98DE14F7C824,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_m634C748D4119E9D2AC7F0887C838B6A2B125DB03,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mC29EE6EF37D9872E422D28750BABD3465BD2EF74,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m33BB80707918E085C311394254E35D827FEBEA70,
	RewardedInterstitialAd_tD1D98AC0E09723A325BDEE7A4CB929BC6620720D_CustomAttributesCacheGenerator_RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m27A124481E82CF9ECF20BD94A3C9396C94CCD948,
	GoogleMobileAds_CustomAttributesCacheGenerator,
};
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, bool ___value0, const RuntimeMethod* method)
{
	{
		bool L_0 = ___value0;
		__this->set_m_wrapNonExceptionThrows_0(L_0);
		return;
	}
}
